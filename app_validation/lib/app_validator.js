/**
 *  Middleware to check for validate the LEAP App.
 *
 *  <AUTHOR>
 **/
const pluginmanager = require("pluginmanager");
const message = require("message");
const common = require("common");
const resCodes = message.error_codes;
const httpCodes = common.http_codes;
const allowedSortFields = global.config.api_gw.appsApi.allowedSortFields ||
  { name: "name", id: "id", status: "status", createdAt: "createdAt", updatedAt: "updatedAt" };
const allowedSortOrder = global.config.api_gw.appsApi.allowedSortOrder ||
  { asc: "asc", desc: "desc" };
const REGULAR_EXP_NAME = global.config.api_gw.appsApi.expressionAppName || "^[a-zA-Z0-9]{1,}[a-zA-Z0-9_ ]*[a-zA-Z0-9]{1,}$";
const MAX_LEN_APPNAME = global.config.api_gw.appsApi.maxLengthAppName || 36;
const MAX_LEN_APPDESC = global.config.api_gw.appsApi.maxLengthAppDesc || 128;
const REGULAR_EXP_APPSTATES = global.config.api_gw.appsApi.expressionAppStates || "^[0-9]{1,2}(,[0-9]{1,2})+$";
const DESC_REGULAR_EXP = "^[0-9a-zA-Z\\s]+$";
module.exports = {
  /**
   * @returns Class refrence of custom HTTP response codes of App management
   */
  validate: async (req, res) => {
    try {
      let plugin = req.body;
      if (!plugin.name) { plugin.name = plugin.type; }
      let result = await pluginmanager.validate(plugin.type, plugin);
      res.json({ errors: result });
    } catch (e) {
      res.status(400).json({ errors: e });
    }
  },
  /**
   * @api validateGetApps: API to validate retreive Apps list.
   * @description This is a middleware for App list API, which validates the query parameters required
   * and their data types.
   *
   * @returns HTTP Response
   */
  validateGetApps: (req, res, next) => {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("API Request params:" + JSON.stringify(req.query));
    }
    let badRequest = httpCodes.badRequest.code;

    if (req.query.page != null) {
      let page = Number(req.query.page);
      if (isNaN(page)) {
        return res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidPageField));
      }
      if (page <= 0) {
        return res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.pageDoesNotExists));
      }
    }
    if (req.query.size != null) {
      let size = Number(req.query.size);
      if (isNaN(size) || size < 0) {
        return res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidSizeField));
      }
    }

    if (req.query.sortf != null && !allowedSortFields[req.query.sortf]) {
      return res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidSortfField));
    }

    if (req.query.order != null && !allowedSortOrder[req.query.order]) {
      return res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidOrderField));
    }
    if (req.query.status != null) {
      let status = req.query.status;
      if (status.indexOf(",") > -1) {
        if (status.match(REGULAR_EXP_APPSTATES) == null) {
          return res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidStatusField));
        }
      } else if (isNaN(Number(status))) {
        return res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidStatusField));
      }
    }
    return next();
  },
  /**
   * @api validateCreateAppInfo: API to validate
   * @description This is a middleware for App creation API, which validates the AppSchema
   *
   * @returns HTTP Response
   */
  validateCreateAppInfo: (req, res, next) => {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Executing validateAppInfo()...");
    }
    if (validateHeadersAndRequiredFields(req, res, false)) {
      req.body.createdBy = req.invoker && req.invoker.id || 0;
      req.body.modifiedBy = req.body.createdBy;
      req.body.owner = req.body.createdBy;
      req.body.createdAt = new Date().getTime();
      req.body.updatedAt = req.body.createdAt;
      req.body.status = 0;
      return next();
    }
  },

  validateUpdateAppInfo: (req, res, next) => {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Executing validateUpdateAppInfo()...");
    }
    if (validateHeadersAndRequiredFields(req, res, true)) {
      req.body.status = 0;
      if (!req.params.appId) {
        return res.status(httpCodes.badRequest.code).json(message.getResponseJson(req.locale, resCodes.missingId));
      }
      req.body.id = req.params.appId;
      req.body.modifiedBy = req.invoker && req.invoker.id || 0;
      req.body.updatedAt = new Date().getTime();
      return next();
    }
  },

  validateAssignApp: (req, res, next) => {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Executing validateAssignApp()...");
    }
    if (validateHeaders(req, res)) {
      if (!req.params.appId) {
        return res.status(httpCodes.badRequest.code).json(message.getResponseJson(req.locale, resCodes.missingId));
      }
      req.body.id = req.params.appId;
      req.body.modifiedBy = req.invoker && req.invoker.id || 0;
      req.body.updatedAt = new Date().getTime();
      return next();
    }
  },

  /**
   * @api validateCreateAppInfo: API to validate
   * @description This is a middleware for App creation API, which validates the AppSchema
   *
   * @returns HTTP Response
   */
  validateCloneAppTemplate: (req, res, next) => {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Executing validateCloneAppTemplate()...");
    }
    if (validateHeadersAndRequiredFields(req, res, false)) {
      if (!req.body.appTemplateId) {
        return res.status(httpCodes.badRequest.code).json(message.getResponseJson(req.locale, resCodes.missingAppTemplateId));
      }
      req.body.id = new Date().getTime();
      req.body.createdBy = req.invoker && req.invoker.id || 0;
      req.body.modifiedBy = req.body.createdBy;
      req.body.owner = req.body.createdBy;
      req.body.createdAt = new Date().getTime();
      req.body.updatedAt = req.body.createdAt;
      req.body.status = 0;
      return next();
    }
  },
  /**
   * @api validateCloneApp: API to validate
   * @description This is a middleware for App creation API, which validates the AppSchema
   *
   * @returns HTTP Response
   */
  validateCloneApp: (req, res, next) => {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Executing validateAppInfo()...");
    }
    if (validateHeadersAndRequiredFields(req, res, false)) {
      req.body.id = new Date().getTime();
      req.body.createdBy = req.invoker && req.invoker.id || 0;
      req.body.modifiedBy = req.body.createdBy;
      req.body.owner = req.body.createdBy;
      req.body.createdAt = new Date().getTime();
      req.body.updatedAt = req.body.createdAt;
      req.body.status = 0;
      return next();
    }
  },

  getModuleErrors: async (appData) => {
    const errors = {};
    try {
      if (typeof appData == "string") {
        appData = JSON.parse(appData);
      }
      const keys = Object.keys(appData.modules);
      for (var i = 0; i < keys.length; i++) {
        const moduleId = keys[i];
        const plugin = appData.modules[moduleId];
        if (!plugin.name) { plugin.name = plugin.type; }
        try {
          errors[moduleId] = await pluginmanager.validate(plugin.type, plugin);
        } catch (e) {
          errors[moduleId] = [];
        }
      }
    } catch (e) {
      global.logger.error(e);
    }
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Appdata validation:" + JSON.stringify(errors));
    }
    return errors;
  },

  validateUploadAppLocaleInfo: (req, res, next) => {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Executing validateUploadAppLocaleInfo()...");
    }

    if (req.headers["content-type"] == null || !req.headers["content-type"].includes("application/json")) {
      res.status(httpCodes.badRequest.code).json(message.getResponseJson(req.locale, resCodes.invalidContentType));
      return false;
    }
    if (!req.params.appId) {
      return res.status(httpCodes.badRequest.code).json(message.getResponseJson(req.locale, resCodes.missingId));
    }
    req.body.updatedAt = new Date().getTime();
    return next();
  },

  validateCreateUssdServiceInfo: (req, res, next) => {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Executing validateCreateUssdServiceInfo()...");
    }
    if (validateUssdHeadersAndRequiredFields(req, res, true)) {
      req.body.createdBy = req.invoker && req.invoker.id || 0;
      req.body.modifiedBy = req.body.createdBy;
      req.body.createdAt = new Date().getTime();
      req.body.updatedAt = req.body.createdAt;
      req.body.status = 1;
      return next();
    }
  },
  validateUpdateUssdServiceInfo: (req, res, next) => {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Executing validateUpdateUssdServiceInfo()...");
    }
    if (validateUssdHeadersAndRequiredFields(req, res, false)) {
      req.body.modifiedBy = req.invoker && req.invoker.id;
      req.body.updatedAt = new Date().getTime();
      req.body.shortcode = req.params.shortcode;
      return next();
    }
  },

  validateCreateUssdConfigInfo: (req, res, next) => {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Executing validateCreateUssdConfigInfo()...");
    }
    if (validateUssdGwHeadersAndRequiredFields(req, res, true)) {
      req.body.createdBy = req.invoker && req.invoker.id || 0;
      req.body.modifiedBy = req.body.createdBy;
      req.body.createdAt = new Date().getTime();
      req.body.updatedAt = req.body.createdAt;
      req.body.status = 1;
      return next();
    }
  },

  validateUpdateUssdConfigInfo: (req, res, next) => {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Executing validateCreateUssdConfigInfo()...");
    }
    if (validateUssdGwHeadersAndRequiredFields(req, res, false)) {
      req.body.modifiedBy = req.invoker && req.invoker.id;
      req.body.updatedAt = new Date().getTime();
      return next();
    }
  }
};

function validateUssdHeadersAndRequiredFields(req, res, flag) {

  let badRequest = httpCodes.badRequest.code;
  if (req.headers["content-type"] == null || !req.headers["content-type"].includes("application/json")) {
    res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidContentType));
    return false;
  }
  if (flag || req.body.name != null) {
    if (!req.body.name) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.missingUssdName));
      return false;
    }
    if (req.body.name.length < 5) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.ussdNameMinLimitUnmet));
      return false;
    }

    if (req.body.name.match(REGULAR_EXP_NAME) == null || req.body.name.split(" ").length - 1 > 5) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidUssdName));
      return false;
    }
    if (req.body.name.length > MAX_LEN_APPNAME) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.ussdNameMaxLimitExceeds));
      return false;
    }
  }

  if (typeof req.body.options == "string") {
    try {
      req.body.options = JSON.parse(req.body.options);
    } catch (error) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidJSONBody));
      return false;
    }
  }

  if (req.body.desc && req.body.desc.length > MAX_LEN_APPDESC) {
    res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.ussdDescMaxLimitExceeds));
    return false;
  }
  if (req.body.desc && req.body.desc.match(DESC_REGULAR_EXP) == null) {
    res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.ussdServiceDescError));
    return false;
  }
  if (req.body.shortcode != null) {
    if (req.body.shortcode.startsWith("*")) {
      req.body.shortcode = req.body.shortcode.slice(1);
    }

    if (req.body.shortcode.endsWith("#")) {
      req.body.shortcode = req.body.shortcode.slice(0, -1);
    }
  }

  return true;
}

function validateUssdGwHeadersAndRequiredFields(req, res, flag) {

  let badRequest = httpCodes.badRequest.code;
  if (req.headers["content-type"] == null || !req.headers["content-type"].includes("application/json")) {
    res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidContentType));
    return false;
  }
  if (flag || req.body.name != null) {
    if (!req.body.name) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.missingUssdGwConfigName));
      return false;
    }
    if (req.body.name.length < 5) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.ussdGwConfigNameMinLimitUnmet));
      return false;
    }

    if (req.body.name.match(REGULAR_EXP_NAME) == null || req.body.name.split(" ").length - 1 > 5) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidUssdGwConfigName));
      return false;
    }
    if (req.body.name.length > MAX_LEN_APPNAME) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.ussdGwConfigNameMaxLimitExceeds));
      return false;
    }
  }

  return true;
}

function validateHeadersAndRequiredFields(req, res, skipNameValidation) {

  let badRequest = httpCodes.badRequest.code;
  if (req.headers["content-type"] == null || !req.headers["content-type"].includes("application/json")) {
    res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidContentType));
    return false;
  }
  if (!skipNameValidation) {
    if (!req.body.name) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.missingName));
      return false;
    }
    if (req.body.name.length < 2) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.appNameMinLimitUnmet));
      return false;
    }

    if (req.body.name.match(REGULAR_EXP_NAME) == null || req.body.name.split(" ").length - 1 > 5) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidName));
      return false;
    }
    if (req.body.name.length > MAX_LEN_APPNAME) {
      res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.appNameMaxLimitExceeds));
      return false;
    }
  }

  if (req.body.desc && req.body.desc.length > MAX_LEN_APPDESC) {
    res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.appDescMaxLimitExceeds));
    return false;
  }
  return true;
}

function validateHeaders(req, res) {
  let badRequest = httpCodes.badRequest.code;
  if (req.headers["content-type"] == null || !req.headers["content-type"].includes("application/json")) {
    res.status(badRequest).json(message.getResponseJson(req.locale, resCodes.invalidContentType));
    return false;
  }
  return true;
}
