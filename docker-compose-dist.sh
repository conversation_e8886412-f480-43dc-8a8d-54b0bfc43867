#!/bin/bash

version="3.0.8"

images=(
    leap/pm_fsync:$version
	leap/api_gw:$version
	leap/gui:$version
	leap/app_engine:$version
	leap/authserver:$version
	leap/cdr_sweeper:$version
	leap/node_logger:$version
	grafana/grafana:latest
	telegraf:latest
	influxdb:latest
	docker.elastic.co/logstash/logstash:7.5.2
	docker.elastic.co/elasticsearch/elasticsearch:7.5.2
)

outdir=dist
imgdir=$outdir/images
patch="leap-patch-$version-`date +"%Y%m%d"`.tgz"

main() {
	mkdir -p $outdir
	mkdir -p $imgdir

        echo "gunzip images/*.gz" >$outdir/loadimg.sh
	for image in ${images[*]} ; do
		out_file=`echo $image | tr "/:" "_-"`.img
		echo building image for $image
                docker save $image -o $imgdir/$out_file
                gzip $imgdir/$out_file
		echo docker load -i images/$out_file >>$outdir/loadimg.sh
	done
        echo mkdir -p /data/leap/logs >>$outdir/loadimg.sh
	echo mkdir -p /data/leap/kpi >>$outdir/loadimg.sh
	echo mkdir -p /data/leap/edrs >>$outdir/loadimg.sh
	echo mkdir -p /data/leap/cdrs >>$outdir/loadimg.sh
	echo mkdir -p /data/leap/cdrStore/cdrs >>$outdir/loadimg.sh
	echo mkdir -p /data/leap/cdrStore/proc >>$outdir/loadimg.sh
	echo mkdir -p /data/leap/cdrStore/backup >>$outdir/loadimg.sh
	echo mkdir -p /data/leap/backup >>$outdir/loadimg.sh
	cp -r  leap-docker-compose-files $outdir
        grep -v "build:" docker-compose.yml > $outdir/docker-compose.yml
	cp README.docker $outdir/README


	echo "creating tar ball $patch ..."
	(cd $outdir; tar -zcf $patch * ; mv $patch ..)
}

main
