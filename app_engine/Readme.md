---
title: LEAP App Engine API Reference

includes:
  - errors

search: true
---

# Introduction
```javascript
let request = require('request');
request('http://*************:4508/app_engine/development/1535665591227?msisdn=254750330721&RefillId=MC46&bundleinfo=25MB&tense=future&edata1=USSD_DATA&edata2=Volume_kes10_1Day_25MB&price=10&day=1&authkey=Y6EgamyWeTx5bKe409k4gqRviSh5PFkKRdTdbki0nHQ&code=544&reqType=once', function (error, response, body) {
  console.log('error:', error); // Print the error if one occurred
  console.log('statusCode:', response && response.statusCode); // Print the response status code if a response was received
  console.log('body:', body); // Print the JSON for the LEAP gateway configapi.
});
```

<b>Welcome to the LEAP App Engine API!</b>

App Engine Service is a NodeJS based Microservice, this microservice is a core component of the LEAP. It provides the Application execution environment for the Apps created and launched by the developers. These Apps are modelled based on business logic.

This service runs in a cluster mode, it is highly scalable interface and it is used to process the subscriber's requests as per the business logic. Apps are designed based on the business need using various "Module" available in the LEAP Developer's App creation page. Module is a sub-process of the App, based on the user input and the outcomes of previous step next Module will be processed.

LEAP Orchestrator is backend application execution module. GUI studio creates the service flow,a  series  of  well-defined  activity  paths  for  the  application,  and  stores  them  as  templates.  Backend orchestrator then loads the application dialog templates created; at either startup or hot reloaded. This  orchestrator  application  manages  interactive  user  sessions  over  any  of  the  communication channels (SMS, USSD). For example, when a user is interacting with a service provider via USSD, the user  will  navigate  between  many  levelsof  menus,  make  certain  choices  at  each  level,  or  navigate between several menus. From the server perspective, this can be an asynchronous interaction that could  end  in  a  minute,  or  continue  for  10  minutes  or  more. All  this  while,  the  server  has  to  keep track  of  the  state  of  each  user  (viz.,  when  menu  is  this  user  in,  what  choices  are  made,  on  which content provider is our app waiting, and so on and so forth.

Some of the salient features are:
<ul>
<li>Input/output Plugins: Ability to support arbitrary communication protocols and interfaces via additional input / output interfaces</li>
<li>Asynchronous and multi-threaded</li>
<li>Hot deployment of new /modified applications</li>
</ul>

LEAP provides the flexibility and controlled way for developer to create the application by defining the application flows using drag and drop connectors.
<ul>
<li>Develop and test application
</li>
<li>Manage and launch application
</li>
<li>Configuration and Administration
</li>
<li>System requirements
</li>
<li>Dashboards and reports</li>
</ul>

#Document History

Version	| Date	| Description |	Author 
--------| ------| ----------- | ------
1 | 15-Nov-2017 | <ul><li>Initial draft</li></ul> | Yuvaraj k
2 | 11-Aug-2018 | <ul><li>Added HTTP QS Interface</li></ul> | Yuvaraj k

#Abbreviations, Acronyms & Definitions
Term | Full form
--------| ------
ACIP| Account Administration Communication Integration Protocol
API | Application Programming Interface
CDR | Call Data Records
DB | Databases
FLARES | Framework for Launching Applications enabling Rapid Evolution of Services
GUI | Graphical User Interface
HTTP | Hypertext Transfer Protocol
IN | Intelligent Network
IVR | Interactive Voice Response
LEAP | Launch Easy Application Platform
OAM | Operations and Maintenance
OS | Operating System
PRD | Product Requirement Document
SMS | Short Message Services
SOAP | Simple Object Access Protocol
TCP/IP | Transmission Control Protocol / Internet Protocol
TPS | Transactions per Second
UCIP | User Communication Integration Protocol
USSD | Unstructured Supplementary Service Data
VSIP | Voucher Server Communication Integration Protocol

# Web Socket Interface: Application Simulation

LEAP App simulation page has console window, which inturn uses the Websocket for client and server interactions.

WebSocket is a simple to use, blazing fast, and thoroughly tested WebSocket client and server implementation.

<aside class="notice"><b>NPM Module: </b>is https://www.npmjs.com/package/ws Version: 4.0.0</aside>

## Design 

<b>Step 1: Pre-Launch settings</b>
<p align="center"><img src="/images/AppSimulation-Step1.jpeg" width=700 alt="Workflow Management Design"></p>

<b>Step 2: Processing module</b>
<p align="center"><img src="/images/AppSimulation-Step2.jpeg" width=700 alt="Workflow Management Design"></p>

## Websocket 

<b>Following steps for setting web console session.</b>

<ul>
<li>
1. Invoke config API to get websocket http://*************:7778/leap_gw/configapi/simulationinfo  
    Response payload:
    {
        "app_url": "http://*************:4508/app_engine/development/",
        "websocket_url": "ws://*************:4508"
    }

</li>
<li>
2. Open new websocket client for "websocket_url" "ws://*************:4508"
</li>
<li>
3. Register the AppId and Invoker by sending following message to the server.

WS_REGISTER|{invokerId}|{appId}

example:
WS_REGISTER|5|1517380984746


Response payload for above request, 

WS_REGISTER|WS_OK|{sessionId}

WS_REGISTER|WS_NOK|App not found
WS_REGISTER|WS_NOK|Unauthorized
WS_REGISTER|WS_NOK|Internal error

example:
WS_REGISTER|WS_OK|b6dba1da-5993-4967-9245-a3bf65dff278
</li>
<li>
4. When registration of console is successful then fire a restful api request for the app simulation launch request.

GET: {app_url}/app_engine/development/{appId}?MSISDN=**********&debugId={sessionId} along with other query parameters.

example:

http://*************:4508/app_engine/development/1517380984746?MSISDN=**********&subscriberNumber=**********&subscriberNumberNAI=**********&transactionCurrency=transactionCurrency&originTransactionID=1&originTimeStamp=********&debugId=b6dba1da-5993-4967-9245-a3bf65dff278 
</li>
<li>
5. Optional: For changing console settings, send following message to server over websocket.

WS_SETTINGS|{sessionId}|{"verbosity": "trace", "pbm": "Y"}

or

WS_SETTINGS|{sessionId}|{"verbosity": "trace"}

or

WS_SETTINGS|{sessionId}|{"pbm": "Y"}

</li>
<li>
6. For selecting next module, send following message to server over websocket.

WS_NEXT|{sessionId}

example: 

WS_NEXT|b6dba1da-5993-4967-9245-a3bf65dff278

</li>
<li>
7. For changing the VARS, send following message to server over websocket.

WS_VARS|{vars}

example:

WS_VARS|{"MSISDN":"**********","subscriberNumber":"**********","subscriberNumberNAI":"**********","transactionCurrency":"transactionCurrency","originTransactionID":"1","originTimeStamp":"********","debugId":"b6dba1da-5993-4967-9245-a3bf65dff278","host":"*************","port":59900,"path":"/jsp/respose.jsp","originNodeType":"EXT","originHostName":"TestClient","dedicatedAccountIDFirst":8,"requestSubDedicatedAccountDetailsFlag":1,"requestFirstAccessibleAndExpiredBalanceAndDateFlag":1,"requestActiveOffersFlag":0,"requestAttributesFlag":0,"negotiatedCapabilities":0,"requestAggregatedProductOfferInformationFlag":0,"messageCapabilityFlag":0,"dedicatedAccountSelection":1,"chargingRequestInformation":""}
</li>
</ul>

## Register Console

Use this endpoint to delete application in the system.

### HTTP Request

`GET http://example.com/app_engine/development/:appId`

<aside class="notice"><b>appId: </b>is application id</aside>

### Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
202 | Accepted | - | 
400 | Bad Request | V9004 | MSISDN query parameter is missing in the request
400 | Bad Request | V9006 | Mising some query parameters
401 | Unauthorized | V9009 | Access denied because of missing/wrong credentials
404 | Not Found | V9007 | Application not found in App store
404 | Not Found | V9008 | Application you are trying to access is not available in this engine
404 | Not Found | V9010 | Application is incomplete End module is missing
500 | Internal Server Error | E9000 | Application Management Internal Error

<aside class="success">
Remember — Register Console API!
</aside>

# HTTP(S) Pull Interface: Application Execution

## Query String Interface

> HTTP Request Payload:

```http

https://localhost:9002/app_engine/development/1564736997957?MSISDN=************

```

> Success Response Payload:

```http
HTTP/1.1 200 OK
X-Powered-By: Express
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET,PUT,POST,PATCH,DELETE,OPTIONS
Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId
X-FRAME-OPTIONS: SAMEORIGIN
X-XSS-Protection: 1;mode=block
X-Content-Type-Options: nosniff
Content-Security-Policy: script-src 'self'
X-Permitted-Cross-Domain-Policies: none
Referrer-Policy: no-referrer
Strict-Transport-Security: max-age=31536000 ; includeSubDomains
txnId: 11565513729019
Freeflow: FB
Content-Type: text/html; charset=utf-8
Content-Length: 59
ETag: W/"3b-bHrx43OLSLTui5JOAH4sbhHo4Bo"
Date: Sun, 11 Aug 2019 08:55:29 GMT
Connection: keep-alive

Dear Customer, Your request has been successfully processed

```

> HTTP Request Payload:

```http

https://localhost:9002/app_engine/development/1564736997957?

```

> Failure Response Payload:

```http
HTTP/1.1 400 Bad Request
X-Powered-By: Express
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET,PUT,POST,PATCH,DELETE,OPTIONS
Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId
X-FRAME-OPTIONS: SAMEORIGIN
X-XSS-Protection: 1;mode=block
X-Content-Type-Options: nosniff
Content-Security-Policy: script-src 'self'
X-Permitted-Cross-Domain-Policies: none
Referrer-Policy: no-referrer
Strict-Transport-Security: max-age=31536000 ; includeSubDomains
txnId: 11565513866923
Content-Type: application/json; charset=utf-8
Content-Length: 73
ETag: W/"49-RiReICB1z/mE/CJ7NpyvL2Ou1SA"
Date: Sun, 11 Aug 2019 08:57:46 GMT
Connection: keep-alive

{
  "code": "V9004",
  "msg": "MSISDN query parameter is missing in the request"
}
```

Use this endpoint to execute an application, which is created in LEAP Platform.

### HTTP Request

`GET http://example.com/app_engine/:mode/:appId`

<aside class="notice">
<b>appId:</b> is Application Identifier
</aside>

### App Engine Modes

Mode | Description
--------- | -----------
development | Application execution which supports step-by-step Simulation of an application.
staging | Staging server for an application for integration test. Pre-Production
production | Production server for an application for Go-Live.

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | Basic YWRtaW46YWRtaW4= | Follow below proceedure to generate the token.

### Procedure to generate Authorization value

#### 1. Concatenate the user name with a colon, and the password. For example, a user name of admin, and a password of admin becomes the following string:
admin:admin

#### 2. Encode this user name and password string in base64 encoding.
#### 3. Include this encoded user name and password in an HTTP Authorization: Basic header. For example, with an encoded user name of admin, and a password of admin, the following header is created:
Authorization: Basic YWRtaW46YWRtaW4=

### HTTP Request Query Parameters

Parameter |  Description | Required
--------- | ----------- | -----------
MSISDN | Mobile Station International Subscriber Directory Number | Mandatory
subscriberInput | Current input of the subscriber | Mandatory
isNewRequest | Identifying whether the request is new free flow request or continuous request | Mandatory
language | Subscriber language code | Optional
... | Reset of the parameters defined in application | Mandatory

### HTTP Request Query Parameter - isNewRequest

Code | Description
--------- | -----------
0 | Indicate process the subscriberInput for existing session
1 | Force to Create new session. 

### HTTP Response Header-Freeflow

Code | Description
--------- | -----------
FB | Free flow break, which indicates end of session
FC | Free flow continue, indicates requires more inputs.

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | OK | - | Success/Failure code defined in Application
400 | Bad Request | V9004 | MSISDN query parameter is missing in the request
400 | Bad Request | V9006 | Mising some query parameters
401 | Unauthorized | V9009 | Access denied because of missing/wrong credentials
404 | Not Found | V9007 | Application not found in App store
404 | Not Found | V9008 | Application you are trying to access is not available in this engine
404 | Not Found | V9010 | Application is incomplete End module is missing
500 | Internal Server Error | E9000 | Application Management Internal Error

<aside class="success">
Remember — HTTP(S)-Query string interface API!
</aside>

## JSON Request/Response

> HTTP Request Payload:

```http

https://localhost:9002/app_engine/development/1564736997957?

```

```json
{
    "MSISDN": "************",
    "subscriberInput": "m",
    "subscriberType": 1,
    "key1": "Text Value",
    "key2": {
        "key1": "JSON Value"
    },
    "key3": [
        "Array value"
    ]
}

```

> Success Response Payload:

```http
HTTP/1.1 200 OK
X-Powered-By: Express
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET,PUT,POST,PATCH,DELETE,OPTIONS
Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId
X-FRAME-OPTIONS: SAMEORIGIN
X-XSS-Protection: 1;mode=block
X-Content-Type-Options: nosniff
Content-Security-Policy: script-src 'self'
X-Permitted-Cross-Domain-Policies: none
Referrer-Policy: no-referrer
Strict-Transport-Security: max-age=31536000 ; includeSubDomains
txnId: 11565519643989
Freeflow: FB
Content-Type: application/json; charset=utf-8
Content-Length: 80
ETag: W/"50-GwQNSx7BXkplvkz/MfLYWNdM1hk"
Date: Sun, 11 Aug 2019 10:34:05 GMT
Connection: keep-alive

{
    "code": 200,
    "msg": "Dear Customer, Your request has been successfully processed"
}

```

> HTTP Request Payload:

```http

https://localhost:9002/app_engine/development/1564736997957?

```

```json
{
    "subscriberInput": "m",
    "subscriberType": 1,
    "key1": "Text Value",
    "key2": {
        "key1": "JSON Value"
    },
    "key3": [
        "Array value"
    ]
}

```

> Failure Response Payload:

```http
HTTP/1.1 400 Bad Request
X-Powered-By: Express
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET,PUT,POST,PATCH,DELETE,OPTIONS
Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId
X-FRAME-OPTIONS: SAMEORIGIN
X-XSS-Protection: 1;mode=block
X-Content-Type-Options: nosniff
Content-Security-Policy: script-src 'self'
X-Permitted-Cross-Domain-Policies: none
Referrer-Policy: no-referrer
Strict-Transport-Security: max-age=31536000 ; includeSubDomains
txnId: 11565513866923
Content-Type: application/json; charset=utf-8
Content-Length: 73
ETag: W/"49-RiReICB1z/mE/CJ7NpyvL2Ou1SA"
Date: Sun, 11 Aug 2019 08:57:46 GMT
Connection: keep-alive

{
  "code": "V9004",
  "msg": "MSISDN query parameter is missing in the request"
}
```

Use this endpoint to execute an application, which is created in LEAP Platform.

### HTTP Request

`POST http://example.com/app_engine/:mode/:appId`

<aside class="notice">
<b>appId:</b> is Application Identifier
</aside>

### App Engine Modes

Mode | Description
--------- | -----------
development | Application execution which supports step-by-step Simulation of an application.
staging | Staging server for an application for integration test. Pre-Production
production | Production server for an application for Go-Live.

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | Basic YWRtaW46YWRtaW4= | Follow below proceedure to generate the token.

### Procedure to generate Authorization value

#### 1. Concatenate the user name with a colon, and the password. For example, a user name of admin, and a password of admin becomes the following string:
admin:admin

#### 2. Encode this user name and password string in base64 encoding.
#### 3. Include this encoded user name and password in an HTTP Authorization: Basic header. For example, with an encoded user name of admin, and a password of admin, the following header is created:
Authorization: Basic YWRtaW46YWRtaW4=

### HTTP Request Query Parameters

Parameter |  Description | Required
--------- | ----------- | -----------
MSISDN | Mobile Station International Subscriber Directory Number | Mandatory
subscriberInput | Current input of the subscriber | Mandatory
isNewRequest | Identifying whether the request is new free flow request or continuous request | Mandatory
language | Subscriber language code | Optional
... | Reset of the parameters defined in application | Mandatory

### HTTP Request Query Parameter - isNewRequest

Code | Description
--------- | -----------
0 | Indicate process the subscriberInput for existing session
1 | Force to Create new session. 

### HTTP Response Header-Freeflow

Code | Description
--------- | -----------
FB | Free flow break, which indicates end of session
FC | Free flow continue, indicates requires more inputs.

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | OK | - | Success/Failure code defined in Application
400 | Bad Request | V9004 | MSISDN query parameter is missing in the request
400 | Bad Request | V9006 | Mising some query parameters
401 | Unauthorized | V9009 | Access denied because of missing/wrong credentials
404 | Not Found | V9007 | Application not found in App store
404 | Not Found | V9008 | Application you are trying to access is not available in this engine
404 | Not Found | V9010 | Application is incomplete End module is missing
500 | Internal Server Error | E9000 | Application Management Internal Error

<aside class="success">
Remember — HTTP-JSON REST API!
</aside>

## CPS-XML Request/Response

> HTTP Request Payload:

```http

https://localhost:9002/cpsxml

```

> HTTP Request - Begin Request:

```json
<?xml version="1.0" encoding="UTF-8"?>
<cps-message>
   <sequence_number>10522</sequence_number>
   <version>32</version>
   <service_type>BR</service_type>
   <source_addr>51991108426</source_addr>
   <dest_addr>*97585</dest_addr>
   <timestamp>2019/01/03 20:39:42</timestamp>
   <command_status>0</command_status>
   <data_coding>0</data_coding>
   <msg_len>0</msg_len>
   <msg_content />
   <msc_gt_addr>51997990091</msc_gt_addr>
   <LAC>1379</LAC>
   <CellID>25584</CellID>
   <IMSI>716101401743485</IMSI>
   <IMEI />	
   <PushID>190103160629267.2074</PushID> <App_url>http%3A%2F%2F172.17.26.152%3A20000%2FNotificacionUSSDServlet%2Fmenu.jsp%3FMSISDN%3D51991108426%26TXID%3D190103160629267.2074%26CODPROMO%3DUP006</App_url>
</cps-message>

```

> HTTP Response - Continue Request:

```json
<?xml version="1.0" encoding="UTF-8"?>
<cps-message>
	<sequence_number>10522</sequence_number>
	<version>32</version>
	<service_type>CR</service_type>
	<source_addr>51991108426</source_addr>
	<dest_addr>*97585</dest_addr>
	<timestamp>2019/01/25 13:11:44</timestamp>
	<command_status>0</command_status>
	<data_coding>0</data_coding>
	<msg_len>25</msg_len>
	<msg_content>Welcome to LEAP
		1) Item 1</msg_content>
	<msc_gt_addr>51997990091</msc_gt_addr>
	<LAC>1379</LAC>
	<CellID>25584</CellID>
	<IMSI>716101401743485</IMSI>
	<PushID>190103160629267.2074</PushID>
</cps-message>

```

> HTTP Request - Continue Action:

```json
<?xml version="1.0" encoding="UTF-8"?>
<cps-message>
   <sequence_number>10522</sequence_number>
   <version>32</version>
   <service_type>CA</service_type>
   <source_addr>51991108426</source_addr>
   <dest_addr>*97585</dest_addr>
   <timestamp>2019/01/03 20:39:42</timestamp>
   <command_status>0</command_status>
   <data_coding>0</data_coding>
   <msg_len>0</msg_len>
   <msg_content>1</msg_content>
   <msc_gt_addr>51997990091</msc_gt_addr>
   <LAC>1379</LAC>
   <CellID>25584</CellID>
   <IMSI>716101401743485</IMSI>
   <IMEI />	
   <PushID>190103160629267.2074</PushID>
   <App_url>http%3A%2F%2F172.17.26.152%3A20000%2FNotificacionUSSDServlet%2Fmenu.jsp%3FMSISDN%3D51991108426%26TXID%3D190103160629267.2074%26CODPROMO%3DUP006</App_url>
</cps-message>

```

> HTTP Response - Continue Notification:

```json
<?xml version="1.0" encoding="UTF-8"?>
<cps-message>
	<sequence_number>10522</sequence_number>
	<version>32</version>
	<service_type>CN</service_type>
	<source_addr>51991108426</source_addr>
	<dest_addr>*97585</dest_addr>
	<timestamp>2019/06/25 13:11:29</timestamp>
	<command_status>0</command_status>
	<data_coding>0</data_coding>
	<msg_len>23</msg_len>
	<msg_content>1) Item 2
		0 main
		b back</msg_content>
	<msc_gt_addr>51997990091</msc_gt_addr>
	<LAC>1379</LAC>
	<CellID>25584</CellID>
	<IMSI>716101401743485</IMSI>
	<PushID>190103160629267.2074</PushID>
</cps-message>
```

Use this endpoint to execute an application using Continuation Passing Style interface.

### HTTP Request

`POST http://example.com/cpsxml`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Content-Type | text/xml or application/xml | This API Accepts only XML buffer.

### HTTP Request - Payload

Code | Description
--------- | -----------
cps-message | Begin Request

### HTTP Request - Service Types

Code | Description
--------- | -----------
BR | Begin Request
CA | Continue Action
CNA | Continue Notification Action

### HTTP Response - Service Types

Code | Description
--------- | -----------
CR | Continue Request
CN | Continue Notification
EA | End Action

<aside class="success">
Remember — HTTP-XML CPS API!
</aside>

# HTTP(S) Push Interface: Application Execution

## USSD Push Interface

> HTTP Request Payload:

```http

http://localhost:9004/ussdpush?MSISDN=************&SENDER=135&GATEWAYID=cf2fc100-d628-11e9-b214-074ccf1338ad&USERNAME=leap&PASSWORD=leap123&MESSAGE=This%20is%20URL%20encoded%20string

```

> Success Response Payload:

```http
HTTP/1.1 200 OK
X-Powered-By Express
Access-Control-Allow-Origin *
Access-Control-Allow-Methods GET,PUT,POST,PATCH,DELETE,OPTIONS
Access-Control-Allow-Headers Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId
X-FRAME-OPTIONS SAMEORIGIN
X-XSS-Protection 1;mode=block
X-Content-Type-Options nosniff
Content-Security-Policy script-src 'self'
X-Permitted-Cross-Domain-Policies none
Referrer-Policy no-referrer
Strict-Transport-Security max-age=31536000 ; includeSubDomains
txnId 11568808040749
Content-Type application/json; charset=utf-8
Content-Length 28
ETag W/"1c-TAiME6pO37FIQdTXQjme58ytF/M"
Date Wed, 18 Sep 2019 12:00:40 GMT
Connection keep-alive

{
    "code": "0",
    "msg": "Success"
}

```

> HTTP Request Payload:

```http

http://localhost:9004/ussdpush?

```

> Failure Response Payload:

```http
HTTP/1.1 400 Bad Request
X-Powered-By ?Express
Access-Control-Allow-Origin *
Access-Control-Allow-Methods GET,PUT,POST,PATCH,DELETE,OPTIONS
Access-Control-Allow-Headers Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId
X-FRAME-OPTIONS SAMEORIGIN
X-XSS-Protection 1;mode=block
X-Content-Type-Options ?nosniff
Content-Security-Policy ?script-src 'self'
X-Permitted-Cross-Domain-Policies ?none
Referrer-Policy ?no-referrer
Strict-Transport-Security ?max-age=31536000 ; includeSubDomains
txnId 11568808130017
Content-Type application/json; charset=utf-8
Content-Length ?131
ETag W/"83-2iCoNysGnvtaQgrU7JW3o2dZBnI"
Date Wed, 18 Sep 2019 12:02:10 GMT
Connection keep-alive

{
    "code": "V9006",
    "msg": "Mising some query parameters",
    "queryParams": [
        "MSISDN",
        "SENDER",
        "MESSAGE",
        "GATEWAYID",
        "USERNAME",
        "PASSWORD"
    ]
}
```

Use this endpoint to push message content to subscriber, which is created in LEAP Platform.

### HTTP Request

`GET http://example.com/ussdpush`

### HTTP Request Query Parameters

Parameter |  Description | Required
--------- | ----------- | -----------
MSISDN | Mobile Station International Subscriber Directory Number | Mandatory
SENDER | Short code | Mandatory
MESSAGE | USSD String to be delivered to MSISDN | Mandatory
GATEWAYID | USSD Gateway account identifier | Mandatory
USERNAME | USSD Gateway account Username | Mandatory
PASSWORD | USSD Gateway account password | Mandatory

### HTTP Response Header-Freeflow

Code | Description
--------- | -----------
FB | Free flow break, which indicates end of session
FC | Free flow continue, indicates requires more inputs.

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | OK | - | Success/Failure code defined in Application
400 | Bad Request | V9004 | MSISDN query parameter is missing in the request
400 | Bad Request | V9006 | Mising some query parameters
400 | Bad Request | V9013 | Invalid shortcode
401 | Unauthorized | V9009 | Access denied because of missing/wrong credentials
500 | Internal Server Error | E9000 | Application Management Internal Error

<aside class="success">
Remember — HTTP(S)-USSD Push Interface API!
</aside>

## App Push Interface

> HTTP Request Payload:

```http

http://localhost:9004/apppush?MSISDN=************&APPID=417af7f0-d626-11e9-aafb-0708775842a5&MESSAGE&USERNAME=leap&PASSWORD=leap123

```

> Success Response Payload:

```http
HTTP/1.1 200 OK
X-Powered-By Express
Access-Control-Allow-Origin *
Access-Control-Allow-Methods GET,PUT,POST,PATCH,DELETE,OPTIONS
Access-Control-Allow-Headers Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId
X-FRAME-OPTIONS SAMEORIGIN
X-XSS-Protection 1;mode=block
X-Content-Type-Options nosniff
Content-Security-Policy script-src 'self'
X-Permitted-Cross-Domain-Policies none
Referrer-Policy no-referrer
Strict-Transport-Security max-age=31536000 ; includeSubDomains
txnId 11568809645948
Content-Type application/json; charset=utf-8
Content-Length 110
ETag W/"6e-9Wejd867rw6UrtFmYEIabODVoTM"
Date Wed, 18 Sep 2019 12:27:25 GMT
Connection keep-alive

{
    "code": "0",
    "msg": "Success"
}

```

> HTTP Request Payload:

```http

http://localhost:9004/apppush?

```

> Failure Response Payload:

```http
HTTP/1.1 400 Bad Request
X-Powered-By Express
Access-Control-Allow-Origin *
Access-Control-Allow-Methods GET,PUT,POST,PATCH,DELETE,OPTIONS
Access-Control-Allow-Headers Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId
X-FRAME-OPTIONS SAMEORIGIN
X-XSS-Protection 1;mode=block
X-Content-Type-Options nosniff
Content-Security-Policy script-src 'self'
X-Permitted-Cross-Domain-Policies none
Referrer-Policy no-referrer
Strict-Transport-Security max-age=31536000 ; includeSubDomains
txnId 11568809645948
Content-Type application/json; charset=utf-8
Content-Length 110
ETag W/"6e-9Wejd867rw6UrtFmYEIabODVoTM"
Date Wed, 18 Sep 2019 12:27:25 GMT
Connection keep-alive

{
    "code": "V9006",
    "msg": "Mising some query parameters",
    "queryParams": [
        "MSISDN",
        "MESSAGE",
        "USERNAME",
        "PASSWORD"
    ]
}
```

Use this endpoint to push application content to subscriber, which is created in LEAP Platform.

### HTTP Request

`GET http://example.com/apppush`

### HTTP Request Query Parameters

Parameter |  Description | Required
--------- | ----------- | -----------
MSISDN | Mobile Station International Subscriber Directory Number | Mandatory
MESSAGE | USSD String to be delivered to MSISDN | Mandatory
APPID | LEAP Application identifierIf this param is present application content will be pushed to subscriber number | Optional
GATEWAYID | USSD Gateway account Identifier | Mandatory
USERNAME | USSD Gateway account Username | Mandatory
PASSWORD | USSD Gateway account password | Mandatory
SENDERADDRESS | Sender Address | Mandatory
CAMPAIGNID | Campaign ID | Mandatory
CAMPAIGNCATEGORY | Campaign Category | Mandatory
INTERFACETYPE | Campaign Interface Type | Mandatory
LOCATION | Subscriber Location | Mandatory
ACCOUNTID | Mapping Push Account Identifier | Mandatory
USERID | Mapping Push User Identifier | Mandatory

### HTTP Response Header-Freeflow

Code | Description
--------- | -----------
FB | Free flow break, which indicates end of session
FC | Free flow continue, indicates requires more inputs.

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | OK | - | Success/Failure code defined in Application
400 | Bad Request | V9004 | MSISDN query parameter is missing in the request
400 | Bad Request | V9006 | Mising some query parameters
400 | Bad Request | V9013 | Invalid shortcode
401 | Unauthorized | V9009 | Access denied because of missing/wrong credentials
404 | Not Found | V9007 | Application not found in App store
404 | Not Found | V9008 | Application you are trying to access is not available in this engine
404 | Not Found | V9010 | Application is incomplete End module is missing
500 | Internal Server Error | E9000 | Application Management Internal Error

<aside class="success">
Remember — HTTP(S)-App Push Interface API!
</aside>

# LEAP CDRs

## App Engine CDR

### CDR Sample

<style type="text/css">
.tg  {border-collapse:collapse;border-spacing:0;}
.tg td{font-family:Arial, sans-serif;font-size:14px;padding:10px 5px;border-style:solid;border-width:1px;overflow:hidden;word-break:normal;border-color:black;}
.tg th{font-family:Arial, sans-serif;font-size:14px;font-weight:normal;padding:10px 5px;border-style:solid;border-width:1px;overflow:hidden;word-break:normal;border-color:black;}
.tg .tg-0pky{border-color:inherit;text-align:left;vertical-align:top}
.tg .tg-0lax{text-align:left;vertical-align:top}
</style>
<table class="tg">
  
  <tr>
<td>{<br>  "nid": "engine_MCKL-5242_9004_1",<br>  "sid": "11574673702949",<br>  "oid": "51791f20-0f64-11ea-8b99-9195f4afc25f",<br>  "aid": "51791f20-0f64-11ea-8b99-9195f4afc25f",<br>  "uid": "91987643210",<br>  "im": "QUERY_STRING",<br>  "st": 1574673702954,<br>  "et": 1574673703059,<br>  "rt": 92,<br>  "ut": "-1",<br>  "nt": "-1",<br>  "ln": "en",<br>  "ct": "QUERY_STRING",<br>  "s": "0",<br>  "p": [<br>    {<br>      "tid": 11574673702958,<br>      "mid": "37f43bd",<br>      "inf": "ucip",<br>      "mtid": 0.1,<br>      "mst": 1574673702958,<br>      "met": 1574673702963,<br>      "mrt": 5,<br>      "ocode": "0",<br>      "mcode": "0",<br>      "si": null,<br>      "ts": "l1:2|l2:1|l2s1:0|l2s2:1|l2s3:0|l2s4:0|l3:1"<br>    },<br>    {<br>      "tid": 11574673702964,<br>      "mid": "8cdf155",<br>      "inf": "appModules",<br>      "mtid": 3.7,<br>      "mst": 1574673702964,<br>      "met": 1574673703051,<br>      "mrt": 87,<br>      "ocode": "200",<br>      "mcode": "0",<br>      "si": null,<br>      "ts": "l1:3|l2:83|l2s1:0|l2s2:83|l2s3:0|l2s4:0|l3:1"<br>    }<br>  ]<br>}</td>
</tr>
</table>

### CDR Field Description

<style type="text/css">
.tg  {border-collapse:collapse;border-spacing:0;}
.tg td{font-family:Arial, sans-serif;font-size:14px;padding:10px 5px;border-style:solid;border-width:1px;overflow:hidden;word-break:normal;border-color:black;}
.tg th{font-family:Arial, sans-serif;font-size:14px;font-weight:normal;padding:10px 5px;border-style:solid;border-width:1px;overflow:hidden;word-break:normal;border-color:black;}
.tg .tg-0pky{border-color:inherit;text-align:left;vertical-align:top}
.tg .tg-0lax{text-align:left;vertical-align:top}
</style>
<table class="tg">
  <tr>
    <th class="tg-0pky">Field Name</th>
    <th class="tg-0pky">Alias</th>
    <th class="tg-0pky">Data type</th>
    <th class="tg-0pky">Possible Values</th>
    <th class="tg-0pky">Mandatory/Optional (M/O)</th>
    <th class="tg-0lax">Description</th>
  </tr>
  <tr>
    <td class="tg-0lax">Node Identifier</td>
    <td class="tg-0lax">nid</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax">"engine_&lt;hostname&gt;_&lt;port&gt;_&lt;clusterid&gt;"</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Node identifier to know that Session was terminated by which instance</td>
  </tr>
  <tr>
    <td class="tg-0lax">Session Idenfier</td>
    <td class="tg-0lax">sid</td>
    <td class="tg-0lax">Numeric</td>
    <td class="tg-0lax">Generated number</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Unique number generated for LEAP execution Session</td>
  </tr>
  <tr>
    <td class="tg-0lax">Original App Id</td>
    <td class="tg-0lax">oid</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax">A unique ID assigned to an application.</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Application idenfier number denotes from which application session originated</td>
  </tr>
  <tr>
    <td class="tg-0lax">Application Identifier</td>
    <td class="tg-0lax">aid</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax">A unique ID assigned to an application.</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Application identifier with which session is terminated</td>
  </tr>
  <tr>
    <td class="tg-0pky">Subscriber Number<br></td>
    <td class="tg-0pky">uid</td>
    <td class="tg-0pky">Alphanumeric</td>
    <td class="tg-0pky"></td>
    <td class="tg-0pky">M</td>
    <td class="tg-0lax">Query parameter MSISDN that indicates the end</td>
  </tr>
  <tr>
    <td class="tg-0pky">Interface Mode</td>
    <td class="tg-0pky">im</td>
    <td class="tg-0pky">Alphanumeric</td>
    <td class="tg-0pky">QUERY_STRING<br>HTTP_JSON<br><br>CPS_XML<br><br>SMPP<br><br>WEBSOCKET<br><br>NEXMO<br></td>
    <td class="tg-0pky">M</td>
    <td class="tg-0lax">This parameter indicates the Application is being executed through which mode of execution</td>
  </tr>
  <tr>
    <td class="tg-0lax">Start Time</td>
    <td class="tg-0lax">st</td>
    <td class="tg-0lax">Numeric</td>
    <td class="tg-0lax">milliseconds</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Sessios start time in milliseconds</td>
  </tr>
  <tr>
    <td class="tg-0lax">End Time</td>
    <td class="tg-0lax">et</td>
    <td class="tg-0lax">Numeric</td>
    <td class="tg-0lax">milliseconds</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Session end time in milliseconds</td>
  </tr>
  <tr>
    <td class="tg-0lax">Round trip time</td>
    <td class="tg-0lax">rt</td>
    <td class="tg-0lax">Numeric</td>
    <td class="tg-0lax"></td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This value denotes the round trip time of session at run time</td>
  </tr>
  <tr>
    <td class="tg-0lax">Subscriber Type</td>
    <td class="tg-0lax">ut</td>
    <td class="tg-0lax">Numeric</td>
    <td class="tg-0lax">Type of subscriber:<br>1 - Prepaid Subscriber<br>2 - Postpaid Subscriber<br>3 - Hybrid Subscriber</td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">LEAP sends request to IN for getting the subscriber type, whether the subscriber is prepaid subscriber or postpaid subscribers.</td>
  </tr>
  <tr>
    <td class="tg-0lax">Subscriber Network Type</td>
    <td class="tg-0lax">nt</td>
    <td class="tg-0lax">Numeric</td>
    <td class="tg-0lax">Types:<br>0 - Onnet<br>1 - Offnet<br>5-International</td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">LEAP sends request to IN for getting the subscriber network type, whether subscriber belongs to home network or other operator.</td>
  </tr>
  <tr>
    <td class="tg-0lax">Subscriber Language</td>
    <td class="tg-0lax">ln</td>
    <td class="tg-0lax">International Locale</td>
    <td class="tg-0lax">Default: en</td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">LEAP sends request to IN for getting the subscriber preferred or current language set on his/her profile created at IN end.</td>
  </tr>
  <tr>
    <td class="tg-0lax">Status Code</td>
    <td class="tg-0lax">s</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax">E9000</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Session Status code which denotes the aplication execution final result</td>
  </tr>
  <tr>
    <td class="tg-0lax">Sender Address</td>
    <td class="tg-0lax">sa</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax">Ex:NGAGE</td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the Sender address for App push interface</td>
  </tr>
  <tr>
    <td class="tg-0lax">Campiagn ID</td>
    <td class="tg-0lax">cid</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax"></td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the child process identifer of campaign push for App push interface</td>
  </tr>
  <tr>
    <td class="tg-0lax">Campiagn Interface Type</td>
    <td class="tg-0lax">it</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax"></td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the interface type of campaign push for App push interface</td>
  </tr>
  <tr>
    <td class="tg-0lax">Campaign Category</td>
    <td class="tg-0lax">cc</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax">Ex:Promo, Education etc</td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the location property of campaign for App push interface</td>
  </tr>
  <tr>
    <td class="tg-0lax">Location</td>
    <td class="tg-0lax">lc</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax"></td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the location property of campaign for App push interface</td>
  </tr>
  <tr>
    <td class="tg-0lax">Account Id</td>
    <td class="tg-0lax">acid</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax"></td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the Mapping Push Account Identifier for App push interface</td>
  </tr>
  <tr>
    <td class="tg-0lax">User Id</td>
    <td class="tg-0lax">usid</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax"></td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the Mapping Push User Identifier for App push interface</td>
  </tr>
   <tr>
    <td class="tg-0lax">Plugin execution Path</td>
    <td class="tg-0lax">p</td>
    <td class="tg-0lax">Array of JSON</td>
    <td class="tg-0lax">
        <table class="tg">
        <tr>
            <th class="tg-0pky">Field Name</th>
            <th class="tg-0pky">Alias</th>
            <th class="tg-0pky">Data type</th>
            <th class="tg-0pky">Mandatory/Optional (M/O)</th>
            <th class="tg-0lax">Description</th>
        </tr>
        <tr>
            <td class="tg-0lax">Transaction ID</td>
            <td class="tg-0lax">tid</td>
            <td class="tg-0lax">Numeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Unique number generated for each transaction</td>
        </tr>
        <tr>
            <td class="tg-0lax">Module ID</td>
            <td class="tg-0lax">mid</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Plugin Identifier</td>
        </tr>
        <tr>
            <td class="tg-0lax">Category ID</td>
            <td class="tg-0lax">inf</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Plugin's Category Identifier</td>
        </tr>
        <tr>
            <td class="tg-0lax">Module Type ID</td>
            <td class="tg-0lax">mtid</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Plugin's Type Identifier</td>
        </tr>
        <tr>
            <td class="tg-0lax">Start Time</td>
            <td class="tg-0lax">mst</td>
            <td class="tg-0lax">Numeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Plugin Execution Start Time in milliseconds</td>
        </tr>
        <tr>
            <td class="tg-0lax">End Time</td>
            <td class="tg-0lax">met</td>
            <td class="tg-0lax">Numeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Plugin Execution End Time in milliseconds</td>
        </tr>
        <tr>
            <td class="tg-0lax">Round Trip Time</td>
            <td class="tg-0lax">mrt</td>
            <td class="tg-0lax">Numeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Actual Plugin Execution Time in milliseconds</td>
        </tr>
        <tr>
            <td class="tg-0lax">Original Plugin Code</td>
            <td class="tg-0lax">ocode</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Actual Plugin Execution status code</td>
        </tr>
        <tr>
            <td class="tg-0lax">Plugin Status Code</td>
            <td class="tg-0lax">mcode</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Plugin status code for reporting</td>
        </tr>
        <tr>
            <td class="tg-0lax">Subscriber Input</td>
            <td class="tg-0lax">si</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">O</td>
            <td class="tg-0lax">User input feeded for this plugin's execution</td>
        </tr>
        <tr>
            <td class="tg-0lax">States Timer</td>
            <td class="tg-0lax">ts</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">O</td>
            <td class="tg-0lax">Time take at various states of Engine.<br>l1: Plugin settings preparation time<br>l2: Execution time<br>l3: Logic evaluation time</td>
        </tr>
        </table>
    </td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Holds the plugin execution properties in the order of execution</td>
  </tr>
</table>

### CDR Processing Call flow
<p align="center"><img src="/images/LEAP-CDR-Flow.png" width=700 alt="App Engine CDR"></p>

### CDR Retention Period
The retention period of CDR is 30 days.

<aside class="success">
Remember — CDR Document!
</aside>
