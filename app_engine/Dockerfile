# Check out https://hub.docker.com/_/node to select a new base image
FROM node:22-bookworm-slim
WORKDIR /home/<USER>/app

# Copy app source code with correct ownership
COPY --chown=node:node . .

# Ensure the node user owns the directory and set secure permissions
RUN chmod -R 755 /home/<USER>/app

# Set environment variables
ENV HOST=0.0.0.0 PORT=3000

# Expose the application port
EXPOSE ${PORT}

# Switch to the non-root "node" user
USER node

# Run the application
CMD ["node", "."]
