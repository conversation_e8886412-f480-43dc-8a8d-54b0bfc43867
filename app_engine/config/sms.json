{"enabled": true, "mode": "roundrobin", "defaultIndex": 0, "options": {"series": {"^91987[0-9]{7}$": {"enabled": true, "index": 0}, "^91986[0-9]{7}$": {"enabled": true, "index": 1}, "^91[0-9]{10}$": {"enabled": true, "index": 3}}, "roundrobin": {"index": [0, 1]}}, "settings": [{"host": "localhost", "port": 5002, "timeout": 5000, "bindTimeout": 10000, "username": "leap", "password": "leap123", "system_type": "", "version": "3.4", "smsNotificationType": "Stored SMS", "fragmentsSize": 160, "maxRetry": 1, "retryInterval": 3000, "enquiryLinkInterval": 10000, "title": "SMS Push"}, {"host": "127.0.0.1", "port": 2775, "timeout": 10000, "bindTimeout": 10000, "username": "leap", "password": "leap123", "system_type": "", "version": "3.4", "smsNotificationType": "Stored SMS", "fragmentsSize": 160, "maxRetry": 1, "retryInterval": 3000, "enquiryLinkInterval": 10000, "title": "SMS Push"}]}