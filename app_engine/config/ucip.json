{"enabled": false, "mode": "series", "defaultIndex": 0, "options": {"series": {"^91987[0-9]{7}$": {"enabled": true, "index": 0}, "^91986[0-9]{7}$": {"enabled": true, "index": 1}, "^91[0-9]{10}$": {"enabled": true, "index": 3}}, "roundrobin": {"index": [0, 1, 2]}}, "settings": [{"host": "*************", "port": 2144, "path": "/mock/ucip", "username": "test", "password": "test", "Content-Type": "text/xml", "Accept-Charset": "UTF-8", "User-Agent": "test 1"}, {"host": "*************", "port": 2144, "path": "/mock/ucip", "username": "test", "password": "test", "Content-Type": "text/xml", "Accept-Charset": "UTF-8", "User-Agent": "test 2"}, {"host": "*************", "port": 2144, "path": "/mock/ucip", "username": "test", "password": "test", "Content-Type": "text/xml", "Accept-Charset": "UTF-8", "User-Agent": "test 3"}]}