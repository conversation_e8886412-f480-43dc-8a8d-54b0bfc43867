"use strict";
/**
 * @class Node
 * @version 1.0.0
 * @since 03 April 2018
 * @description model class to help menu structure.
 *
 * <AUTHOR>
 */

const NodeType = require("./NodeType");

class Node {

  constructor(opts) {
    this.uid = opts.uid || opts.id;
    this.id = opts.id;
    this.parentid = opts.parentid;
    this.index = opts.index;
    this.name = opts.name;
    this.type = opts.type;
    this.title = opts.title;
    if (opts.enabled != null) {
      this.enabled = opts.enabled;
    } else {
      this.enabled = true;
    }
    this.data = opts.data;
    this.contentType = opts.contentType;
    this.menuProperty = opts.menuProperty || {};
    this.settings = opts.settings || { padding: ") " };
    this.questionBank = opts.questionBank || { questionNumber: 0 };
    if (opts.children && opts.children.items) {
      this.children = opts.children;
    } else {
      this.children = { items: [] };
    }
    this.parent = null;
    this.level = opts.level && Number(opts.level) || 0;
    this.pageNumber = opts.pageNumber && Number(opts.pageNumber) || 0;
    this.pageInfo = opts.pageInfo || { currentPage: 0, firstPage: 0 };
    this.lookupTable = {};
  }

  setUID(uid) {
    this.uid = uid;
  }

  getUID() {
    return this.uid;
  }

  setId(id) {
    this.id = id;
  }

  getId() {
    return this.id;
  }

  setParentId(parentid) {
    this.parentid = parentid;
  }

  getParentId() {
    return this.parentid;
  }

  setIndex(index) {
    this.index = index;
    // this.parent && this.parent.lookupTable
  }

  getIndex() {
    return this.index;
  }

  setName(name) {
    this.name = name;
  }

  getName() {
    return this.name;
  }

  setType(type) {
    if (NodeType[type]) {
      this.type = type;
    }
  }

  getType() {
    if (!this.type) {
      this.type = NodeType.CHOICE;
    }
    return this.type;
  }

  setEnabled(enabled) {
    this.enabled = enabled;
  }

  isEnabled() {
    return this.enabled;
  }

  setLevel(level) {
    this.level = level;
  }

  getLevel() {
    return this.level;
  }

  setPageInfo(pageInfo) {
    if (pageInfo == null) {
      this.pageInfo = { currentPage: 0, firstPage: 0 };
    } else {
      this.pageInfo = pageInfo;
    }
  }

  getPageInfo() {
    return this.pageInfo;
  }

  setTitle(title) {
    this.title = title;
  }

  getTitle() {
    if (!this.title) {
      this.title = "";
    }
    return this.title;
  }

  setData(data) {
    this.data = data;
  }

  getData() {
    if (!this.data) {
      this.data = {};
    }
    return this.data;
  }

  setContentType(contentType) {
    this.contentType = contentType;
  }

  getContentType() {
    return this.contentType;
  }

  setParentNode(node) {
    this.parent = node;
  }

  getParentNode() {
    return this.parent;
  }

  setPageNumber(pageNumber) {
    this.pageNumber = pageNumber;
  }

  getPageNumber() {
    return this.pageNumber;
  }

  setSettings(settings) {
    this.settings = settings;
  }

  getSettings() {
    return this.settings;
  }

  setProperties(menuProperty) {
    this.menuProperty = menuProperty;
  }

  addProperty(menuProperty) {
    if (this.menuProperty == null) this.menuProperty = {};
    let index = Object.keys(this.menuProperty).length;
    this.menuProperty[index] = menuProperty;
  }

  getProperties() {
    if (!this.menuProperty) {
      this.menuProperty = {};
    }
    if (this.parent != null) {
      let menuProps = {};
      let props = this.parent.getProperties();
      Object.keys(props).forEach(key => {
        if (key.trim().length > 0 && props[key] != null) {
          let obj = props[key];
          menuProps[obj.key] = obj.value;
        }
      });
      Object.keys(this.menuProperty).forEach(key => {
        if (key.trim().length > 0) {
          let obj = this.menuProperty[key];
          menuProps[obj.key] = obj.value;
        }
      });
      let i = 0;
      this.menuProperty = {};
      Object.keys(menuProps).forEach(key => {
        if (key.trim().length > 0) {
          this.menuProperty[String(i)] = { key: key, value: menuProps[key] };
          i++;
        }
      });
    }
    return this.menuProperty;
  }

  setQuestionBank(questionBank) {
    this.questionBank = questionBank;
  }

  getQuestionBank() {
    return this.questionBank;
  }

  addChild(node) {
    node.setParentNode(this);
    node.setParentId(this.id);
    this.children.items[this.children.items.length] = node;
  }

  getCount() {
    return this.children.items && this.children.items.length || 0;
  }

  getChildren() {
    if (!(this.children && this.children.items)) {
      this.children = { items: [] };
    }
    return this.children.items;
  }

  resetLookup() {
    this.lookupTable = {};
  }

  getLookUpKeyByValue(value) {
    return Object.keys(this.lookupTable).find(key => this.lookupTable[key] === value);
  }

  setLookup(index) {
    let key;
    let sind = this.children.items[index].getSettings().static_index;
    if (sind == null || sind.trim().length === 0) {
      key = this.children.items[index].getIndex();
    } else {
      key = this.children.items[index].getSettings().static_index;
    }
    this.lookupTable[key] = index;
  }

  lookup(index) {
    if (this.lookupTable[index] != null) return this.lookupTable[index];
    return -1;
  }

  getChild(index) {
    return this.children.items[index];
  }

  removeChildren() {
    this.children = { items: [] };
  }

  toJSON() {
    return {
      uid: this.uid,
      id: this.id,
      index: this.index,
      name: this.name,
      type: this.type,
      level: this.level,
      title: this.title,
      data: this.data,
      menuProperty: this.menuProperty,
      children: this.children || { items: [] },
      pageNumber: this.pageNumber,
      questionBank: this.questionBank,
      enabled: this.enabled,
      settings: this.settings,
      contentType: this.contentType
    };
  }

  toString() {
    return JSON.stringify(this.toJSON());
  }
}

module.exports = Node;
