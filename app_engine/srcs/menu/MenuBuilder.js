"use strict";
/**
 * @class MenuBuilder
 * @version 1.0.0
 * @since 03 April 2018
 * @description Core module which help to build menu for Menu module used in the application
 *
 * <AUTHOR>
 */

const Node = require("./Node");
const NodeType = require("./NodeType");
const AppCache = require("../cache/AppCache");
const request = require("request");
const qs = require("query-string");
const common = require("common");
const FreeFlowStates = common.FreeFlowStates;
const message = require("message");
const error_codes = message.error_codes;
const utility = require("utility");
const CDRWriter = require("../lib/writeMenuCDR");

const MenuErrorCodes = require("./MenuErrorCodes");
const HTTP_CODES = require("../lib/http_codes");
const MAIN_MENU = "Main Menu";
let nav_translation = {};

const NEWLINE = "\n";

class MenuBuilder {

  constructor(opts) {
    this.cid = opts.cid;
    this.appId = opts.appId;
    this.mid = opts.mid;
    this.userId = opts.userId;
    this.moduleId = opts.moduleId;
    this.sessionId = opts.sessionId;
    this.isMainMenu = opts.isMainMenu;
    this.mainMenuOption = opts.mainMenuOption;
    this.isMainAppMenu = opts.isMainAppMenu;
    this.menuNavigationEnable = opts.menuNavigationEnable || false;
    if (global.config.app_engine.commonMenuNavigation) {
      this.navigationOpts = setNavigationOpts({});
    }
    else {
      this.navigationOpts = setNavigationOpts(opts.menuNavigationOptions);
    }
    this.menuIndexPadding = opts.menuIndexPadding || ") ";
    if (!this.isMainMenu && this.mainMenuOption != null && !global.config.app_engine.commonMenuNavigation) {
      this.navigationOpts.mainMenu.code = this.mainMenuOption;
    }
    this.language = opts.language || global.config.defaultLanguage;
    this.bufferSize = opts.bufferSize || 180;
    this.macros = opts.macros;
    this.translation = opts.translation || {};
    this.dynamic_status = opts.dynamic_status;
    this._multiAccess = opts.multiAccess;

    if (opts.rootContext != null) {
      this.rootContext = prepareMenuContext(opts.rootContext);
    } else {
      this.rootContext = prepareMenuContext(opts.contextData);
    }
    if (opts.context != null) {
      this.context = prepareMenuContext(opts.context);
      this.context.setPageInfo(opts.pageInfo);
    } else {
      this.context = this.rootContext;
    }
    this.paginate();
  }

  setMultiAccess(multiAccess) {
    this._multiAccess = multiAccess;
  }

  setMacros(macros) {
    this.macros = macros;
  }

  setisPaginateSuccess() {
    this.isPaginateSuccess = false;
  }

  getHelpMenu() {

    let buffer = this.navigationOpts.mainMenu.code + this.menuIndexPadding + this.navigationOpts.mainMenu.message + NEWLINE;
    buffer += this.navigationOpts.firstPage.code + this.menuIndexPadding + this.navigationOpts.firstPage.message + NEWLINE;
    buffer += this.navigationOpts.prevPage.code + this.menuIndexPadding + this.navigationOpts.prevPage.message + NEWLINE;
    buffer += this.navigationOpts.nextPage.code + this.menuIndexPadding + this.navigationOpts.nextPage.message + NEWLINE;
    buffer += this.navigationOpts.lastPage.code + this.menuIndexPadding + this.navigationOpts.lastPage.message + NEWLINE;

    return this.response({
      ext: this.context.getUID() || MAIN_MENU,
      code: MenuErrorCodes.SUCCESS,
      message: buffer
    });
  }

  getMainMenu() {
    this.context = this.rootContext;
    this.context.pageNumber = 0;
    this.paginate();
    if (this.isPaginateSuccess == false) {
      return this.response({
        ext: this.context.getUID() || MAIN_MENU,
        code: MenuErrorCodes.FAILURE,
        message: message.getResponseJson(this.language, MenuErrorCodes.LOOP).msg
      });
    }
    return this.getFirstPageMenu();
  }

  handleDynamicMenu(userAnswered) {
    const self = this;
    switch (this.context.getContentType()) {
      case "url":
        return new Promise((resolve, reject) => {
          // Wrap the async operation in a separate function
          async function processRequest() {
            try {
              let url = self.macros.replaceAllPlaceHolders(self.context.getData());
              let data = qs.parseUrl(url);
              let str = {};
              let props = self.context.getProperties();
              for (let i = 0; i < Object.keys(props).length; i++) {
                if (props[i] != null) {
                  str[props[i].key] = self.macros.replaceAllPlaceHolders(props[i].value);
                }
              }
              data.query = Object.assign(data.query, str);
              if (self.dynamic_status == null || self.dynamic_status === 0) {
                data.query.isNewRequest = 1;
              } else {
                data.query.subscriberInput = userAnswered;
                data.query.isNewRequest = 0;
              }
  
              data.query.language = self.language;
              url = data.url + "?" + qs.stringify(data.query);
              if (global.logger.isTraceEnabled()) {
                global.logger.trace("Dynamic URL:", url);
              }
  
              let req = {
                url,
                method: "GET",
                headers: {
                  "Content-Type": "text/html"
                },
                timeout: 30000
              };
              req.agent = getPoolAgent(req);
  
              let call = await httpCall(req);
              if (global.logger.isTraceEnabled()) {
                global.logger.trace("Dynamic Response:", call);
              }
              if (call.code === 200) {
                let responseCode = (call.response.headers["freeflow"] == FreeFlowStates.break || call.response.headers["cont"] == FreeFlowStates.break) 
                  ? MenuErrorCodes.NOCHILDREN 
                  : MenuErrorCodes.CONTINUE;
                self.dynamic_status = 1;
                resolve(self.response({
                  ext: self.context.getUID() || MAIN_MENU,
                  code: responseCode,
                  message: call.body,
                  contentType: self.context.getContentType()
                }));
              } else if (call.code === 400) {
                resolve(self.response({
                  ext: self.context.getUID() || MAIN_MENU,
                  code: MenuErrorCodes.FAILURE,
                  message: typeof call.body === "object" && call.body.msg || "Bad request"
                }));
              } else {
                resolve(self.response({
                  ext: self.context.getUID() || MAIN_MENU,
                  code: MenuErrorCodes.FAILURE,
                  message: call.body
                }));
              }
            } catch (e) {
              reject(e);
            }
          }
  
          // Call the async function
          processRequest().catch((err) => reject(err));
        });
  
      case "variable":
        let json = self.macros.replaceAllPlaceHolders(self.context.getData());
        if (json != null) {
          self.context = prepareMenuContext(json);
        } else {
          self.context = self.rootContext;
        }
        self.paginate();
        self.context.pageNumber = 0;
        return self.getFirstPageMenu();
    }
  
    return self.response({
      ext: self.context.getUID() || MAIN_MENU,
      code: MenuErrorCodes.FAILURE,
      message: "Dynamic menu not implemented"
    });
  }
  

  handleDynamicWithStaticMenu(userAnswered) {
    const self = this; // Capture 'this' context
    return new Promise((resolve, reject) => {
      // Define an asynchronous function for handling the dynamic menu
      async function processRequest() {
        try {
          let url = self.macros.replaceAllPlaceHolders(self.context.getData());
          let data = qs.parseUrl(url);
          let str = {};
          let props = self.context.getProperties();
          for (let i = 0; i < Object.keys(props).length; i++) {
            if (props[i] != null) {
              str[props[i].key] = self.macros.replaceAllPlaceHolders(props[i].value);
            }
          }
          data.query = Object.assign(data.query, str);
          data.query.isNewRequest = 1;
          data.query.subscriberInput = userAnswered;
          data.query.language = self.language;
          url = data.url + "?" + qs.stringify(data.query);
  
          if (global.logger.isTraceEnabled()) {
            global.logger.trace("handleDynamicWithStaticMenu URL:", url);
          }
  
          let req = {
            url,
            method: "GET",
            headers: {
              "Content-Type": "text/html"
            },
            timeout: 30000
          };
          req.agent = getPoolAgent(req);
  
          // Log static response
          if (global.logger.isTraceEnabled()) {
            global.logger.trace("handleDynamicWithStaticMenu static response:", self.context.getTitle());
          }
  
          // Resolve the promise with the static response
          resolve(self.response({
            ext: self.context.getUID() || MAIN_MENU,
            code: MenuErrorCodes.STATIC,
            message: self.context.getTitle()
          }));
  
          // Perform the HTTP call
          let call = await httpCall(req);
          if (global.logger.isTraceEnabled()) {
            global.logger.trace("handleDynamicWithStaticMenu dynamic response:", JSON.stringify(call));
          }
        } catch (error) {
          // Reject the promise if an error occurs
          reject(error);
        }
      }
  
      // Call the async function
      processRequest().catch((err) => reject(err));
    });
  }
  

  handleStaticMenu() {
    return this.response({
      ext: this.context.getUID() || MAIN_MENU,
      code: MenuErrorCodes.STATIC,
      message: this.context.getData()
    });
  }

  handleCustomMenu() {
    switch (this.context.getSettings().customMenuType) {
      case 1:
        return this.getMainMenu();
      case 2:
        // this.context = this.context.parent || searchNode(this.rootContext, parentId);
        return this.getPreviousMenu();
      case 3:
        return this.getFirstPageMenu();
      case 4:
        return this.getLastPageMenu();
      case 5:
        return this.response({
          ext: this.context.getUID() || MAIN_MENU,
          code: MenuErrorCodes.NOCHILDREN,
          message: []
        });
    }
    return null;
  }

  handleQuestionarie(userAnswered) {
    if (this.context.getQuestionBank().questionNumber == null) {
      this.context.getQuestionBank().questionNumber = 0;
    }
    let questionNumber = this.context.getQuestionBank().questionNumber || 0;
    if (userAnswered != null) {
      CDRWriter.emit("PLUGIN_CDR", this.appId, this.moduleId, "menu", "-", "-", "-", "-", this.sessionId, this.userId, "questionnaire", this.context.getChild(questionNumber).getQuestionBank().tag, userAnswered);
      if (this.menuNavigationEnable) {
        if (userAnswered == '*') {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace(this.cid + "Opt: " + "Setting Previous Menu Application and Module");
          }
          return this.response({
            ext: this.context.getUID() || MAIN_MENU,
            code: MenuErrorCodes.NOCHILDREN,
            message: {},
            isPreviousMenuOption: true
          });
        }
        if (userAnswered == '#') {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace(this.cid + "Opt: " + "Setting Main Menu Application");
          }
          return this.response({
            ext: this.context.getUID() || MAIN_MENU,
            code: MenuErrorCodes.NOCHILDREN,
            message: {},
            isMainMenuOption: true
          });
        }
      }
      this.context.getChild(questionNumber).getQuestionBank().answer = userAnswered;
      questionNumber++;
    }
    if (questionNumber >= this.context.getCount()) {
      if (this.context.getContentType() == "url") {
        this.context.getChildren().forEach(child => {
          if (child.getQuestionBank().tag != null && child.getQuestionBank().tag.length > 0)
            this.context.addProperty({ key: child.getQuestionBank().tag, value: child.getQuestionBank().answer });
        });
        return this.handleDynamicMenu(userAnswered);
      } else {
        let res = [];
        this.context.getChildren().forEach(child => {
          res.push({ question: child.getQuestionBank().tag, answer: child.getQuestionBank().answer });
        });
        return this.response({
          ext: this.context.getUID() || MAIN_MENU,
          code: MenuErrorCodes.NOCHILDREN,
          message: this.context.getName(),
          properties: res
        });
      }
    }
    this.context.getQuestionBank().questionNumber = questionNumber;
    let message;
    if (this.menuNavigationEnable) {
      message = this.getMenuTitles(this.context.getChild(questionNumber)) + "\n# Home\n* Back"
    }
    else {
      message = this.getMenuTitles(this.context.getChild(questionNumber));
    }
    return this.response({
      ext: this.context.getChild(questionNumber).getUID() + ":" + this.context.getChild(questionNumber).getName(),
      code: MenuErrorCodes.CONTINUE,
      message: message
    });
  }

  async loadTemplate() {
    let info = await AppCache.getAppLocaleTemplate(this.appId, this.language);
    if (info != null && info.translation) {
      this.translation = info.translation.menu_titles;
      nav_translation = info.translation.menu_navigation_options;
    }
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("LOAD LANG TEMPALTE:", JSON.stringify(this.translation));
      global.logger.trace("LOAD LANG NAVIGATION TEMPALTE:", JSON.stringify(nav_translation));
    }
    const isUnicodeBody = utility.isUnicode(JSON.stringify(this.translation));
    const isUnicodeNavOptions = utility.isUnicode(JSON.stringify(this.navigationOpts));
    const isUnicode = isUnicodeBody || isUnicodeNavOptions;
    if (isUnicode) {
      this.bufferSize = 160 / 2;
    }
    else {
      this.bufferSize = 160;
    }
  }

  getMenuTitles(child) {
    let title;
    if (this.translation != null) {
      if (this.translation[this.mid]) {
        title = this.translation[this.mid][child.getId()] || this.translation[this.mid][child.getUID()];
      } else {
        title = this.translation[child.getId()] || this.translation[child.getUID()];
      }
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("GetMenuTitle from Translation table, ID:", child.getUID(), ", title:", title);
      }
      if (title == null || title.length == 0)
        title = child.getName();
    } else {
      title = child.getName();
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("GetMenuTitle from Node name table, ID:", child.getUID(), ", title:", title);
      }
    }
    if (typeof title == "string") {
      if (this.macros != null && title.indexOf("$") > -1) {
        title = this.macros.replaceAllPlaceHolders(title);
      }
    }
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("GetMenuTitle after replacing placeholders ID:", child.getUID(), ", title:", title);
    }
    return title;
  }

  paginate() {
    if (this.context == null || this.context.getChildren() == null) {
      throw new Error("Context is not defined");
    }
    this.isPaginateSuccess = true;
    let index = 1, pageNumber = 0, pageSize = 0, temp;
    if (global.config.app_engine.menuNavigationEnable) {
      if (!(this.isMainAppMenu)) {
        pageSize += getNavigationMessage(this.cid, this.navigationOpts.mainMenu, "mainMenu").length;
        pageSize += getNavigationMessage(this.cid, this.navigationOpts.backwardSkip, "backwardSkip").length;
        pageSize += getNavigationMessage(this.cid, this.navigationOpts.nextPage, "nextPage").length;
      }
      else {
        pageSize += getNavigationMessage(this.cid, this.navigationOpts.nextPage, "nextPage").length;
      }
      pageSize += getNavigationMessage(this.cid, this.navigationOpts.lastPage, "lastPage").length;
    }
    this.context.resetLookup();
    for (let i = 0; i < this.context.getCount(); i++) {
      let menuName = this.getMenuTitles(this.context.getChild(i));
      if (this.context.getChild(i).getType() != NodeType.TITLE) {
        this.context.getChild(i).setIndex(index++);
        this.context.setLookup(i);
      }
      if (this.context.getChild(i).isEnabled()) {
        switch (this.context.getChild(i).getType()) {
          case NodeType.TITLE:
          case NodeType.CUSTOM:
          case NodeType.QUESTIONNAIRE:
          case NodeType.QUESTION:
          case NodeType.EXIT: {
            temp = menuName + NEWLINE;
          }
            break;
          default: {
            let padding = (this.context.getChild(i).getSettings().padding != null) ? this.context.getChild(i).getSettings().padding : this.menuIndexPadding;
            temp = this.context.getLookUpKeyByValue(i) + padding + menuName + NEWLINE;
          }
        }

        if (global.logger.isTraceEnabled()) {
          global.logger.trace(this.cid + "PBI: %d, Name: %s, Type: %s, APNo:%d, RPNo:%d", this.context.getLookUpKeyByValue(i), menuName, this.context.getChild(i).getType(), this.context.getChild(i).getPageNumber(), pageNumber);
        }
        pageSize += temp.length;
        if (global.logger.isTraceEnabled()) {
          global.logger.trace(this.cid + "Adding PI: %d, Name: %s, Type: %s", this.context.getChild(i).getIndex(), menuName, this.context.getChild(i).getType(), ", Size: " + pageSize, ", PNo: ", pageNumber, temp, temp && temp.length);
        }
      } else {
        if (global.logger.isTraceEnabled()) {
          global.logger.trace(this.cid + "Skipping PI: %d, Name: %s, Type: %s", this.context.getChild(i).getIndex(), menuName, this.context.getChild(i).getType(), ", Size: " + pageSize, ", PNo: ", pageNumber, temp, temp && temp.length);
        }
      }
      let menuItemLength = temp.length + getNavigationMessage(this.cid, this.navigationOpts.nextPage, "nextPage").length + getNavigationMessage(this.cid, this.navigationOpts.prevPage, "prevPage").length + 2;
      if (global.config.app_engine.menuNavigationEnable) {
        //	let menuItemLength = temp.length + getNavigationMessage(this.cid, this.navigationOpts.nextPage, "nextPage").length + getNavigationMessage(this.cid, this.navigationOpts.lastPage, "lastPage").length + 20;
        if (menuItemLength > this.bufferSize) {
          this.isPaginateSuccess = false;
          break;
        }

        if (global.config.app_engine.menuNavigationEnable) {
          if (pageSize <= this.bufferSize) {
            this.context.getChild(i).setPageNumber(pageNumber);
          } else {
            pageNumber = Number(pageNumber) + 1;
            pageSize = 0;
            if (i > 0) i--;
            index--;
            pageSize += this.getMenuPaginationOptions("", this.context.level, pageNumber, pageNumber + 1).length;
          }
        }
      }
      this.context.pageInfo.lastPage = pageNumber;
    }
  }

  getFirstPageMenu() {
    if (this.context.getCount() == 1 && this.context.getChild(0).isEnabled()) {
      if (this.context.getChild(0).getType() == NodeType.QUESTIONNAIRE) {
        this.context = this.context.getChild(0);
        return this.handleQuestionarie();
      } else if (this.context.getChild(0).getType() == NodeType.DYNAMIC && this.context.getChild(0).getContentType() == "variable") {
        this.context = this.context.getChild(0);
        return this.handleDynamicMenu();
      }
    }
    let buffer = this.getPageBuffer(this.context.pageInfo.firstPage);
    this.context.pageInfo.currentPage = this.context.pageInfo.firstPage;
    buffer = this.getMenuPaginationOptions(buffer, this.context.level, this.context.pageInfo.currentPage, this.context.pageInfo.lastPage);
    return this.response({
      ext: this.context.getUID() || MAIN_MENU,
      code: MenuErrorCodes.SUCCESS,
      message: buffer
    });
  }

  getLastPageMenu() {
    let buffer = this.getPageBuffer(this.context.pageInfo.lastPage);
    this.context.pageInfo.currentPage = this.context.pageInfo.lastPage;
    buffer = this.getMenuPaginationOptions(buffer, this.context.level, this.context.pageInfo.currentPage, this.context.pageInfo.lastPage);
    return this.response({
      ext: this.context.getUID() || MAIN_MENU,
      code: MenuErrorCodes.SUCCESS,
      message: buffer
    });
  }

  async getNextMenu(option) {

    if (global.logger.isTraceEnabled()) {
      global.logger.trace(this.cid + "Get Next menu for user input: " + option);
    }
    if (this.context == null || this.context.getChildren() == null) {
      throw new Error("Context is not defined");
    }
    let index = this.context.lookup(option);
    let nextOptions = this.context.getChild(index);
    let buffer = "";
    if (nextOptions != null && nextOptions.isEnabled()) {
      this.context = nextOptions;
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "Next Menu Type: " + this.context.getType());
      }
      if (this.context.getCount() == 1 && this.context.getChild(0).getType() == NodeType.QUESTIONNAIRE) {
        this.context = this.context.getChild(0);
        return this.handleQuestionarie();
      }
      switch (this.context.getType()) {
        case NodeType.EXIT: {
          let errorCode = (this.context.getSettings().errorCode != null) ? this.context.getSettings().errorCode : MenuErrorCodes.EXIT;
          return this.response({
            ext: this.context.getUID() || MAIN_MENU,
            code: MenuErrorCodes.EXIT,
            message: errorCode
          });
        }
        case NodeType.DYNAMIC:
          return await this.handleDynamicMenu(option);
        case NodeType.STATIC_DYNAMIC:
          return await this.handleDynamicWithStaticMenu(option);
        case NodeType.QUESTIONNAIRE:
          return this.handleQuestionarie();
        case NodeType.CUSTOM:
          return this.handleCustomMenu();
        case NodeType.STATIC:
          return this.handleStaticMenu();
      }
      this.context.pageInfo.currentPage = -1;
      if (this.context.getCount() === 0) {
        return this.response({
          ext: this.context.getUID() || MAIN_MENU,
          code: MenuErrorCodes.NOCHILDREN,
          properties: this.context.getProperties()
        });
      }
      this.paginate();
      if (this.isPaginateSuccess == false) {
        return this.response({
          ext: this.context.getUID() || MAIN_MENU,
          code: MenuErrorCodes.FAILURE,
          message: message.getResponseJson(this.language, MenuErrorCodes.LOOP).msg
        });
      }
      this.context.pageInfo.currentPage = -1;
    } else {
      if (this._multiAccess) {
        buffer = message.getResponseJson(this.language, HTTP_CODES.SMPP_INVALID_SHORTCODE).msg;
        return this.response({
          ext: this.context.getUID() || MAIN_MENU,
          code: MenuErrorCodes.FAILURE,
          message: buffer
        });
      } else if (option != this.navigationOpts.nextPage.code) {
        if (global.config.app_engine.menuNavigationEnable)
          buffer = message.getResponseJson(this.language, HTTP_CODES.SMPP_INVALID_INPUT).msg + NEWLINE;
      }
    }
    this.context.pageInfo.currentPage = Number(this.context.pageInfo.currentPage) + 1;
    if (global.logger.isTraceEnabled()) {
      global.logger.trace(this.cid + "GetNextmenu-currentPage: %d, lastPage: %d", this.context.pageInfo.currentPage, this.context.pageInfo.lastPage);
    }
    if (this.context.pageInfo.currentPage > this.context.pageInfo.lastPage) {
      this.context.pageInfo.currentPage = 0;
    }

    buffer += this.getPageBuffer(this.context.pageInfo.currentPage);
    buffer = this.getMenuPaginationOptions(buffer, this.context.level, this.context.pageInfo.currentPage, this.context.pageInfo.lastPage);
    return this.response({
      ext: this.context.getUID() || MAIN_MENU,
      code: MenuErrorCodes.SUCCESS,
      message: buffer
    });
  }

  getMenuPaginationOptions(buffer, level, currentPage, lastPage) {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace(this.cid + "Prepare MenuPaginationOptions- Level:%d, currentPage: %d, lastPage: %d", level, currentPage, lastPage);
    }
    if (global.config.app_engine.menuNavigationEnable) {
      if (!(this.isMainAppMenu)) {
        buffer = buffer + getNavigationMessage(this.cid, this.navigationOpts.mainMenu, "mainMenu");
        if (currentPage == 0) {
          buffer = buffer + getNavigationMessage(this.cid, this.navigationOpts.backwardSkip, "backwardSkip");
        }
      }
      if (currentPage > 1) {
        buffer = buffer + getNavigationMessage(this.cid, this.navigationOpts.firstPage, "firstPage");
      }
      if (currentPage > 0) {
        buffer = buffer + getNavigationMessage(this.cid, this.navigationOpts.prevPage, "previousPage");
      }
      if (currentPage < lastPage) {
        buffer = buffer + getNavigationMessage(this.cid, this.navigationOpts.nextPage, "nextPage");
        if (lastPage > 1) {
          buffer = buffer + getNavigationMessage(this.cid, this.navigationOpts.lastPage, "lastPage");
        }
      }
      if (global.config.app_engine.menuHelpEnable && currentPage == lastPage) {
        buffer = buffer + getNavigationMessage(this.cid, this.navigationOpts.help, "help");
      }

      return buffer.slice(0, -1);
    }
    return buffer;
  }

  getPreviousMenu() {
    if (this.context.pageInfo.currentPage === 0) {
      let parentId = this.context && this.context.getParentId();
      this.context = this.context.parent || searchNode(this.rootContext, parentId);
      this.paginate();
      this.context.pageInfo.currentPage = 1;
    }
    this.context.pageInfo.currentPage = this.context.pageInfo.currentPage - 1;
    let buffer = this.getPageBuffer(this.context.pageInfo.currentPage);
    buffer = this.getMenuPaginationOptions(buffer, this.context.level, this.context.pageInfo.currentPage, this.context.pageInfo.lastPage);
    return this.response({
      ext: this.context.getUID() || MAIN_MENU,
      code: MenuErrorCodes.SUCCESS,
      message: buffer
    });
  }

  async execute(option) {
    try {
      if (option != null) {
        let index = this.context.lookup(option);
        if (index != -1) {
          let selectedOption = this.getMenuTitles(this.context.getChild(index));
          CDRWriter.emit("PLUGIN_CDR", this.appId, this.moduleId, "menu", "-", "-", "-", "-", this.sessionId, this.userId, "option", selectedOption, option);
        }
      }
      if (option != null && option.trim().length === 0) option = null;
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "User Input: " + option + ", Current Context: " + this.context.getType());
      }
      switch (this.context.getType()) {
        case NodeType.QUESTIONNAIRE:
        case NodeType.QUESTION:
          return this.handleQuestionarie(option);
        case NodeType.DYNAMIC:
          return await this.handleDynamicMenu(option);
        case NodeType.STATIC_DYNAMIC:
          return await this.handleDynamicWithStaticMenu(option);
      }
  
      this.paginate();
      if (this.isPaginateSuccess == false) {
        return this.response({
          ext: this.context.getUID() || MAIN_MENU,
          code: MenuErrorCodes.FAILURE,
          message: message.getResponseJson(this.language, MenuErrorCodes.LOOP).msg
        });
      }
      if (this.context.getCount() == 0) {
        return this.response({
          ext: this.context.getUID() || MAIN_MENU,
          code: MenuErrorCodes.NOCHILDREN,
          message: {}
        });
      }
      if (option == null) option = 'null';
  
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "User Input: " + option + ", Children: " + this.context.getCount());
      }
      switch (option) {
        case global.config.app_engine.sessionCleanKey: {
          return this.response({
            ext: this.context.getUID() || MAIN_MENU,
            code: MenuErrorCodes.CLEAN_SESSION,
            message: [],
            properties: []
          });
        }
        case this.navigationOpts.help.code: {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace(this.cid + "Opt: " + this.navigationOpts.help.message);
          }
          return this.getHelpMenu();
        }
        case 'null': {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace(this.cid + "Opt: " + this.navigationOpts.mainMenu.message);
          }
          return this.getMainMenu();
        }
        case this.navigationOpts.mainMenu.code: {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace(this.cid + "Opt: " + "Setting Main Menu Application");
          }
          return this.response({
            ext: this.context.getUID() || MAIN_MENU,
            code: MenuErrorCodes.NOCHILDREN,
            message: {},
            isMainMenuOption: true
          });
        }
        case this.navigationOpts.firstPage.code: {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace(this.cid + "Opt: " + this.navigationOpts.firstPage.message);
          }
          return this.getFirstPageMenu();
        }
        case this.navigationOpts.nextPage.code: {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace(this.cid + "Opt: " + this.navigationOpts.nextPage.message);
          }
          return await this.getNextMenu(this.navigationOpts.nextPage.code);
        }
        case this.navigationOpts.backwardSkip.code: {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace(this.cid + "Opt: " + "Setting Previous Menu Application and Module");
          }
          if (this.context.level === 0) {
            return this.response({
              ext: this.context.getUID() || MAIN_MENU,
              code: MenuErrorCodes.NOCHILDREN,
              message: {},
              isPreviousMenuOption: true
            });
          } else {
            // Only reset pageInfo.currentPage and getPreviousMenu if level > 0
            this.context.pageInfo.currentPage = 0; // Resetting to 0
            return this.getPreviousMenu(); // Return here instead
          }
        }
        case this.navigationOpts.prevPage.code: {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace(this.cid + "Opt: " + this.navigationOpts.prevPage.message);
          }
          return this.getPreviousMenu();
        }
        case this.navigationOpts.lastPage.code: {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace(this.cid + "Opt: " + this.navigationOpts.lastPage.message);
          }
          return this.getLastPageMenu();
        }
        default: {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace(this.cid + "Default option %d", Number(option));
          }
          if (!isNaN(Number(option))) {
            let opt = Number(option);
            if (global.logger.isTraceEnabled()) {
              global.logger.trace(this.cid + "Get next menu for option %d", opt);
            }
            return await this.getNextMenu(opt);
          } else {
            return this.getDefaultPage();
          }
        }
      }
  
    } catch (e) {
      global.logger.error("Failed to execute menu", e);
      return this.getDefaultPage();
    }
  }
  
  getDefaultPage() {
    if (this._multiAccess) {
      return this.response({
        ext: this.context.getUID() || MAIN_MENU,
        code: MenuErrorCodes.FAILURE,
        message: message.getResponseJson(this.language, HTTP_CODES.SMPP_INVALID_SHORTCODE).msg
      });
    } else {
      this.context.pageNumber = 0;
      this.paginate();
      if (this.isPaginateSuccess == false) {
        return this.response({
          ext: this.context.getUID() || MAIN_MENU,
          code: MenuErrorCodes.FAILURE,
          message: message.getResponseJson(this.language, MenuErrorCodes.LOOP).msg
        });
      }
      let main = this.getFirstPageMenu();
      if (global.config.app_engine.menuNavigationEnable)
        return this.response({
          ext: this.context.getUID() || MAIN_MENU,
          code: MenuErrorCodes.CONTINUE,
          message: message.getResponseJson(this.language, HTTP_CODES.SMPP_INVALID_INPUT).msg + NEWLINE + main.message
        });
      return this.response({
        ext: this.context.getUID() || MAIN_MENU,
        code: MenuErrorCodes.CONTINUE,
        message: main.message
      });
    }
  }

  getPageBuffer(pageNumber) {
    let buffer = "";
    for (let i = 0; i < this.context.getCount(); i++) {
      let menuName = this.getMenuTitles(this.context.getChild(i));
      let child = this.context.getChild(i);
      if (child.getPageNumber() == pageNumber) {
        switch (child.getType()) {
          case NodeType.TITLE:
          case NodeType.CUSTOM:
          case NodeType.EXIT:
            if (child.isEnabled()) {
              buffer = buffer + menuName + NEWLINE;
            }
            break;
          default:
            if (child.isEnabled()) {
              let index = this.context.getLookUpKeyByValue(i);
              let padding = (child.getSettings().padding != null) ? child.getSettings().padding : this.menuIndexPadding;

              if (global.logger.isTraceEnabled()) {
                global.logger.trace(this.cid + "PBI: %d, Name: %s, Type: %s, APNo:%d, RPNo:%d", index, menuName, child.getType(), child.getPageNumber(), pageNumber);
              }
              buffer = buffer + index + padding + menuName + NEWLINE;
            }
        }
      }
    }
    return buffer;
  }

  response(resp = {}) {
    if (global.logger.isTraceEnabled()) {
      let len = resp.message && resp.message.length || -1;
      global.logger.trace(this.cid + "GetRespMsg Code:", resp.code, ", Msg:", resp.message, ", Len:", len);
    }
    resp.type = this.context.type;
    return resp;
  }

  toJSON() {
    let deserializeObj;
    try {
      deserializeObj = {
        rootContext: jsonifyMenu(this.rootContext),
        context: jsonifyMenu(this.context),
        language: this.language,
        bufferSize: this.bufferSize,
        pageInfo: this.context.pageInfo,
        mainMenuOption: this.mainMenuOption,
        dynamic_status: this.dynamic_status,
        menuNavigationOptions: this.navigationOpts,
        translation: this.translation,
        menuNavigationEnable: this.menuNavigationEnable
      };
      return deserializeObj;
    } finally {
      deserializeObj = null;
    }
  }

  toString() {
    return JSON.stringify(this.toJSON());
  }
}

module.exports = MenuBuilder;

function jsonifyMenu(item) {
  if (!item) {
    return {};
  }
  let result = {
    uid: item.getUID(),
    id: item.getId(),
    parentid: item.getParentId(),
    name: item.getName(),
    type: item.getType(),
    level: item.getLevel(),
    title: item.getTitle(),
    index: item.getIndex(),
    enabled: item.isEnabled(),
    pageNumber: item.getPageNumber(),
    data: item.getData(),
    contentType: item.getContentType(),
    menuProperty: item.getProperties(),
    settings: item.getSettings(),
    questionBank: item.getQuestionBank(),
    children: { items: [] }
  };

  for (let i = 0; i < item.getChildren().length; i++) {
    result.children.items[i] = jsonifyMenu(item.getChildren()[i]);
  }

  return result;
}

function searchNode(node, id) {
  if (node == null) return null;
  if (id == null) return node;
  if (node.getId() == id || node.getUID() == id) return node;

  let children = node.getChildren();
  if (children != null)
    for (let i = 0; i < children.length; i++) {
      let res = searchNode(children[i], id);
      if (res != null) return res;
    }
  return null;
}

function prepareMenuContext(opts) {
  if (opts == null) {
    opts = { level: 0, children: { items: [] } };
  } else if (typeof opts == "string") {
    opts = JSON.parse(opts);
  }
  if (opts.children == null) opts.children = { items: [] };
  if (opts.level == null) opts.level = 0;
  if (opts.questionBank == null || typeof opts.questionBank == "string") {
    opts.questionBank = {};
  }
  if (opts.type == NodeType.QUESTION) {
    if (opts.question != null) {
      opts.name = opts.question;
    }
    if (opts.tag != null) {
      opts.questionBank.tag = opts.tag
    }
  }
  let node = {
    id: opts.id,
    uid: opts.uid || opts.id,
    parentid: opts.parentid,
    name: opts.name,
    type: opts.type || NodeType.CHOICE,
    level: opts.level || 0,
    title: opts.title,
    data: opts.data || {},
    enabled: opts.enabled,
    pageNumber: opts.pageNumber,
    contentType: opts.contentType,
    menuProperty: opts.menuProperty || {},
    settings: opts.settings || {},
    questionBank: opts.questionBank
  };
  let menuItem = new Node(node);
  for (let i = 0; i < opts.children.items.length; i++) {
    opts.children.items[i].level = Number(opts.level) + 1;
    menuItem.addChild(prepareMenuContext(opts.children.items[i]));
  }
  return menuItem;
}

function getNavigationMessage(cid, opts, navOpt) {
  if (global.logger.isTraceEnabled()) {
    global.logger.trace(cid + "GetNavMsg Code: %s, Msg: %s, Len: %d", opts.code, opts.message, opts.message.length);
  }
  if (nav_translation != null && nav_translation[navOpt] != undefined && !global.config.app_engine.commonMenuNavigation) {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("GetMenuNavigationOptions from Translation table for ", opts.code, ", message:", opts.message);
    }
    return opts.code + " " + nav_translation[navOpt] + NEWLINE;
  }
  return opts.code + " " + opts.message + NEWLINE;
}

function setNavigationOpts(opts) {
  return {
    mainMenu: opts && opts.mainMenu || { code: "#", message: "Home" },
    nextPage: opts && opts.nextPage || { code: "n", message: "next" },
    backwardSkip: opts && opts.backwardSkip || { code: "*", message: "Back" },
    firstPage: opts && opts.firstPage || { code: "F", message: "first" },
    prevPage: opts && (opts.prevPage || opts.previousPage) || { code: "b", message: "back" },
    lastPage: opts && opts.lastPage || { code: "L", message: "last" },
    help: opts && opts.help || { code: "h", message: "Help" }
  };
}

function getMenuBufferSize(bufferSize, language) {
  let buff;
  if (bufferSize != null) {
    // Set buff to 180 if bufferSize is provided
    buff = 180;
  } else {
    // Use the provided bufferSize if it's null
    buff = bufferSize;
  }
  return buff;
}


function httpCall(req) {
  return new Promise(resolve => {
    request(req, (error, response, body) => {
      if (error) {
        if (error.code == "ESOCKETTIMEDOUT" || error.code == "ETIMEDOUT") {
          resolve({
            code: error_codes.pluginExecTimeOut,
            body: "Sorry!! External application is not responding..."
          });
        } else {
          resolve({
            code: error.code,
            body: "External application down..."
          });
        }
      } else {
        resolve({
          code: response.statusCode,
          response,
          body
        });
      }
    });
  });
}

const agentpool = {};

function getPoolAgent(req) {
  let myURL, key;
  try {
    myURL = new URL(req.url);
    key = myURL.hostname + ":" + myURL.port;
    if (!agentpool.hasOwnProperty(key)) {
      const protocol = myURL.protocol == "https" ? require("https") : require("http");
      agentpool[key] = new protocol.Agent({
        maxSockets: 100,
        keepAlive: true,
        maxFreeSockets: 10,
        timeout: req.timeout || 30000,
        keepAliveMsecs: 30000
      });
    }
    return agentpool[key];
  } finally {
    key = null;
    myURL = null;
  }
}
