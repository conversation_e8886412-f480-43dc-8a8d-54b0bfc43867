"use strict";

const Enums = require("../lib/enums");
const VERBOSE = Enums.VERBOSE;
const WS = Enums.WS;
const UUID = require("uuid").v4;
const WebSocket = require("ws");

const WS_DELIM = "|";
const UserSessionStore = require("../cache/UserSessionStore");

const sockets = {};

function consoleLog(ws, verbose, data) {
  let log = WS.CONSOLE + WS_DELIM + verbose.desc + WS_DELIM + data;
  if (global.logger.isTraceEnabled()) { global.logger.trace(log); }
  if (ws) { ws.send(log); }
}

function send(ws, data) {
  if (global.logger.isTraceEnabled()) { global.logger.trace(data); }
  if (ws) { ws.send(data); }
}
function readData(index, data) {
  if (data && data.indexOf(WS_DELIM) > -1) {
    const arr = data.split(WS_DELIM);
    if (arr.length >= index + 1) {
      return arr[index];
    }
  }
  return null;
}

function readKeyword(data) {
  try {
    const keyword = readData(0, data);
    if (keyword) { return keyword; }
  } catch (e) {
    // Ignore
  }
  return -1;
}

function readInvoker(data) {
  try {
    const input = readData(1, data);
    if (input) { return input; }
  } catch (e) {
    // Ignore
  }
  return -1;
}

function readSettings(data) {
  try {
    const settings = readData(2, data);
    if (settings) { return JSON.parse(settings); }
  } catch (e) {
    // Ignore
  }
  return { verbosity: "NONE", pbm: "Y" };
}

function readVars(data) {
  try {
    const vars = readData(2, data);
    if (vars) { return JSON.parse(vars); }
  } catch (e) {
    // Ignore
  }
  return null;
}

function readInput(data) {
  try {
    const vars = readData(2, data);
    if (vars) { return vars; }
  } catch (e) {
    // Ignore
  }
  return null;
}

function handleRegister(socket, data) {
  const sessionId = UUID();
  try {
    socket.sessionId = sessionId;
    const invoker = readInvoker(data);
    consoleLog(socket, VERBOSE.TRACE, "SessionId:" + sessionId);
    sockets[sessionId] = { invoker, socket, settings: { verbosity: VERBOSE.TRACE, pbm: true } };
    send(socket, WS.REGISTER + WS_DELIM + WS.OK + WS_DELIM + sessionId);
    consoleLog(socket, VERBOSE.INFO, "Register Invoker successfully");
  } catch (e) {
    consoleLog(socket, VERBOSE.ERROR, "Invoker Registration failed " + e.toString());
    send(socket, WS.REGISTER + WS_DELIM + WS.NOT_OK + WS_DELIM + sessionId);
  }
}

function handleSettings(socket, data) {
  let sessionId;
  if (!socket) { return; }
  try {
    sessionId = readInvoker(data);
    const session = sockets[sessionId];

    if (session) {
      const settings = readSettings(data);
      if (settings.verbosity) {
        const verbose = settings.verbosity.toUpperCase();
        consoleLog(socket, VERBOSE.INFO, "Registering Verbose: " + verbose);
        if (VERBOSE[verbose]) {
          session.settings.verbosity = VERBOSE[verbose];
          consoleLog(socket, VERBOSE.INFO, "Registered the settings: Verbosity " + JSON.stringify(session.settings.verbosity));
        }
      }
      if (settings.pbm) {
        const pbm = settings.pbm.toUpperCase();
        consoleLog(socket, VERBOSE.INFO, "Registering PBM: " + pbm);
        if (pbm === "Y" || pbm === "N") {
          session.settings.pbm = pbm === "Y";
          consoleLog(socket, VERBOSE.INFO, "Registered the settings: Pause between modules");
        }
      }
      consoleLog(socket, VERBOSE.INFO, "Registered the settings");
      send(socket, WS.SETTINGS + WS_DELIM + WS.OK + WS_DELIM + sessionId);
    } else {
      consoleLog(socket, VERBOSE.ERROR, "Webconsole Session not found");
      send(socket, WS.SETTINGS + WS_DELIM + WS.NOT_OK + "|Console not registered");
    }
  } catch (e) {
    global.logger.error(e);
    consoleLog(socket, VERBOSE.ERROR, "Invoker Registration failed " + e.toString());
    send(socket, WS.SETTINGS + WS_DELIM + WS.NOT_OK + WS_DELIM + sessionId);
  }
}

async function handleNext(socket, data) {
  let sessionId;
  try {
    sessionId = readInvoker(data);
    consoleLog(socket, VERBOSE.TRACE, "SessionId: " + sessionId);
    const session = await UserSessionStore.getInvokerSession(sessionId);
    consoleLog(socket, VERBOSE.TRACE, "Session(" + sessionId + "): " + typeof session);

    if (session) {
      consoleLog(socket, VERBOSE.TRACE, "SessionId: " + session.getUUID());
      session.emit(WS.CONSOLE, VERBOSE.TRACE, "Session found");
      require("../app_man").websocketInterface(session.getOriginAppId(), session.getUserId(), { debugId: sessionId, isNewRequest: 0 });
      session.emit(WS.CONSOLE, VERBOSE.INFO, "Next operation accepted successfully ");
      send(socket, WS.NEXT + WS_DELIM + WS.OK + WS_DELIM + sessionId);
    } else {
      consoleLog(socket, VERBOSE.ERROR, "Session not found");
      send(socket, WS.NEXT + WS_DELIM + WS.NOT_OK + WS_DELIM + sessionId);
    }
  } catch (e) {
    global.logger.error(e);
    consoleLog(socket, VERBOSE.ERROR, "Session not found");
    send(socket, WS.NEXT + WS_DELIM + WS.NOT_OK + WS_DELIM + sessionId);
  }
}

async function handleMenuInput(socket, incomingData) {
  let sessionId;
  try {
    sessionId = readInvoker(incomingData);
    const session = await UserSessionStore.getInvokerSession(sessionId);
    if (session) {
      consoleLog(socket, VERBOSE.TRACE, "SessionId: " + session.getUUID());
      session.emit(WS.CONSOLE, VERBOSE.INFO, "Session found");
      let subscriberInput = readInput(incomingData);
      consoleLog(socket, VERBOSE.INFO, "User input: " + subscriberInput);
      session.emit(WS.CONSOLE, VERBOSE.INFO, "Input accepted successfully ");
      require("../app_man").websocketInterface(session.getOriginAppId(), session.getUserId(), { debugId: sessionId, isNewRequest: 0, subscriberInput: subscriberInput });
      send(socket, WS.INPUT + WS_DELIM + WS.OK + WS_DELIM + sessionId);

    } else {
      consoleLog(socket, VERBOSE.ERROR, "Session not found");
      send(socket, WS.INPUT + WS_DELIM + WS.NOT_OK + WS_DELIM + sessionId);
    }
  } catch (e) {
    global.logger.error(e);
    consoleLog(socket, VERBOSE.ERROR, "Session not found");
    send(socket, WS.INPUT + WS_DELIM + WS.NOT_OK + WS_DELIM + sessionId);
  }
}

async function handleInput(socket, incomingData) {
  let sessionId;
  try {
    sessionId = readInvoker(incomingData);
    const session = await UserSessionStore.getInvokerSession(sessionId);
    if (session) {
      consoleLog(socket, VERBOSE.TRACE, "SessionId: " + session.getUUID());
      session.emit(WS.CONSOLE, VERBOSE.INFO, "Session found");
      const subscriberInput = readInput(incomingData);
      if (subscriberInput) {
        consoleLog(socket, VERBOSE.INFO, "Valid input");
        session.emit(WS.CONSOLE, VERBOSE.INFO, "Input accepted successfully ");
        require("../app_man").websocketInterface(session.getOriginAppId(), session.getUserId(), { debugId: sessionId, isNewRequest: 0, subscriberInput });
        send(socket, WS.INPUT + WS_DELIM + WS.OK + WS_DELIM + sessionId);
      } else {
        consoleLog(socket, VERBOSE.INFO, "Invalid input");
        send(socket, WS.INPUT + WS_DELIM + WS.NOT_OK + WS_DELIM + sessionId);
      }
    } else {
      consoleLog(socket, VERBOSE.ERROR, "Session not found");
      send(socket, WS.INPUT + WS_DELIM + WS.NOT_OK + WS_DELIM + sessionId);
    }
  } catch (e) {
    global.logger.error(e);
    consoleLog(socket, VERBOSE.ERROR, "Session not found");
    send(socket, WS.INPUT + WS_DELIM + WS.NOT_OK + WS_DELIM + sessionId);
  }
}

async function handleVars(socket, incomingData) {
  let sessionId;
  try {
    sessionId = readInvoker(incomingData);
    const vars = readVars(incomingData);
    const session = await UserSessionStore.getInvokerSession(sessionId);
    if (session) {
      consoleLog(socket, VERBOSE.TRACE, "SessionId: " + session.getUUID());
      session.emit(WS.CONSOLE, VERBOSE.INFO, "Session found");
      if (vars) {
        session.emit(WS.CONSOLE, VERBOSE.INFO, "Valid variables");
        session.getContext().setProcess(vars);
        session.emit(WS.CONSOLE, VERBOSE.INFO, "Variables saved successfully");
        send(socket, WS.VARS + WS_DELIM + WS.OK + WS_DELIM + sessionId);
      } else {
        session.emit(WS.CONSOLE, VERBOSE.ERROR, "Invalid variables");
        send(socket, WS.VARS + WS_DELIM + WS.NOT_OK + WS_DELIM + sessionId);
      }
    } else {
      consoleLog(socket, VERBOSE.ERROR, "Session not found");
      send(socket, WS.VARS + WS_DELIM + WS.NOT_OK + WS_DELIM + sessionId);
    }
  } catch (e) {
    global.logger.error(e);
    consoleLog(socket, VERBOSE.ERROR, "Session not found");
    send(socket, WS.VARS + WS_DELIM + WS.NOT_OK + WS_DELIM + sessionId);
  }
}

function unRegisterEvent(socket) {
  if (socket) {
    delete sockets[socket.sessionId];
    socket = null;
  }
}

function registerEvent(socket, incomingData) {
  if (socket && incomingData) {
    let keyword = readKeyword(incomingData) || "NA";
    switch (keyword) {
      case WS.REGISTER: handleRegister(socket, incomingData);
        break;
      case WS.SETTINGS: handleSettings(socket, incomingData);
        break;
      case WS.NEXT: handleNext(socket, incomingData);
        break;
      case WS.MENU: handleMenuInput(socket, incomingData);
        break;
      case WS.INPUT: handleInput(socket, incomingData);
        break;
      case WS.VARS: handleVars(socket, incomingData);
        break;
      default:
        consoleLog(socket, VERBOSE.WARN, "Keyword not identified: " + keyword);
    }
  }
}

module.exports = {
  init: (server) => {
    try {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Initializing Websocket");
      }
      let wss = new WebSocket.Server({ server });

      wss.on("connection", wsClient => {
        if (global.logger.isTraceEnabled()) { global.logger.trace("Obtained Websocket client " + wsClient); }
        wsClient.on("open", (incomingData) => {
          if (global.logger.isTraceEnabled()) { global.logger.trace("WS-EVENT-OPEN: " + incomingData); }
        });
        wsClient.on("close", incomingData => {
          if (global.logger.isTraceEnabled()) { global.logger.trace("WS-EVENT-CLOSE: " + incomingData); }
          unRegisterEvent(wsClient);
        });
        wsClient.on("error", (error) => {
          if (global.logger.isTraceEnabled()) { global.logger.trace("WS-EVENT-ERROR: " + error); }
        });
        wsClient.on("message", incomingData => {
          registerEvent(wsClient, incomingData);
        });
      });
    } catch (e) {
      global.logger.error("Failed to init WS", e);
    }
    return Promise.resolve();
  },
  get: (sessionId) => { return sockets[sessionId]; },
  clear: (sessionId) => { delete sockets[sessionId]; }
};
