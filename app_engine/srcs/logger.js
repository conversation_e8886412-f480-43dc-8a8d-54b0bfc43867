"use strict";
/**
 ** logger module that initializes log4js settings.
 **/

const log4js = require("log4js");
const path = require("path");
const fs = require("fs");
const mkdirp = require("mkdirp");
const fileStreamRotator = require("file-stream-rotator");
const Morgan = require("morgan");
const dateformat = require("dateformat");

var logger;

module.exports = {
    init: () => {
        // For this module to be initialized successfully for the first time,
        // "global.config.log" variable should have been set.
        if (!(global.config && global.config.instance && global.config.instance.log)) {
            throw new Error("logger.js -- cannot initialize, because 'global.config' is not set.");
        }

        // create the logs folder if not existing.
        let logdir;
        if (global.config.instance.log.logdir != null) {
            logdir = path.resolve(global.config.instance.log.logdir);
        } else {
            logdir = path.join(__dirname, "/logs");
        }
        if (!fs.existsSync(logdir)) {
            mkdirp.sync(logdir);
        }
        let logConfiguration = global.config.instance.log.log4js;
        Object.keys(logConfiguration.appenders).forEach(appender => {
            try {
                if (logConfiguration.appenders[appender].type != "console") {
                    logConfiguration.appenders[appender].filename = path.join(logdir, logConfiguration.appenders[appender].filename);
                }
            } catch (e) {
                console.error(e);
            }
        });

        Object.keys(logConfiguration.categories).forEach(category => {
            try {
                logConfiguration.categories[category].appenders = [logConfiguration.categories[category].appender];
                delete logConfiguration.categories[category].appender;
            } catch (error) {
                //ignore
            }
        });
        log4js.configure(logConfiguration);
        logger = log4js.getLogger(logConfiguration.categories.default.appenders[0]);
        logger.level = logConfiguration.categories.default.level;
        logger.warn("Log4j initialized");
        global.log4js = log4js;
        global.logger = logger;
        if(global.config.instance.log.enable_access_log == true) {
            let accessLogStream = fileStreamRotator.getStream({
                    date_format: 'YYYYMMDDHH',
                    filename: path.join(global.config.instance.log.logdir, global.config.instance.log.cdr + '_%DATE%.cdr'),
                    frequency: 'custom',
                    verbose: false
            });
            Morgan.token('date', function(){
                      return dateformat(new Date(), "yyyy/mm/dd HH:MM:ss:l");
            });
           global.logger.morgan = Morgan(':date' + global.config.instance.log.cdrFormat, { stream: accessLogStream });
       }
        if (logConfiguration.categories.smpp_events != null) {
            global.smpplogger = log4js.getLogger(logConfiguration.categories.smpp_events.appenders[0]);
        } else {
            global.smpplogger = logger;
        }
        if (logConfiguration.categories.pluginCDR != null) {
            global.pluginCDR = log4js.getLogger(logConfiguration.categories.pluginCDR.appenders[0]);
        } else {
            global.pluginCDR = logger;
        }
    }
};
