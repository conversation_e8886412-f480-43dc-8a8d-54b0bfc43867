"use strict";
// ==============================
// CONCRETE STATE
// ==============================

/**
 * @class EndContextState
 * @version 1.0.0
 * @since 03 April 2018
 * @description Concrete state which provide the implementation to Send intermdiate or final response to user's request on a given app
 *
 *
 * <AUTHOR>
 */

// Import required App engine state managing classes
const ContextState = require("./ContextState");
const UserSessionStore = require("../cache/UserSessionStore");

// Import context helpers
const KPI = require("app_kpi");

// Import Enums and constants
const ENUMS = require("../lib/enums");
const WS = ENUMS.WS, VERBOSE = ENUMS.VERBOSE;
const KPI_KEY = KPI.KEYS.tps;

class EndContextState extends ContextState {

  async execute(session) {
    try {
      let et = new Date().getTime();
      session.emit("LOG_ENTRY");
      if (global.logger.isTraceEnabled()) {
        session.emit(WS.CONSOLE, VERBOSE.TRACE, "Terminate session");
        session.emit(WS.CONSOLE, VERBOSE.TRACE, "Destroying Session");
      }
      await UserSessionStore.del(session);
      if (global.logger.isInfoEnabled()) {
        session.emit(WS.CONSOLE, VERBOSE.INFO, "Destroyed Session");
      }
      let appId = session.getAppId();
      let responseCode = session.getResponseCode();
      let st = session.getStartTime();
      KPI.emit(KPI_KEY, { aid: appId, set: et, sst: st, code: responseCode });
      session = null;
    } catch (e) {
      global.logger.error("Failed to Close session object", e);
    }
  }
}

module.exports = EndContextState;
