"use strict";

const ENUMS = require("../lib/enums");
const ENVIRONMENT = ENUMS.ENVIRONMENT,
  WS = ENUMS.WS,
  VERBOSE = ENUMS.VERBOSE,
  INTERFACES = ENUMS.INTERFACES;

const EventEmitter = require("events");
const ModuleContext = require("./ModuleContext");
const AppMacros = require("app_macros");
const utility = require("utility");
const common = require("common");
const message = require("message");
const FreeFlowStates = common.FreeFlowStates;
const HTTP_CODES = require("../lib/http_codes");

const PrepareContextState = require("./PrepareContextState");
const ExecuteContextState = require("./ExecuteContextState");
const EvaluateContextState = require("./EvaluateContextState");
const EndContextState = require("./EndContextState");
const AppCache = require("../cache/AppCache");
const UserSessionStore = require("../cache/UserSessionStore");
const CDRQUEUE = require("../lib/cdrqueue");
const ResponseHandler = require("../lib/ResponseHandler");
const SmppConstants = require("../ussd_service/SmppConstants");
const UssdRequest = require("../ussd_service/models/UssdRequest");
const CPSContants = require("../ussd_service/CPSContants");
const ******** = require("../lib/********");
const OS = require("os");
const KPI = require("app_kpi");
const KPI_INT_RES = KPI.KEYS.interface_res;

const SUBMIT_DELAY_RESPONSE = global.config.instance.interfaces && global.config.instance.interfaces.smpp && global.config.instance.interfaces.smpp.delay_between_submitsm || 5000;

const WS_DELIMITER = "|",
  ResponseCode = "response.responseCode",
  SymbolDolar = "$";

var CVTS = null;

if (global.config.app_engine.convertValueToString != null) {
  CVTS = global.config.app_engine.convertValueToString.split(",");
}

const SessionFlowHeader = "Freeflow";

class Session extends EventEmitter {

  constructor(opts) {
    super();
    try {
      this._waitMid = opts.waitMid || -1;
      this._id = opts.id || utility.getUniqueTxnId();
      this._originAppId = opts.originAppId || "";
      this._appId = opts.appId || "";
      this._previousMenuAppIds = opts.previousMenuAppIds || [];
      this._isMainMenu = false;
      this._from = opts.from || "";
      this._userId = opts.userId || "";
      this._locale = opts.locale || global.config.defaultLanguage;
      this._startTime = opts.startTime || new Date().getTime();
      this._endModuleId = opts.endModuleId;
      this._imode = opts.imode || INTERFACES.QUERY_STRING;
      this._imr_flag = opts.imr;
      this._im_code = null;
      this._currentState = null;
      this._ussdRequest = opts.ussdRequest && new UssdRequest(opts.ussdRequest);
      this._isNewRequest = opts.isNewRequest || true;
      this._states = {
        prepare: new PrepareContextState(),
        execute: new ExecuteContextState(),
        evaluate: new EvaluateContextState(),
        end: new EndContextState()
      };
      this.macros = new AppMacros.ExpressionEngine(opts);
      this._path = opts.path || [];
      this._mode = opts.mode || ENVIRONMENT.PROD;
      this._multiAccessCodes = opts.multiAccessCodes || [];
      //Optional
      this.freeflow = opts.freeflow || SessionFlowHeader;
      this.freeflowState = null;
      this._mid = opts.moduleId || -1;
      this._previousMenuModuleIds = opts.previousMenuModuleIds || [];
      this._invoker = opts.invoker || -1;
      this.context = opts.context && new ModuleContext({ ...opts.context, userId: this._userId, sessionId: this._id, appId: this._appId, mid: this._mid, moduleId: this._mid, language: this._locale, macros: this.macros, cid: this.getConsoleId() }) || {};
      this.new = (opts.new == true);
      this._opts = opts.optional || { leapControlMenu: true, externalNewRequest: true };
      this.registerEvents();
      let mData = this.context.getQueryParams();

      Object.keys(mData).forEach((key) => {
        if (CVTS != null && CVTS.includes(key) != -1) {
          this.setParam(key, JSON.stringify(mData[key]));
        } else {
          this.setParam(key, mData[key]);
          if (key == "SIMSERIAL") {
            this.setParam(key, JSON.stringify(mData[key]));
          }
        }
      });
    } catch (e) {
      global.logger.error("Failed to serialize the Session Obj", e);
    }
  }

  /**
   * State Machine
   */
  execute() {
    this._currentState.execute(this);
  }

  setIsNewRequest(type) {
    this._isNewRequest = type;
  }

  getIsNewRequest() {
    return this._isNewRequest;
  }

  getCurrentState() {
    return this._currentState;
  }

  start() {
    return this.setCurrentState(this.getStates().prepare);
  }

  end() {
    return this.setCurrentState(this.getStates().end);
  }

  setOpts(opts) {
    this._opts = opts;
  }

  getOpts() {
    return this._opts;
  }

  setCurrentState(state) {
    this._currentState = state;
    this.execute();
  }

  setResponseHeader(key, value) {
    if (key == this.freeflow) {
      this.freeflowState = value;
    } else {
      this._callback && this._callback.header(key, value);
    }
  }

  getStates() {
    return this._states;
  }

  async save(timeout) {
    console.log("ELSON: Timeout in save Session file:" + timeout);
    if (global.logger.isTraceEnabled()) {
      global.logger.trace(this.getConsoleId() + "Saving Session");
    }
    return await UserSessionStore.set(this, timeout);
  }

  /**
   * Setter and Getter
   */
  getUUID() { return this._id; }

  getContext() { return this.context; }

  getVars() {
    const vars = this.context.getVars();
    if (vars) {
      let data = this.macros.getDataTable();
      let process = {};
      Object.keys(vars.process).forEach(key => {
        process[key] = data["$" + key];
      });
      vars.process = process;
    }
    return vars;
  }

  getSessionId() { return this._id; }

  getMode() { return this._mode; }

  getOriginAppId() { return this._originAppId; }

  getAppId() { return this._appId; }

  getUserId() { return this._userId; }

  getModuleId() { return this._mid; }

  getWaitModuleId() { return this._waitMid; }

  getPreviousMenuModuleId() {
    return this._previousMenuModuleIds.pop();
  }


  getPreviousMenuAppId() {
    return this._previousMenuAppIds.pop();
  }

  getIsMainAppMenu() {
    if (this._previousMenuAppIds.length == 0) {
      return true;
    }
    else {
      return false;
    }
  }

  getImrFlagEnabled() { return this._imr_flag; }

  getEndModuleId() { return this._endModuleId; }

  getProcess() { return this.macros.getDataTable(); }

  getInvoker() { return this._invoker; }

  getInterfaceMode() { return this._imode; }

  getCallback() { return this._callback; }

  getMacros() { return this.macros; }

  getMsgType() { return this.macros.getDataTable()["msgType"]; }

  getLocale() {
    try {
      this._locale = this.macros.get(SymbolDolar + "language");
    } catch (e) {
      this._locale = global.config.defaultLanguage;
    }
    return this._locale;
  }

  getResponseCode() {
    let responseCode = this.macros.get(SymbolDolar + ResponseCode);
    if (responseCode != null) {
      return responseCode;
    }
    return this.context.getProcessParam(ResponseCode);
  }

  getStartTime() {
    return this._startTime;
  }

  getUssdRequest() {
    return this._ussdRequest;
  }

  setInterfaceMode(mode = "http") { this._imode = mode; }

  setCallback(res) { this._callback = res; }

  setOriginAppId(originAppId) { this._originAppId = originAppId; }

  setAppId(appId) { this._appId = appId; }

  setUserId(userId) { this._userId = userId; }

  setModuleId(moduleId) {
    this._mid = moduleId;
  }

  setWaitModuleId(moduleId) {
    this._waitMid = moduleId;
  }

  setPreviousMenuModuleId(moduleId) {
    this._previousMenuModuleIds.push(moduleId);
  }

  setPreviousMenuAppId(appId) {
    this._previousMenuAppIds.push(appId);
  }

  flushPreviousMenuTrace() {
    this._previousMenuAppIds = [];
    this._previousMenuModuleIds = [];
  }

  setEndModuleId(endModuleId) { this._endModuleId = endModuleId; }

  setImrFlag(flag) { this._imr_flag = flag; }

  setContext(context) {
    this.new = false;
    this.context = context;
  }

  setUserResponse(json) {
    this._userResponse = json;
    if (global.logger.isTraceEnabled()) {
      try {
        global.logger.trace(this.getConsoleId() + "UserResponseMessage:" + JSON.stringify(this._userResponse));
      } catch (e) {
        //ignore
      }
    }
  }

  setResponseCode(code) {
    if (code != null) {
      if (typeof code == "string" && code.trim().length === 0) {
        return -1;
      }
      if (typeof code == "number") {
        code = String(code);
      }
      if (global.logger.isTraceEnabled()) {
        this.emit(WS.CONSOLE, VERBOSE.TRACE, "Setting response code " + code);
      }
      return this.setParam(ResponseCode, code);
    }
    return -1;
  }

  setUssdRequest(ussdRequest) {
    this._ussdRequest = ussdRequest;
  }

  setParam(key, val) {
    try {

      let pluginname = this.getContext().getPluginName();
      if (typeof val == "string" && (pluginname == "oracle" || pluginname == "mysql" || pluginname == "mariadb" || pluginname == "WriteCDR") && key == "query") {
        val = this.macros.replaceAllPlaceHolders(val);
      }
      else if (typeof val == "string" && pluginname != "codeModule") {
        val = this.macros.eval(val);
      }
      this.macros.set(key, val);
    } catch (e) {
      global.logger.error(this.getConsoleId() + "Failed to setParam", e);
    }
    return false;
  }

  async setMacros() {
    try {
      if (!this.macros) {
        this.macros = new AppMacros.ExpressionEngine();
      }
      let mData = this.context.getProcess();
      Object.keys(mData).forEach((key) => {
        this.setParam(key, mData[key]);
      });
      this.macros.getDataTable()["$subscriberInput"] = this.getContext().getSubscriberInput();
    } catch (e) {
      global.logger.error("Failed to set macros", e);
    }
    return this.context;
  }

  isNew() {
    if (this.new == null) { this.new = true; }
    return (this.new == true);
  }

  existing() {
    this.new = false;
  }

  isPBM() {
    if (this._mode == ENVIRONMENT.DEV) {
      let WebsocketManager = require("../websocket/WebsocketManager");
      const webConsole = WebsocketManager.get(this._invoker);
      return webConsole && webConsole.settings && webConsole.settings.pbm || false;
    }
    return false;
  }

  isMultiAccess() {
    return this._ussdRequest && this._ussdRequest.isMultiAccess;
  }

  getMultiAccessCodes() {
    return this.multiAccessCodes;
  }

  setMultiAccessCodes(multiAccessCodes) {
    this.multiAccessCodes = multiAccessCodes;
  }

  sendImmediateResponse(code, msg) {
    this._im_code = code;
    return new Promise((resolve) => {
      try {
        if (global.logger.isTraceEnabled()) {
          global.logger.warn(utility.YYYYMMDDHHmmssSSS() + "|IResponse|UID:" + this.getUserId() + "|AID:" + this.getAppId() + "|MODE:" + this._mode + "|INT:" + this._imode + "|HR:" + (this._callback != null) + "|FF:" + FreeFlowStates.break + "|MSG:" + msg);
        }

        const handleResponse = async () => {
          switch (this._imode) {
            case INTERFACES.QUERY_STRING: {
              if (this._callback) {
                if (this.freeflow != null) {
                  this._callback.header(this.freeflow, FreeFlowStates.break);
                }
                this._callback.send(msg);
                this._callback = null;
              }
              resolve(1);
              break;
            }
            case INTERFACES.HTTP_JSON: {
              if (this._callback) {
                if (this.freeflow != null) {
                  this._callback.header(this.freeflow, FreeFlowStates.break);
                }
                this._callback.json({ code: 200, msg: msg });
                this._callback = null;
              }
              resolve(1);
              break;
            }
            case INTERFACES.SMPP: {
              if (this._ussdRequest) {
                this._ussdRequest.setContent(msg);
                ResponseHandler.sendSmppResponse(this._ussdRequest, SmppConstants.SERVICE_OP_PSSR_RESPONSE);
                setTimeout(() => {
                  resolve(2);
                }, SUBMIT_DELAY_RESPONSE);
              }
              break;
            }
            case INTERFACES.CPS_XML: {
              if (this._ussdRequest) {
                this._ussdRequest.setContent(msg);
                ResponseHandler.sendCpsResponse(this._callback, this._ussdRequest, CPSContants.BEGIN_NOTIFY);
                resolve(2);
              }
              break;
            }
            case INTERFACES.NEXMO: {
              await ********.sendNexmoMessage(this._from, this._userId, msg);
              resolve(3);
              break;
            }
            default:
              resolve(3);
          }
          KPI.emit(KPI_INT_RES, this._imode);
        };

        // Call the async handling function
        handleResponse();
      } catch (error) {
        global.logger.error("Exception in sendImmediateResponse", error);
        resolve(-1);
      }
    });
  }

  getConsoleId() {
    return "SID:" + this.getSessionId() + "|UID:" + this.getUserId() + "|AID:" + this.getAppId() + "|MID:" + this.getModuleId() + "|";
  }

  async sendDefaultResponse() {
    let msg = message.getResponseJson(this._locale, HTTP_CODES.UNDEFINED_ENDMODULE).msg;
    this.setUserResponse({ type: ENUMS.MSGTYPE.USSD, msg });
    await this.sendResponse(FreeFlowStates.break, "TERMINATE");
    return this.end();
  }

  sendResponse(flow, option) {
    return new Promise((resolve) => {
      try {
        if (global.logger.isTraceEnabled()) {
          try {
            global.logger.trace(this.getConsoleId() + "SendMessage:" + JSON.stringify(this._userResponse));
          } catch (e) {
            //ignore
          }
        }
        if (this.freeflowState != null) {
          flow = this.freeflowState;
          this.freeflowState = null;
        }
        if (global.logger.isTraceEnabled()) {
          global.logger.warn(
            utility.YYYYMMDDHHmmssSSS() +
            "|Response|UID:" + this.getUserId() +
            "|AID:" + this.getAppId() +
            "|MODE:" + this._mode +
            "|INT:" + this._imode +
            "|HR:" + (this._callback != null) +
            "|FF:" + flow +
            "|MSG:" + this._userResponse.msg +
            "|Origin:", option
          );
        }
        if (this._userResponse.msg == null || (typeof this._userResponse.msg === "string" && this._userResponse.msg.trim().length === 0)) {
          return resolve(); // Resolving immediately if the message is null or empty
        }

        KPI.emit(KPI_INT_RES, this._imode);
        const handleResponse = async () => {
          switch (this._imode) {
            case INTERFACES.QUERY_STRING: {
              if (this._callback != null) {
                let codes = this.getMultiAccessCodes();
                if (codes != null && codes.length > 0 && flow === FreeFlowStates.continue) {
                  let option = this.getMultiAccessCodes().shift();
                  if (option != null) {
                    this.getContext().setSubscriberInput(String(option));
                    return this.start();
                  }
                }
                if (this.freeflow != null) {
                  this._callback.header(this.freeflow, flow);
                }
                if (this.context.getQueryParams().contentType === "text") {
                  this._callback.send(this._userResponse.msg);
                } else {
                  this._callback.json({ ...this._userResponse, code: this.getResponseCode() });
                }
                this._callback = null;
                return resolve(1);
              } else if (option !== "RESPONSE") {
                await ResponseHandler.sendSmppFlash({
                  MSISDN: this.getUserId(),
                  SENDER: global.leap_settings.defaults.smpp_sc,
                  MESSAGE: this._userResponse.msg,
                  GATEWAYID: global.leap_settings.defaults.smpp_gw_id,
                  USERNAME: global.leap_settings.defaults.smpp_gw_user,
                  PASSWORD: global.leap_settings.defaults.smpp_gw_password
                }, SmppConstants.SERVICE_OP_PSSR_RESPONSE);
                return resolve(2);
              } else {
                return this.end();
              }
            }
            case INTERFACES.CALLBACK: {
              this._callback({ code: this.getResponseCode(), msg: this._userResponse.msg });
              return resolve();
            }
            case INTERFACES.HTTP_JSON: {
              if (this._callback != null) {
                let codes = this.getMultiAccessCodes();
                if (codes != null && codes.length > 0 && flow === FreeFlowStates.continue) {
                  let option = this.getMultiAccessCodes().shift();
                  if (option != null) {
                    this.getContext().setSubscriberInput(String(option));
                    return this.start();
                  }
                }
                if (this.freeflow != null) {
                  this._callback.header(this.freeflow, flow);
                }
                this._callback.json({ code: this.getResponseCode(), msg: this._userResponse.msg });
                this._callback = null;
                return resolve(1);
              } else if (option !== "RESPONSE") {
                await ResponseHandler.sendSmppFlash({
                  MSISDN: this.getUserId(),
                  SENDER: global.leap_settings.defaults.smpp_sc,
                  MESSAGE: this._userResponse.msg,
                  GATEWAYID: global.leap_settings.defaults.smpp_gw_id,
                  USERNAME: global.leap_settings.defaults.smpp_gw_user,
                  PASSWORD: global.leap_settings.defaults.smpp_gw_password
                }, SmppConstants.SERVICE_OP_PSSR_RESPONSE);
                return resolve(2);
              } else {
                return this.end();
              }
            }
            case INTERFACES.NEXMO: {
              await ********.sendNexmoMessage(this._from, this._userId, this._userResponse.msg);
              return resolve();
            }
            case INTERFACES.SMPP: {
              this._ussdRequest.setContent(this._userResponse.msg);
              if (flow === FreeFlowStates.continue) {
                if (this._ussdRequest.isMultiAccess) {
                  let option = this._ussdRequest.getMultiAccessCodes() && this._ussdRequest.getMultiAccessCodes().shift();
                  if (option != null && option.length > 0) {
                    this.getContext().setSubscriberInput(String(option));
                    return this.start();
                  } else {
                    this._ussdRequest.setMultiAccess(false);
                  }
                }
              }
              ResponseHandler.sendSmppResponse(this._ussdRequest, (flow === FreeFlowStates.break) ? SmppConstants.SERVICE_OP_PSSR_RESPONSE : SmppConstants.SERVICE_OP_USSR_REQUEST);
              return resolve();
            }
            case INTERFACES.CPS_XML: {
              this._ussdRequest.setContent(this._userResponse.msg);
              let type = CPSContants.END_ACTION;
              let isContinue = !(flow === FreeFlowStates.break);
              switch (this._ussdRequest.getServiceType()) {
                case CPSContants.PUSH_REQUEST:
                  type = CPSContants.BEGIN_REQUEST;
                  break;
                case CPSContants.BEGIN_REQUEST:
                  if (isContinue)
                    type = CPSContants.CONTINUE_REQUEST;
                  else
                    type = CPSContants.BEGIN_NOTIFY;
                  break;
                case CPSContants.CONTINUE_ACTION:
                  if (isContinue)
                    type = CPSContants.CONTINUE_NOTIFICATION;
                  break;
                case CPSContants.CONTINUE_NOTIFICATION_ACTION:
                  type = CPSContants.END_ACTION;
                  break;
              }
              this._ussdRequest.addOptionalParam("SENDERADDRESS", this.macros.get(SymbolDolar + "SENDERADDRESS"));
              this._ussdRequest.addOptionalParam("CPID", this.macros.get(SymbolDolar + "CPID"));
              ResponseHandler.sendCpsResponse(this._callback, this._ussdRequest, type);
              return resolve();
            }
            default:
              return resolve();
          }
        };
        handleResponse();
        this._userResponse = null;
      } catch (e) {
        global.logger.error(this.getConsoleId() + "Failed to send HTTP Response", e);
        resolve(0);
      }
    });
  }


  writePluginCDR(originalCode = 0, typeCasted, input, ext = "") {
    let moduleEntry;
    try {
      if (typeCasted == null) typeCasted = originalCode;
      let cat = "Unknown";
      let mid = this.getModuleId() || -1;
      let moduleData = AppCache.getModuleData(this.getAppId(), mid);
      let typeId = moduleData && parseInt(moduleData.typeId, 10) || 0;
      if (global.categories.hasOwnProperty(typeId)) {
        cat = global.categories[typeId].type;
      }
      if (this.context.met == null || this.context.met < 0)
        this.context.met = new Date().getTime();
      moduleEntry = {
        tid: Number(this.context.txnId),
        mid: mid,
        inf: cat,
        hn: OS.hostname(),
        mtid: this.context.getTypeID() || -1,
        mst: this.context.mst,
        met: this.context.met,
        mrt: this.context.met - this.context.mst,
        ocode: originalCode,
        mcode: typeCasted,
        si: input || "",
        ts: "l1:" + this.context.l1_time + "|l2:" + this.context.l2_time + "|l2s1:" + this.context.l2_s1_time + "|l2s2:" + this.context.l2_s2_time + "|l2s3:" + this.context.l2_s3_time + "|l2s4:" + this.context.l2_s4_time + "|l3:" + this.context.l3_time,
        ext: String(ext),
        vars: JSON.stringify(this.context.vars)
      };
      this._path.push(moduleEntry);
    } finally {
      moduleEntry = null;
    }
  }

  /**
   * Add listeners
   */
  registerEvents() {

    this.setMaxListeners(100);

    this.on("LOG_ENTRY", () => {
      let cdrEnvelope, logEntry;
      try {

        let rt = appResponseTime(this._path);
        let process = this.getProcess();
        let stype = process.subscriberType || "-1";
        let netType = process.subscriberNetType || "-1";
        let plen = this._path.length;

        let scode = -1;
        if (this._im_code != null) {
          scode = this._im_code;
        } else if (this._path[plen - 1] != null) {
          scode = this._path[plen - 1].mcode;
        } else if (this._path[plen] != null) {
          scode = this._path[plen].mcode;
        }
        logEntry = {
          nid: global.nodeid,
          sid: String(this._id),
          oid: String(this._originAppId) || "-1",
          aid: String(this._appId) || "-1",
          uid: String(this._userId),
          im: this._imode || INTERFACES.QUERY_STRING,
          st: this._startTime,
          et: new Date().getTime(),
          rt: rt || (new Date().getTime() - this._startTime),
          ut: stype && String(stype) || "-1",
          nt: netType && String(netType) || "-1",
          ln: this._locale,
          s: scode,
          bn: this.macros.get(SymbolDolar + "PACKNAME") || "null",
          bnPrice: this.macros.get(SymbolDolar + "PRICE") || "null",
          p: this._path
        };

        if (this._imode == INTERFACES.CPS_XML) {
          logEntry.snr = this.macros.get(SymbolDolar + "SENDERADDRESS");
          logEntry.cid = this.macros.get(SymbolDolar + "CAMPAIGNID");
          logEntry.cpid = this.macros.get(SymbolDolar + "CPID");
          logEntry.it = this.macros.get(SymbolDolar + "INTERFACETYPE");
          logEntry.lc = this.macros.get(SymbolDolar + "LOCATION");
          logEntry.mc = this.macros.get(SymbolDolar + "CAMPAIGNCATEGORY");
          logEntry.acid = this.macros.get(SymbolDolar + "ACCOUNTID");
          logEntry.usid = this.macros.get(SymbolDolar + "USERID");
          logEntry.sts = scode;
        }
        cdrEnvelope = {
          "index": {
            "_index": "leap." + toDate(logEntry.st),
            "_id": String(logEntry.sid) + String(logEntry.uid)
          }
        };

        if (global.leap_settings.isElk7) {
          delete cdrEnvelope.index._type;
        }

        if (!global.isDevServer) {
          CDRQUEUE.log2queue(JSON.stringify(cdrEnvelope) + "\n" + JSON.stringify(logEntry));
        } else {
          global.logger.error("LEAPCDR:" + JSON.stringify(logEntry));
        }
      } catch (e) {
        global.logger.error("Failed to process Logentry , path:" + this._path, e);
      } finally {
        cdrEnvelope = null;
        logEntry = null;
      }
    });

    let WebsocketManager = require("../websocket/WebsocketManager");
    this.on(WS.OK, () => {
      if (this._mode == ENVIRONMENT.DEV) {
        const webConsole = WebsocketManager.get(this._invoker);
        if (webConsole && webConsole.socket) {
          webConsole.socket.send(WS.OK + WS_DELIMITER + this._invoker + WS_DELIMITER + this._appId);
        }
      }
    });

    this.on(WS.NOT_OK, () => {
      if (this._mode == ENVIRONMENT.DEV) {
        const webConsole = WebsocketManager.get(this._invoker);
        if (webConsole && webConsole.socket) {
          webConsole.socket.send(WS.NOT_OK + WS_DELIMITER + this._invoker + WS_DELIMITER + this._appId);
        }
      }
    });

    this.on(WS.VARS, () => {
      if (this._mode == ENVIRONMENT.DEV) {
        try {
          const webConsole = WebsocketManager.get(this._invoker);
          if (webConsole && webConsole.socket) {
            try {
              webConsole.socket.send(WS.VARS + WS_DELIMITER + this._invoker + WS_DELIMITER + JSON.stringify(this.getProcess()));
            } catch (e) {
              //ignore
            }
          }
        } catch (e) {
          global.logger.error("Failed to cast the Vars to webconsole", e);
        }
      }
    });

    this.on(WS.CONSOLE, (verbosity, ...message) => {

      switch (verbosity) {
        case VERBOSE.ERROR:
          global.logger.error(this.getConsoleId(), ...message);
          break;
        case VERBOSE.WARN:
          global.logger.warn(this.getConsoleId(), ...message);
          break;
        case VERBOSE.INFO:
          global.logger.info(this.getConsoleId(), ...message);
          break;
        case VERBOSE.DEBUG:
          global.logger.debug(this.getConsoleId(), ...message);
          break;
        case VERBOSE.TRACE:
          global.logger.trace(this.getConsoleId(), ...message);
          break;
        default:
          global.logger.warn(this.getConsoleId(), ...message);
      }

      if (this._mode == ENVIRONMENT.DEV) {
        const webConsole = WebsocketManager.get(this._invoker);
        if (webConsole && webConsole.socket && webConsole.settings && verbosity.code >= webConsole.settings.verbosity.code) {
          webConsole.socket.send(WS.CONSOLE + WS_DELIMITER + verbosity.desc + WS_DELIMITER + message);
        }
      }
    });

    this.on(WS.PROCESSED, (responseCode) => {
      let ocode = responseCode;

      try {
        let endModule = AppCache.getModuleData(this.getAppId(), this.getEndModuleId());
        if (endModule.process.success.code.includes(responseCode)) {
          responseCode = "0";
        }
      } catch (error) {
        //ignore
      }
      this.writePluginCDR(ocode, responseCode, this.context.getSubscriberInput());
      if (this._mode == ENVIRONMENT.DEV) {
        const webConsole = WebsocketManager.get(this._invoker);
        if (webConsole && webConsole.socket) {
          webConsole.socket.send(WS.PROCESSED + WS_DELIMITER + this._invoker + WS_DELIMITER + this._mid);
        }
      }
    });

    this.on(WS.MENU, async (msg, type, timeout) => {
      console.log("ELSON: Timeout in Session file:" + timeout);
      type = type || ENUMS.MSGTYPE.USSD;
      this.setUserResponse({ type, msg });

      if (this._mode == ENVIRONMENT.DEV) {
        const webConsole = WebsocketManager.get(this._invoker);

        if (webConsole && webConsole.socket) {
          try {
            webConsole.socket.send(WS.MENU + WS_DELIMITER + this._invoker + WS_DELIMITER + JSON.stringify(this._userResponse));
          } catch (e) {
            //ignore
          }
        }
      } else {
        await this.sendResponse(FreeFlowStates.continue, "MENU");
        if (timeout) {
          this.save(timeout);
        }
        else {
          await this.save();
        }
      }
    });

    this.on(WS.RESPONSE, async (msg, type) => {
      type = type || ENUMS.MSGTYPE.USSD;
      this.setUserResponse({ type, msg });

      if (this._mode == ENVIRONMENT.DEV) {
        const webConsole = WebsocketManager.get(this._invoker);

        if (webConsole && webConsole.socket) {
          try {
            webConsole.socket.send(WS.RESPONSE + WS_DELIMITER + this._invoker + WS_DELIMITER + JSON.stringify(this._userResponse));
          } catch (e) {
            //ignore
          }
        }
      } else {
        await this.sendResponse(FreeFlowStates.break, "RESPONSE");
        await UserSessionStore.del(this);
      }
    });

    this.on(WS.TERMINATE, async (msg, type) => {
      type = type || ENUMS.MSGTYPE.USSD;
      this.setUserResponse({ type, msg });
      if (this._mode == ENVIRONMENT.DEV) {
        const webConsole = WebsocketManager.get(this._invoker);
        if (webConsole && webConsole.socket) {
          try {
            webConsole.socket.send(WS.TERMINATE + WS_DELIMITER + this._invoker + WS_DELIMITER + JSON.stringify(this._userResponse));
          } catch (e) {
            //ignore
          }
        }
      } else {
        await this.sendResponse(FreeFlowStates.break, "TERMINATE");
        await UserSessionStore.del(this);
      }
    });
  }

  toJSON() {
    return {
      id: this._id,
      waitMid: this._waitMid,
      appId: this._appId,
      originAppId: this._originAppId,
      userId: this._userId,
      from: this._from,
      moduleId: this._mid,
      previousMenuModuleIds: this._previousMenuModuleIds,
      previousMenuAppIds: this._previousMenuAppIds,
      invoker: this._invoker,
      mode: this._mode,
      imode: this._imode,
      context: this.context.toJSON() || {},
      new: this.new,
      dataTable: this.macros.getDataTable() || {},
      startTime: this._startTime,
      endModuleId: this._endModuleId,
      imr: this._imr_flag,
      freeflow: this.freeflow,
      path: this._path,
      optional: this._opts,
      multiAccessCodes: this._multiAccessCodes,
      ussdRequest: this._ussdRequest,
      isNewRequest: this._isNewRequest
    };
  }

  toString() {
    return JSON.stringify(this.toJSON());
  }

  destroy() {
    this.macros.destroy();
    this.macros = null;
    this.context = null;
  }
}

module.exports = Session;


/////////////////////////////////////////
////////////Helper Function//////////////
/////////////////////////////////////////

function toDate(epochDate) {
  let date = new Date(epochDate);
  try {
    return (pad(date.getFullYear(), 4) + "." + pad((date.getMonth() + 1), 2) + "." + pad(date.getDate(), 2));
  } finally {
    date = null;
  }
}

function pad(n, width, z = "0") {
  n = n + "";
  return n.length >= width ? n : new Array(width - n.length + 1).join(z) + n;
}

function appResponseTime(pathArray) {
  let respTime = 0;
  if (pathArray)
    for (let i = 0; i < pathArray.length; i++) {
      respTime += Number(pathArray[i].mrt);
    }
  return respTime;
}
