"use strict";
// ==============================
// CONCRETE STATE
// ==============================

/**
 * @class PrepareContextState
 * @version 1.0.0
 * @since 03 April 2018
 * @description Concrete state which provide the implementation to Prepare the module context data for execution
 *
 * <AUTHOR>
 */

// Import required App engine state managing classes
const ContextState = require("./ContextState");

// Import context helpers
const AppCache = require("../cache/AppCache");
const MenuBuilder = require("../menu/MenuBuilder");

// Import Enums and constants
const ENUMS = require("../lib/enums");
const ENVIRONMENT = ENUMS.ENVIRONMENT, VERBOSE = ENUMS.VERBOSE, EVENTS = ENUMS.EVENTS, WS = ENUMS.WS;
const APPEXEC_ERRORCODES = require("../lib/http_codes");

const PLUGIN = require("../lib/plugin_settings");
const DEFAULT_MENU_OPTS = {
  mainMenu: { code: "#", message: "Home" },
  nextPage: { code: "n", message: "Next Page" },
  firstPage: { code: "f", message: "First Page" },
  backwardSkip: { code: "*", message: "Back" },
  previousPage: { code: "b", message: "Prev Page" },
  lastPage: { code: "l", message: "Last Page" }
};

class PrepareContextState extends ContextState {

  async execute(session) {
    try {
      await session.sendImmediateResponse("200", "OK");
      session.getContext().emit(EVENTS.INIT);
      if (global.logger.isTraceEnabled()) {
        session.emit(WS.CONSOLE, VERBOSE.INFO, "Started Processing, PluginName:" + session.getContext().getPluginName()
          + ", isDevMode " + (session.getMode() == ENVIRONMENT.DEV)
          + ", isNewSession " + session.isNew() + ", AppId:" + session.getAppId());
      }

      let appExists = AppCache.exists(session.getAppId());
      if (appExists) {
        if (global.logger.isTraceEnabled()) {
          session.emit(WS.CONSOLE, VERBOSE.TRACE, "Module Data found");
        }
        let mdata = AppCache.getModuleData(session.getAppId(), session.getModuleId());
        if (mdata == null) {
          session.emit(WS.CONSOLE, VERBOSE.ERROR, "Failed to Prepare execution context for the plugin " + session.getModuleId());
          session.setResponseCode(APPEXEC_ERRORCODES.INTERNAL_ERROR);
          mdata = AppCache.getModuleData(session.getAppId(), session.getEndModuleId());
          if (mdata == null) {
            await session.sendDefaultResponse();
          }
        }
        if (session.isNew()) {
          if (global.logger.isTraceEnabled()) {
            session.emit(WS.CONSOLE, VERBOSE.TRACE, "New session created");
          }
          session.existing();
        } else {
          if (global.logger.isTraceEnabled()) {
            session.emit(WS.CONSOLE, VERBOSE.TRACE, "Session exists, Context is " + session.getModuleId());
          }
          await mergeContext(mdata, session);
        }

        if (session.getContext().getPluginName() == "appMenu" && session.getOpts().lastMenuModuleId != session.getModuleId()) {
          session.getContext().setMenuContext(null);
        }

        if (session.getContext().getPluginName() == "appMenu" && session.getContext().getMenuContext() == null) {
          let menuConfig = { sessionId: session.getSessionId() };
          if (global.logger.isTraceEnabled()) {
            session.emit(WS.CONSOLE, VERBOSE.TRACE, "Creating menu context");
          }
          if (session.getContext().getProcess().menu != null) {
            menuConfig.menuNavigationEnable = mdata.settings.menuNavigationEnable || false;
            menuConfig.menuNavigationOptions = mdata.menuNavigationOptions || DEFAULT_MENU_OPTS;
            menuConfig.language = session.getLocale() || global.config.defaultLanguage;
            menuConfig.contextData = session.getContext().getProcess().menu;
            menuConfig.appId = session.getAppId();
            menuConfig.macros = session.getMacros();
            menuConfig.userId = session.getUserId();
            menuConfig.moduleId = session.getModuleId();
          }
          menuConfig.isMainMenu = (session.getOpts().mainMenuContext == null);
          if (menuConfig.isMainMenu == false) {
            menuConfig.mainMenuOption = session.getOpts().mainMenuContext.navigationOpts && session.getOpts().mainMenuContext.navigationOpts.mainMenu.code;
          }
          if (session.getIsMainAppMenu()) {
            menuConfig.isMainAppMenu = true;
          }
          else {
            menuConfig.isMainAppMenu = false;
          }
          menuConfig.isMultiAccess = session.isMultiAccess();
          session.getContext().setSubscriberInput(null);
          menuConfig.cid = session.getConsoleId();
          menuConfig.mid = session.getModuleId();
          let context = new MenuBuilder(menuConfig);
          await context.loadTemplate();
          session.getContext().setMenuContext(context);
          if (session.getOpts().mainMenuContext == null) {
            session.getOpts().mainMenuContext = context;
            session.getOpts().mainMenuModuleId = session.getModuleId();
          }

          session.getOpts().lastMenuModuleId = session.getModuleId();
        }

        if (global.logger.isInfoEnabled()) {
          session.emit(WS.CONSOLE, VERBOSE.INFO, "Context is ready for execution");
        }
      } else {
        return session.end();
      }
      if (session.getContext().getPluginName() == "appMenu" && session.getContext().getMenuContext() != null) {
        session.getContext().getMenuContext().setMultiAccess(session.isMultiAccess());
        session.getContext().getMenuContext().setMacros(session.getMacros());
      }
      session.emit(WS.VARS);
      session.getContext().emit(EVENTS.READY);
      return session.setCurrentState(session.getStates().execute);
    } catch (e) {
      global.logger.error("Exception in PrepareContextState", e);
      if (session != null) {
        session.emit(WS.CONSOLE, VERBOSE.ERROR, "Failed to Prepare execution context for the plugin " + e.toString());
        session.setResponseCode(APPEXEC_ERRORCODES.INTERNAL_ERROR);
        session.setModuleId(session.getEndModuleId());
        return session.setCurrentState(session.getStates().prepare);
      }
      return null;
    }
  }
}

function resolveObject(session, data) {
  if (data != null && !Array.isArray(data)) {
    Object.keys(data).forEach(key => {
      let item = data[key];
      if (typeof item == "object") {
        Object.keys(item).forEach(skey => {
          item[skey] = session.macros.eval(item[skey]);
        });
      } else {
        item = session.macros.eval(item);
      }
      data[key] = item;
    });
  }
  return data;
}

async function mergeContext(mdata, session) {
  let mid;
  try {
    if (mdata != null) {
      if (global.logger.isInfoEnabled())
        session.emit(WS.CONSOLE, VERBOSE.INFO, "Request accepted, AppId:" + session.getAppId());
      if (global.isDevServer || global.logger.isInfoEnabled())
        session.emit(WS.CONSOLE, VERBOSE.TRACE, "Processing Module:" + session.getModuleId());
      if (global.logger.isInfoEnabled())
        session.emit(WS.CONSOLE, VERBOSE.INFO, "Merge Context for " + session.getModuleId() + ", Previous Module is " + session.getContext().getPluginName());
      session.getContext().setTypeID(mdata.typeId);
      session.getContext().setPluginName(mdata.type);

      let mname = session.getContext().getPluginName();
      mid = session.getModuleId();
      if (global.logger.isInfoEnabled()) {
        session.emit(WS.CONSOLE, VERBOSE.INFO, "Current Module is " + mname);
      }

      let process = {};
      Object.keys(mdata.input).forEach((key) => {
        process[key] = mdata.input[key];
      });

      Object.keys(mdata.process).forEach((key) => {
        process[key] = mdata.process[key];
        if (typeof process[key] == "object") {
          process[key] = resolveObject(session, process[key]);
        }
      });

      session.getContext().emit(EVENTS.LOAD_DATA);
      let settings = PLUGIN.getSettings(session.getUserId(), mdata.typeId, mdata.settings);
      if (global.leap_settings.isDynamicPluginSettingsEnabled) {
        Object.keys(settings).forEach(key => {
          let before = settings[key];
          settings[key] = session.macros.replaceAllPlaceHolders(settings[key]);
          if (global.logger.isTraceEnabled()) {
            global.logger.trace("isDynamicPluginSettingsEnabled::: KEY:" + key + ", BEFORE:" + before + ", AFTER:" + settings[key]);
          }
        });
      }
      if (global.logger.isTraceEnabled()) {
        let logChange = {};
        let logChange1 = Object.assign(logChange, settings);
        if (logChange.password) {
          logChange.password = "xxxxxxxxx"; // NOSONAR : This is just a sample value
        }
        global.logger.trace("Plugin settings for PluginName:" + mdata.type + ", Settings:" + JSON.stringify(logChange));
      }
      session.getContext().setSettings(settings);
      session.getContext().setProcess(process);
      await session.setMacros();
      session.getContext().setOutput(mdata.output);
      session.getContext().setConditionsMap(mdata.conditionsMap);

      if (mdata.type == "sms") {
        let locale = session.getLocale();
        if (locale != null) {
          locale = String(locale);
        }
        if (locale == null || locale.startsWith("$")) {
          locale = global.config.defaultLanguage;
        }
        let template = await AppCache.getAppLocaleTemplate(session.getAppId(), locale);
        let short_message = template.translation && template.translation.sms_titles && template.translation.sms_titles[session.getModuleId()];
        if (global.logger.isInfoEnabled()) {
          session.emit(WS.CONSOLE, VERBOSE.INFO, "Plugin:" + mname + ",Locale:" + locale + ",SMS MsgTemplate:" + short_message);
        }
        if (short_message != null) {
          session.setParam("short_message", short_message);
        }
      }
      session.getContext().emit(EVENTS.CTX_MERGE);
      if (global.logger.isInfoEnabled()) {
        session.emit(WS.CONSOLE, VERBOSE.INFO, "Plugin:" + mname + ",Merged context variables successfully");
      }
    }
  } catch (e) {
    global.logger.error("Failed to Merge module Context MID: " + mid, e);
  }
  return session;
}

module.exports = PrepareContextState;
