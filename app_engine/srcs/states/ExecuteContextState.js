"use strict";
// ==============================
// CONCRETE STATE
// ==============================

/**
 * @class ExecuteContextState
 * @version 1.0.0
 * @since 03 April 2018
 * @description Concrete state which provide the implementation to Execute/Invoke the Plugin using the module context data.
 *
 *
 * <AUTHOR>
 */

// Import required App engine state managing classes
const ContextState = require("./ContextState");

// Import context helpers
const util = require("util");
const PM = require("pluginmanager");
const MenuErrorCodes = require("../menu/MenuErrorCodes");
const NodeType = require("../menu/NodeType");
const MessageUtils = require("../lib/MessageUtils");
const AppCache = require("../cache/AppCache");

// Import Enums and constants
const ENUMS = require("../lib/enums")
const VERBOSE = ENUMS.VERBOSE;
const WS = ENUMS.WS;
const EVENTS = ENUMS.EVENTS;
const MSGTYPE = ENUMS.MSGTYPE;
const INTERFACES = ENUMS.INTERFACES;

const APPEXEC_ERRORCODES = require("../lib/http_codes");
const common = require("common");
const FreeFlowStates = common.FreeFlowStates;
const KPI = require("app_kpi");
const KPI_KEY = KPI.KEYS.plugin_stats;
const AppMacros = require("app_macros");

class ExecuteContextState extends ContextState {

  async execute(session) {
    let mid, pluginRequest, userId, pluginName, pluginResponse = {}, appId;
    try {
      session.getContext().emit(EVENTS.START);
      mid = session.getModuleId();
      appId = session.getAppId();
      userId = session.getUserId();
      if (session.getContext() != null) {
        pluginName = session.getContext().getPluginName();
        switch (pluginName) {
          case "waitforresponse":
            pluginRequest = Object.assign({}, session.getVars());
            if (session.getModuleId() !== session.getWaitModuleId()) {
              session.setWaitModuleId(session.getModuleId());
              session.getContext().emit(EVENTS.MENU, session.getModuleId());
              let message = session.getMacros().replaceAllPlaceHolders(pluginRequest.process.responseBody)
              console.log("ELSON: Timeout in Execution file:" + pluginRequest.process.timeout);
              session.emit(WS.MENU, message, null, pluginRequest.process.timeout);
              if (global.logger.isTraceEnabled()) {
                session.emit(WS.CONSOLE, VERBOSE.TRACE, "Save session");
              }
              session.getContext().emit(EVENTS.SKIP);
              return;
            }
            else {
              session.setWaitModuleId(-1);
              return session.setCurrentState(session.getStates().evaluate);
            }
          case "appMenu": {
            session.getOpts().externalNewRequest = true;
            pluginRequest = session.getContext().getSubscriberInput();
            session.getContext().setSubscriberInput(null);
            if (global.logger.isInfoEnabled()) {
              session.emit(WS.CONSOLE, VERBOSE.INFO, "Existing menu context, User input: " + pluginRequest);
            }
            session.getContext().emit(EVENTS.PRE_PROC);
            pluginResponse = await session.getContext().getMenuContext().execute(pluginRequest);
            session.getContext().emit(EVENTS.EXEC_TIME);
            if (global.logger.isTraceEnabled()) {
              try {
                session.emit(WS.CONSOLE, VERBOSE.TRACE, "AppMenu Response " + JSON.stringify(pluginResponse));
              } catch (e) {
                //ignore
              }
            }
            session.writePluginCDR(pluginResponse.code, pluginResponse.code, pluginRequest, pluginResponse.ext);

            switch (pluginResponse.code) {
              case MenuErrorCodes.FAILURE:
              case MenuErrorCodes.STATIC: {
                session.getContext().emit(EVENTS.MENU, session.getModuleId());
                session.emit(WS.TERMINATE, pluginResponse.message);
                session.getContext().emit(EVENTS.SKIP);
                return session.end();
              }
              case MenuErrorCodes.EXIT: {
                session.setResponseCode(pluginResponse.message || MenuErrorCodes.EXIT);
                session.setModuleId(session.getEndModuleId());
                session.getContext().emit(EVENTS.SKIP);
                return session.setCurrentState(session.getStates().prepare);
              }
              case MenuErrorCodes.CLEAN_SESSION: {
                session.setResponseCode(MenuErrorCodes.CLEAN_SESSION);
                session.getContext().emit(EVENTS.POST_PROC);
                return session.setCurrentState(session.getStates().evaluate);
              }
              case MenuErrorCodes.NOCHILDREN: {
                try {
                  switch (pluginResponse.type) {
                    case NodeType.DYNAMIC: {
                      session.emit(WS.RESPONSE, pluginResponse.message);
                      return session.end();
                    }
                    case NodeType.QUESTIONNAIRE: {
                      if (pluginResponse.isMainMenuOption) {
                        session.flushPreviousMenuTrace();
                        let startId = AppCache.getStartModuleID(session.getOriginAppId());
                        let endId = AppCache.getEndModuleID(session.getOriginAppId());
                        session.setAppId(session.getOriginAppId());
                        session.setModuleId(startId);
                        session.setEndModuleId(endId);
                        session.getContext().emit(EVENTS.SKIP);
                        return session.setCurrentState(session.getStates().prepare);
                      }
                      if (pluginResponse.isPreviousMenuOption) {
                        let previousMenuModuleId = session.getPreviousMenuModuleId();
                        let previousMenuAppId = session.getPreviousMenuAppId();
                        if (typeof previousMenuModuleId !== 'undefined' && typeof previousMenuAppId !== 'undefined') {
                          let startId = previousMenuModuleId;
                          let endId = AppCache.getEndModuleID(previousMenuAppId);
                          session.setAppId(previousMenuAppId);
                          session.setModuleId(startId);
                          session.setEndModuleId(endId);
                          session.getContext().emit(EVENTS.SKIP);
                          return session.setCurrentState(session.getStates().prepare);
                        }
                        else {
                          let startId = AppCache.getStartModuleID(session.getOriginAppId());
                          let endId = AppCache.getEndModuleID(session.getOriginAppId());
                          session.setAppId(session.getOriginAppId());
                          session.setModuleId(startId);
                          session.setEndModuleId(endId);
                          session.getContext().emit(EVENTS.SKIP);
                          return session.setCurrentState(session.getStates().prepare);
                        }
                      }
                      if (pluginResponse.contentType == "url") {
                        session.getContext().emit(EVENTS.MENU, session.getModuleId());
                        session.emit(WS.TERMINATE, pluginResponse.message);
                        session.getContext().emit(EVENTS.SKIP);
                        return session.end();
                      } else {
                        pluginResponse.properties && pluginResponse.properties.forEach(item => {
                          if (global.logger.isTraceEnabled()) {
                            session.emit(WS.CONSOLE, VERBOSE.TRACE, "QuestionTag " + item.question + " answer " + item.answer);
                          }
                          if (item.question && item.question.length > 0)
                            session.getMacros().set(item.question, item.answer);
                        });
                      }
                    }
                      break;
                    case NodeType.CHOICE:
                      if (pluginResponse.isMainMenuOption) {
                        session.flushPreviousMenuTrace();
                        let startId = AppCache.getStartModuleID(session.getOriginAppId());
                        let endId = AppCache.getEndModuleID(session.getOriginAppId());
                        session.setAppId(session.getOriginAppId());
                        session.setModuleId(startId);
                        session.setEndModuleId(endId);
                        session.getContext().emit(EVENTS.SKIP);
                        return session.setCurrentState(session.getStates().prepare);
                      }
                      if (pluginResponse.isPreviousMenuOption) {
                        let previousMenuModuleId = session.getPreviousMenuModuleId();
                        let previousMenuAppId = session.getPreviousMenuAppId();
                        if (typeof previousMenuModuleId !== 'undefined' && typeof previousMenuAppId !== 'undefined') {
                          let startId = previousMenuModuleId;
                          let endId = AppCache.getEndModuleID(previousMenuAppId);
                          session.setAppId(previousMenuAppId);
                          session.setModuleId(startId);
                          session.setEndModuleId(endId);
                          session.getContext().emit(EVENTS.SKIP);
                          return session.setCurrentState(session.getStates().prepare);
                        }
                        else {
                          let startId = AppCache.getStartModuleID(session.getOriginAppId());
                          let endId = AppCache.getEndModuleID(session.getOriginAppId());
                          session.setAppId(session.getOriginAppId());
                          session.setModuleId(startId);
                          session.setEndModuleId(endId);
                          session.getContext().emit(EVENTS.SKIP);
                          return session.setCurrentState(session.getStates().prepare);
                        }
                      }
                    case NodeType.CUSTOM:
                    case NodeType.EXIT:
                      let props = pluginResponse.properties;
                      Object.keys(props)
                        .forEach(index => {
                          if (props[index].key.length > 0) {
                            if (global.logger.isTraceEnabled()) {
                              session.emit(WS.CONSOLE, VERBOSE.TRACE, "MenuProperty " + props[index].key + " value " + props[index].value);
                            }
                            session.getMacros().set(props[index].key, props[index].value);
                          }
                        });
                      break;
                  }
                } catch (e) {
                  global.logger.error("Failed to post process menu response", e);
                  session.emit(WS.CONSOLE, VERBOSE.ERROR, e.toString());
                }
                session.setResponseCode(MenuErrorCodes.SUCCESS);
                session.getContext().emit(EVENTS.POST_PROC);
                return session.setCurrentState(session.getStates().evaluate);
              }
              case MenuErrorCodes.SUCCESS:
              case MenuErrorCodes.CONTINUE: {
                session.getContext().emit(EVENTS.MENU, session.getModuleId());
                session.emit(WS.MENU, pluginResponse.message);
                if (global.logger.isTraceEnabled()) {
                  session.emit(WS.CONSOLE, VERBOSE.TRACE, "Save session");
                }
                session.getContext().emit(EVENTS.SKIP);
                return;
              }
            }
          }
            break;
          case "xmlPullInterface": {
            pluginRequest = Object.assign({}, session.getVars());
            pluginRequest.pluginName = pluginName;
            pluginRequest.appId = appId;
            pluginRequest.mid = mid;
            delete pluginRequest.process.finalsearchresult;
            delete pluginRequest.process.response;
            if (global.logger.isInfoEnabled()) {
              try {
                let object1 = {};
                let object2 = Object.assign(object1, pluginRequest.settings);
                if (object1.password) {
                  object1.password = "xxxxxxxxx"; // NOSONAR : This is just a sample value
                }
                session.emit(WS.CONSOLE, VERBOSE.INFO, "Executing " + pluginName + " Plugin Settings " + JSON.stringify(object1));
                session.emit(WS.CONSOLE, VERBOSE.INFO, "Executing " + pluginName + " Plugin Request " + JSON.stringify(pluginRequest.process));
              } catch (e) {
                //ignore
              }
              session.emit(WS.CONSOLE, VERBOSE.TRACE, "Plugin state isExternalNewRequest:" + session.getOpts().externalNewRequest);
            }
            if (session.getOpts().externalNewRequest === false) {
              pluginRequest.process.subsInput = session.getContext().getSubscriberInput();
              pluginRequest.process.newRequest = 0;
            } else {
              pluginRequest.process.newRequest = 1;
              pluginRequest.process.sessionId = new Date().getTime();
              session.macros.set("$sessionid", pluginRequest.process.sessionId);
              session.macros.set("$cisSessionID", pluginRequest.process.sessionId);
            }

            session.getContext().emit(EVENTS.PRE_PROC);
            pluginResponse = await PM.execute(pluginRequest);
            session.getContext().emit(EVENTS.EXEC_TIME);
            session.getOpts().externalNewRequest = false;
            session.getOpts().leapControlMenu = true;

            if (global.logger.isInfoEnabled()) {
              try {
                session.emit(WS.CONSOLE, VERBOSE.INFO, "Plugin Response of " + pluginRequest.pluginName + ", Response " + JSON.stringify(pluginResponse));
              } catch (e) {
                //ignore
              }
            }

            session.writePluginCDR(pluginResponse.code, pluginResponse.code, pluginRequest.process.subsInput);

            if (pluginResponse.code == 0) {
              let xmlResponse = pluginResponse.msg;
              let fstate = xmlResponse.freeflow && xmlResponse.freeflow.freeflowState || FreeFlowStates.break;
              let menuId = (session.getOpts().mainMenuModuleId != null);
              if (menuId && xmlResponse.applicationResponse === session.getContext().getSettings().AMCMain) {
                session.setModuleId(session.getOpts().mainMenuModuleId);
                session.getOpts().externalNewRequest = true;
                session.getContext().emit(EVENTS.SKIP);
                session.getContext().setMenuContext(null);
                return session.setCurrentState(session.getStates().prepare);
              }
              else if (menuId && xmlResponse.applicationResponse === session.getContext().getSettings().AMCBack) {
                session.setModuleId(session.getOpts().lastMenuModuleId);
                session.getContext().setPluginName("appMenu");
                if (session.getContext().getMenuContext() != null) {
                  let context = findParentNode(session.getContext().getMenuContext().rootContext, session.getContext().getMenuContext().context.getId());
                  if (context != null) {
                    session.getContext().getMenuContext().context = context;
                  }
                }
                session.getContext().setSubscriberInput("f");
                session.getContext().emit(EVENTS.SKIP);
                return session.setCurrentState(session.getStates().execute);
              }
              else if (menuId && xmlResponse.applicationResponse === session.getContext().getSettings().AMCContinue) {
                session.getContext().emit(EVENTS.POST_PROC);
                return session.setCurrentState(session.getStates().evaluate);
              }
              else if (fstate == FreeFlowStates.break) {
                session.emit(WS.RESPONSE, xmlResponse.applicationResponse);
                session.setResponseCode(MenuErrorCodes.SUCCESS);
                session.getContext().emit(EVENTS.SKIP);
                return session.end();
              } else if (fstate == FreeFlowStates.continue) {
                session.getContext().emit(EVENTS.MENU, session.getModuleId());
                session.emit(WS.MENU, xmlResponse.applicationResponse);
                session.getOpts().leapControlMenu = false;
              }
              if (global.logger.isTraceEnabled()) {
                session.emit(WS.CONSOLE, VERBOSE.TRACE, "Save session");
              }
              session.getContext().emit(EVENTS.SKIP);
              await session.save();
              return;
            } else {
              session.emit(WS.TERMINATE, "System is busy. Please try again later");
              session.getContext().emit(EVENTS.SKIP);
              return session.end();
            }
          }
          case "appRedirect": {
            pluginRequest = Object.assign({}, session.getVars());
            let startId = AppCache.getStartModuleID(pluginRequest.process.appId);
            let endId = AppCache.getEndModuleID(pluginRequest.process.appId);
            session.getContext().emit(EVENTS.PRE_PROC);
            if (pluginRequest.process.getcallback == "Yes") {
              session.getContext().emit(EVENTS.EXEC_TIME);
              const newSession = await (require("../lib/SessionManager")).getExistingOrCreateNew(pluginRequest.process.appId, session.getUserId(), {
                isNewRequest: 1,
                language: session.getLocale(),
                imode: INTERFACES.CALLBACK,
                txnId: session.getSessionId()
              });
              newSession.macros = new AppMacros.ExpressionEngine({ dataTable: session.macros.getDataTable() });
              newSession.setOriginAppId(appId);
              newSession.setCallback(response => {
                session.setParam("response.msg", response.msg);
                session.setResponseCode(response.code);
                KPI.emit(KPI_KEY, { aid: appId, mid: mid, mname: "appRedirect", code: response.code });
                session.getContext().emit(EVENTS.POST_PROC);
                return session.setCurrentState(session.getStates().evaluate);
              });
              return newSession.start();
            } else {
              session.getContext().emit(EVENTS.EXEC_TIME);
              session.setAppId(pluginRequest.process.appId);
              session.setModuleId(startId);
              session.setEndModuleId(endId);
              session.getContext().emit(EVENTS.SKIP);
              return session.setCurrentState(session.getStates().prepare);
            }
          }
          case "appEnd": {
            let type = session.getMsgType() || MSGTYPE.USSD;
            if (global.logger.isTraceEnabled()) {
              session.emit(WS.CONSOLE, VERBOSE.TRACE, "Prepare response message");
            }
            session.getContext().emit(EVENTS.PRE_PROC);
            let message = await MessageUtils.getMessage(session);

            if (global.logger.isTraceEnabled()) {
              session.emit(WS.CONSOLE, VERBOSE.TRACE, "Message: " + message);
              session.emit(WS.CONSOLE, VERBOSE.TRACE, "Macros Datatable " + JSON.stringify(session.getMacros().getDataTable()));
            }
            message = session.getMacros().replaceAllPlaceHolders(message);
            session.getContext().emit(EVENTS.EXEC_TIME);
            session.getContext().emit(EVENTS.POST_PROC);
            if (global.logger.isInfoEnabled()) {
              session.emit(WS.CONSOLE, VERBOSE.INFO, "Final Response message: " + message);
            }
            session.getContext().emit(EVENTS.PROCESSED);
            session.getContext().emit(EVENTS.END, session.getResponseCode());
            session.emit(WS.RESPONSE, message, type);
          }
            break;
          default: {
            pluginRequest = Object.assign({}, session.getVars());
            pluginRequest.pluginName = pluginName;
            pluginRequest.appId = appId;
            pluginRequest.mid = mid;
            pluginRequest.userId = userId;
            if (typeof pluginRequest.process["qs"] === "object") {
              let queryParams = {};
              let keys = Object.keys(pluginRequest.process["qs"]);
              for (let i = 0; i < keys.length; i++) {
                let key = keys[i];
                if (key.trim().length > 0) {
                  let obj = pluginRequest.process["qs"][key];
                  let val = session.getMacros().eval(obj.value);
                  queryParams[key] = { key: obj.key, value: val };
                }
              }
              pluginRequest.process["qs"] = queryParams;
            }
            if (typeof pluginRequest.process["headers"] === "object") {
              let queryParams = [];
              let headers = pluginRequest.process["headers"];
              //let keys = Object.keys(pluginRequest.process["qs"]);
              for (let i = 0; i < headers.length; i++) {
                let header = headers[i];
                let headerKey = header.headerKey;
                let headerValue = header.headerValue;
                if (headerKey.trim().length > 0) {
                  let newHeaderKey = session.getMacros().eval(headerKey);
                  let newHeaderValue = session.getMacros().eval(headerValue);
                  queryParams[i] = { headerKey: newHeaderKey, headerValue: newHeaderValue };
                }
              }
              pluginRequest.process["headers"] = queryParams;
            }
            delete pluginRequest.process.finalsearchresult;
            delete pluginRequest.process.response;

            switch (pluginName) {
              case "codeModule":
                pluginRequest.process.dataTable = session.getProcess();
                break;
            }

            if (global.logger.isInfoEnabled()) {
              try {
                let infologChange = {};
                let infologChange1 = Object.assign(infologChange, pluginRequest.settings);
                if (infologChange.password) {
                  infologChange.password = "xxxxxxxxx"; // NOSONAR : This is just a sample value
                }
                session.emit(WS.CONSOLE, VERBOSE.INFO, "Executing " + pluginName + " Plugin Settings " + JSON.stringify(infologChange));
                session.emit(WS.CONSOLE, VERBOSE.INFO, "Executing " + pluginName + " Plugin Request " + JSON.stringify(pluginRequest.process));
              } catch (e) {
                //ignore
              }
            }

            session.getContext().emit(EVENTS.PRE_PROC);
            session.emit(WS.CONSOLE, VERBOSE.INFO, "Orginal Plugin Request DEBUG:" + JSON.stringify(pluginRequest.query));
            pluginRequest.settings["authorization"] = session.getMacros().get('authorization');
            pluginResponse = await PM.execute(pluginRequest);
            session.getContext().emit(EVENTS.EXEC_TIME);
            if (global.logger.isInfoEnabled()) {
              try {
                session.emit(WS.CONSOLE, VERBOSE.INFO, "Plugin Response of " + pluginRequest.pluginName + ", Response " + JSON.stringify(pluginResponse));
              } catch (e) {
                //ignore
              }
            }
            switch (pluginName) {
              case "appStart":
                if (pluginResponse.code == 898) {
                  session.setResponseCode(APPEXEC_ERRORCODES.SYSTEM_BUSY_ERROR);
                  session.setModuleId(session.getEndModuleId());
                  KPI.emit(KPI_KEY, { aid: appId, mid: mid, mname: pluginName, code: APPEXEC_ERRORCODES.SYSTEM_BUSY_ERROR });
                  return session.setCurrentState(session.getStates().prepare);
                }
                else if (session.getContext().getSettings().immediateResponseFlag) {
                  try {
                    session.setResponseCode("0");
                    let immResponse = await MessageUtils.getMessage(session);
                    session.sendImmediateResponse(immResponse);
                  } catch (e) {
                    global.logger.error("Failed to prepare immediateResponseFlag", e);
                  }
                }
                break;
            }

            if (pluginResponse && Object.keys(pluginResponse).length > 0) {
              let pluginResponseCode;
              if (global.logger.isInfoEnabled()) {
                session.emit(WS.CONSOLE, VERBOSE.INFO, "Plugin Execution succeeded");
              }
              if (typeof pluginResponse.resultsets === "object") {
                session.setParam("resultsets", pluginResponse.resultsets);
              }
              if (typeof pluginResponse.msg === "object") {
                session.setParam("response", pluginResponse.msg);
                pluginResponseCode = (pluginResponse.msg.responseCode != null) ? pluginResponse.msg.responseCode : pluginResponse.code;
              } else {
                pluginResponseCode = pluginResponse.code;
                if (pluginResponse.msg != null) {
                  session.setParam("response.msg", pluginResponse.msg);
                }
              }
              KPI.emit(KPI_KEY, { aid: appId, mid: mid, mname: pluginName, code: pluginResponseCode });
              session.setResponseCode(pluginResponseCode);
              session.getContext().emit(EVENTS.POST_PROC);
              return session.setCurrentState(session.getStates().evaluate);
            } else {
              session.setResponseCode(APPEXEC_ERRORCODES.SESSION_FAULT_ERROR);
              session.setModuleId(session.getEndModuleId());
              KPI.emit(KPI_KEY, { aid: appId, mid: mid, mname: pluginName, code: APPEXEC_ERRORCODES.SESSION_FAULT_ERROR });
              session.getContext().emit(EVENTS.SKIP);
              return session.setCurrentState(session.getStates().prepare);
            }
          }
        }
      }
    } catch (e) {
      global.pecounter--;
      global.logger.error("Failed to Execute the plugin MID: " + mid, e);
      if (session && util.isString(e.toString())) {
        session.emit(WS.CONSOLE, VERBOSE.ERROR, "Failed to Execute the plugin " + e.toString());
      }
      session.setResponseCode(APPEXEC_ERRORCODES.INTERNAL_ERROR);
      session.setModuleId(session.getEndModuleId());
      KPI.emit(KPI_KEY, { aid: appId, mid: mid, mname: pluginName, code: APPEXEC_ERRORCODES.INTERNAL_ERROR });
      return session.setCurrentState(session.getStates().prepare);
    } finally {
      pluginName = null;
      pluginRequest = null;
      pluginResponse = null;
    }
    return session.end();
  }
}

module.exports = ExecuteContextState;

function findParentNode(node, id) {
  if (node.getId() == id) {
    return node.getParentNode();
  }

  for (let i = 0; i < node.getCount(); i++) {
    let child = node.getChild(i);
    if (child.getType() != "TITLE") {

      let result = findParentNode(child, id);
      if (result != null) return result;
    }
  }
  return null;
}
