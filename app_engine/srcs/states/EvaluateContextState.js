"use strict";
// ==============================
// CONCRETE STATE
// ==============================

/**
 * @class EvaluateContextState
 * @version 1.0.0
 * @since 03 April 2018
 * @description Concrete state which provide the implementation to Resolve the Plugin response and identify the next module.
 *
 *
 * <AUTHOR>
 */

// Import required App engine state managing classes
const ContextState = require("./ContextState");

// Import context helpers
const util = require("util");
const AppCache = require("../cache/AppCache");
const MessageUtils = require("../lib/MessageUtils");

// Import Enums and constants
const ENUMS = require("../lib/enums");
const WS = ENUMS.WS, VERBOSE = ENUMS.VERBOSE, EVENTS = ENUMS.EVENTS;

const APPEXEC_ERRORCODES = require("../lib/http_codes");

class EvaluateContextState extends ContextState {

  async execute(session) {
    let moduleId = null;
    try {
      if (session.getContext().getPluginName() == 'appMenu') {
        session.setPreviousMenuModuleId(session.getModuleId());
        session.setPreviousMenuAppId(session.getAppId());
      }
      let ocode = String(session.getResponseCode());
      session.getContext().emit(EVENTS.PROCESSED);
      if (session.getContext().getOutputMode() == false) {
        let conditions = session.getContext().getOutputConditions();
        let map = session.getContext().getConditionsMap();
        if (global.logger.isInfoEnabled()) {
          try {
            session.emit(WS.CONSOLE, VERBOSE.INFO, "Eval Conditions " + JSON.stringify(conditions));
          } catch (e) {
            //ignore
          }
        }
        for (let i = 0; i < map.length; i++) {
          moduleId = map[i];
          let originalModuleId = moduleId;
          if (moduleId.includes("_")) {
            moduleId = moduleId.split("_")[0];
          }
          let condition = conditions[originalModuleId].statement || conditions[originalModuleId];
          if (conditions[originalModuleId].isActive == false || condition.length === 0 || (condition.expr
            && condition.expr.length == 3
            && condition.expr[0].length === 0
            && condition.expr[1].length === 0
            && condition.expr[2].length === 0)) {
            if (global.logger.isTraceEnabled()) {
              session.emit(WS.CONSOLE, VERBOSE.TRACE, "Condition " + JSON.stringify(condition) + " is inactive");
            }
            moduleId = null;
            continue;
          }
          if (global.logger.isInfoEnabled()) {
            try {
              session.emit(WS.CONSOLE, VERBOSE.INFO, "Condition " + JSON.stringify(condition));
            } catch (e) {
              //ignore
            }
          }

          let result = false;
          if (global.logger.isTraceEnabled()) {
            try {
              session.emit(WS.CONSOLE, VERBOSE.TRACE, "Eval Condition " + JSON.stringify(condition));
            } catch (e) {
              //ignore
            }
          }
          try {
            result = session.getMacros().evalCondition(condition);
          } catch (e) {
            result = false;
          }
          session.emit(WS.CONSOLE, VERBOSE.INFO, "Condition " + JSON.stringify(condition) + " result: " + result);
          if (result) {
            if (conditions[originalModuleId].fallbackcode != null) {
              if (global.logger.isTraceEnabled()) {
                session.emit(WS.CONSOLE, VERBOSE.TRACE, "fallbackcode: " + conditions[originalModuleId].fallbackcode);
              }
              session.setResponseCode(conditions[originalModuleId].fallbackcode);
            }
            break;
          } else {
            moduleId = null;
          }
        }
        if (moduleId == null && session.getContext().getPluginName() != "appEnd") {
          let fallbackcode = session.getVars().output.fallbackcode;
          if (fallbackcode != null) {
            session.setResponseCode(fallbackcode);
          } else {
            session.setResponseCode(APPEXEC_ERRORCODES.SESSION_FAULT_ERROR);
          }
          moduleId = session.getEndModuleId();
        }
      } else {
        try {
          let app = AppCache.getApp(session.getAppId());
          let sanbox = {};
          Object.keys(app.config).forEach(key => {
            if (session.getContext().getOutputScripts().includes(key)) {
              sanbox[key] = app.config[key];
            }
          });
          let result = session.getMacros().evalScript(session, sanbox, WS.CONSOLE, VERBOSE);
          if (global.logger.isTraceEnabled()) {
            session.emit(WS.CONSOLE, VERBOSE.TRACE, "Script evaluation output: " + JSON.stringify(result));
          }
          session.setResponseCode(result.responseCode);
          if (typeof result.nextModuleId == "object") {
            moduleId = await result.nextModuleId;
          } else {
            moduleId = result.nextModuleId;
          }
          if (result.multiAccessCodes != null && Array.isArray(result.multiAccessCodes)) {
            session.setMultiAccessCodes(result.multiAccessCodes);
          }
          if (result.headers != null) {
            Object.keys(result.headers).forEach(key => {
              session.setResponseHeader(key, result.headers[key]);
            });
          }
          if (result.vars != null) {
            Object.keys(result.vars).forEach(key => {
              if (global.logger.isTraceEnabled()) {
                session.emit(WS.CONSOLE, VERBOSE.TRACE, "Exporting variable key: " + key + ", value: " + JSON.stringify(result.vars[key]));
              }
              session.getMacros().set(key, result.vars[key]);
            });
          }
          if (result.immediateResponse == true) {
            try {
              let immResponse = await MessageUtils.getMessage(session);
              await session.sendImmediateResponse(result.responseCode, immResponse);
            } catch (e) {
              global.logger.error("Failed to prepare immediateResponseFlag", e);
            }
          }
        } catch (e) {
          global.logger.error(e);
          session.emit(WS.CONSOLE, VERBOSE.ERROR, e.toString());
        }
        if (moduleId == null && session.getContext().getPluginName() != "appEnd") {
          moduleId = session.getEndModuleId();
        }
      }

      let responseCode = String(session.getResponseCode());
      //let ocode = responseCode;

      try {
        let endModule = AppCache.getModuleData(session.getAppId(), session.getEndModuleId());
        if (endModule.process.success.code.includes(responseCode)) {
          responseCode = "0";
        }
      } catch (error) {
        //ignore
      }
      session.writePluginCDR(ocode, responseCode, session.context.getSubscriberInput());

      session.getContext().emit(EVENTS.END, ocode);
      session.emit(WS.PROCESSED, ocode);
      session.emit(WS.CONSOLE, VERBOSE.INFO, "Next Module Id: " + moduleId);
      session.setModuleId(moduleId);

      if (session.isPBM()) {
        if (global.logger.isTraceEnabled()) {
          session.emit(WS.CONSOLE, VERBOSE.TRACE, "Pause between module is enabled, Save session");
        }
        await session.save();
        return;
      }
      return session.setCurrentState(session.getStates().prepare);
    } catch (e) {
      global.logger.error("Failed to Eval the plugin ", e);
      if (session && util.isString(e.toString())) {
        session.emit(WS.CONSOLE, VERBOSE.ERROR, "Failed to Execute the plugin " + e.toString());
      }
      session.setResponseCode(APPEXEC_ERRORCODES.INTERNAL_ERROR);
      session.setModuleId(session.getEndModuleId());
      return session.setCurrentState(session.getStates().prepare);
    }
  }
}

module.exports = EvaluateContextState;
