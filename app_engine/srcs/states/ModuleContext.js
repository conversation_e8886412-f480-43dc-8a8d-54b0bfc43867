"use strict";
const EventEmitter = require("events");

const utility = require("utility");
const ENUMS = require("../lib/enums");
const MenuBuilder = require("../menu/MenuBuilder");
const KPI = require("app_kpi");
const KPI_KEY = KPI.KEYS.module;
const EVENTS = ENUMS.EVENTS;

class ModuleContext extends EventEmitter {

  constructor(opts) {
    super();
    try {
      this.cid = "SID:" + opts.sessionId + "|UID:" + opts.userId + "|AID:" + opts.appId + "|MID:" + opts.mid + "|";

      this.l1_time = 0;
      this.l1_s1_time = 0;
      this.l1_s2_time = 0;
      this.l2_time = 0;
      this.l2_s1_time = 0;
      this.l2_s2_time = 0;
      this.l2_s3_time = 0;
      this.l2_s4_time = 0;
      this.l3_time = 0;

      if (typeof opts == "string") {
        opts = JSON.parse(opts);
      }
      this.sessionId = opts.sessionId;
      this.userId = opts.userId || "";
      this.appId = opts.appId || "";
      this.moduleId = opts.moduleId || -1;
      this.mid = opts.mid || -1;
      this.typeId = opts.typeId || -1;
      this.mname = opts.mname || "appStart";
      this.menuContext = null;
      this.input = null;
      if (opts.menuContext != null) {
        if (opts.menuNavigationOptions) {
          opts.menuContext.menuNavigationOptions = opts.menuNavigationOptions;
        }
        opts.menuContext.sessionId = this.sessionId;
        opts.menuContext.macros = opts.macros;
        opts.menuContext.cid = this.cid;
        opts.menuContext.appId = this.appId;
        opts.menuContext.mid = this.mid;
        opts.menuContext.userId = this.userId;
        opts.menuContext.moduleId = this.moduleId;
        this.menuContext = new MenuBuilder(opts.menuContext);
      }
      if (opts.vars != null) {
        if (typeof opts.vars == "string") {
          this.vars = JSON.parse(opts.vars);
        } else {
          this.vars = opts.vars;
        }
      } else {
        this.vars = { settings: {}, query: {}, input: {}, process: {} };
      }
      if (opts.settings != null) { this.vars.settings = opts.settings; }
      if (opts.query != null) { this.vars.query = opts.query; }
      if (opts.input != null) { this.vars.input = opts.input; }
      if (opts.process != null) { this.vars.process = opts.process; }
      if (opts.output != null) { this.vars.output = opts.output; }
      if (opts.conditionsMap != null) { this.vars.conditionsMap = opts.conditionsMap; }
      this.registerEvents();
    } catch (e) {
      global.logger.error(this.cid + "Failed to instantiate Context", e);
    }
  }

  getState() {
    return {
      id: this.id,
      typeId: this.typeId
    };
  }
  getTypeID() { return this.typeId; }
  getPluginName() { return this.mname; }
  getVars() { return this.vars; }
  getSettings() { return this.vars.settings; }
  getQueryParams() { return this.vars.query; }
  getInput() { return this.vars.input; }
  getProcess() { return this.vars.process; }
  getOutputMode() {
    if (this.vars.output != null) {
      return this.vars.output.codeActive == true;
    }
    return false;
  }
  getFallBackCode() { return this.vars.output && this.vars.output.fallbackcode; }
  getConditionsMap() { return this.vars.conditionsMap || []; }
  getOutputConditions() { return this.vars.output && this.vars.output.conditions; }
  getOutputScripts() { return this.vars.output && this.vars.output.customCode; }
  getSubsInput() { return this.input; }
  getSubscriberInput() { return this.vars.subscriberInput; }
  getMenuContext() { return this.menuContext; }
  getProcessParam(key) { return this.vars.process[key]; }

  setTypeID(typeId) { this.typeId = Number(typeId); }
  setPluginName(mname) { this.mname = mname; }
  setVars(vars) {
    if (vars != null) {
      if (vars.settings != null ||
        vars.input != null ||
        vars.process != null) {
        this.vars = vars;
      } else {
        throw Error("Invalid Vars");
      }
    }
  }
  setSettings(settings) { if (settings != null) { this.vars.settings = settings; } }
  setQueryParams(query) { if (query != null) { this.vars.query = query; } }
  setInput(input) { if (input != null) { this.vars.input = input; } }
  setProcess(process) { if (process != null) { this.vars.process = process; } }
  setOutput(output) { if (output != null) { this.vars.output = output; } }
  setConditionsMap(conditionsMap) { if (conditionsMap != null) { this.vars.conditionsMap = conditionsMap; } }
  setSubsInput(input) {
    this.input = input;
    this.setSubscriberInput(input);
  }
  setSubscriberInput(subscriberInput) { this.vars.subscriberInput = subscriberInput; }
  setMenuContext(menuContext) { this.menuContext = menuContext; }

  registerEvents() {

    this.setMaxListeners(100);

    this.on(EVENTS.INIT, () => {
      this.mst = new Date().getTime();
      this.txnId = utility.getUniqueTxnId();
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "Started Initializing the module context preparation for " + this.mname + " module");
      }
    });

    this.on(EVENTS.LOAD_DATA, () => {
      this.l1_s1_time = new Date().getTime();
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "Loaded the module data from AppCache for " + this.mname + " module");
      }
    });

    this.on(EVENTS.CTX_MERGE, () => {
      this.l1_s2_time = new Date().getTime();
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "Successfully merged the module context for " + this.mname + " module");
      }
    });

    this.on(EVENTS.READY, () => {
      let now = new Date().getTime();
      this.l1_time = now - this.mst;
      this.l1_s1_time = this.l1_s1_time - this.mst;
      this.l1_s2_time = this.l1_s2_time - this.mst - this.l1_s1_time;

      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "Module context is ready for execution of " + this.mname + " module");
      }
    });

    this.on(EVENTS.START, () => {
      this.l2_time = new Date().getTime();
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "AppExecution-Started the execution process for " + this.mname + " module");
      }
      this.l2_s1_time = new Date().getTime();
    });

    this.on(EVENTS.PRE_PROC, () => {
      global.pecounter++;
      this.l2_s2_time = new Date().getTime();
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "AppExecution-Request is Ready to execute " + this.mname + " module");
      }
    });

    this.on(EVENTS.EXEC_TIME, (checkCounter = true) => {
      this.met = new Date().getTime();
      if (checkCounter)
        global.pecounter--;
      this.l2_s3_time = new Date().getTime();
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "AppExecution-Got Response for the " + this.mname + " module");
      }
    });

    this.on(EVENTS.POST_PROC, () => {
      this.l2_s4_time = new Date().getTime();
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "AppExecution-Post processing completed for response " + this.mname + " module");
      }
    });

    this.on(EVENTS.MENU, (menu) => {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "AppExecution-Received menu from " + menu + " module " + this.mname);
      }
      this.l3_time = new Date().getTime() - this.l3_time;
      KPI.emit(KPI_KEY, { aid: this.appId, mid: this.typeId, mname: this.mname, met: this.met, mst: this.mst, mcode: 0, l1: this.l1_time, l2: this.l2_time, l2s1: this.l2_s1_time, l2s2: this.l2_s2_time, l2s3: this.l2_s3_time, l2s4: this.l2_s4_time, l3: this.l3_time });
    });

    this.on(EVENTS.PROCESSED, () => {
      this.l3_time = new Date().getTime();
      this.l2_s4_time = this.l3_time - this.l2_s4_time;
      this.l2_s3_time = this.l3_time - this.l2_s4_time - this.l2_s3_time;
      this.l2_s2_time = this.l3_time - this.l2_s4_time - this.l2_s3_time - this.l2_s2_time;
      this.l2_s1_time = this.l3_time - this.l2_s4_time - this.l2_s3_time - this.l2_s2_time - this.l2_s1_time;

      this.l2_time = this.l3_time - this.l2_time;
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "AppExecution completed the Request Processing " + this.mname + " module ");
      }
    });

    this.on(EVENTS.SKIP, () => {
      this.emit(EVENTS.EXEC_TIME, false);
      this.emit(EVENTS.POST_PROC);
      this.emit(EVENTS.PROCESSED);
      this.emit(EVENTS.END, 0);
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "Skip " + this.mname + " module ");
      }
    });

    this.on(EVENTS.END, (responseCode) => {
      this.met = new Date().getTime();
      this.l3_time = this.met - this.l3_time;
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(this.cid + "Evaluated " + this.mname + " module ");
      }
      if (this.mname == "mysql") {
        this.vars.process.query = "";
      }

      if (global.logger.isTraceEnabled()) {
        global.logger.warn(this.executionTime());
      }
      KPI.emit(KPI_KEY, { aid: this.appId, mid: this.typeId, mname: this.mname, met: this.met, mst: this.mst, mcode: responseCode, l1: this.l1_time, l2: this.l2_time, l2s1: this.l2_s1_time, l2s2: this.l2_s2_time, l2s3: this.l2_s3_time, l2s4: this.l2_s4_time, l3: this.l3_time });
    });
  }

  executionTime() {
    return new Date().toISOString() + "|UID:" + this.userId + "|AID:" + this.appId + "|MID:" + this.mid + "|TYPE:" + this.typeId + "|MN:" + this.mname + "|ET:" + (this.met - this.mst) + "|SI:" + this.input + "|L1:" + this.l1_time + "|L2:" + this.l2_time + "|L3:" + this.l3_time + "|L1-S1:" + this.l1_s1_time + "|L1-S2:" + this.l1_s2_time + "|L2-S1:" + this.l2_s1_time + "|L2-S2:" + this.l2_s2_time + "|L2-S3:" + this.l2_s3_time + "|L2-S4:" + this.l2_s4_time;
  }

  toJSON() {
    return {
      appId: this.appId,
      moduleId: this.moduleId,
      mid: this.mid,
      txnId: this.txnId,
      mst: this.mst,
      met: this.met,
      typeId: this.typeId,
      mname: this.mname,
      vars: this.vars,
      menuContext: this.menuContext && this.menuContext.toJSON()
    };
  }

  toString() {
    return JSON.stringify(this.toJSON());
  }
}

module.exports = ModuleContext;
