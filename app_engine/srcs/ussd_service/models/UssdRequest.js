
const sep = "|";
const NULL = "NULL";

class UssdRequest {
  constructor(opts = {}) {
    this.appId = opts.appId;
    this.cellId = opts.cellId;
    this.content = opts.content;
    this.hlr = opts.hlr;
    this.imsi = opts.imsi;
    this.isMultiAccess = opts.isMultiAccess || false;
    this.multiAccessCodes = opts.multiAccessCodes || [];
    this.its_session_info = opts.its_session_info;
    this.lac = opts.lac;
    this.language = opts.language;
    this.lastAccessedTime = opts.lastAccessedTime;
    this.lastResponseTime = opts.lastResponseTime;
    this.mcc = opts.mcc || "";
    this.mnc = opts.mnc || "";
    this.msc = opts.msc || "";
    this.msisdn = opts.msisdn;
    this.network_error_code = opts.network_error_code;
    this.optionalParams = opts.optionalParams;
    this.pushGatewayId = opts.pushGatewayId;
    this.reqSequenceNo = opts.reqSequenceNo;
    this.selectedOption = opts.selectedOption || 0;
    this.serviceCode = opts.serviceCode || -1;
    this.serviceName = opts.serviceName || "Unknown";
    this.serviceType = opts.serviceType || "USSD";
    this.serviceOperation = opts.serviceOperation;
    this.sessionId = opts.sessionId;
    this.sessionStartTime = opts.sessionStartTime || new Date().getTime();
    this.vlr = opts.vlr || "";
    this.gtAddress = opts.gt_addr || -1;
    this.PushID = opts.PushID || "";
    this.version = opts.version || "32";
    this.aclEnabled = opts.aclEnabled;
    this.aclAllowedLevel = opts.aclAllowedLevel;
    this.maxUssdMessageCharacters = opts.maxUssdMessageCharacters || 182;
    this.sourceAddressTON = opts.sourceAddressTON;
    this.sourceAddressNPI = opts.sourceAddressNPI;
    this.destinationAddressTON = opts.destinationAddressTON;
    this.destinationAddressNPI = opts.destinationAddressNPI;
  }

  getAppId() {
    return this.appId;
  }

  setAppId(appId) {
    this.appId = appId;
  }

  getCellId() {
    return this.cellId;
  }

  setCellId(cellId) {
    this.cellId = cellId;
  }

  getContent() {
    return this.content;
  }

  setContent(content) {
    this.content = content;
  }

  getHlr() {
    return this.hlr;
  }

  setHlr(hlr) {
    this.hlr = hlr;
  }

  getImsi() {
    return this.imsi;
  }

  setImsi(imsi) {
    this.imsi = imsi;
  }

  getItsSessionInfo() {
    return this.its_session_info;
  }

  setItsSessionInfo(its_session_info) {
    this.its_session_info = its_session_info;
  }

  setMultiAccess(isMultiAccess) {
    this.isMultiAccess = isMultiAccess;
  }

  getMultiAccessCodes() {
    return this.multiAccessCodes;
  }

  setMultiAccessCodes(multiAccessCodes) {
    this.multiAccessCodes = multiAccessCodes;
  }

  getLac() {
    return this.lac;
  }

  setLac(lac) {
    this.lac = lac;
  }

  getLanguage() {
    return this.language;
  }

  setLanguage(language) {
    this.language = language;
  }

  getLastAccessedTime() {
    return this.lastAccessedTime;
  }

  setLastAccessedTime(lastAccessedTime) {
    this.lastAccessedTime = lastAccessedTime;
  }

  getLastResponseTime() {
    return this.lastResponseTime;
  }

  setLastResponseTime(lastResponseTime) {
    this.lastResponseTime = lastResponseTime;
  }

  getMcc() {
    return this.mcc;
  }

  setMcc(mcc) {
    this.mcc = mcc;
  }

  getMnc() {
    return this.mnc;
  }

  setMnc(mnc) {
    this.mnc = mnc;
  }

  getMsc() {
    return this.msc;
  }

  setMsc(msc) {
    this.msc = msc;
  }

  getGtAddress() {
    return this.gtAddress;
  }

  setGtAddress(gtAddress) {
    this.gtAddress = gtAddress;
  }

  getMsisdn() {
    return this.msisdn;
  }

  setMsisdn(msisdn) {
    this.msisdn = msisdn;
  }

  getNetworkErrorCode() {
    return this.network_error_code;
  }

  setNetworkErrorCode(network_error_code) {
    this.network_error_code = network_error_code;
  }

  getOptionalParams() {
    return this.optionalParams;
  }

  setOptionalParams(optionalParams) {
    this.optionalParams = optionalParams;
  }

  addOptionalParam(key, value) {
    if (this.optionalParams == null) this.optionalParams = {};
    this.optionalParams[key] = value;
  }

  getPushGatewayId() {
    return this.pushGatewayId;
  }

  setPushGatewayId(pushGatewayId) {
    this.pushGatewayId = pushGatewayId;
  }

  getReqSequenceNo() {
    return this.reqSequenceNo;
  }

  setReqSequenceNo(reqSequenceNo) {
    this.reqSequenceNo = reqSequenceNo;
  }

  getSelectedOption() {
    return this.selectedOption;
  }

  setSelectedOption(selectedOption) {
    this.selectedOption = selectedOption;
  }

  getServiceCode() {
    return this.serviceCode;
  }

  setServiceCode(serviceCode) {
    this.serviceCode = serviceCode;
  }

  getServiceName() {
    return this.serviceName;
  }

  setServiceName(serviceName) {
    this.serviceName = serviceName;
  }

  getServiceType() {
    return this.serviceType;
  }

  setServiceType(serviceType) {
    this.serviceType = serviceType;
  }

  getServiceOperation() {
    return this.serviceOperation;
  }

  setServiceOperation(serviceOperation) {
    this.serviceOperation = serviceOperation;
  }

  getSessionId() {
    return this.sessionId;
  }

  setSessionId(sessionId) {
    this.sessionId = sessionId;
  }

  getSessionStartTime() {
    return this.sessionStartTime;
  }

  setSessionStartTime(sessionStartTime) {
    this.sessionStartTime = sessionStartTime;
  }

  getVersion() {
    return this.version;
  }

  setVersion(version) {
    this.version = version;
  }

  getPushID() {
    return this.PushID;
  }

  setPushID(PushID) {
    this.PushID = PushID;
  }

  getVlr() {
    return this.vlr;
  }

  setVlr(vlr) {
    this.vlr = vlr;
  }

  isAclEnabled() {
    return this.aclEnabled;
  }

  setAclEnabled(aclEnabled) {
    this.aclEnabled = aclEnabled;
  }

  getAclAllowedLevel() {
    return this.aclAllowedLevel;
  }

  setAclAllowedLevel(aclAllowedLevel) {
    this.aclAllowedLevel = aclAllowedLevel;
  }

  getMaxUssdMessageCharacters() {
    return this.maxUssdMessageCharacters;
  }

  setMaxUssdMessageCharacters(maxUssdMessageCharacters) {
    this.maxUssdMessageCharacters = maxUssdMessageCharacters;
  }

  getSourceAddressTON() {
    return this.sourceAddressTON;
  }

  setSourceAddressTON(sourceAddressTON) {
    this.sourceAddressTON = sourceAddressTON;
  }

  getSourceAddressNPI() {
    return this.sourceAddressNPI;
  }

  setSourceAddressNPI(sourceAddressNPI) {
    this.sourceAddressNPI = sourceAddressNPI;
  }

  getDestAddressTON() {
    return this.destinationAddressTON;
  }

  setDestAddressTON(destinationAddressTON) {
    this.destinationAddressTON = destinationAddressTON;
  }

  getDestAddressNPI() {
    return this.destinationAddressNPI;
  }

  setDestAddressNPI(destinationAddressNPI) {
    this.destinationAddressNPI = destinationAddressNPI;
  }

  toJSON() {
    return {
      appId: this.appId,
      cellId: this.cellId,
      content: this.content,
      hlr: this.hlr,
      imsi: this.imsi,
      isMultiAccess: this.isMultiAccess,
      multiAccessCodes: this.multiAccessCodes,
      its_session_info: this.its_session_info,
      lac: this.lac,
      language: this.language,
      lastAccessedTime: this.lastAccessedTime,
      lastResponseTime: this.lastResponseTime,
      location: this.location,
      mcc: this.mcc,
      mnc: this.mnc,
      msc: this.msc,
      msisdn: this.msisdn,
      network_error_code: this.network_error_code,
      optionalParams: this.optionalParams,
      pushGatewayId: this.pushGatewayId,
      reqSequenceNo: this.reqSequenceNo,
      selectedOption: this.selectedOption,
      serviceCode: this.serviceCode,
      serviceName: this.serviceName,
      serviceType: this.serviceType,
      serviceOperation: this.serviceOperation,
      sessionId: this.sessionId,
      sessionStartTime: this.sessionStartTime,
      vlr: this.vlr,
      aclEnabled: this.aclEnabled,
      aclAllowedLevel: this.aclAllowedLevel,
      maxUssdMessageCharacters: this.maxUssdMessageCharacters,
      sourceAddressTON: this.sourceAddressTON,
      sourceAddressNPI: this.sourceAddressNPI,
      destinationAddressTON: this.destinationAddressTON,
      destinationAddressNPI: this.destinationAddressNPI
    };
  }

  print(opt) {
    let data = opt && opt + sep || "";
    data += this.sessionStartTime; data += sep;
    data += getData(this.reqSequenceNo); data += sep;
    data += getData(this.sessionId); data += sep;
    data += getData(this.appId); data += sep;
    data += getData(this.serviceName); data += sep;
    data += getData(this.serviceType); data += sep;
    data += getOperation(getData(this.serviceOperation)); data += sep;
    data += getData(this.pushGatewayId); data += sep;
    data += getData(this.msisdn); data += sep;
    data += getData(this.imsi); data += sep;
    data += getData(this.serviceCode); data += sep;
    data += getData(this.selectedOption); data += sep;
    data += (this.lastResponseTime - this.lastAccessedTime); data += sep;
    data += this.content && toHex(this.content) || NULL; data += sep;
    data += getData(this.sourceAddressTON); data += sep;
    data += getData(this.sourceAddressNPI); data += sep;
    data += getData(this.destinationAddressTON); data += sep;
    data += getData(this.destinationAddressNPI); data += sep;
    return data;
  }
}
module.exports = UssdRequest;

const Operations = {
  "0": "PSSD Indication",
  "1": "PSSR Indication",
  "2": "USSR Request",
  "3": "USSN Request",
  "16": "PSSD Response",
  "17": "PSSR Response",
  "18": "USSR Confirm",
  "19": "USSN Confirm"
};

function getOperation(op) {
  if (op == null) return "Unknown";
  if (Operations.hasOwnProperty(op))
    return Operations[op];
  return "UnknownOpCode:" + op;
}

function getData(data) {
  return (data != null) ? data : NULL;
}

function toHex(str) {
  let result = "";
  if (str != null)
    for (let i = 0; i < str.length; i++) {
      result += str.charCodeAt(i).toString(16);
    }
  return result;
}
