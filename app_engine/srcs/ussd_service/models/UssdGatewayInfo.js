const UssdPositionEnum = require("../UssdPositionEnum");
const DEFAULT_PARAMS = "SERVICE_STRING||IMSI||MSISDN||MSC||MCC||MNC||LAC||CI";

class UssdGatewayInfo {

  constructor(opts = {}) {
    this.id = opts.id;
    this.name = opts.name;
    this.type = opts.type;
    this.ip = opts.ip;
    this.port = opts.port;
    this.user = opts.user;
    this.pass = opts.pass;
    this.enquiry_link_interval = opts.enquiry_link_interval;
    this.no_of_channels = opts.no_of_channels;
    this.system_type = opts.system_type;
    this.interface_type = opts.interface_type;
    this.version = opts.version;
    this.status = opts.status || 0;
    this.buffer_size = opts.buffer_size || 0;
    if (opts.parameters_order != null && typeof opts.parameters_order == "string") {
      this.parameters_order = UssdPositionEnum.convertToArray(opts.parameters_order);
    } else {
      this.parameters_order = UssdPositionEnum.convertToArray(DEFAULT_PARAMS);
    }
    this.url = opts.url;
    this.retry = opts.retry;
    this.maxUssdMessageCharacters = (opts.maxUssdMessageCharacters != null) ? opts.maxUssdMessageCharacters : 160;
    this.shortcode_delimiter = opts.shortcode_delimiter;
    this.msg_delimiter = opts.msg_delimiter;
  }

  getId() {
    return this.id;
  }
  getName() {
    return this.name;
  }
  getType() {
    return this.type;
  }
  getIp() {
    return this.ip;
  }
  getPort() {
    return this.port;
  }
  getUser() {
    return this.user;
  }
  getPass() {
    return this.pass;
  }
  getEnquiryLinkInterval() {
    return this.enquiry_link_interval;
  }
  getNoOfChannels() {
    return this.no_of_channels;
  }
  getSystemType() {
    return this.system_type;
  }
  getInterfaceType() {
    return this.interface_type;
  }
  getVersion() {
    return this.version;
  }
  getStatus() {
    return this.status;
  }
  getBufferSize() {
    return this.buffer_size;
  }
  getParametersOrder() {
    return this.parameters_order;
  }
  getUrl() {
    return this.url;
  }
  getMaxUssdMessageCharacters() {
    return this.maxUssdMessageCharacters;
  }
  getSCDelimiter() {
    return this.shortcode_delimiter;
  }
  getSMDelimiter() {
    return this.msg_delimiter;
  }

  setId(id) {
    this.id = id;
  }
  setName(name) {
    this.name = name;
  }
  setType(type) {
    this.type = type;
  }
  setIp(ip) {
    this.ip = ip;
  }
  setPort(port) {
    this.port = port;
  }
  setUser(user) {
    this.user = user;
  }
  setPass(pass) {
    this.pass = pass;
  }
  setEnquiryLinkInterval(enquiry_link_interval) {
    this.enquiry_link_interval = enquiry_link_interval;
  }
  setNoOfChannels(no_of_channels) {
    this.no_of_channels = no_of_channels;
  }
  setSystemType(system_type) {
    this.system_type = system_type;
  }
  setInterfaceType(interface_type) {
    this.interface_type = interface_type;
  }
  setVersion(version) {
    this.version = version;
  }
  setStatus(status) {
    this.status = status;
  }
  setBufferSize(buffer_size) {
    this.buffer_size = buffer_size;
  }
  setParametersOrder(parameters_order) {
    this.parameters_order = parameters_order;
  }
  setUrl(url) {
    this.url = url;
  }
  setMaxUssdMessageCharacters(maxUssdMessageCharacters) {
    this.maxUssdMessageCharacters = maxUssdMessageCharacters;
  }
  setSCDelimiter(shortcode_delimiter) {
    this.shortcode_delimiter = shortcode_delimiter;
  }
  setSMDelimiter(msg_delimiter) {
    this.msg_delimiter = msg_delimiter;
  }
}
module.exports = UssdGatewayInfo;
