const UssdServiceCache = require("../cache/UssdServiceCache");
let sessionMap = {};

module.exports = {

  get: (systemid) => {
    return sessionMap[systemid];
  },

  set: (systemid, session) => {
    sessionMap[systemid] = session;
  },

  delete: (id) => {
    delete sessionMap[id];
  },

  sendEnquiryLinks: (systemid) => {
    sendEL(systemid);
  }
};

function sendEL(id) {
  let session = sessionMap[id];
  if (session != null) {
    session.enquire_link();
    let gatewayInfo = UssdServiceCache.getGatewayInfo(id);
    let interval = gatewayInfo && gatewayInfo.getEnquiryLinkInterval() || 60000;
    setTimeout(sendEL.bind(null, id), interval);
  }
}
