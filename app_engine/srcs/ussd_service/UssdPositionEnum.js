const DOUBLE_PIPE_SEPERATOR = "||";

const Keywords = Object.freeze({
  SERVICE_STRING: "SERVICE_STRING",
  IMSI: "IMSI",
  MSISDN: "MSISDN",
  MSC: "MSC",
  MCC: "MCC",
  MNC: "MNC",
  LAC: "LAC",
  CI: "CI",
  HLR: "HLR",
  INVALID: ""
});

const Position = new Map([
  [Keywords.SERVICE_STRING, 0],
  [Keywords.IMSI, 1],
  [Keywords.MSISDN, 2],
  [Keywords.MSC, 3],
  [Keywords.MCC, 4],
  [Keywords.MNC, 5],
  [Keywords.LAC, 6],
  [Keywords.CI, 7],
  [Keywords.HLR, 8],
  [Keywords.INVALID, 9]
]);

module.exports = {
  Keywords: Keywords,

  get: (str) => {
    return Position.get(str);
  },

  convertToArray: (str) => {
    let arr = [];
    try {
      str.split(DOUBLE_PIPE_SEPERATOR).forEach(element => {
        arr.push(Position.get(element));
      });
    } catch (e) {
      global.logger.error(e);
    }
    return arr;
  }
};
