const smpp = require("smpp");
const SmppDecoderEncoder = require("./SmppDecoderEncoder");
const UssdServiceCache = require("../cache/UssdServiceCache");
const SmppHelper = require("./SmppHelper");
const AppStore = require("app_store");
const common = require("common");
const whiteboard = common.whiteboard;
require("events").EventEmitter.prototype._maxListeners = 0;

const AppMan = require("../app_man");

const GWAccounts = {};
module.exports = {

  init: async () => {
    global.logger.warn("Initializing the USSD Gateway Connections");
    let ussdGatewayConfigurations = UssdServiceCache.listGatewayConfigs();
    Object.keys(ussdGatewayConfigurations).forEach(key => {
      if (ussdGatewayConfigurations[key].type == 0)
        bindUssdGateway(ussdGatewayConfigurations[key]);
    });

    whiteboard.on("mount_ussdgw", async (gwId) => {
      try {
        let ussdGatewayConfig = await AppStore.findUssdGatewayConfig(gwId);
        if (ussdGatewayConfig && ussdGatewayConfig.status == 1) {
          global.smpplogger.error("GATEWAY-MOUNT|SID:" + gwId + "|INFO:" + JSON.stringify(ussdGatewayConfig));
          UssdServiceCache.setGatewayConfig(gwId, ussdGatewayConfig);
          if (ussdGatewayConfig.type == 0) {
            await unbind(gwId);
            bindUssdGateway(UssdServiceCache.getGatewayInfo(gwId));
          }
        }

      } catch (e) {
        global.logger.error("Failed to Mount USSDGateway:", gwId, e);
      }
    });

    whiteboard.on("unmount_ussdgw", async (gwId) => {
      try {
        global.smpplogger.error("GATEWAY-UNMOUNT|SID:" + gwId);
        let ussdGatewayConfig = UssdServiceCache.getGatewayInfo(gwId);
        if (ussdGatewayConfig && ussdGatewayConfig.type == 0) {
          await unbind(gwId);
        }
      } catch (e) {
        global.logger.error("Failed to Unmount USSDGateway:", gwId, e);
      }
    });
    whiteboard.subscribe("mount_ussdgw");
    whiteboard.subscribe("unmount_ussdgw");
  }
};

function unbind(gwId) {
  return new Promise(resolve => {
    let session = SmppHelper.get(gwId);
    if (session) {
      session.unbind(pdu => {
        global.smpplogger.error("SMPP-UNBIND|SID:" + gwId + "|RSP:" + JSON.stringify(pdu));
        SmppHelper.delete(gwId);
        resolve(pdu);
      });
    } else {
      resolve(null);
    }
  });
}

function bindUssdGateway(gatewayInfo) {
  try {

    global.smpplogger.error("SMPP-GW-IN|GW:" + gatewayInfo.getName() + "|Status:" + gatewayInfo.getStatus() + "|InterfaceType:" + gatewayInfo.getType() + "|INFO:" + gatewayInfo);
    if (gatewayInfo.getStatus() == 1) {
      let connectionString = "";
      switch (Number(gatewayInfo.getType())) {
        case 0:
          connectionString = "smpp://" + gatewayInfo.getIp() + ":" + gatewayInfo.getPort();
          break;
        case 1:
        case 2:
          connectionString = gatewayInfo.getUrl();
          break
        default:
          connectionString = "smpp://localhost:2775";
      }
      global.smpplogger.error("SMPP-BIND-INIT|SID:" + gatewayInfo.id + "CS:" + connectionString);
      let session = smpp.connect(connectionString);
      session.gatewayInfo = gatewayInfo;
      session.on("error", err => {
        global.smpplogger.error("SMPP-ERROR|SID:" + session.id + "|PDU:" + JSON.stringify(err));
        if (err && (err.code == "ECONNRESET" || err.code == "ECONNREFUSED")) {
          SmppHelper.delete(session.id);
          setTimeout(() => {
            session = null;
            bindUssdGateway(gatewayInfo);
          }, 5000);
        }
      });
      session.bind_transceiver({
        system_id: gatewayInfo.getUser(),
        password: gatewayInfo.getPass()
      }, (bindTransceiver) => {
        if (bindTransceiver.command_status === 0) {
          global.smpplogger.error("SMPP-BIND-STATUS|SID:" + gatewayInfo.id + "|CS:" + connectionString + "|BindTransceiver-SUCCESS:" + JSON.stringify(bindTransceiver));
          UssdServiceCache.setGatewaySessionInfo(gatewayInfo, session);
          session.id = gatewayInfo.id;
          GWAccounts[gatewayInfo.getId()] = session;
          SmppHelper.set(session.id, session);
          SmppHelper.sendEnquiryLinks(session.id);
          session.on("deliver_sm", (pdu) => {
            global.smpplogger.trace("SMPP-DELIVER_SM|SID:" + session.id + "|PDU:" + JSON.stringify(pdu));
            session.send(pdu.response());
            let ussdRequest = SmppDecoderEncoder.decode(pdu, gatewayInfo);
            let code = UssdServiceCache.getExactServiceCode(ussdRequest);
            let ussdServiceInfo = UssdServiceCache.findUssdServiceInfo(code);
            if (ussdServiceInfo != null) {
              ussdRequest.setAppId(ussdServiceInfo.appId);
              ussdRequest.setServiceName(ussdServiceInfo.name);
              ussdRequest.setOptionalParams(ussdServiceInfo.options);
            } else {
              ussdRequest.setAppId(-1);
              ussdRequest.setServiceName("Unknown");
              ussdRequest.setOptionalParams({});
            }
            AppMan.smppInterface(ussdRequest);
          });
          session.on("submit_sm", (pdu) => {
            global.smpplogger.trace("SMPP-SUBMIT_SM|SID:" + session.id + "|PDU:" + JSON.stringify(pdu));
            session.send(pdu.response());
          });
        } else {
          global.smpplogger.error("SMPP-BIND-STATUS|CS:" + connectionString + "|BindTransceiver-FAIL:" + JSON.stringify(bindTransceiver));
        }
      });
    }
  } catch (error) {
    global.smpplogger.error("SMPP-EXPECTION|", gatewayInfo, error);
  }
}
