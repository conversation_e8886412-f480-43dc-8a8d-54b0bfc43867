const UssdRequest = require("./models/UssdRequest");
const SmppConstants = require("./SmppConstants");

module.exports = {
  decode: (pdu, gatewayInfo) => {
    let serviceOperation = pdu.ussd_service_op || 0;
    let message = pdu.short_message.message;
    if (typeof pdu.network_error_code == "object") {
      pdu.network_error_code = pdu.network_error_code.toString();
    }
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("SMPP Subscriber input '" + message + "'");
    }
    if (message && message.length == 0) return;
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Received the DELIVER_SM packet", JSON.stringify(pdu));
    }
    let ussdRequest = new UssdRequest({
      lastAccessedTime: new Date().getTime(),
      reqSequenceNo: pdu.sequence_number,
      serviceOperation: serviceOperation,
      serviceCode: pdu.destination_addr,
      serviceType: pdu.service_type,
      msisdn: pdu.source_addr,
      sourceAddressTON: pdu.source_addr_ton,
      sourceAddressNPI: pdu.source_addr_npi,
      destinationAddressTON: pdu.dest_addr_ton,
      destinationAddressNPI: pdu.dest_addr_npi,
      its_session_info: pdu.its_session_info || Buffer.alloc(2),
      network_error_code: pdu.network_error_code,
      pushGatewayId: gatewayInfo.id,
      maxUssdMessageCharacters: gatewayInfo.maxUssdMessageCharacters,
      hlr: pdu.hlr && pdu.hlr.message,
      vlr: pdu.vlr && pdu.vlr.message
    });
    let shortcode_delimiter = gatewayInfo.shortcode_delimiter;

    if (serviceOperation === SmppConstants.SERVICE_OP_PSSR_INDICATION
      || serviceOperation === SmppConstants.SERVICE_OP_PSSD_INDICATION) {
      if (message.startsWith(gatewayInfo.msg_delimiter)) {
        message = shortcode_delimiter + message;
      }
      let msgParts = message.split(gatewayInfo.msg_delimiter);
      let i = 0;
      gatewayInfo.getParametersOrder().forEach(position => {
        let msg = msgParts[i];
        i++;
        if (msg) {
          let value = msg.trim();
          switch (position) {
            case 0://SERVICE_STRING
              if (value.length > 0) {
                ussdRequest.setSelectedOption(value);
              } else {
                ussdRequest.setSelectedOption(1);
              }
              break;
            case 1://IMSI
              ussdRequest.setImsi(value);
              break;
            case 2://MSISDN
              break;
            case 3://MSC
              ussdRequest.setMsc(value);
              break;
            case 4://MCC
              ussdRequest.setMcc(value);
              break;
            case 5://MNC
              ussdRequest.setMnc(value);
              break;
            case 6://LAC
              ussdRequest.setLac(value);
              break;
            case 7://CI
              ussdRequest.setCellId(value);
              break;
            case 8://HLR
              ussdRequest.setHlr(value);
              break;
            default:
              global.logger.warn("Unknown or unhandled parameter defined:" + position);
          }
        }
      });
    } else if (serviceOperation === SmppConstants.SERVICE_OP_USSR_CONFIRM) {
      if (message == null || message.length === 0) {
        message = "1";
      }
      ussdRequest.setSelectedOption(message);
    }
    if (ussdRequest.getSelectedOption() == null) {
      ussdRequest.setSelectedOption("0");
    }

    if (ussdRequest.getSelectedOption().length > 1 && ussdRequest.getSelectedOption().includes(shortcode_delimiter)) {
      let str = ussdRequest.getSelectedOption();
      if (str.length > 1 && str.startsWith(shortcode_delimiter)) {
        str = str.substring(1);
      }
      let arr = str.split(shortcode_delimiter);
      arr.shift();
      ussdRequest.setMultiAccess(true);
      ussdRequest.setMultiAccessCodes(arr);
    }
    return ussdRequest;
  }
};

function getMessageText(pdu) {
  if (typeof pdu.network_error_code == "object") {
    pdu.network_error_code = pdu.network_error_code.toString();
  }
  let messageText = pdu.short_message.message;
  if (global.logger.isTraceEnabled()) {
    global.logger.trace("SMPP Subscriber input '" + messageText + "'");
  }
  return messageText == null ? messageText : messageText.trim();
}
