let smpp = require("smpp");
const cluster = require("cluster");
const UssdServiceCache = require("../cache/UssdServiceCache");
const UssdGatewayInfo = require("./models/UssdGatewayInfo");
const SmppHelper = require("./SmppHelper");
const SmppDecoderEncoder = require("./SmppDecoderEncoder");
const AppMan = require("../app_man");
const utility = require("utility");
const DEFAULT_SMPP_PORT = 2776;

module.exports = {
  init: (opts = { port: DEFAULT_SMPP_PORT }) => {
    if (cluster.isMaster) {
      global.logger.warn("Starting the App Engine SMPP Server");
      let server = smpp.createServer(session => {
        session.on("bind_transmitter", pdu => {
          session.send(pdu.response({
            command_status: smpp.ESME_RBINDFAIL
          }));
        });
        session.on("bind_receiver", pdu => {
          session.send(pdu.response({
            command_status: smpp.ESME_RBINDFAIL
          }));
        });

        session.on("bind_transceiver", pdu => {
          session.pause();
          checkAsyncUserPass(pdu.system_id, pdu.password, err => {
            if (err) {
              session.send(pdu.response({
                command_status: smpp.ESME_RBINDFAIL
              }));
              session.close();
              return;
            }
            let gatewayInfo = new UssdGatewayInfo({
              id: "SMPP-PULL:" + utility.getUniqueTxnId(),
              name: pdu.system_id,
              user: pdu.system_id,
              pass: pdu.password,
              enquiry_link_interval: 10000
            });
            session.send(pdu.response());
            session.resume();
            UssdServiceCache.setGatewaySessionInfo(gatewayInfo, session);
            session.id = gatewayInfo.id;
            SmppHelper.set(session.id, session);
            SmppHelper.sendEnquiryLinks(session.id);
            session.on("deliver_sm", (pdu) => {
              session.send(pdu.response());
              let ussdRequest = SmppDecoderEncoder.decode(pdu, gatewayInfo);
              let code = UssdServiceCache.getExactServiceCode(ussdRequest);
              let ussdServiceInfo = UssdServiceCache.findUssdServiceInfo(code);
              if (ussdServiceInfo != null) {
                ussdRequest.setAppId(ussdServiceInfo.appId);
                ussdRequest.setServiceName(ussdServiceInfo.name);
                ussdRequest.setOptionalParams(ussdServiceInfo.options);
              } else {
                ussdRequest.setAppId(-1);
                ussdRequest.setServiceName("Unknown");
                ussdRequest.setOptionalParams({});
              }
              AppMan.smppInterface(ussdRequest);
            });
            session.on("submit_sm", (pdu) => {
              session.send(pdu.response());
            });
            session.on("unbind", pdu => {
              session.send(pdu.response());
              session.close();
            });

            session.on("enquire_link", pdu => {
              session.send(pdu.response());
            });
            session.on("close", () => {
              SmppHelper.delete(session.id);
            });
            session.on("error", () => {
              SmppHelper.delete(session.id);
            });
          });

        });
      });

      server.listen(opts.port);
      global.logger.warn("Started SMPP Server with port:", opts.port);
    }
  }
}

function checkAsyncUserPass(system_id, password, done) {
  let interfaces = global.config.instance.interfaces;
  let accounts = interfaces.smpp && interfaces.smpp.enabled && interfaces.smpp.accounts || {};
  if (accounts[system_id] && accounts[system_id].status && accounts[system_id].password == password) {
    global.logger.info("Bind: %s-%s ESME_OK", system_id, password);
    done();
  } else {
    global.logger.warn("Bind: %s-%s ESME_RBINDFAIL", system_id, password);
    done("Invalid User Cred");
  }
}
