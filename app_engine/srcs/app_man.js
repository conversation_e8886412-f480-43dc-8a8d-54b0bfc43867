"use strict";
/**
 *  Application Manager -- A module that is responsible for
 *  -- loading, launching LEAP applications
 *  -- providing state management and persistence support
 *
 **/
// Include other modules as needed.
const Enums = require("./lib/enums");
const INTERFACES = Enums.INTERFACES;

const AppCache = require("./cache/AppCache");
const UserSessionStore = require("./cache/UserSessionStore");
const UssdServiceCache = require("./cache/UssdServiceCache");
const WebsocketManager = require("./websocket/WebsocketManager");
const VALCODES = require("./lib/http_codes");
const message = require("message");
const auth = require("basic-auth");
const SmppConstants = require("./ussd_service/SmppConstants");
const CPSContants = require("./ussd_service/CPSContants");
const ResponseHandler = require("./lib/ResponseHandler");
const EventLogger = require("./lib/EventLogger");
const UssdRequest = require("./ussd_service/models/UssdRequest");
const NexmoMapping = require("../config/nexmo.json").keywordMapping;
const utility = require("utility");
const SessionManager = require("./lib/SessionManager");
const KPI = require("app_kpi");
const KPI_INT_REQ = KPI.KEYS.interface_req;
const KPI_INT_RES = KPI.KEYS.interface_res;
const common = require("common");
const HTTPCODES = common.http_codes;
const jwt = require("jsonwebtoken");


const USSD_QUERY_PARAMS = [
  "MSISDN",
  "SENDER",
  "MESSAGE",
  "GATEWAYID",
  "USERNAME",
  "PASSWORD"
];

const APP_PUSH_PARAMS = [
  "MSISDN",
  "MESSAGE",
  "GATEWAYID",
  "USERNAME",
  "PASSWORD",
  "SENDERADDRESS",
  "CAMPAIGNID",
  "CPID",
  "INTERFACETYPE",
  "LOCATION",
  "CAMPAIGNCATEGORY",
  "ACCOUNTID",
  "USERID"
];

const INHERIT_PARAMS = {
  "subscriberInput": true,
  "language": true,
  "contentType": true,
  "isNewRequest": true,
  "txnId": true,
  "appId": true,
  "imethod": true,
  "imode": true,
  "debugId": true
};

module.exports = {
  /**
   * HTTP Interface end point for LEAP App Engine
   */
  httpInterface: async (req, res) => {
    try {
      KPI.emit(KPI_INT_REQ, req.qs.imode);
      let appExists = AppCache.exists(req.params.appId);
      let omode = AppCache.checkAppInNotAllowedState(req.params.appId) == null; // Application status is not in allowed filter
      if (!appExists && omode) {
        KPI.emit(KPI_INT_RES, req.qs.imode);
        return res.status(HTTPCODES.resourceNotFound.code)
          .json(message.getResponseJson(req.locale, VALCODES.APP_NOT_FOUND));
      } else if (omode == false) {
        KPI.emit(KPI_INT_RES, req.qs.imode);
        return res.status(HTTPCODES.resourceNotFound.code)
          .json(message.getResponseJson(req.locale, VALCODES.APP_NOT_LOADED));
      }
      let startId = AppCache.getStartModuleID(req.params.appId);
      const moduleData = AppCache.getModuleData(req.params.appId, startId);

      if (moduleData.process.trigger == 'whatsapp') {
        moduleData.settings.aparty = "entry[0].changes[0].value.messages[0].from";
      }

      req.userId = getValueFromJson(req.qs, moduleData.settings.aparty);
      if (req.userId == null) {
        KPI.emit(KPI_INT_RES, req.qs.imode);
        return res.status(HTTPCODES.badRequest.code)
          .json(message.getResponseJson(req.locale, VALCODES.MISSING_MSISDN));
      }
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("TxnId:", req.txnId, ",Qs:" + JSON.stringify(req.qs));
      }

      if (req.qs.debugId && !WebsocketManager.get(req.qs.debugId)) {
        global.logger.error("TxnId:", req.txnId, "Unregistered Debugger ID:" + req.qs.debugId);
        KPI.emit(KPI_INT_RES, req.qs.imode);
        return res.status(HTTPCODES.badRequest.code)
          .json(message.getResponseJson(req.locale, VALCODES.DEBUG_UNREGISTERED_INVOKER));
      }

      if (!req.qs.debugId && moduleData.settings.auth_required) {
        let token, decoded;
        if (req.headers.hasOwnProperty("authorization")) {
          token = req.headers["authorization"].substring(7);
        } else {
          return res.status(401).json(message.getResponseJson(req.locale, 605));
        }
        let secretKey = global.config.authSecret;
        try {
          console.log("Token:" + token + ": secretKey:" + secretKey);
          decoded = jwt.verify(token, secretKey);
        } catch (error) {
          if (error.name == 'JsonWebTokenError')
            return res.status(401).json(message.getResponseJson(req.locale, 606));
          else if (error.name == 'TokenExpiredError') {
            if (req.path != '/refreshToken')
              return res.status(401).json(message.getResponseJson(req.locale, 607));
            else {
              decoded = jwt.decode(token);
            }
          }
        }
        if (!decoded || decoded.username != AppCache.getAppOwner(req.params.appId)) {
          KPI.emit(KPI_INT_RES, req.qs.imode);
          return res.status(HTTPCODES.invalidAuth.code)
            .json(message.getResponseJson(req.locale, VALCODES.UNAUTHORIZED));
        }
      }

      if (AppCache.getEndModuleID(req.params.appId) == null) {
        KPI.emit(KPI_INT_RES, req.qs.imode);
        return res.status(HTTPCODES.resourceNotFound.code)
          .json(message.getResponseJson(req.locale, VALCODES.UNDEFINED_ENDMODULE));
      }

      const session = await SessionManager.getExistingOrCreateNew(req.params.appId, req.userId, req.qs);
      if (req.qs.debugId != null) {
        KPI.emit(KPI_INT_RES, req.qs.imode);
        res.status(HTTPCODES.accepted.code)
          .json(message.getResponseJson(req.locale, VALCODES.REQUEST_ACCEPTED));
      }

      if (moduleData.process.trigger == 'whatsapp') {
        session.setParam('phone_number', req.userId);
        session.setParam('name', req.qs.entry[0].changes[0].value.contacts[0].profile.name);
        session.setParam('message', req.qs.entry[0].changes[0].value.messages[0].text.body);
      }

      session.setCallback(res);

      return session.start();

    } catch (e) {
      global.logger.error("Failed to process Input request", e);
    }
    KPI.emit(KPI_INT_RES, req.qs.imode);
    return res.status(HTTPCODES.internalServerError.code)
      .json(message.getResponseJson(req.locale, VALCODES.INTERNAL_ERROR));
  },

  sessionExtender: async (req, res) => {
    try {
      let key = req.body.key;
      let appId = key.split("_")[0];
      let userId = key.split("_")[1];
      let appExists = AppCache.exists(appId);
      if (!appExists) {
        return res.status(HTTPCODES.resourceNotFound.code)
          .json(message.getResponseJson(req.locale, VALCODES.APP_NOT_FOUND));
      }

      const session = await SessionManager.getExpiredSession(appId, userId);

      session.setCallback(res);

      return session.start();

    } catch (e) {
      global.logger.error("Failed to process Input request", e);
    }
    KPI.emit(KPI_INT_RES, req.qs.imode);
    return res.status(HTTPCODES.internalServerError.code)
      .json(message.getResponseJson(req.locale, VALCODES.INTERNAL_ERROR));
  },

  /**
  * SMPP Interface end point for LEAP App Engine
  */
  smppInterface: async (ussdRequest, flag = true) => {
    try {
      KPI.emit(KPI_INT_REQ, INTERFACES.SMPP);
      if (ussdRequest.getServiceOperation() == SmppConstants.SERVICE_OP_ABORT_REQ) {
        if (global.logger.isTraceEnabled()) {
          global.logger.trace("SMPP-APP-MAN: Received ABORT", JSON.stringify(ussdRequest.toJSON()));
        }
        EventLogger.emit("USSDRequest", ussdRequest);
        return;
      }

      if (ussdRequest.getAppId() == -1) {
        if (global.logger.isTraceEnabled()) {
          global.logger.trace("SMPP-APP-MAN: App ID Is null, Checking AppId exists in Session store for", ussdRequest.getMsisdn());
        }

        let appId = await UserSessionStore.getAppId(ussdRequest.getMsisdn());
        if (appId != null) ussdRequest.setAppId(appId);
      }
      if (flag && ussdRequest.getAppId() == null || ussdRequest.getAppId() == -1) {
        EventLogger.emit("USSDRequest", ussdRequest);
        ussdRequest.setContent(message.getResponseJson(global.config.defaultLanguage, VALCODES.SMPP_SC_NOT_MAPPED).msg);
        if (global.logger.isTraceEnabled()) {
          global.logger.trace("SMPP-APP-MAN: Application is not linked to service for shortcode", ussdRequest.getServiceCode());
        }
        ResponseHandler.sendSmppResponse(ussdRequest, SmppConstants.SERVICE_OP_PSSR_RESPONSE);
        return;
      }
      if (flag && ussdRequest.isMultiAccess && ussdRequest.getMultiAccessCodes().length > global.leap_settings.defaults.ussd_allowed_access_level) {
        EventLogger.emit("USSDRequest", ussdRequest);
        ussdRequest.setContent(message.getResponseJson(global.config.defaultLanguage, VALCODES.SMPP_INVALID_SHORTCODE).msg);
        if (global.logger.isTraceEnabled()) {
          global.logger.trace("SMPP-APP-MAN: Multi level access is blocked for shortcode", ussdRequest.getServiceCode(), "dialed by ", ussdRequest.getMsisdn());
        }
        ResponseHandler.sendSmppResponse(ussdRequest, SmppConstants.SERVICE_OP_PSSR_RESPONSE);
        return;
      }

      let appExists = AppCache.exists(ussdRequest.getAppId());
      if (flag && !appExists) {
        EventLogger.emit("USSDRequest", ussdRequest);
        ussdRequest.setContent(message.getResponseJson(global.config.defaultLanguage, VALCODES.SMPP_APP_NOT_MAPPED).msg);

        if (global.logger.isTraceEnabled()) {
          global.logger.trace("SMPP-APP-MAN: Application is not linked to service for shortcode", ussdRequest.getServiceCode());
        }
        flag && ResponseHandler.sendSmppResponse(ussdRequest, SmppConstants.SERVICE_OP_PSSR_RESPONSE);
        return;
      }

      let startId = AppCache.getStartModuleID(ussdRequest.getAppId());
      const moduleData = AppCache.getModuleData(ussdRequest.getAppId(), startId);
      let qs = {
        isNewRequest: (ussdRequest.getServiceOperation() == SmppConstants.SERVICE_OP_PSSR_INDICATION) ? 1 : 0,
        subscriberInput: ussdRequest.getSelectedOption(),
        imode: INTERFACES.SMPP,
        multiAccessCodes: ussdRequest.getMultiAccessCodes(),
        VLR: ussdRequest.getVlr() || "",
        HLR: ussdRequest.getHlr() || "",
        IMSI: ussdRequest.getImsi() || "",
        MSC: ussdRequest.getMsc() || "",
        MCC: ussdRequest.getMcc() || "",
        MNC: ussdRequest.getMnc() || "",
        LAC: ussdRequest.getLac() || "",
        CellId: ussdRequest.getCellId() || ""
      };
      if (qs.isNewRequest == 1) {
        qs.sessionid = utility.getUniqueTxnId();
        qs.transactionid = qs.sessionid;
      }
      let opts = ussdRequest.getOptionalParams();
      opts && typeof opts == "object" && Object.keys(opts).forEach(key => {
        qs[key] = opts[key];
      });
      qs[moduleData.settings.aparty] = ussdRequest.getMsisdn();

      if (global.logger.isTraceEnabled()) {
        global.logger.trace("SMPP-APP-MAN: QUERY PARAMS:", JSON.stringify(qs));
      }
      const session = await SessionManager.getExistingOrCreateNew(ussdRequest.getAppId(), ussdRequest.getMsisdn(), qs);
      ussdRequest.setSessionId(session.getSessionId());
      EventLogger.emit("USSDRequest", ussdRequest);
      session.setUssdRequest(ussdRequest);
      session.start();
    } catch (e) {
      global.logger.error("Failed to process Smpp request", e);
    }
  },

  /**
  * Web-Socket Interface end point for LEAP App Engine
  */
  websocketInterface: async (appId, userId, query) => {
    if (global.logger.isInfoEnabled()) {
      global.logger.info("Session(" + appId + ":" + userId + ") Executing app");
    }
    const session = await SessionManager.getExistingOrCreateNew(appId, userId, query);
    return session.start();
  },

  /**
   * USSD Interface end point for LEAP App Engine
   */
  ussdPushInterface: async (req, res) => {
    try {
      KPI.emit(KPI_INT_REQ, "USSD_PUSH");
      let params = [];
      if (req.query != null) USSD_QUERY_PARAMS.forEach(param => {
        if (!req.query.hasOwnProperty(param))
          params.push(param);
      });

      if (params.length > 0) {
        let missingParams = message.getResponseJson(req.locale,
          VALCODES.MISSING_QUERY_PARAM);
        missingParams.queryParams = params;
        return res.status(HTTPCODES.badRequest.code).json(missingParams);
      }

      let response = await ResponseHandler.sendSmppFlash(req.query, SmppConstants.SERVICE_OP_USSN_REQUEST);
      switch (response.command_status) {
        case 400:
          return res.status(HTTPCODES.invalidAuth.code)
            .json(message.getResponseJson(req.locale,
              VALCODES.UNAUTHORIZED));
        case -1:
          return res.status(HTTPCODES.badRequest.code)
            .json(message.getResponseJson(req.locale, VALCODES.SMPP_INVALID_SHORTCODE));
        default:
          return res.status(HTTPCODES.ok.code)
            .json(message.getResponseJson(req.locale, VALCODES.REQUEST_PROCESSED));
      }
    } catch (error) {
      global.logger.error(error);
      return res.status(HTTPCODES.internalServerError.code)
        .json(message.getResponseJson(req.locale, VALCODES.INTERNAL_ERROR));
    }
  },

  /**
  * USSD App Push Interface end point for LEAP App Engine
  */
  appPushInterface: async (req, res) => {
    try {
      KPI.emit(KPI_INT_REQ, "APP_PUSH");

      let params = [];
      if (req.query != null) APP_PUSH_PARAMS.forEach(param => {
        if (!req.query.hasOwnProperty(param))
          params.push(param);
      });

      if (params.length > 0) {
        let missingParams = message.getResponseJson(req.locale,
          VALCODES.MISSING_QUERY_PARAM);
        missingParams.queryParams = params;
        return res.status(HTTPCODES.badRequest.code).json(missingParams);
      }

      let gatewayInfo = UssdServiceCache.getGatewayInfo(req.query.GATEWAYID);
      if (gatewayInfo == null)
        return res.status(HTTPCODES.ok.code)
          .json(message.getResponseJson(req.locale, VALCODES.GATEWAY_NOT_ACTIVE));

      if (req.query.APPID != null && req.query.APPID.trim().length > 0) {

        if (req.query.APPID != -1) {
          let appExists = AppCache.exists(req.query.APPID);
          let omode = AppCache.checkAppInNotAllowedState(req.query.APPID) == null; // Application status is not in allowed filter
          if (!appExists) {
            return res.status(404).json(message.getResponseJson(req.locale, VALCODES.APP_NOT_FOUND));
          } else if (omode == false) {
            return res.status(404).json(message.getResponseJson(req.locale, VALCODES.APP_NOT_LOADED));
          }
        }
        if (gatewayInfo.getType() == 0) {
          let ussdRequest = new UssdRequest({
            appId: req.query.APPID,
            msisdn: req.query.MSISDN,
            pushGatewayId: req.query.GATEWAYID,
            serviceCode: global.leap_settings.defaults.cps_sc
          });
          module.exports.smppInterface(ussdRequest);
          return res.status(HTTPCODES.ok.code)
            .json(message.getResponseJson(req.locale, VALCODES.REQUEST_PROCESSED));
        } else if (gatewayInfo.getType() == 1) {
          if (req.body == null) req.body = {};
          req.body.sequence_number = 0;
          req.body.source_addr = req.query.MSISDN;
          req.body.service_type = CPSContants.PUSH_REQUEST;
          req.body.dest_addr = global.leap_settings.defaults.cps_sc;
          req.qs = {
            txnId: req.txnId,
            appId: req.params.appId,
            imethod: req.method,
            imode: INTERFACES.CPS_XML,
            ...req.query
          };

          module.exports.cpsXMLInterface(req);
          res.send("OK");
        }
      } else {
        // Content Push
        req.query.SENDER = global.leap_settings.defaults.smpp_sc;
        let response = await ResponseHandler.sendSmppFlash(req.query, SmppConstants.SERVICE_OP_PSSR_RESPONSE);
        switch (response.command_status) {
          case 400:
            return res.status(HTTPCODES.invalidAuth.code)
              .json(message.getResponseJson(req.locale, VALCODES.UNAUTHORIZED));
          case -1:
            return res.status(HTTPCODES.badRequest.code)
              .json(message.getResponseJson(req.locale, VALCODES.SMPP_INVALID_SHORTCODE));
          default:
            return res.status(HTTPCODES.ok.code)
              .json(message.getResponseJson(req.locale, VALCODES.REQUEST_PROCESSED));
        }
      }

    } catch (error) {
      global.logger.error(error);
      res.status(HTTPCODES.internalServerError.code)
        .json(message.getResponseJson(req.locale, VALCODES.INTERNAL_ERROR));
    }
  },

  /**
  * Whatsapp Interface end point for LEAP App Engine
  */
  nexmoInterface: async (req, res) => {
    try {
      req.qs.subscriberInput = req.body.message.content.text;
      req.qs.MSISDN = req.body.from.number;
      req.qs.imode = INTERFACES.NEXMO;
      req.qs.from = req.body.to.number;
      KPI.emit(KPI_INT_REQ, req.qs.imode);

      if (req.qs.subscriberInput != null &&
        Object.keys(NexmoMapping).includes(req.qs.subscriberInput.toLowerCase())) {
        req.qs.isNewRequest = 1;
        req.qs.appId = null;
      } else {
        req.qs.appId = await UserSessionStore.getAppId(req.qs.MSISDN);
      }

      if (req.qs.appId != null) {
        req.qs.isNewRequest = 0;
      } else {
        req.qs.isNewRequest = 1;
        req.qs.appId = AppCache.findNexmoAppId(req.qs.subscriberInput);
      }
      req.qs.imode = INTERFACES.NEXMO;

      const session = await SessionManager.getExistingOrCreateNew(req.qs.appId, req.qs.MSISDN, req.qs);
      res.status(HTTPCODES.ok.code).json("Ack-Success");
      return session.start();
    } catch (e) {
      global.logger.error("Failed to process Input request", e);
    }
    return res.status(HTTPCODES.internalServerError.code)
      .json(message.getResponseJson(req.locale, VALCODES.INTERNAL_ERROR));
  },

  /**
  * A CPS (Continuation Passing Style) XML Interface end point for LEAP App Engine
  */
  cpsXMLInterface: async (req, res) => {
    try {
      KPI.emit(KPI_INT_REQ, req.qs.imode);
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("CPS-APP-MAN: Query String:", JSON.stringify(req.qs));
      }
      let ussdRequest = new UssdRequest({
        lastAccessedTime: new Date().getTime(),
        reqSequenceNo: req.body.sequence_number,
        serviceCode: req.body.dest_addr,
        serviceType: req.body.service_type,
        msisdn: req.body.source_addr,
        selectedOption: typeof req.body.msg_content == "string" ? req.body.msg_content : "",
        sourceAddressTON: req.body.source_addr_ton || 0,
        sourceAddressNPI: req.body.source_addr_npi || 0,
        destinationAddressTON: req.body.dest_addr_ton || 0,
        destinationAddressNPI: req.body.dest_addr_npi || 0,
        hlr: req.body.hlr,
        vlr: req.body.vlr,
        lac: req.body.LAC,
        imsi: req.body.IMSI,
        cellId: req.body.CellID,
        gt_addr: req.body.msc_gt_addr,
        PushID: req.body.PushID,
        content: req.qs.MESSAGE
      });

      if (req.query.APPID != null) {
        ussdRequest.setAppId(req.query.APPID);
        ussdRequest.setServiceName("App Push");
      }
      if (ussdRequest.getAppId() == null) {
        if (global.logger.isTraceEnabled()) {
          global.logger.trace("CPS-APP-MAN: App ID Is null, Checking AppId exists in Session store for", ussdRequest.getMsisdn());
        }
        let appId = await UserSessionStore.getAppId(ussdRequest.getMsisdn());
        if (appId != null) ussdRequest.setAppId(appId);
      }
      if (ussdRequest.getAppId() == -1) {
        if (global.logger.isTraceEnabled()) {
          global.logger.trace("CPS-APP-MAN: USSD Flash message", ussdRequest.getContent());
        }
        ussdRequest.addOptionalParam("SENDERADDRESS", req.query.SENDERADDRESS);
        //ussdRequest.addOptionalParam("CAMPAIGNID", this.macros.get(SymbolDolar + "CAMPAIGNID"));
        ussdRequest.addOptionalParam("CPID", req.query.CPID);
        //ussdRequest.addOptionalParam("INTERFACETYPE", this.macros.get(SymbolDolar + "INTERFACETYPE"));
        //ussdRequest.addOptionalParam("LOCATION", this.macros.get(SymbolDolar + "LOCATION"));
        //ussdRequest.addOptionalParam("CAMPAIGNCATEGORY", this.macros.get(SymbolDolar + "CAMPAIGNCATEGORY"));
        //ussdRequest.addOptionalParam("ACCOUNTID", this.macros.get(SymbolDolar + "ACCOUNTID"));
        //ussdRequest.addOptionalParam("USERID", this.macros.get(SymbolDolar + "USERID"));
        return ResponseHandler.sendCpsResponse(res, ussdRequest, CPSContants.BEGIN_NOTIFY);
      }

      if (ussdRequest.getAppId() == null) {
        ussdRequest.setContent(message.getResponseJson(global.config.defaultLanguage, VALCODES.SMPP_SC_NOT_MAPPED).msg);
        if (global.logger.isTraceEnabled()) {
          global.logger.trace("CPS-APP-MAN: Application is not linked to service for shortcode", ussdRequest.getServiceCode());
        }
        return ResponseHandler.sendCpsResponse(res, ussdRequest, CPSContants.BEGIN_NOTIFY);
      }

      let appExists = AppCache.exists(ussdRequest.getAppId());
      if (!appExists) {
        ussdRequest.setContent(message.getResponseJson(global.config.defaultLanguage, VALCODES.SMPP_APP_NOT_MAPPED).msg);

        if (global.logger.isTraceEnabled()) {
          global.logger.trace("CPS-APP-MAN: Application is not linked to service for shortcode", ussdRequest.getServiceCode());
        }
        return ResponseHandler.sendCpsResponse(res, ussdRequest, CPSContants.BEGIN_NOTIFY);
      }

      let startId = AppCache.getStartModuleID(ussdRequest.getAppId());
      const moduleData = AppCache.getModuleData(ussdRequest.getAppId(), startId);
      let qs = {
        ...req.query,
        isNewRequest: (ussdRequest.getServiceType() == CPSContants.BEGIN_REQUEST) ? 1 : 0,
        subscriberInput: ussdRequest.getSelectedOption(),
        imode: INTERFACES.CPS_XML,
        VLR: ussdRequest.getVlr() || "",
        HLR: ussdRequest.getHlr() || ""
      };
      if (qs.isNewRequest == 1) {
        qs.sessionid = utility.getUniqueTxnId();
        qs.transactionid = qs.sessionid;
      }

      qs[moduleData.settings.aparty] = ussdRequest.getMsisdn();

      if (global.logger.isTraceEnabled()) {
        global.logger.trace("CPS-APP-MAN: QUERY PARAMS:", JSON.stringify(qs));
      }
      const session = await SessionManager.getExistingOrCreateNew(ussdRequest.getAppId(), ussdRequest.getMsisdn(), qs);
      ussdRequest.setSessionId(session.getSessionId());
      session.setCallback(res);
      session.setUssdRequest(ussdRequest);
      return session.start();
    } catch (e) {
      global.logger.error("Failed to process Input request", e);
      return res.status(HTTPCODES.internalServerError.code).send(e);
    }
  }
};

function getValueFromJson(json, path) {
  // Split the path into components and handle array indices
  const keys = path.split(/[\.\[\]]+/).filter(Boolean);

  // Recursive function to navigate through the json
  function navigate(obj, keyParts) {
    // Base case: If the key part list is empty, return the object
    if (keyParts.length === 0) {
      return obj;
    }

    // Get the first key part and the rest of the path
    const firstKey = keyParts.shift();

    // Check if the key is an array index or object key
    if (Array.isArray(obj) && /^\d+$/.test(firstKey)) {
      // Convert to integer if it is an array index
      const index = parseInt(firstKey, 10);
      if (index < 0 || index >= obj.length) {
        throw new Error(`Array index out of bounds: ${firstKey}`);
      }
      return navigate(obj[index], keyParts);
    } else if (firstKey in obj) {
      // Proceed if it is an object key
      return navigate(obj[firstKey], keyParts);
    } else {
      throw new Error(`Key not found: ${firstKey}`);
    }
  }

  // Start the navigation through the JSON object
  return navigate(json, keys);
}