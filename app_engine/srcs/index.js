"use strict";
/***
 *
 *  LEAP application platform -- entry point file.
 *  Initiates a web services to handle requests on apps.
 *
 *  Basic steps here:
 *  1.  Scan the apps directory and load available apps.
 *  2.  Start web service and be ready.
 *
 ***/

// Essential required modules
const path = require("path");
const fs = require("fs");
const args = require("commander");
const cluster = require("cluster");
const express = require("express");
const ConfigTree = require("config-tree");
const utility = require("utility");
const xml2json = require("xml2json");
const tls = require("tls");
const net = require('net');

global.health = { report: {} };
const { constants } = require("crypto");
const KPI = require("app_kpi");
const OAM = require("oam");
const bodyParser = require("body-parser");
const http = require("http");
const https = require("https")
const os = require("os");
const common = require("common");
const message = require("message");
const whiteboard = common.whiteboard;
const redis_man = common.redis_man;
const HTTPCODES = common.http_codes;

const ENUMS = require("./lib/enums");
const INTERFACES = ENUMS.INTERFACES;

global.logger = console;
global.componentName = "app_engine";
global.leap_settings = require("../config/settings.json");

/**
 * Module variable definitions here.
 * Note: The modules may be initialized *later*, after initBasic() succeeds.
 * This helps us to "require" the modules in a synchronous manner
 * without side-effects.
 **/
initBasic()
  .then(function () {
    global.logger.warn("Checking connectivity with app_store");
    return checkTelnetConnection(global.config.app_store.host, global.config.app_store.port);
  })
  .then(function () {
    global.logger.warn("Checking connectivity with pm_fsync");
    return checkTelnetConnection(global.config.pm_fsync.host, global.config.pm_fsync.port);
  })
  .then(function () {
    global.logger.warn("Finished with basic initialization.");
    global.logger.warn("------------------------------------");

    // Find out if we have to run in single-proc mode or cluster-mode.
    global.clusterSize = getClusterSize(); // Ensure this function is defined or imported
    if (global.clusterSize > 1) {
      return startServer(global.clusterSize); // Use global.clusterSize
    } else {
      // If cluster size is 1, run in single mode
      return startServer(1);
    }
  })
  .catch(function (e) {
    global.logger.error("error while starting LEAP platform...", e);
    OAM.raiseCriticalAlert("app_engine")
      .then(() => {
        console.error(new Date() + " NEEDS ATTENTION: before process exit in init basic then block, process id:::" + process.pid + " and process ppid: " + process.ppid);
        global.logger.error("NEEDS ATTENTION: before process exit in init basic then block, process id:::" + process.pid + " and process ppid: " + process.ppid);
        process.exit(1);
      })
      .catch(err => {
        console.error(new Date() + " NEEDS ATTENTION: before process exit in init basic catch block, process id:::" + process.pid + " and process ppid: " + process.ppid);
        global.logger.error("NEEDS ATTENTION: before process exit in init basic catch block, process id:::" + process.pid + " and process ppid: " + process.ppid);
        console.error("Failed to raise critical alert for App-Engine process", err);
        process.exit(1);
      });
  });


/**
 * Performs level-0 initialization, which include:
 * => reading / parsing command-line arguments
 * => Setting up global vars for other files / modules
 * => Reading configuration from config server
 **/
async function initBasic() {
  global.clusterId = 1;
  args
    .version(common.version)
    .option("-H, --host [host]", "Config server host -- defaults to", process.env.CONFIG_HOST || "127.0.0.1")
    .option("-p, --port [port]", "Config server port -- defaults to", process.env.CONFIG_PORT || 6380)
    .option("-n, --db [db]", "Config server db index -- defaults to", process.env.CONFIG_DB || 0)
    .option("-a, --password [password]", "config server password", process.env.CONFIG_AUTH || undefined)
    .option("-l, --log [level]", "Config server log level -- defaults to", "ERROR")
    .option("-t, --type [type]", "Type of App Engine server -- defaults to", process.env.LEAP_APP_ENGINE || "development")
    .option("-d, --dump [dump]", "Type of App Engine server -- defaults to", null)
    .parse(process.argv);
  printAppInfo();
  global.args = args.opts();
  global.mode = global.args.type;
  global.logger.info("App Engine is runnging as %s server", global.mode);
  setServerMode();
  global.opts = {
    args: global.args,
    configTasks: [
      "getDBInfo",
      "getModuleInfoEx",
      "getGlobalInfo"
    ],
    keys2read: {
      getDBInfo: ["app_store"],
      getModuleInfoEx: ["app_engine", "app_engine_" + global.args.type, "pm_fsync", "acl_manager"],
      getGlobalInfo: ["whiteboard", "timezone", "kpi", "oam", "certificates", "security", "authSecret"]
    }
  };
  const ConfigProxy = new ConfigTree(global.opts);
  return ConfigProxy.readConfig()
    .then(async () => {
      setConfigurations();
      ConfigProxy.on("reload_config", async (pattern, channel, key) => {
        try {
          console.log("CONFIG_CHANGE_EVENT", pattern, channel, key);
          if (channel.includes("system:settings:components:app_engine")) {
            setConfigurations();
          }
        } catch (e) {
          console.error("Failed reload the Config", e);
        }
      });
    })
    .catch(e => {
      global.logger.error("Failed to start LEAP Platform, Connection to:", global.args.host, global.args.port, "Failed", e);
      OAM.emit("criticalAlert", "app_engine_configserver_conn");
      process.exit(1);
    });
}

function setConfigurations() {
  global.config.instance = global.config["app_engine_" + global.mode];
  delete require.cache[require.resolve("./logger")];
  require("./logger").init();
  global.config.defaultLanguage = global.config.app_engine.defaultLanguage;

  if (global.config.app_engine.plugin_exec) {
    global.httpClientSettings = global.config.app_engine.plugin_exec.client;
    global.peThrottleValue = global.config.app_engine.plugin_exec.thresholdValue;
  } else {
    global.httpClientSettings = {
      maxSockets: 100,
      keepAlive: true,
      maxFreeSockets: 10
    };
    global.peThrottleValue = -1;
  }
  console.log("Plugin execution Throttle Value:", global.peThrottleValue);
}

function setServerMode() {
  global.isDevServer = false;
  global.isStagingServer = false;
  global.isProductionServer = false;
  switch (global.mode) {
    case "development": global.isDevServer = true;
      break;
    case "staging": global.isStagingServer = true;
      break;
    case "production": global.isProductionServer = true;
      break;
    default:
      global.logger.error("You have entered Invalid Execution Mode- Possible values are development, staging and production");
      process.exit(1);
  }
}
/**
 * Runs the initializer code for each of the worker's instance
 * (if running in cluster mode),
 * or runs this code, directly if running in non-cluster mode.
 **/
async function initCall() {
  try {
    global.logger.warn("Config loaded successfully");
    await OAM.init(global.config.oam);
    if (global.isProductionServer) {
      await KPI.init({
        app_engine_kpi_enabled: true,
        ...global.config.kpi,
        interval: 1000,
        policy: "file",
        thresholdHeapMemory: global.leap_settings.thresholdHeapMemory
      });
    }
    OAM.emit("clearAlert", "app_engine_configserver_conn");
    whiteboard.init(global.config.whiteboard);
    await require("./lib/cdrqueue").init();
    await require("./cache/UserSessionStore").init();
    await require("pluginmanager").init();

    await require("./lib/plugin_settings").init((global.isProductionServer) ? "prod" : "dev");
    await require("app_store").init();
    if (global.config.app_engine.userPreferenceStoreCheck)
      await require("preference_store").init();

    const AppCache = require("./cache/AppCache");
    await AppCache.registerEvents();
    await AppCache.loadApps();

    if (global.isProductionServer) {
      const UssdServiceCache = require("./cache/UssdServiceCache");
      await UssdServiceCache.registerEvents();
      await UssdServiceCache.loadUssdServices();
      const UssdGateWayService = require("./ussd_service/index");
      UssdGateWayService.init();
    }

    global.logger.warn("Apps loading succeeded, Total Apps: " + AppCache.totalApps());
    message.init({
      autoReload: true,
      defaultLocale: global.config.defaultLanguage,
      defaultKey: "E9000"
    });

    whiteboard.on("health_check", msg => {
      global.logger.warn("Requested for health report", msg);
      if (msg == "all" || msg == os.hostname()) {
        global.health.hostname = os.hostname();
        global.health.cid = global.clusterId;
        global.health.mode = global.mode;
        global.health.nid = global.nodeid;
        global.health.updatedAt = Date.now();
        global.health.report.redis = redis_man.health();
        whiteboard.publish("health_report", JSON.stringify(global.health));
      }
    });
    whiteboard.subscribe("health_check");

    await startWebServer();
    OAM.emit("clearAlert", "app_engine");
  } catch (e) {
    console.error("Error while starting APP Engine. ", e);
    OAM.emit("criticalAlert", "app_engine");
    process.exit(1);
  }
}

function createServer(app) {
  global.logger.info("Preparing AppEngine server...");
  return http.createServer(app);
}

function createSecureServer(app) {
  global.logger.info("Preparing secure AppEngine server...");
  return https.createServer({
    key: fs.readFileSync(path.resolve(global.config.certificates.key), "utf8"),
    cert: fs.readFileSync(path.resolve(global.config.certificates.cert), "utf8"),
    secureOptions: constants.SSL_OP_NO_SSLv2 | constants.SSL_OP_NO_SSLv3 | constants.SSL_OP_NO_TLSv1 | constants.SSL_OP_NO_TLSv1_1,
    ciphers: "ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:",
    honorCipherOrder: true
  }, app);
}

function locale_settings(req, res, next) {
  req.locale = req.query.locale || req.query.language || global.config.defaultLanguage;
  next();
}

function cps_decoder(req, res, next) {
  req.qs = {
    txnId: req.txnId,
    appId: req.params.appId,
    imethod: req.method,
    imode: INTERFACES.CPS_XML,
    ...req.query
  };

  if (req.headers["content-type"].includes("xml")) {
    req.body = xml2json.toJson(req.body);
    req.body = JSON.parse(req.body)["cps-message"];
  }
  next();
}

function pre_process(req, res, next) {
  req.qs = {
    txnId: req.txnId,
    appId: req.params.appId,
    imethod: req.method,
    imode: INTERFACES.QUERY_STRING,
    ...req.headers,
    ...req.query
  };

  if (req.method == "POST") {
    if (req.headers["content-type"].includes("json")) {
      req.qs = {
        ...req.qs,
        ...req.body
      };
      req.qs.contentType = "json";
      req.qs.imode = INTERFACES.HTTP_JSON
    }
    else if (req.headers["content-type"].includes("x-www-form-urlencoded")) {
      req.qs = {
        ...req.qs,
        ...req.body
      };
    }
  }
  next();
}

function routers() {
  const Router = express.Router();
  const AppManager = require("./app_man");
  const contextPath = global.config.instance.contextPath + "/:appId";
  global.logger.warn("Restifying the engine with contextPath:", contextPath);
  Router.post("/cpsxml", locale_settings, cps_decoder, AppManager.cpsXMLInterface);
  Router.post("/nexmo", locale_settings, pre_process, AppManager.nexmoInterface);
  Router.get("/apppush", locale_settings, AppManager.appPushInterface);
  Router.get("/ussdpush", locale_settings, AppManager.ussdPushInterface);
  Router.post("/ussdpush", locale_settings, AppManager.ussdPushInterface);
  Router.post("/session_extender", AppManager.sessionExtender);
  Router.get(contextPath, locale_settings, pre_process, AppManager.httpInterface);
  Router.post(contextPath, locale_settings, pre_process, AppManager.httpInterface);
  return Router;
}

async function startWebServer() {
  const app = express();
  app.disable("x-powered-by");
  app.use(utility.setTxnId);
  app.use(utility.filterHttpMethods);
  app.use(utility.set_cors_headers);
  app.set("etag", false); // turning off etag
  app.use(bodyParser.text({ type: "application/xml" }));
  app.use(bodyParser.urlencoded({
    extended: true
  }));
  app.use(bodyParser.json());
  if (global.config.instance.log.enable_access_log == true) {
    app.use(global.logger.morgan);
    console.log("Engine access log is enabled");
  } else {
    console.log("Engine access log is disabled.No access logs will be written");
  }
  app.use("/", routers());
  app.use("/docs", express.static(path.join(__dirname, "../apidoc")));
  if (global.leap_settings.isHeapdumpApiEnabled) {
    app.get("/heapdump", (req, res) => {
      const heapdump = require("heapdump");
      let filename = path.join(global.config.instance.log.logdir, `heapDump-${Date.now()}.heapsnapshot`);
      heapdump.writeSnapshot(filename, err => {
        console.log("Heap dump of a bloated server written to", filename);
        res.status(HTTPCODES.ok.code).send({ msg: "successfully took a heap dump", heapdump: filename });
      });
    });
  }
  app.use((req, res, next) => {
    res.status(HTTPCODES.resourceNotFound.code).json(HTTPCODES.resourceNotFound);
  });
  app.use((err, req, res, next) => {
    res.status(HTTPCODES.internalServerError.code)
      .json(HTTPCODES.internalServerError);
  });

  let protocol = global.config.instance.protocol;
  let server = (protocol == null || protocol != "https") ? createServer(app) : createSecureServer(app);

  if (global.isDevServer) {
    await require("./websocket/WebsocketManager").init(server);
  }

  let port = global.config.instance.port;
  server.listen(port, () => {
    let identifier = "engine_" + (process.env.NODE_NAME || os.hostname());
    global.nodeid = identifier + "_" + port + "_" + global.clusterId;
    global.logger.warn("ProcessID:", process.pid, ", Node identifier: ", global.nodeid, ", Mode:", global.mode);
    let cid = cluster.worker && cluster.worker.id || 1;
    global.logger.warn(cid, "AppExecution Service running at port: " + port);
    console.warn(cid, "AppExecution Service running at port: " + port);
    if (global.leap_settings.isMetricsEnabled) {
      const appmetrics = require("appmetrics-dash");
      appmetrics.monitor({
        title: "App Engine Metrics-" + global.mode,
        url: "/metrics",
        docs: "/docs",
        server: server
      });
    }
  });
}

function getClusterSize() {
  global.cpus = os.cpus().length;
  console.log("Total number of vCPUs:", global.cpus);
  let configuredCPUs = 1;
  if (global.isProductionServer) {
    if (global.config.instance.clusterCPUs > global.cpus) {
      configuredCPUs = global.cpus;
    } else {
      configuredCPUs = global.config.instance.clusterCPUs;
    }
  }
  console.log("Starting with Cluster size:", configuredCPUs);
  return Math.min(configuredCPUs, global.cpus);
}

function startServer(clusterSize = 1) {
  if (clusterSize > 1) {
    global.logger.warn("Running the app_engine in cluster mode...");
  } else {
    global.logger.warn("Running the app_engine in single-proc mode...");
  }

  if (cluster.isMaster && clusterSize > 1) {
    global.logger.info("Master cluster setting up " + clusterSize + " workers...");
    for (let i = 0; i < clusterSize; i++) {
      cluster.fork();
    }

    cluster.on("online", function (worker) {
      global.logger.info("Worker " + worker.process.pid + " is online");
    });

    cluster.on("exit", function (worker, code, signal) {
      global.logger.info("Worker " + worker.process.pid + " died with code: " + code + ", and signal: " + signal);

      // Restrict the loop to the object's own properties
      for (let id in cluster.workers) {
        if (Object.prototype.hasOwnProperty.call(cluster.workers, id)) {
          cluster.workers[id].kill();
        }
      }

      // exit the master process
      process.exit(0);
    });
  } else {
    global.clusterId = (cluster.worker) ? cluster.worker.id : 1;
    initCall();
  }
}


/**
 *  Prints application title and version.
 **/
function printAppInfo() {
  if (cluster.isMaster) {
    let banner = fs.readFileSync(path.join(__dirname, "../banner.txt")).toString();
    console.log(banner);
  }
}

function checkTelnetConnection(host, port) {
  return new Promise((resolve, reject) => {
    const socket = new net.Socket();
    socket.setTimeout(5000); // Set timeout to 5 seconds

    socket.once('connect', () => {
      console.log(`Connection successful to ${host}:${port}`);
      socket.destroy();
      resolve();
    });

    socket.once('error', (err) => {
      console.error(`Connection failed to ${host}:${port} - ${err.message}`);
      socket.destroy();
      reject(err);
    });

    socket.once('timeout', () => {
      console.error(`Connection timeout to ${host}:${port}`);
      socket.destroy();
      reject(new Error('Connection timeout'));
    });

    socket.connect(port, host);
  });
}

process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);

process.on("uncaughtException", (e) => {
  global.logger.error("Uncaught Expection", e);
});

process.on("unhandledRejection", (reason, p) => {
  global.logger.error("Unhandled Rejection", reason, p);
});

function shutdown() {
  global.logger.error("Received kill signal. Initiating shutdown...");
  OAM.init(global.config.oam);
  OAM.raiseCriticalAlert("app_engine")
    .then(() => {
      process.exit(1);
    }).catch(err => {
      console.error("Failed to Critical alert for App-Engine process", err);
      process.exit(1);
    });
}
