"use strict";
const AppStore = require("app_store");
const common = require("common");
const BPromise = require("bluebird");
const KPI = require("app_kpi");
const whiteboard = common.whiteboard;
const KPI_KEY = KPI.KEYS.appCache;
const UssdGatewayInfo = require("../ussd_service/models/UssdGatewayInfo");

const gateway_sessions = {};

class UssdServiceCache {

  constructor() {
    this.ussd_services = {};
    this.gw_services = {};
  }

  async registerEvents() {
    global.logger.warn("Initializing the Ussd Service Cache");

    whiteboard.on("mount_ussdsc", async (shortcode) => {
      try {
        let ussdServiceInfo = await getUssdServiceInfo(shortcode);
        if (ussdServiceInfo.status == 1 && ussdServiceInfo.shortcode != -1) {
          global.logger.warn("Mounting the ShortCode %s:%s into UssdServiceCache", ussdServiceInfo.id, ussdServiceInfo);
          this.ussd_services[ussdServiceInfo.shortcode] = ussdServiceInfo;
        }
      } catch (e) {
        global.logger.error("Failed to Mount UssdService:", shortcode, e);
      }
    });

    whiteboard.on("unmount_ussdsc", async (shortcode) => {
      try {
        if (this.ussd_services.hasOwnProperty(shortcode)) {
          global.logger.warn("Unmounting the UssdService %s:%s from UssdServiceCache", shortcode, this.ussd_services[shortcode].shortcode);
          delete this.ussd_services[shortcode];
        }
      } catch (e) {
        global.logger.error("Failed to Unmount UssdService:", shortcode, e);
      }
    });
    whiteboard.subscribe("mount_ussdsc");
    whiteboard.subscribe("unmount_ussdsc");
  }

  async loadUssdServices() {

    let ussdServices = null, startTime = new Date().getTime();
    try {
      global.logger.warn("Loading ussdServices ...");
      const list = await AppStore.listUssdServices();

      let indexes = [];
      for (let i = 0; i < list.length; i++) {
        indexes.push(i);
      }

      this.ussd_services = {};
      ussdServices = await BPromise.reduce(indexes, findAndLoadUssdServices, list)
        .then(result => {
          for (let i = 0; i < result.length; i++) {
            if (result[i].status == 1 && result[i].appId != -1) {
              if (global.logger.isTraceEnabled()) {
                global.logger.trace("Loading UssdService " + result[i].shortcode);
              }
              this.ussd_services[result[i].shortcode] = result[i];
            }
          }
          return this.ussd_services;
        });
    } catch (e) {
      global.logger.error("Failed to loadUssdServices:", e);
    }
    try {
      this.gw_services = {};
      let gateway_configs = await AppStore.listUssdGatewayConfigs();

      let indexes = [];
      for (let i = 0; i < gateway_configs.length; i++) {
        indexes.push(i);
      }

      await BPromise.reduce(indexes, findAndLoadUssdGateway, gateway_configs)
        .then(result => {
          for (let i = 0; i < result.length; i++) {
            if (result[i].status == 1) {
              if (global.logger.isTraceEnabled()) {
                global.logger.trace("Loading GatewayConfig " + result[i].name);
              }
              this.gw_services[result[i].id] = new UssdGatewayInfo(result[i]);
            }
          }
          return this.gw_services;
        });

    } catch (e) {
      global.logger.error("Failed to Retrieve GatewayConfigs:", e);
    }
    KPI.emit(KPI_KEY, startTime, new Date().getTime(), "loadUssdServices");
    return ussdServices;
  }

  findUssdServiceInfo(input) {
    if (input != null) {
      if (this.ussd_services.hasOwnProperty(input)) {
        return this.ussd_services[input];
      } else {
        let delim = global.config.shortCodeDelimiter && global.config.shortCodeDelimiter[0] || "*";
        let sc = input.split(delim)[0];
        return this.ussd_services[sc];
      }
    }
  }

  listShortCodes() {
    return Object.keys(this.ussd_services);
  }

  setGatewaySessionInfo(gatewayInfo, session) {
    gateway_sessions[gatewayInfo.id] = {
      gatewayInfo,
      session
    }
  }

  getGatewaySessionInfo(id) {
    if (gateway_sessions.hasOwnProperty(id)) {
      return gateway_sessions[id].session;
    }
    return null;
  }

  getExactServiceCode(ussdRequest) {
    let delim = global.leap_settings.defaults.ussd_servicecode_delimeter || "*";
    let serviceCodesList = this.listShortCodes();
    if (serviceCodesList.length === 0) {
      return null;
    }
    let serviceCode = ussdRequest && ussdRequest.getServiceCode();
    if (serviceCode == null || serviceCode == -1 || serviceCode.trim().length === 0) {
      return null;
    }
    serviceCode = serviceCode.trim();
    if (serviceCode.startsWith(delim)) {
      serviceCode = serviceCode.substring(1);
    }

    let finalServiceCode = null;
    if (serviceCode.includes(delim)) {
      let splittedServiceCode = serviceCode.split(delim);
      let tmpServiceCode = "";
      for (let scode in splittedServiceCode) {
        if (tmpServiceCode.trim().length > 0) {
          tmpServiceCode += delim;
        }
        tmpServiceCode += scode;
        if (serviceCodesList.includes(tmpServiceCode)) {
          finalServiceCode = tmpServiceCode;
        }
      }
    }
    else {
      finalServiceCode = serviceCode;
    }
    return finalServiceCode;
  }

  listGatewayConfigs() {
    return this.gw_services;
  }

  getGatewayInfo(id) {
    return this.gw_services[id];
  }

  setGatewayConfig(id, info) {
    if (info.status == 1)
      this.gw_services[id] = new UssdGatewayInfo(info);
  }

  deleteGatewayInfo(id) {
    delete this.gw_services[id];
  }
}

module.exports = new UssdServiceCache();

async function findAndLoadUssdServices(list, index) {
  let ussdService;
  try {
    ussdService = list[index].id;
    const ussdServicesInfo = await getUssdServiceInfo(ussdService);
    if (ussdServicesInfo != null && ussdServicesInfo.status == 1) {
      list[index] = ussdServicesInfo;
    } else {
      global.logger.error("ShortCode:" + ussdService + " is not ready to load because of some errors");
    }
  } catch (e) {
    global.logger.error("Failed to findAndLoadUssdServices:", ussdService, e);
  }
  return list;
}

async function findAndLoadUssdGateway(list, index) {
  let gatewayId;
  try {
    gatewayId = list[index].id;
    const ussdServicesInfo = await getUssdGatewayInfo(gatewayId);
    if (ussdServicesInfo != null && ussdServicesInfo.status == 1) {
      list[index] = ussdServicesInfo;
    } else {
      global.logger.error("Gateway connfig:" + gatewayId + " is not ready to load because of some errors");
    }
  } catch (e) {
    global.logger.error("Failed to findAndLoadUssdGateway:", gatewayId, e);
  }
  return list;
}

async function getUssdServiceInfo(shortcode) {
  try {
    return await AppStore.findUssdService(shortcode);
  } catch (e) {
    global.logger.error("Failed to Retrieve ShortCodeInfo:", shortcode, e);
    return null;
  }
}

async function getUssdGatewayInfo(gatewayId) {
  try {
    return await AppStore.findUssdGatewayConfig(gatewayId);
  } catch (e) {
    global.logger.error("Failed to Retrieve UssdGatewayInfo:", gatewayId, e);
    return null;
  }
}
