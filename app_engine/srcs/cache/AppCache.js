"use strict";
const PM = require("pluginmanager");
const AppStore = require("app_store");
const common = require("common");
const BPromise = require("bluebird");
const KPI = require("app_kpi");
const whiteboard = common.whiteboard;
const KPI_KEY = KPI.KEYS.appCache;
const _ = require("lodash");
const NexmoMapping = require("../../config/nexmo.json").keywordMapping;
const UUID = require("uuid").v4;

class AppCache {

  constructor() {
    this.apps = {};
    this.appNameIdMapping = {};
    this.appLocaleCache = {};
    this.appEndSchema = {};
    this.appsNotInAllowedState = {};
  }

  async registerEvents() {
    global.logger.warn("Initializing the App Cache");
    this.appEndSchema = await PM.getMetaData("appEnd");

    whiteboard.on("mount_app", async (appId) => {
      try {
        let app = await getAppData(appId);
        if (app != null) {
          if (isAlowed(app.status)) {
            global.logger.warn("Mounting the App %s:%s into AppCache", app.id, app.name);
            this.apps[app.id] = app;
            this.appNameIdMapping[app.name] = app.id;
            this.appLocaleCache[app.id] = {};
            delete this.appsNotInAllowedState[app.id];
          } else {
            this.appsNotInAllowedState[app.id] = app.name;
          }
        }
      } catch (e) {
        global.logger.error("Failed to Mount App:", appId, e);
      }
    });

    whiteboard.on("unmount_app", async (appId) => {
      try {
        if (this.apps.hasOwnProperty(appId)) {
          global.logger.warn("Unmounting the App %s:%s from AppCache", appId, this.apps[appId].name);
          delete this.appNameIdMapping[this.apps[appId].name];
          delete this.apps[appId];
          delete this.appLocaleCache[appId];
          delete this.appsNotInAllowedState[appId]
        }
      } catch (e) {
        global.logger.error("Failed to Unmount App:", appId, e);
      }
    });
    whiteboard.subscribe("mount_app");
    whiteboard.subscribe("unmount_app");
  }

  totalApps() {
    let startTime = new Date().getTime();
    let len = Object.keys(this.apps).length;
    KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getAppsCount");
    return len;
  }

  getAppByName(name) {
    return this.getApp(this.appNameIdMapping[name]);
  }

  findNexmoAppId(keyword) {
    if (keyword) {
      keyword = String(keyword).toLowerCase();
      if (NexmoMapping.hasOwnProperty(keyword)) {
        return NexmoMapping[keyword].appId;
      }
      let keys = Object.keys(NexmoMapping);
      for (let i = 0; i < keys.length; i++) {
        let key = keys[i];
        if (keyword.includes(key)) return NexmoMapping[key].appId;
      }
    }
    return NexmoMapping.default.appId;
  }

  getApp(appId) {
    try {
      let startTime = new Date().getTime();
      let app = _.cloneDeep(this.apps[appId]);
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "get");
      return app;
    } catch (error) {
      // ignore errors
    }
    return null;
  }

  exists(appId) {
    try {
      let startTime = new Date().getTime();
      let app = this.apps[appId] && this.apps[appId].appData != null;
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "exists");
      return app;
    } catch (error) {
      // ignore errors
    }
    return null;
  }

  getStartModuleID(appId) {
    try {
      let startTime = new Date().getTime();
      let startId = this.apps[appId] && this.apps[appId].appData != null && this.apps[appId].appData.startId;
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "startId");
      return startId;
    } catch (error) {
      // ignore errors
    }
    return null;
  }

  getAppOwner(appId) {
    try {
      let startTime = new Date().getTime();
      let ngage_id = this.apps[appId] && this.apps[appId].ngage_id;
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getAppOwner");
      return ngage_id;
    } catch (error) {
      // ignore errors
    }
    return null;
  }

  getEndModuleID(appId) {
    try {
      let startTime = new Date().getTime();
      let endId = this.apps[appId] && this.apps[appId].appData != null && this.apps[appId].appData.endId;
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "endId");
      return endId;
    } catch (error) {
      // ignore errors
    }
    return null;
  }

  checkAppInNotAllowedState(appId) {
    try {
      let startTime = new Date().getTime();
      let app = this.appsNotInAllowedState[appId];
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getNotAllowedApp");
      return app;
    } catch (error) {
      // ignore errors
    }
    return null;
  }

  getModuleData(appId, moduleId) {
    let startTime = new Date().getTime(), moduleData = null;
    let app = this.apps[appId];
    try {
      if (app.appData.modules.hasOwnProperty(moduleId)) {
        moduleData = _.cloneDeep(app.appData.modules[moduleId]);
      }
    } catch (error) {
      //ignore
    }
    KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getModule");
    return moduleData;
  }

  getEndModuleSchema() {
    return this.appEndSchema;
  }

  getAppLocaleTemplate(appId, locale) {
    if (locale != null) {
        locale = String(locale);
    }
    let startTime = new Date().getTime(), localContext;
    return new Promise((resolve, reject) => {
        // Use an async function to handle the await
        (async () => {
            try {
                if (this.appLocaleCache === null) this.appLocaleCache = {};
                if (this.appLocaleCache.hasOwnProperty(appId) && this.appLocaleCache[appId].hasOwnProperty(locale)) {
                    if (this.appLocaleCache[appId][locale] != null) {
                        localContext = _.cloneDeep(this.appLocaleCache[appId][locale]);
                    } else {
                        localContext = _.cloneDeep(this.appLocaleCache[appId][global.config.defaultLanguage]);
                    }
                } else {
                    if (this.appLocaleCache[appId] === null) this.appLocaleCache[appId] = {};
                    this.appLocaleCache[appId][locale] = await AppStore.getAppLocaleTemplate(appId, locale);
                    if (typeof this.appLocaleCache[appId][locale].translation === "string") {
                        this.appLocaleCache[appId][locale].translation = JSON.parse(this.appLocaleCache[appId][locale].translation);
                    }
                    localContext = _.cloneDeep(this.appLocaleCache[appId][locale]);
                }
                KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getAppLocaleTemplate");
                resolve(localContext);
            } catch (e) {
                global.logger.error("Failed to getAppLocaleTemplate:", e);
                reject(e);
            }
        })(); // Immediately invoke the async function
    });
}


  async loadApps() {

    let apps = null, startTime = new Date().getTime();
    try {
      global.logger.warn("Loading applications ...");
      let list;
      if (global.isDevServer) {
        list = await AppStore.listApps(null);
        list = list.rows;
      }
      else {
        list = await AppStore.listLiveApps(null);
      }
      let indexes = [];
      for (let i = 0; i < list.length; i++) {
        indexes.push(i);
      }
      apps = await BPromise.reduce(indexes, findAndLoadApps, list)
        .then(result => {
          for (let i = 0; i < result.length; i++) {
            if (isAlowed(result[i].status)) {
              if (global.logger.isTraceEnabled()) {
                global.logger.trace("Loading app " + result[i].name);
              }
              this.apps[result[i].id] = result[i];
              this.appNameIdMapping[result[i].name] = result[i].id;
              this.appLocaleCache[result[i].id] = {};
            } else {
              if (global.logger.isTraceEnabled()) {
                global.logger.trace("App(" + result[i].name + ") not ready for execution");
              }
              this.appsNotInAllowedState[result[i].id] = result[i].name;
            }
          }
          return this.apps;
        });

    } catch (e) {
      global.logger.error("Failed to loadApps:", e);
    }
    KPI.emit(KPI_KEY, startTime, new Date().getTime(), "loadApps");
    return apps;
  }

  listAppIds() {
    return Object.keys(this.apps);
  }
}

module.exports = new AppCache();

function isAlowed(status) {

  try {
    let filterConfig = global.config.app_engine.app_cache && global.config.app_engine.app_cache.filter;
    return filterConfig[global.mode].split(",").includes(String(status).trim());
  } catch (error) {
    global.logger.error("Failed to LoadApp", error);
  }
  return false;
}
async function findAndLoadApps(list, index) {
  let appId;
  try {
    appId = list[index].id;
    const app = await getAppData(appId);
    if (app != null && app.appData != null) {
      list[index] = app;
    } else {
      global.logger.error("AppId:" + appId + " is not ready to load because of some errors");
    }
  } catch (e) {
    global.logger.error("Failed to findAndLoadApps:", appId, e);
  }
  return list;
}

async function getAppData(appId) {
  try {
    let app;
    if (global.isDevServer) {
      app = await AppStore.findApp(appId)
    }
    else {
      app = await AppStore.findLiveApp(appId)
    }
    if (app != null && app.appData != null && app.appData.modules != null) {
      app.config = {};
      let keys = Object.keys(app.appData.modules);
      for (let i = 0; i < keys.length; i++) {
        let moduleData = app.appData.modules[keys[i]];
        switch (moduleData.type) {
          case "appEnd": {
            app.appData.endId = keys[i];
            if (moduleData.process.success != null) {
              moduleData.process.success.code = expandErrorCodes(moduleData.process.success.code);
            }
            if (moduleData.process.customErrors != null) {
              moduleData.process.customErrors.forEach(customError => {
                customError.code = expandErrorCodes(customError.code);
              });
            }
          }
            break;
          case "appConfig": {
            try {
              app.config["$" + moduleData.settings.alias] = JSON.parse(moduleData.process.query);
            } catch (error) {

            }
          }
            break;
        }
        if (moduleData.type == 'menu') {
          let menuNavigationOptions = {
            "mainMenu": {
              "code": "0",
              "message": "Main Menu",
              "hintMessage": "Main Menu"
            },
            "backwardSkipMenu": {
              "code": "s",
              "message": "Backward skip",
              "hintMessage": "Backward skip"
            },
            "firstPage": {
              "code": "f",
              "message": "First page",
              "hintMessage": "First page"
            },
            "lastPage": {
              "code": "l",
              "message": "Last page",
              "hintMessage": "Last page"
            },
            "previousPage": {
              "code": "b",
              "message": "Prev page",
              "hintMessage": "Prev page"
            },
            "nextPage": {
              "code": "*",
              "message": "Next page",
              "hintMessage": "Next page"
            }
          }
          let sampleJson = {
            "id": "Menu Item; #5d0aeea0-7e13-11ee-bb9d-7dce78fb4875",
            "uid": "5d0aeea0-7e13-11ee-bb9d-7dce78fb4875",
            "name": "UNO",
            "type": "CHOICE",
            "update": false,
            "children": {
              "items": []
            },
            "enabled": true
          }
          moduleData.typeId = "0.3";
          moduleData.type = "appMenu";
          let menuItems = moduleData.process.menuItems;
          let menuArray = [];
          for (var j = 0; j < menuItems.length; j++) {
            let menuName = menuItems[j].menuName;
            let uuid = UUID();
            menuArray.push({
              ...sampleJson,
              "name": menuName,
              "id": "Menu Item; #" + uuid,
              "uid": uuid,
              "menuProperty": {
                "0": {
                  "key": moduleData.process.variable_name,
                  "value": menuItems[j].menuValue
                }
              }
            })
          }
          moduleData.process = {
            "menu": {
              "children": {
                "items": menuArray
              }
            }
          }
          moduleData.menuNavigationOptions = moduleData.process.menuNavigationOptions;
          global.logger.error("Elson debug:" + JSON.stringify(moduleData.process));
        }
        moduleData.output.customCode = '';
        moduleData.output.customCode += functionStartString();
        let customCode = '';
        if (parseInt(moduleData.typeId, 10) == 1) {
          customCode += setVariables(moduleData.coordinates.nodeData.id);
        }
        if (moduleData.typeId == "0.1") {
          customCode += getAppStartCode();
        }
        if (moduleData.isChoiceLinked) {
          let type = moduleData.type;
          let choiceModuleId = moduleData.output.customCodeIds.conditionalLink[0];
          let choiceInformation = app.appData.modules[choiceModuleId];
          let match_conditions = choiceInformation.process.match_conditions;
          for (let i = 0; i < match_conditions.length; i++) {
            let conditionPair = match_conditions[i];
            customCode += appendCondition(conditionPair.key, conditionPair.condition, conditionPair.value, conditionPair.moduleId, type, conditionPair.id);
          }
          customCode += elseConditonString(choiceInformation.process.no_match_module_id, choiceModuleId);
        }
        else if (moduleData.isScriptLinked) {
          let scriptModuleId = moduleData.output.customCodeIds.conditionalLink[0];
          let scriptInformation = app.appData.modules[scriptModuleId];
          customCode += scriptInformation.process.code;
        }
        else if (moduleData.isRepeatLinked) {
          let retryModuleId = moduleData.output.customCodeIds.conditionalLink[0];
          let retry_information = app.appData.modules[retryModuleId];
          let retry_count = retry_information.process.repeat_count;
          let current_module_id = moduleData.coordinates.nodeData.id;
          let next_module_id = retryModuleId;
          customCode += getRetryCode(retry_count, current_module_id, next_module_id);
        }
        else {
          if (moduleData && moduleData.output && moduleData.output.customCodeIds && moduleData.output.customCodeIds.conditionalLink)
            customCode += genericReturnStatement(moduleData.output.customCodeIds.conditionalLink[0]);
        }
        moduleData.output.customCode += customCode;
        moduleData.output.customCode += functionEndString();
        global.logger.error("Module ID:" + moduleData.typeId + "Custom Code generated:" + moduleData.output.customCode);
      }
      return app;
    }
  } catch (e) {
    global.logger.error("Failed to Retrieve App:", appId, e);
    return null;
  }
}

const getOperator = (condition) => {
  const operatorJson = {
    "Equal To": "==",
    "Not Equal To": "!==",
    "Less Than": "<",
    "Greater Than": ">",
    "Greater Than or Equal To": ">=",
    "Less Than or Equal To": "<="
  }
  return operatorJson[condition] || "==";
}

const appendCondition = (key, condition, value, moduleId, type, branchId) => {
  let modifiedKey = getActualKey(key, type);
  let operator = getOperator(condition);
  return `
  if($${modifiedKey} ${operator} "${value}"){
    logger.info("Choice Id: ${branchId}");
    return "${moduleId}";
  }
  `
}

const keyModifiedMapping = {
  "zendesk": {
    "id": "id"
  }
}

const getActualKey = (key, type) => {
  if (type == 'appStart') {
    return key;
  }
  if (type == 'appMenu') {
    return key;
  }
  if (type == 'http') {
    let modifiedKey = "response." + key;
    return modifiedKey;
  }
  if (type == 'waitforresponse') {
    return key;
  }
  if (keyModifiedMapping.hasOwnProperty(type)) {
    let mappedVars = keyModifiedMapping[type];
    let modifiedKey = "response." + mappedVars[key];
    return modifiedKey;
  }
  else {
    let modifiedKey = "response." + key;
    return modifiedKey;
  }
}

const elseConditonString = (moduleId, choiceModuleId) => {
  return `
  else {
    logger.error("no_match|${moduleId}|${choiceModuleId}");
    return "${moduleId}";
  }
  `
}

const functionStartString = () => {
  return `function main(){
    `;
}

const functionEndString = () => {
  return `}`;
}

const setVariables = (moduleId) => {
  return `
  let moduleResponse = $moduleResponse;
  moduleResponse["${moduleId}"] = $response;
  setVariable('moduleResponse', moduleResponse);
  `
}

const genericReturnStatement = (moduleId) => {
  return `
  return "${moduleId}";
  `
}

const getAppStartCode = () => {
  return `
  let moduleResponse = {};
  setVariable('moduleResponse', moduleResponse);
  `
}

const getRetryCode = (retry_count, current_module_id, next_module_id) => {
  let modified_retry_count = parseInt(retry_count, 10);
  modified_retry_count = modified_retry_count - 1;
  return `
  if(typeof $counter === 'undefined'){
		setVariable('counter', ${modified_retry_count});
    if($response.code == '200'){
			return "${next_module_id}";
		}
		else{
			return "${current_module_id}";
		}
	}
	if($counter == 1){
    return "${next_module_id}";
  }
  else{
    let counter = $counter;
    counter = counter - 1;
    setVariable('counter', counter);
    if($response.code == '200'){
			return "${next_module_id}";
		}
		else{
			return "${current_module_id}";
		}
	}
  `
}

function expandErrorCodes(codes) {
  let succodes = [];
  codes && codes.forEach(code => {
    if (code.includes("-")) {
      try {
        let arr = code.split("-");
        let start, end, prefix = "";
        if (isNaN(arr[0])) {
          start = Number(arr[0].substring(1));
          prefix = arr[0].charAt(0);
        } else {
          start = Number(arr[0]);
        }
        if (isNaN(arr[1])) {
          end = Number(arr[1].substring(1));
        } else {
          end = Number(arr[1]);
        }
        for (; start <= end; start++) {
          succodes.push(prefix + start);
        }
      } catch (error) {
        //ignore
      }
    } else {
      succodes.push(code);
    }

  });
  return succodes;
}