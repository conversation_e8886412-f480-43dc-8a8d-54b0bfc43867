"use strict";
const common = require("common");
const redis_man = common.redis_man;
const Redis = require('ioredis');
const cluster = require('cluster');


const zlib = require("zlib");
const ENUMS = require("../lib/enums");
const ENVIRONMENT = ENUMS.ENVIRONMENT;
const KPI = require("app_kpi");
const OAM = require("oam");

const oam_alert_oid = "app_engine_sessionstore_conn";
const KPI_KEY = KPI.KEYS.sessionstore;
const cache = {};

var sessionStoreConfig = global.config && global.config.app_engine && global.config.app_engine.sessionstore;

if (sessionStoreConfig == null) {
  sessionStoreConfig = {
    host: "127.0.0.1",
    port: 6379,
    db: 0,
    timeout: 60,
    compression_required: false
  };
}

sessionStoreConfig.timeout = sessionStoreConfig.timeout || 60;
sessionStoreConfig.delta = sessionStoreConfig.delta || 5;

if (sessionStoreConfig.compression_required === null) {
  sessionStoreConfig.compression_required = false;
}

let invokerSessions = {};
let redis_conn_key = "userSessionCache";


module.exports = {

  init: async () => {
    global.logger.warn("Initializing the User Session Store");
    try {
      invokerSessions = {};
      redis_man.init({
        key: redis_conn_key,
        config: sessionStoreConfig,
        oid: oam_alert_oid
      });

      let connection = await redis_man.getConnection(redis_conn_key);
      await connection.set(redis_conn_key, "connected", "EX", sessionStoreConfig.timeout);
      await connection.get(redis_conn_key);
    } catch (e) {
      global.logger.error("Exception in UserSessionStore Init", e);
      OAM.emit("criticalAlert", oam_alert_oid);
    }
  },

  set: async (session, timeout_wait) => {
    console.log("ELSON: Timeout in Session Store file:" + timeout_wait);
    let startTime = new Date().getTime(), sessionId;
    try {
      session.sessionExpiryTime = startTime + (timeout_wait * 1000);
      if (session.getMode() === ENVIRONMENT.DEV) {
        invokerSessions[session.getInvoker()] = session;
      }
      sessionId = getKey({ appId: session.getOriginAppId(), userId: session.getUserId() });
      let timeout;
      if (timeout_wait) {
        timeout = Number(timeout_wait);
      }
      else {
        timeout = 10;
      }
      if (!global.isDevServer && sessionStoreConfig.cachePolicy === "redis") {
        let connection = await redis_man.getConnection(redis_conn_key);
        let data = session.toString();
        if (sessionStoreConfig.compression_required === false) {
          await connection.set(sessionId, data, "EX", timeout);
          await connection.set("meta_" + sessionId, data, "EX", timeout + Number(sessionStoreConfig.delta));
        } else {
          return zlib.deflate(data, async (err, buffer) => {
            if (!err) {
              await connection.set(sessionId, buffer.toString("base64"), "EX", timeout);
              await connection.set("meta_" + sessionId, buffer.toString("base64"), "EX", timeout + sessionStoreConfig.delta);
            } else {
              // handle error
            }
          });
        }
      } else {
        cache[sessionId] = session;
      }
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "set");
      return true;
    } catch (e) {
      global.logger.error("USS=" + sessionId + " Failed to save Session context", e);
    }
    return false;
  },

  del: async session => {
    if (session === null) return false;
    let sessionId, startTime = new Date().getTime(), result;
    try {
      sessionId = getKey({ appId: session.getOriginAppId(), userId: session.getUserId() });
      if (session.getInvoker()) {
        delete invokerSessions[session.getInvoker()];
      }
      delete cache[sessionId];
      if (sessionStoreConfig.cachePolicy === "redis") {
        let connection = await redis_man.getConnection(redis_conn_key);
        result = await connection.del(sessionId);
      }
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "del");
      return result;
    } catch (e) {
      global.logger.error("USS=" + sessionId + " Failed delete Session context ", e);
    }
    return false;
  },

  get: (appId, userId) => {
    let startTime = new Date().getTime();
    return new Promise(resolve => {
        let sessionId;
        try {
            sessionId = getKey({ appId, userId });
            if (!global.isDevServer && sessionStoreConfig.cachePolicy === "redis") {
                redis_man.getConnection(redis_conn_key)
                    .then(connection => {
                        return connection.exists(sessionId)
                            .then(exists => {
                                if (exists === 1) {
                                    return connection.get(sessionId).then(session => {
                                        if (sessionStoreConfig.compression_required === false) {
                                            KPI.emit(KPI_KEY, startTime, new Date().getTime(), "get");
                                            return resolve(session);
                                        } else {
                                            zlib.unzip(Buffer.from(session, "base64"), (err, buffer) => {
                                                if (!err) {
                                                    session = buffer.toString();
                                                    KPI.emit(KPI_KEY, startTime, new Date().getTime(), "get");
                                                    return resolve(session);
                                                } else {
                                                    global.logger.error("Error decompressing session", err);
                                                    return resolve(null);
                                                }
                                            });
                                        }
                                    });
                                } else {
                                    return resolve(null);
                                }
                            });
                    })
                    .catch(e => {
                        global.logger.error("USS=" + sessionId + " Failed get Session context ", e);
                        resolve(null);
                    });
            } else {
                let session = cache[sessionId];
                if (session != null && session.sessionExpiryTime > new Date().getTime()) {
                    return resolve(session);
                } else {
                    delete cache[sessionId];
                    return resolve(null);
                }
            }
        } catch (e) {
            global.logger.error("USS=" + sessionId + " Failed get Session context ", e);
            resolve(null);
        }
    });
},
 
  getAppId: (userId) => {
    return new Promise(resolve => {
      try {
        if (!global.isDevServer && sessionStoreConfig.cachePolicy === "redis") {
          redis_man.getConnection(redis_conn_key)
            .then(connection => {
              return connection.keys("*_" + userId).then(keys => {
                if (keys.length > 0) {
                  resolve(keys[0].split("_")[0]);
                } else {
                  return resolve(null);
                }
              });
            })
            .catch(e => {
              global.logger.error("USS=" + userId + " Failed get Session context ", e);
              resolve(null);
            });
        } else {
          resolve(null);
        }
      } catch (e) {
        global.logger.error("USS=" + userId + " Failed get Session context ", e);
        resolve(null);
      }
    });
  },
  

  getInvokerSession: (invoker) => {
    return invokerSessions[invoker];
  }
};

function getKey(opts) {
  return opts.appId + "_" + opts.userId;
}
