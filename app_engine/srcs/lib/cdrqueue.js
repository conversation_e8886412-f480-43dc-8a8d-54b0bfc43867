"use strict";
/**
 * @class CDRQueue
 * @version 1.0.0
 * @since 03 April 2018
 * @description CDR Queue manager which helps to push CDR records to to Redis QUEUE
 *
 *
 * <AUTHOR>
 */

const fs = require("fs"),
  mkdirp = require("mkdirp"),
  common = require("common"),
  redis_man = common.redis_man,
  mysql_man = common.mysql_man;

const KPI = require("app_kpi");
const OAM = require("oam");
const path = require("path");

if (global.config.app_engine.cdrqueue == null) {
  global.config.app_engine.cdrqueue = {
    host: "127.0.0.1",
    port: 3306,
    db: 1,
    log_key: "LEAP_CDRQ",
    limit: 10000,
    fallback_cdr_path: "/data/leap/cdrs",
    redisInsertEnable: true
  };
}
if (process.env.LEAP_CONFIG_DBTYPE == 'mysql') {
  global.config.app_engine.cdrqueue = {
    host: process.env.LEAP_CONFIG_HOST_MYSQL,
    port: process.env.LEAP_CONFIG_MARIADB_PORT,
    db: process.env.CONFIG_DB,
    log_key: "LEAP_CDRQ",
    limit: 10000,
    fallback_cdr_path: "/data/leap/cdrs",
    redisInsertEnable: false
  };
}

if (global.config.app_engine.cdrqueue.log_key == null) {
  global.config.app_engine.cdrqueue.log_key = "LEAP_CDRQ";
}

if (global.config.app_engine.cdrqueue.fallback_cdr_path == null) {
  global.config.app_engine.cdrqueue.fallback_cdr_path = "/data/leap/cdrs";
}

if (global.config.app_engine.cdrqueue.limit == null) {
  global.config.app_engine.cdrqueue.limit = 10000;
}

if (global.config.app_engine.cdrqueue.redisInsertEnable == null) {
  global.config.app_engine.cdrqueue.redisInsertEnable = true;
}

let drqredisInsertEnable = false;
try {
  if (global.config.app_engine.delivery_report_queue && global.config.app_engine.delivery_report_queue.redisInsertEnable != null) {
    drqredisInsertEnable = global.config.app_engine.delivery_report_queue.redisInsertEnable;
  }
} catch (e) {
  drqredisInsertEnable = false;
  global.logger.error("global.config.app_engine.delivery_report_queue.redisInsertEnable is not defined: So setting to false", e);
}

const cdrPath = global.config.app_engine.cdrqueue.fallback_cdr_path;

const LOG_KEY = global.config.app_engine.cdrqueue.log_key || "LEAP_CDRQ",
  log2queueKey = KPI.KEYS.cdrQ,
  ussddr2queueKey = KPI.KEYS.ussddrQ,
  clusterId = global.clusterId,
  cdrqueue_alert_oid = "app_engine_cdrqueue_conn",
  ussddrqueue_alert_oid = "app_engine_cdrqueue_conn";

let isMapped = false, cdrqueueRedisInsertEnable = global.config.app_engine.cdrqueue.redisInsertEnable;

module.exports = {
  init: async () => {
    global.logger.warn("Initializing the CDR Queue");
    if (process.env.LEAP_CONFIG_DBTYPE == 'mysql') {
      let connection = await mysql_man.getConnection()
      if (!connection.connection.connectionId) {
        global.logger.warn("CDR Queue conn status: Not Connected");
        OAM.emit("criticalAlert", cdrqueue_alert_oid);
      } else {
        global.logger.warn("CDR Queue conn status: Connected");
        OAM.emit("clearAlert", cdrqueue_alert_oid);
      }
      if (global.config.app_engine.delivery_report_queue != null) {
        connection = await mysql_man.getConnection();
        if (!connection.connection.connectionId) {
          global.logger.warn("USSD DR Queue conn status: Not Connected");
          OAM.emit("criticalAlert", ussddrqueue_alert_oid);
        } else {
          global.logger.warn("USSD DR Queue conn status: Connected");
          OAM.emit("clearAlert", ussddrqueue_alert_oid);
        }
      }
    }
    else {
      redis_man.init({
        key: log2queueKey,
        config: global.config.app_engine.cdrqueue,
        oid: cdrqueue_alert_oid
      });

      if (global.config.app_engine.delivery_report_queue != null) {
        redis_man.init({
          key: ussddr2queueKey,
          config: global.config.app_engine.delivery_report_queue,
          oid: ussddrqueue_alert_oid
        });
      }

      var connection = await redis_man.getConnection(log2queueKey);
      connection.set("status", "connected");
      connection.get("status", (err, result) => {
        if (err) {
          global.logger.warn("CDR Queue conn status: Not Connected");
          OAM.emit("criticalAlert", cdrqueue_alert_oid);
        } else {
          global.logger.warn("CDR Queue conn status: Connected", result);
          OAM.emit("clearAlert", cdrqueue_alert_oid);
        }
      });

      if (global.config.app_engine.delivery_report_queue != null) {
        connection = await redis_man.getConnection(ussddr2queueKey);
        connection.set("status", "connected");
        connection.get("status", (err, result) => {
          if (err) {
            global.logger.warn("USSD DR Queue conn status: Not Connected");
            OAM.emit("criticalAlert", ussddrqueue_alert_oid);
          } else {
            global.logger.warn("USSD DR Queue conn status: Connected", result);
            OAM.emit("clearAlert", ussddrqueue_alert_oid);
          }
        });
      }
    }
    return connection;
  },

  log2queue: async cdr => {
    let startTime = new Date().getTime();
    if (!cdrqueueRedisInsertEnable) {
      handleError(startTime, log2queueKey, cdr);
    }
    else {
      try {
        let connection = await redis_man.getConnection(log2queueKey);
        connection && connection.lpush(LOG_KEY, cdr)
          .then(entry => cdr = null)
          .catch(e => {
            handleError(startTime, log2queueKey, cdr);
          });
        KPI.emit(log2queueKey, startTime, new Date().getTime(), "redis");
      } catch (e) {
        handleError(startTime, log2queueKey, cdr);
      }
    }
  },

  dr2queue: async cdr => {
    let startTime = new Date().getTime();
    if (!drqredisInsertEnable) {
      handleError(startTime, log2queueKey, cdr);
    }
    else {
      try {
        global.logger.info("CPSCDR|" + cdr);
        if (global.config.app_engine.delivery_report_queue != null) {
          let connection = await redis_man.getConnection(ussddr2queueKey);
          connection && connection.lpush(global.config.app_engine.delivery_report_queue.log_key, cdr)
            .then(entry => cdr = null)
            .catch(e => {
              handleError(startTime, ussddr2queueKey, cdr);
            });
          KPI.emit(ussddr2queueKey, startTime, new Date().getTime(), "redis");
        }
      } catch (e) {
        handleError(startTime, ussddr2queueKey, cdr);
      }
    }
  }
};

/** create and write file * */
function writeFile(filePath, buffer) {

  if (isMapped == false) {
    try {
      mkdirp.sync(cdrPath);
    } catch (error) {
      //ignore
    }
    isMapped = true;
  }

  fs.appendFile(filePath, buffer + "\n", (err) => {
    if (err) throw err;
  });
}

function handleError(startTime, key, cdr) {
  let dt = new Date();
  let file = "_" + dt.getFullYear() + dt.getMonth() + dt.getUTCDate();
  writeFile(path.join(cdrPath, key + clusterId + file + ".cdr"), cdr);
  KPI.emit(key, startTime, new Date().getTime(), "cdr");
  cdr = null;
}
