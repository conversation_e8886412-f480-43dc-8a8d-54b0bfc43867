"use strict";
/**
 * @class Plugin-settings
 * @version 1.0.0
 * @since 03 April 2018
 * @description utiliy module helps to load production plugin settings at run time.
 *
 * <AUTHOR>
 */

const pluginmanager = require("pluginmanager");
var plugins = {};
const common = require("common");
const whiteboard = common.whiteboard;
var execMode = "dev";

module.exports = {

  init: async (mode) => {
    execMode = mode;
    global.logger.warn("Initializing the plugin settings, ExecMode:", execMode);
    global.prod_settings = await pluginmanager.getModeSettings(execMode);
    global.logger.warn("PM Settings:", JSON.stringify(global.prod_settings));

    plugins["1.1"] = require("../../config/sms.json");
    initRR("1.1");
    plugins["3"] = require("../../config/ucip.json");
    initRR("3");
    plugins["4"] = require("../../config/acip.json");
    initRR("4");
    plugins["6"] = require("../../config/vsip.json");
    initRR("6");
    await registerEvents();
  },

  getSettings: (msisdn, pluginId, defaultSettings) => {
    if (pluginId == "1.5" || pluginId == "1.6" || pluginId == "1.7" || pluginId == "1.8" || pluginId == "1.9") {
      return defaultSettings;
    }
    let id = (pluginId == "1.1") ? pluginId : parseInt(pluginId, 10);

    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Get Plugin Settings for Plugin ID:" + id);
    }
    if (id === 0 || id === 2 || global.isDevServer) {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Default Settings is retrieved for for Plugin ID:" + id);
      }
      return defaultSettings;
    }

    if (!global.leap_settings.multi_plugin_settings) {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Multi plugin settings is disabled for Plugin ID:", id);
      }
      let settings = global.prod_settings[String(pluginId)] || global.prod_settings[String(id)] || defaultSettings;
      settings.timeout = defaultSettings.timeout || 60000;
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Plugin timeout:", settings.timeout);
      }
      if (pluginId == "5.4") {
        settings.AMCMain = defaultSettings.AMCMain || "";
        settings.AMCBack = defaultSettings.AMCBack || "";
        settings.AMCContinue = defaultSettings.AMCContinue || "";
      }
      return settings;
    }
    if (plugins.hasOwnProperty(id)) {
      let pluginInfo = plugins[id];
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Multi-pluginInfo for Plugin ID:" + id + " is " + JSON.stringify(pluginInfo) + " and mode is " + pluginInfo.mode);
        global.logger.trace("Multi-pluginInfo for Plugin ID:" + id + " is enabled? = " + pluginInfo.enabled);
      }
      if (pluginInfo.enabled) {
        switch (pluginInfo.mode) {
          case "series": {
            return getSeriesSetting(String(msisdn), id);
          }
          case "roundrobin": {
            return getRRSetting(id);
          }
        }
      }
    }
    let settings = {};
    if (global.prod_settings.hasOwnProperty(String(pluginId))) {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Get Plugin setings for pluginId:", pluginId);
      }
      settings = global.prod_settings[String(pluginId)]
    } else if (global.prod_settings.hasOwnProperty(String(id))) {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Get Plugin setings for id:", id);
      }
      settings = global.prod_settings[String(id)]
    } else {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Using defaultSettings:", defaultSettings);
      }
      settings = defaultSettings;
    }

    settings.timeout = defaultSettings.timeout || 60000;
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Plugin timeout:", settings.timeout);
    }
    return settings;
  }
};

function registerEvents() {
  whiteboard.on("mount_pm_settings", async (pluginId) => {
    let prod_settings;
    try {
      pluginId = String(pluginId);
      prod_settings = await pluginmanager.getModeSettings(execMode);
      global.prod_settings[pluginId] = prod_settings[pluginId];
      global.logger.info("Updating plugin settings for", pluginId, ", New Settings:", global.prod_settings[pluginId]);
    } catch (e) {
      global.logger.error("Failed to Mount Plugin setting:", pluginId, e);
    } finally {
      prod_settings = null;
    }
  });

  whiteboard.on("unmount_pm_settings", (pluginId) => {
    try {
      delete global.prod_settings[String(pluginId)];
    } catch (e) {
      global.logger.error("Failed to Unmount Plugin setting:", pluginId, e);
    }
  });
  whiteboard.subscribe("mount_pm_settings");
  whiteboard.subscribe("unmount_pm_settings");
}

function getSeriesSetting(msisdn, categoryId) {
  let series = plugins[categoryId].options.series;
  let key = Object.keys(series).find(expr => {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("PluginID:" + categoryId + ", expr:" + expr + " MSISDN:" + msisdn);
    }
    return new RegExp(expr).test(msisdn);
  });
  let index = series[key] && series[key].index || plugins[categoryId].defaultIndex;
  if (global.logger.isTraceEnabled()) {
    global.logger.trace("PluginID:" + categoryId + ", currIndex:" + index);
  }
  return plugins[categoryId].settings[index];
}

function getRRSetting(categoryId) {
  if (plugins[categoryId].currIndex < plugins[categoryId].size) {
    plugins[categoryId].currIndex++;
  } else {
    plugins[categoryId].currIndex = 0;
  }
  if (global.logger.isTraceEnabled()) {
    global.logger.trace("PluginID:" + categoryId + ", currIndex:", plugins[categoryId].currIndex);
  }
  return plugins[categoryId].settings[plugins[categoryId].currIndex];
}

function initRR(categoryId) {
  if (plugins[categoryId].enabled && plugins[categoryId].mode == "roundrobin") {
    plugins[categoryId].size = plugins[categoryId].settings.length - 1;
    plugins[categoryId].currIndex = -1;
  }
}
