const UssdServiceCache = require("../cache/UssdServiceCache");
const SmppConstants = require("../ussd_service/SmppConstants");
const CPSContants = require("../ussd_service/CPSContants");
const EventLogger = require("./EventLogger");
const AppMacros = require("app_macros");
const request = require("request");
const CDRQUEUE = require("./cdrqueue");
const Enums = require("../lib/enums");
const INTERFACES = Enums.INTERFACES;
const KPI = require("app_kpi");
const KPI_INT_RES = KPI.KEYS.interface_res;
const xml2json = require("xml2json");
const CPSErrors = require("./CPSErrors");
const PIPE = "|";
const http = require("http");

function callbackHandler(pdu) {
  switch (pdu.command) {
    case "deliver_sm_resp":
      break;
    case "submit_sm_resp":
      break;
  }
}

const KeepAliveAgent = new http.Agent({
  maxSockets: 50,
  keepAlive: true,
  maxFreeSockets: 10,
  timeout: 30000
});

module.exports = {

  sendSmppFlash: (ussdRequest, serviceOperation) => {
    return new Promise((resolve, reject) => {
      let packet;
      try {
        let smppSession = UssdServiceCache.getGatewaySessionInfo(ussdRequest.GATEWAYID);
        if (smppSession != null) {
          let gatewayInfo = UssdServiceCache.getGatewayInfo(ussdRequest.GATEWAYID);
          if (gatewayInfo == null || gatewayInfo.getUser() != ussdRequest.USERNAME || gatewayInfo.getPass() != ussdRequest.PASSWORD) {
            return resolve({ command_status: 400 });
          }
          if (ussdRequest.MESSAGE == null || ussdRequest.MESSAGE.trim().length == 0) {
            return;
          }
          packet = {
            source_addr: ussdRequest.SENDER,
            destination_addr: ussdRequest.MSISDN,
            data_coding: SmppConstants.PLAIN_DATA_CODING,
            short_message: Buffer.from(ussdRequest.MESSAGE),
            ussd_service_op: serviceOperation,
            its_session_info: Buffer.alloc(2),
            service_type: "USSD"
          };
          smppSession.submit_sm(packet, pdu => {
            KPI.emit(KPI_INT_RES, "SMPP_FLASH");
            packet = null;
            EventLogger.emit("USSDPush", ussdRequest, pdu);
            resolve(pdu);
          });
        } else {
          resolve({ command_status: -1 });
        }
      } catch (e) {
        reject(e);
      }
    });
  },

  sendSmppResponse: (ussdRequest, serviceOperation) => {
    let smppSession = UssdServiceCache.getGatewaySessionInfo(ussdRequest.getPushGatewayId());
    if (smppSession != null) {
      let message;
      try {
        message = ussdRequest.getContent().substring(0, ussdRequest.getMaxUssdMessageCharacters());
      } catch (e) {
        //ignore
      }
      if (message == null || message.trim().length == 0) {
        return;
      }

      let packet = {
        service_type: ussdRequest.getServiceType(),
        source_addr: ussdRequest.getServiceCode(),
        destination_addr: ussdRequest.getMsisdn(),
        data_coding: SmppConstants.PLAIN_DATA_CODING,
        short_message: Buffer.from(message),
        ussd_service_op: serviceOperation,
        its_session_info: ussdRequest.getItsSessionInfo() || Buffer.alloc(2),
        dest_addr_ton: ussdRequest.getSourceAddressTON(),
        dest_addr_npi: ussdRequest.getSourceAddressNPI(),
        source_addr_ton: ussdRequest.getDestAddressTON(),
        source_addr_npi: ussdRequest.getDestAddressNPI()
      };
      smppSession.submit_sm(packet, callbackHandler);
      EventLogger.emit("USSDResponse", ussdRequest);
      if (serviceOperation == SmppConstants.SERVICE_OP_PSSR_RESPONSE) {
        ussdRequest.setItsSessionInfo(Buffer.alloc(2));
      }
    }
  },

  sendCpsResponse: async (res, ussdRequest, service_type) => {
    let buffer;
    let sentTime = new Date().getTime();
    try {
      let gatewayInfo = UssdServiceCache.getGatewayInfo(global.leap_settings.defaults.cps_gw_id);
      buffer = CPSBuffer({
        sentTime: sentTime,
        sequence_number: ussdRequest.getReqSequenceNo(),
        service_type: service_type,
        version: ussdRequest.getVersion(),
        source_addr: ussdRequest.getServiceCode(),
        dest_addr: ussdRequest.getMsisdn(),
        msg_content: ussdRequest.getContent(),
        msc_gt_addr: ussdRequest.getGtAddress(),
        LAC: ussdRequest.getLac(),
        CellID: ussdRequest.getCellId(),
        IMSI: ussdRequest.getImsi(),
        PushID: ussdRequest.getPushID(),
        App_url: global.leap_settings.defaults.cps_callback_url
      });
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("CPS-SEND:", buffer);
      }
      if (res != null) {
        res.send(buffer);
        if (global.logger.isTraceEnabled()) {
          global.logger.trace("CPS-SENT over HTTP response:");
        }
        KPI.emit(KPI_INT_RES, INTERFACES.CPS_XML);
      } else {

        if (gatewayInfo && gatewayInfo.status == 1) {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace("CPS-GW:", gatewayInfo.getUrl());
          }

          let resp = await httpCall({
            headers: { "Content-Type": "application/xml; charset=utf-8" },
            url: gatewayInfo.getUrl(),
            method: "POST",
            timeout: global.leap_settings.defaults.cps_gw_timeout,
            body: buffer,
            agent: KeepAliveAgent
          });

          if (global.logger.isTraceEnabled()) {
            global.logger.trace("CPS-RESP:", resp);
          }
          if (resp.code == 200 && (service_type == CPSContants.PUSH_REQUEST || service_type == CPSContants.BEGIN_NOTIFY)) {
            if (resp.body != null && resp.body.length > 0) {
              try {
                resp.body = JSON.parse(xml2json.toJson(resp.body))["cps-message"];
              } catch (error) {
                global.logger.error(resp);
              }
              let code = resp.body && resp.body.command_status || 3011;
              let SubmitSMcdr = ussdRequest.getMsisdn() + PIPE + ussdRequest.getOptionalParams().SENDERADDRESS + PIPE + ussdRequest.getOptionalParams().CPID + PIPE + Date.now() + PIPE + code + PIPE + CPSErrors.getMessage(code) + PIPE + sentTime + "|0|0";
              CDRQUEUE.dr2queue(1 + PIPE + SubmitSMcdr);// Submit callback
              if (code == 0)
                CDRQUEUE.dr2queue(2 + PIPE + SubmitSMcdr);// delivery report callback
            } else if (resp.code != null) {
              let SubmitSMcdr = ussdRequest.getMsisdn() + PIPE + ussdRequest.getOptionalParams().SENDERADDRESS + PIPE + ussdRequest.getOptionalParams().CPID + PIPE + Date.now() + PIPE + CPSErrors.getStatusCode(resp.code) + PIPE + resp.code + PIPE + sentTime + "|0|0";
              CDRQUEUE.dr2queue(1 + PIPE + SubmitSMcdr);// Submit callback
            }
          }
          KPI.emit(KPI_INT_RES, "CPS_POST");
        }
      }
    } catch (e) {
      global.logger.error(e);
    } finally {
      buffer = null;
    }
  }
};

function CPSBuffer(options) {

  options.timestamp = AppMacros.definitions.
    _date2customformat(new Date(options.sentTime), "yyyy/mm/dd HH:MM:ss");
  options.msg_len = options.msg_content.length;
  options.data_coding = 0;
  options.command_status = 0;

  if (global.logger.isTraceEnabled()) {
    global.logger.trace("CPS-PAYLOAD:", JSON.stringify(options));
  }
  return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
    "<cps-message>" +
    "<sequence_number>" + options.sequence_number + "</sequence_number>" +
    "<version>" + options.version + "</version>" +
    "<service_type>" + options.service_type + "</service_type>" +
    "<source_addr>*" + options.source_addr + "</source_addr>" +
    "<dest_addr>" + options.dest_addr + "</dest_addr>" +
    "<timestamp>" + options.timestamp + "</timestamp>" +
    "<command_status>" + options.command_status + "</command_status>" +
    "<data_coding>" + options.data_coding + "</data_coding>" +
    "<msg_len>" + options.msg_len + "</msg_len>" +
    "<msg_content>" + options.msg_content + "</msg_content>" +
    "<msc_gt_addr>" + options.msc_gt_addr + "</msc_gt_addr>" +
    "<LAC>" + options.LAC + "</LAC>" +
    "<CellID>" + options.CellID + "</CellID>" +
    "<IMSI>" + options.IMSI + "</IMSI>" +
    "<PushID>" + options.PushID + "</PushID>" +
    "<App_url>" + options.App_url + "</App_url>" +
    "</cps-message>";
}

function httpCall(req) {
  let diagnostics = {};
  req.time = true;
  return new Promise(resolve => {
    request(req, (error, response, body) => {
      if (response != null) {
        diagnostics.elapsedTime = response.elapsedTime;
        diagnostics.responseStartTime = response.responseStartTime;
        diagnostics.timingStart = response.timingStart;
        diagnostics.timings = response.timings;
        diagnostics.timingPhases = response.timingPhases;
      }

      if (error) {
        if (error.code == "ESOCKETTIMEDOUT" || error.code == "ETIMEDOUT") {
          resolve({
            code: error.code,
            body: { code: error.code, msg: "Plugin execution timedout, code:" + error.code, response },
            diagnostics
          });
        } else {
          resolve({
            code: error.code,
            diagnostics
          });
        }
      } else {
        resolve({
          headers: response && response.headers || {},
          code: response && response.statusCode || 200,
          body,
          diagnostics
        });
      }
    });
  });
}
