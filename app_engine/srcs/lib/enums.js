"use strict";
/**
 * @class ENUM
 * @version 1.0.0
 * @since 03 April 2018
 * @description holds all constant vars required for app engine component
 *
 * <AUTHOR>
 */

module.exports = {

  EVENTS: Object.freeze({
    INIT: "EVENT_INIT",
    LOAD_DATA: "LOAD_MODULE_TIME",
    CTX_MERGE: "MODULE_CTXMERG_TIME",
    READY: "EVENT_READY",
    START: "EVENT_START",
    ENTERED: "EVENT_ENETERED",
    PRE_PROC: "PRE_PROC_TIME",
    EXEC_TIME: "EXECUTION_TIME",
    POST_PROC: "POST_PROC_TIME",
    MENU: "EVENT_MENU",
    CANCEL: "EVENT_CANCEL",
    PROCESSED: "EVENT_PROCESSED",
    END: "EVENT_END",
    SKIP: "EVENT_SKIP"
  }),

  VERBOSE: Object.freeze({
    TRACE: { code: 0, desc: "TRACE" },
    DEBUG: { code: 1, desc: "DEBUG" },
    INFO: { code: 2, desc: "INFO" },
    WARN: { code: 3, desc: "WARN" },
    ERROR: { code: 4, desc: "ERROR" },
    FATAL: { code: 5, desc: "FATAL" }
  }),

  ENVIRONMENT: Object.freeze({
    DEV: "DEV_MODE",
    PROD: "PROD_MODE"
  }),

  WS: Object.freeze({
    REGISTER: "WS_REGISTER",
    SETTINGS: "WS_SETTINGS",
    NEXT: "WS_NEXT",
    INPUT: "WS_INPUT",
    VARS: "WS_VARS",
    CONSOLE: "WS_CONSOLE",
    MENU: "WS_MENU",
    RESPONSE: "WS_RESPONSE",
    PROCESSED: "WS_PROCESSED",
    OK: "WS_OK",
    NOT_OK: "WS_NOK",
    TERMINATE: "WS_TERMINATE"
  }),

  MSGTYPE: Object.freeze({
    SMS: "SMS Message",
    USSD: "USSD Message",
    MMS: "MMS Message"
  }),

  INTERFACES: Object.freeze({
    QUERY_STRING: "QUERY_STRING",
    HTTP_JSON: "HTTP_JSON",
    CPS_XML: "CPS_XML",
    SMPP: "SMPP",
    WEBSOCKET: "WEBSOCKET",
    NEXMO: "NEXMO",
    CALLBACK: "CALLBACK",
  }),

  PUSH_TYPES: Object.freeze({
    TEXT: 1,
    MENU: 2,
    PROXY_MENU: 3
  })
};
