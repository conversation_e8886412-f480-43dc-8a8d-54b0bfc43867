"use strict";
/**
 * @class HTTP Codes
 * @version 1.0.0
 * @since 03 April 2018
 * @description holds all error codes required for app engine
 *
 * <AUTHOR>
 */

module.exports = Object.freeze({
  DEBUG_MISSING_INVOKER_ID: "V9001",
  DEBUG_UNREGISTERED_INVOKER: "V9002",
  MISSING_APPID: "V9003",
  MISSING_MSISDN: "V9004",
  INVALID_MSISDN: "V9005",
  MISSING_QUERY_PARAM: "V9006",
  APP_NOT_FOUND: "V9007",
  APP_NOT_LOADED: "V9008",
  UNAUTHORIZED: "V9009",
  UNDEFINED_ENDMODULE: "V9010",
  SMPP_SC_NOT_MAPPED: "V9011",
  SMPP_APP_NOT_MAPPED: "V9012",
  SMPP_INVALID_SHORTCODE: "V9013",
  SMPP_INVALID_INPUT: "V9014",
  GATEWAY_NOT_ACTIVE: "V9016",
  INVALID_PARAMS: "V9017",
  REQUEST_ACCEPTED: "202",
  REQUEST_PROCESSED: "0",
  INTERNAL_ERROR: "E9000",
  SESSION_FAULT_ERROR: "E9000",
  SESSION_TIMEOUT: "V9015",
  SYSTEM_BUSY_ERROR: "E898"
});
