const events = require("events");

class EventLogger extends events.EventEmitter {
  constructor() {
    super();
    this.registerEvents();
  }

  registerEvents() {
    global.smpplogger.trace("Register events");
    this.on("USSDResponse", async (pdu) => {
      pdu.setLastResponseTime(new Date().getTime());
      global.smpplogger.error(pdu.print("SMPP-RES"));
    });

    this.on("USSDPush", async (ussdRequest, ussdResponse) => {
      global.smpplogger.error("SMPP-PUSH|"
        + ussdRequest.GATEWAYID + "|"
        + ussdRequest.SENDER + "|"
        + ussdRequest.MSISDN + "|"
        + ussdResponse.command_status + "|"
        + ussdResponse.message_id + "|"
        + ussdRequest.MESSAGE);
    });

    this.on("USSDRequest", async (pdu) => {
      pdu.setLastResponseTime(new Date().getTime());
      global.smpplogger.error(pdu.print("SMPP-REQ"));
    });
  }
}

module.exports = new EventLogger();
