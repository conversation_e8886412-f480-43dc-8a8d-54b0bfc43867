const events = require("events");
const PIPE = "|";


class CDRWriter extends events.EventEmitter {
  constructor() {
    super();
    this.registerEvents();
  }

  registerEvents() {
    this.on("PLUGIN_CDR", (appId, moduleId, pluginName, responseCode, responseTime, request, response, sessionId, userId, menuType, menuTitle, userInput) => {
      this.writeCDR(appId, moduleId, pluginName, responseCode, responseTime, request, response, sessionId, userId, menuType, menuTitle, userInput);
    });
  }

  writeCDR(appId, moduleId, pluginName, responseCode, responseTime, request, response, sessionId, userId, menuType, menuTitle, userInput) {
    try {
      if (global.pluginCDR && global.pluginCDR.isInfoEnabled()) {
        global.pluginCDR.error(appId + PIPE + moduleId + PIPE + pluginName + PIPE + responseCode + PIPE + responseTime + PIPE + request + PIPE + response + PIPE + sessionId + PIPE + userId + PIPE + menuType + PIPE + menuTitle + PIPE + userInput);
      }
    } catch (e) {
      global.logger.error("Expection occured while writing CDRS:", e);
    } finally {
      sessionId = null,
        appId = null,
        moduleId = null;
      pluginName = null;
      menuType = null;
      menuType = null;
      menuTitle = null;
      userInput = null;
    }
  }
}

module.exports = new CDRWriter();