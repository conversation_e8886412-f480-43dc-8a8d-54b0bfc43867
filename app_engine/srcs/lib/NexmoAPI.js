const NexmoSettings = require("../../config/nexmo.json");
const request = require("request");

module.exports = {
  sendNexmoMessage: (wa_from, wa_to, wa_text) => {
    return new Promise((resolve, reject) => {
      let req = {
        url: NexmoSettings.url,
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
          "User-Agent": "nexmo-node/2.5.3-beta-3 node/10.16.0",
          "Authorization": "Bearer " + NexmoSettings.token
        },
        body: JSON.stringify({
          to: {
            type: "whatsapp",
            number: wa_to
          },
          from: {
            type: "whatsapp",
            number: wa_from
          },
          message: {
            content: {
              type: "text",
              text: wa_text
            }
          }
        })
      };
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Sending Whatsapp message '" + wa_text + "' to " + this._userId + ", Req::", JSON.stringify(req));
      }
      request(req, (err, response, body) => {
        if (err) {
          if (global.logger.isTraceEnabled()) {
            global.logger.trace("Whatsapp message Status on " + this._userId + " ERR::", err);
          }
          return reject();
        }
        if (global.logger.isTraceEnabled()) {
          global.logger.trace("Whatsapp message Status on " + this._userId + " is " + response.statusCode + ", Response:", body);
        }
        resolve();
      });
    });
  }
};
