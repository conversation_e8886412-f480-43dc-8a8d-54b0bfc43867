const props = Object.freeze({
  0: "Success",
  1001: "Operation time-out",
  1002: "Internal system error",
  2000: "A wrong message format",
  2001: "Data type error",
  2002: "Parameter does not exist",
  2003: "Data value can not be null",
  2004: "Parameter value invalid",
  3001: "Sequence number error",
  3002: "Version error",
  3003: "Service type error",
  3004: "Value of the source_addr item error",
  3005: "Value of the dest_addr item error",
  3006: "Value of the data_coding item error",
  3007: "Value of the msg_len item error",
  3008: "Value of the msg_content item error",
  3009: "Value of the msg_content item can not be null",
  3010: "Length of message over-long",
  3011: "Invalid response",
  ESOCKETTIMEDOUT: "Plugin execution timedout"
});

module.exports = {
  getMessage: key => {
    if (props.hasOwnProperty(key)) {
      return props[key];
    }
    return "Unknown";
  },

  getStatusCode: (code) => {
    switch (code) {
      case "ECONNRESET":
        return 4001;
      case "ETIMEDOUT":
      case "ESOCKETTIMEDOUT":
        return 4002;
    }
    return 4003;
  }
};
