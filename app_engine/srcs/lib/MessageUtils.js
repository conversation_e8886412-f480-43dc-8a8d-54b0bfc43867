"use strict";

// ==============================
// MESSAGE UTILITY
// ==============================

/**
 * @class MessageUtils
 * @version 1.0.0
 * @since 03 April 2018
 * @description Concrete state which provide the implementation to Execute/Invoke the Plugin using the module context data.
 *
 *
 * <AUTHOR>
 */

// Import required App engine state managing classes

// Import context helpers
const AppCache = require("../cache/AppCache");
const APPEXEC_ERRORCODES = require("./http_codes");

// Import Enums and constants
const ENUMS = require("./enums");
const WS = ENUMS.WS, VERBOSE = ENUMS.VERBOSE;
const DEFAULT_MESSAGE = "Dear Customer, Your request cannot be processed now. Please try later.";

module.exports = {
  getMessage: async function (session) {

    let message, i, defaultErrorCode = APPEXEC_ERRORCODES.INTERNAL_ERROR, moduleContext;
    let locale = session.getLocale() || global.config.app_engine.defaultLanguage;

    if (locale != global.config.defaultLanguage) {
      if (global.logger.isTraceEnabled()) {
        session.emit(WS.CONSOLE, VERBOSE.TRACE, "Loading language template for appId:" + session.getAppId());
      }
      let context = await AppCache.getAppLocaleTemplate(session.getAppId(), locale);
      if (context != null && context.translation != null) {
        moduleContext = context.translation;
      }
    }

    if (moduleContext == null) {
      let moduleData = AppCache.getModuleData(session.getAppId(), session.getEndModuleId());
      if (moduleData == null) {
        return DEFAULT_MESSAGE;
      }
      moduleContext = {
        appId: session.getAppId(),
        defaultError: moduleData.process.defaultError,
        success: moduleData.process.success,
        customErrors: moduleData.process.customErrors,
        systemBusyMessage: moduleData.process.systemBusyMessage
      };
    }
    if (moduleContext.defaultError == null) {
      let appEndSchema = AppCache.getEndModuleSchema();
      moduleContext.defaultError = {
        code: appEndSchema.properties.process.properties.defaultError.properties.code.default,
        message: appEndSchema.properties.process.properties.defaultError.properties.message.default
      };
      if (global.logger.isTraceEnabled()) {
        try {
          session.emit(WS.CONSOLE, VERBOSE.TRACE, "Resetting defaultError codes: " + JSON.stringify(moduleContext.defaultError));
        } catch (e) {
          //ignore
        }
      }
      defaultErrorCode = moduleContext.defaultError.code;
    }
    if (moduleContext.success == null) {
      let appEndSchema = AppCache.getEndModuleSchema();
      moduleContext.success = {
        code: appEndSchema.properties.process.properties.success.properties.code.default,
        message: appEndSchema.properties.process.properties.success.properties.message.default
      };
      if (global.logger.isTraceEnabled()) {
        try {
          session.emit(WS.CONSOLE, VERBOSE.TRACE, "Resetting success codes: " + JSON.stringify(moduleContext.success));
        } catch (e) {
          //ignore
        }
      }
    }

    if (moduleContext.systemBusyMessage == null) {
      let appEndSchema = AppCache.getEndModuleSchema();
      moduleContext.systemBusyMessage = {
        code: appEndSchema.properties.process.properties.systemBusyMessage.properties.code.default,
        message: appEndSchema.properties.process.properties.systemBusyMessage.properties.message.default
      };
      if (global.logger.isTraceEnabled()) {
        try {
          session.emit(WS.CONSOLE, VERBOSE.TRACE, "Resetting systemBusyMessage codes: " + JSON.stringify(moduleContext.systemBusyMessage));
        } catch (e) {
          //ignore
        }
      }
    }
    let responseCode = String(session.getResponseCode());

    if (global.logger.isTraceEnabled()) {
      session.emit(WS.CONSOLE, VERBOSE.TRACE, "OriginalResponseCode: " + responseCode);
    }

    if (responseCode == null) {
      responseCode = defaultErrorCode;
    }
    if (responseCode.trim().length === 0) {
      responseCode = "0";
    }
    if (global.logger.isTraceEnabled()) {
      session.emit(WS.CONSOLE, VERBOSE.TRACE, "DefaultErrorCode: " + defaultErrorCode);
      session.emit(WS.CONSOLE, VERBOSE.TRACE, "ResponseCode: " + responseCode);
      session.emit(WS.CONSOLE, VERBOSE.TRACE, "Language: " + locale);
    }

    if (responseCode == defaultErrorCode) {
      message = moduleContext.defaultError && moduleContext.defaultError.message || DEFAULT_MESSAGE;
      message = session.macros.replaceAllPlaceHolders(message);
      return message;
    }

    if (responseCode == moduleContext.systemBusyMessage.code) {
      if (global.logger.isTraceEnabled()) {
        session.emit(WS.CONSOLE, VERBOSE.TRACE, "Checking responseCode belongs to System Busy Message code");
      }
      return moduleContext.systemBusyMessage.message || DEFAULT_MESSAGE;
    }

    if (global.logger.isTraceEnabled()) {
      try {
        session.emit(WS.CONSOLE, VERBOSE.TRACE, "Success Codes: " + JSON.stringify(moduleContext.success));
      } catch (e) {
        //ignore
      }
    }
    if (message == null && moduleContext.success) {
      if (global.logger.isTraceEnabled()) {
        session.emit(WS.CONSOLE, VERBOSE.TRACE, "Checking responseCode belongs to Success codes");
      }
      message = msgLookup(session, moduleContext.success, responseCode);
    }

    if (global.logger.isTraceEnabled()) {
      try {
        session.emit(WS.CONSOLE, VERBOSE.TRACE, "Custom Codes: " + JSON.stringify(moduleContext.customErrors));
      } catch (e) {
        //ignore
      }
    }
    if (message == null && moduleContext.customErrors) {
      if (global.logger.isTraceEnabled()) {
        session.emit(WS.CONSOLE, VERBOSE.TRACE, "Checking responseCode belongs to Custom Error codes");
      }
      for (i = 0; i < moduleContext.customErrors.length; i++) {
        message = msgLookup(session, moduleContext.customErrors[i], responseCode);
        if (message != null) {
          if (global.logger.isTraceEnabled()) {
            session.emit(WS.CONSOLE, VERBOSE.TRACE, "Message: " + message);
          }
          break;
        }
      }
    }
    if (message == null) {
      if (global.logger.isTraceEnabled()) {
        session.emit(WS.CONSOLE, VERBOSE.TRACE, "Checking responseCode belongs to default Error code");
      }
      message = moduleContext.defaultError && moduleContext.defaultError.message || DEFAULT_MESSAGE;
    }
    return session.macros.replaceAllPlaceHolders(message);
  }
};

function isBetween(x, min, max) {
  return x >= min && x <= max;
}

function msgLookup(session, moduleContext, responseCode) {
  if (global.logger.isTraceEnabled()) {
    try {
      session.emit(WS.CONSOLE, VERBOSE.TRACE, "msgLookup:" + JSON.stringify(moduleContext) + ", ResponseCode: " + responseCode);
    } catch (e) {
      //ignore
    }
  }
  if (moduleContext.hasOwnProperty(responseCode)) {
    return moduleContext[responseCode];
  }

  if (moduleContext.code != null) {
    if (moduleContext.code.hasOwnProperty(responseCode) ||
      isNaN(responseCode) && moduleContext.code.hasOwnProperty(responseCode.substring(1))) {
      return moduleContext.message;
    }

    let i, j, code, array;
    for (i = 0; i < moduleContext.code.length; i++) {
      code = moduleContext.code[i];
      if (code == responseCode) {
        return moduleContext.message;
      }
      else if (code.includes("-")) {
        array = code.split("-");
        for (j = 0; j < array.length; j++) {
          let temp = Number(array[j]);
          if (isNaN(temp)) {
            array[j] = Number(array[j].slice(1));
          }
        }
        if (isNaN(responseCode)) {
          responseCode = Number(responseCode.slice(1));
        }
        if (isBetween(responseCode, array[0], array[array.length - 1])) {
          return moduleContext.message;
        }
      }
    }
  }
  return null;
}
