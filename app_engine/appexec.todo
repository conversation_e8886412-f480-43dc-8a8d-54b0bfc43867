
✔ KPI write to <PERSON><PERSON><PERSON> file @done (2018-4-27 12:29:59)
✔ CDR write to elasticsearch @done (2018-4-28 17:15:39)
✔ Uniform plugin Response format @done (10/24/2018, 4:44:22 PM)
✔ Catch exec error time @done (2018-6-11 11:24:12)
✔ Respond state as execute state, End module refactoring @done (2018-4-27 11:13:38)
✔ Loading Launched Apps with zero errors. @done (10/24/2018, 4:44:30 PM)
✔ ioredis implementation in Session Cache, use of JSON @done (2018-4-25 14:14:32)
✔ KPI for redis operation, cache @done (2018-5-2 07:38:55)
✔ Deep data trversal @done (2018-4-26 12:45:09)
✔ OAM integration @done (2018-5-2 07:38:39)
✔ Handling POST @done (2018-4-30 14:54:22)
✔ New Menu JSON integration @done (2018-5-4 17:35:05)
✔ Handling fallback @done (2018-5-8 06:58:05)
✔ Event based OAM @done (2018-7-16 19:41:49)
✔ User Session timeout @done (2018-6-13 14:21:29)
✔ Dynamic Menu handling @done (9/14/2018, 7:15:37 PM)
✔ TPS management @done (2018-6-11 11:23:51)
✔ Handle If end module is missing @done (2018-5-15 16:06:26)
✔ Event based AppCache loadin reloading @done (9/14/2018, 7:15:43 PM)
✔ AppCache Loading/reloading based on lauch or schedule date @done (9/14/2018, 7:15:55 PM)
✔ Schedule management @done (10/9/2018, 4:22:08 PM)
✔ mode wise app cache Loading @done (10/9/2018, 4:22:54 PM)
✔ session serialize and deserialize correction @done (10/24/2018, 4:44:56 PM)
✔ API Doc @done(19-05-02 20:11)
✘ Dockerizing a Node.js web app @cancelled(18-12-18 17:39)
☐ Move menu into PM
☐ CAAS
✘ modulewise cdr logs @cancelled(18-12-18 17:39)
☐ encrypted password for configtree redis access
✔ SMPP Enquiry link, Session management @done (11/12/2018, 12:38:58 PM)
☐ HTTP body encoded for XML payload
✔ Multiple Plugin settings @done(18-12-18 17:38)
    ✔ MSISDN series based @done(18-12-18 17:38)
    ✔ Round robin @done(18-12-18 17:38)
    ✔ Use dev settings itself @done(18-12-18 17:38)
✔ Menu Visibility feature @critical @done(23-10-19 17:38)
✔ App Export/Import. JSON @low @done(19-05-02 20:11)
✔ App Archive
☐ Menu Clone
✔ Module Clone @done(19-05-31 08:18)
☐ Menu Import/Export
✔ SMPP rebinding @done(19-11-28 07:48)
✔ appmetrics to monitor the performance and profiling @done(20-05-12 09:07)
☐ USSD Gateway account creation UDP integration
☐ Audit trail not listing
☐ CDR Node logger not working with Docker ELK
☐ Influx DB replication 
✔ Update nodejs Version to make use of streaming fixes done by community @done(20-06-30 23:11)
☐ App engine health monitor
☐ Alert management for Plugin internal queue increase
☐ Implement plugin CDR for ALL plugin