{"name": "leap", "version": "3.0.9", "description": "Lean Enterprise Applications Platform", "author": "<EMAIL>", "license": "ISC", "main": "./srcs/index.js", "repository": {"type": "git", "url": "http://blrgitlab.comviva.com/mbs/MBS-LEAP-3.0.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node ./srcs/index.js"}, "keywords": ["leap", "app_engine"], "dependencies": {"@opentelemetry/api": "^1.7.0", "@opentelemetry/auto-instrumentations-node": "^0.40.3", "@opentelemetry/exporter-prometheus": "^0.46.0", "@opentelemetry/metrics": "^0.24.0", "@opentelemetry/sdk-node": "^0.46.0", "app_kpi": "file:../../libs/app_kpi", "app_macros": "file:../../libs/app_macros", "app_store": "file:../../libs/app_store", "app_validation": "file:../../libs/app_validation", "axios": "^0.27.2", "basic-auth": "^2.0.1", "bluebird": "^3.7.2", "colors": "^1.4.0", "commander": "^5.1.0", "common": "file:../../libs/common", "config-tree": "file:../../libs/config-tree", "dateformat": "^3.0.3", "express": "^4.17.1", "express-rate-limit": "^7.1.4", "file-stream-rotator": "^0.5.7", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.15", "log4js": "6.3.0", "message": "file:../../libs/message", "mkdirp": "^1.0.4", "morgan": "^1.10.0", "oam": "file:../../libs/oam", "pluginmanager": "file:../../libs/PluginManager", "preference_store": "file:../../libs/preference_store", "query-string": "^6.13.1", "request": "^2.88.2", "smpp": "file:../../extlibs/smpp", "utility": "file:../../libs/utility", "uuid": "^8.2.0", "ws": "^7.3.1", "xml2json": "file:../../extlibs/xml2json", "nyc": "15.1.0"}}