
<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>LEAP App Engine API Reference</title>
    <link rel="shortcut icon" type="image/x-icon" href="images/fav.ico" sizes="16x16" />
    <style>
      .highlight table td { padding: 5px; }
.highlight table pre { margin: 0; }
.highlight .gh {
  color: #999999;
}
.highlight .sr {
  color: #f6aa11;
}
.highlight .go {
  color: #888888;
}
.highlight .gp {
  color: #555555;
}
.highlight .gs {
}
.highlight .gu {
  color: #aaaaaa;
}
.highlight .nb {
  color: #f6aa11;
}
.highlight .cm {
  color: #75715e;
}
.highlight .cp {
  color: #75715e;
}
.highlight .c1 {
  color: #75715e;
}
.highlight .cs {
  color: #75715e;
}
.highlight .c, .highlight .cd {
  color: #75715e;
}
.highlight .err {
  color: #960050;
}
.highlight .gr {
  color: #960050;
}
.highlight .gt {
  color: #960050;
}
.highlight .gd {
  color: #49483e;
}
.highlight .gi {
  color: #49483e;
}
.highlight .ge {
  color: #49483e;
}
.highlight .kc {
  color: #66d9ef;
}
.highlight .kd {
  color: #66d9ef;
}
.highlight .kr {
  color: #66d9ef;
}
.highlight .no {
  color: #66d9ef;
}
.highlight .kt {
  color: #66d9ef;
}
.highlight .mf {
  color: #ae81ff;
}
.highlight .mh {
  color: #ae81ff;
}
.highlight .il {
  color: #ae81ff;
}
.highlight .mi {
  color: #ae81ff;
}
.highlight .mo {
  color: #ae81ff;
}
.highlight .m, .highlight .mb, .highlight .mx {
  color: #ae81ff;
}
.highlight .sc {
  color: #ae81ff;
}
.highlight .se {
  color: #ae81ff;
}
.highlight .ss {
  color: #ae81ff;
}
.highlight .sd {
  color: #e6db74;
}
.highlight .s2 {
  color: #e6db74;
}
.highlight .sb {
  color: #e6db74;
}
.highlight .sh {
  color: #e6db74;
}
.highlight .si {
  color: #e6db74;
}
.highlight .sx {
  color: #e6db74;
}
.highlight .s1 {
  color: #e6db74;
}
.highlight .s {
  color: #e6db74;
}
.highlight .na {
  color: #a6e22e;
}
.highlight .nc {
  color: #a6e22e;
}
.highlight .nd {
  color: #a6e22e;
}
.highlight .ne {
  color: #a6e22e;
}
.highlight .nf {
  color: #a6e22e;
}
.highlight .vc {
  color: #ffffff;
}
.highlight .nn {
  color: #ffffff;
}
.highlight .nl {
  color: #ffffff;
}
.highlight .ni {
  color: #ffffff;
}
.highlight .bp {
  color: #ffffff;
}
.highlight .vg {
  color: #ffffff;
}
.highlight .vi {
  color: #ffffff;
}
.highlight .nv {
  color: #ffffff;
}
.highlight .w {
  color: #ffffff;
}
.highlight {
  color: #ffffff;
}
.highlight .n, .highlight .py, .highlight .nx {
  color: #ffffff;
}
.highlight .ow {
  color: #f92672;
}
.highlight .nt {
  color: #f92672;
}
.highlight .k, .highlight .kv {
  color: #f92672;
}
.highlight .kn {
  color: #f92672;
}
.highlight .kp {
  color: #f92672;
}
.highlight .o {
  color: #f92672;
}
    </style>
    <link href="stylesheets/screen.css" rel="stylesheet" media="screen" />
    <link href="stylesheets/print.css" rel="stylesheet" media="print" />
      <script src="javascripts/all.js"></script>
  </head>

  <body class="index" data-languages="[]">
    <a href="#" id="nav-button">
      <span>
        NAV
        <img src="images/navbar.png" alt="Navbar" />
      </span>
    </a>
    <div class="toc-wrapper">
        <div class="search">
          <input type="text" class="search" id="input-search" placeholder="Search">
        </div>
        <ul class="search-results"></ul>
      <ul id="toc" class="toc-list-h1">
          <li>
            <a href="#introduction" class="toc-h1 toc-link" data-title="Introduction">Introduction</a>
          </li>
          <li>
            <a href="#document-history" class="toc-h1 toc-link" data-title="Document History">Document History</a>
          </li>
          <li>
            <a href="#abbreviations-acronyms-amp-definitions" class="toc-h1 toc-link" data-title="Abbreviations, Acronyms &amp; Definitions">Abbreviations, Acronyms &amp; Definitions</a>
          </li>
          <li>
            <a href="#web-socket-interface-application-simulation" class="toc-h1 toc-link" data-title="Web Socket Interface: Application Simulation">Web Socket Interface: Application Simulation</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#design" class="toc-h2 toc-link" data-title="Design">Design</a>
                  </li>
                  <li>
                    <a href="#websocket" class="toc-h2 toc-link" data-title="Websocket">Websocket</a>
                  </li>
                  <li>
                    <a href="#register-console" class="toc-h2 toc-link" data-title="Register Console">Register Console</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#http-s-pull-interface-application-execution" class="toc-h1 toc-link" data-title="HTTP(S) Pull Interface: Application Execution">HTTP(S) Pull Interface: Application Execution</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#query-string-interface" class="toc-h2 toc-link" data-title="Query String Interface">Query String Interface</a>
                  </li>
                  <li>
                    <a href="#json-request-response" class="toc-h2 toc-link" data-title="JSON Request/Response">JSON Request/Response</a>
                  </li>
                  <li>
                    <a href="#cps-xml-request-response" class="toc-h2 toc-link" data-title="CPS-XML Request/Response">CPS-XML Request/Response</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#http-s-push-interface-application-execution" class="toc-h1 toc-link" data-title="HTTP(S) Push Interface: Application Execution">HTTP(S) Push Interface: Application Execution</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#ussd-push-interface" class="toc-h2 toc-link" data-title="USSD Push Interface">USSD Push Interface</a>
                  </li>
                  <li>
                    <a href="#app-push-interface" class="toc-h2 toc-link" data-title="App Push Interface">App Push Interface</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#leap-cdrs" class="toc-h1 toc-link" data-title="LEAP CDRs">LEAP CDRs</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#app-engine-cdr" class="toc-h2 toc-link" data-title="App Engine CDR">App Engine CDR</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#errors" class="toc-h1 toc-link" data-title="Errors">Errors</a>
          </li>
      </ul>
    </div>
    <div class="page-wrapper">
      <div class="dark-box"></div>
      <div class="content">
        <h1 id='introduction'>Introduction</h1><pre class="highlight javascript tab-javascript"><code><span class="kd">let</span> <span class="nx">request</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="s1">'request'</span><span class="p">);</span>
<span class="nx">request</span><span class="p">(</span><span class="s1">'http://*************:4508/app_engine/development/1535665591227?msisdn=254750330721&amp;RefillId=MC46&amp;bundleinfo=25MB&amp;tense=future&amp;edata1=USSD_DATA&amp;edata2=Volume_kes10_1Day_25MB&amp;price=10&amp;day=1&amp;authkey=Y6EgamyWeTx5bKe409k4gqRviSh5PFkKRdTdbki0nHQ&amp;code=544&amp;reqType=once'</span><span class="p">,</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">error</span><span class="p">,</span> <span class="nx">response</span><span class="p">,</span> <span class="nx">body</span><span class="p">)</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">'error:'</span><span class="p">,</span> <span class="nx">error</span><span class="p">);</span> <span class="c1">// Print the error if one occurred</span>
  <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">'statusCode:'</span><span class="p">,</span> <span class="nx">response</span> <span class="o">&amp;&amp;</span> <span class="nx">response</span><span class="p">.</span><span class="nx">statusCode</span><span class="p">);</span> <span class="c1">// Print the response status code if a response was received</span>
  <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">'body:'</span><span class="p">,</span> <span class="nx">body</span><span class="p">);</span> <span class="c1">// Print the JSON for the LEAP gateway configapi.</span>
<span class="p">});</span>
</code></pre>
<p><b>Welcome to the LEAP App Engine API!</b></p>

<p>App Engine Service is a NodeJS based Microservice, this microservice is a core component of the LEAP. It provides the Application execution environment for the Apps created and launched by the developers. These Apps are modelled based on business logic.</p>

<p>This service runs in a cluster mode, it is highly scalable interface and it is used to process the subscriber&#39;s requests as per the business logic. Apps are designed based on the business need using various &quot;Module&quot; available in the LEAP Developer&#39;s App creation page. Module is a sub-process of the App, based on the user input and the outcomes of previous step next Module will be processed.</p>

<p>LEAP Orchestrator is backend application execution module. GUI studio creates the service flow,a  series  of  well-defined  activity  paths  for  the  application,  and  stores  them  as  templates.  Backend orchestrator then loads the application dialog templates created; at either startup or hot reloaded. This  orchestrator  application  manages  interactive  user  sessions  over  any  of  the  communication channels (SMS, USSD). For example, when a user is interacting with a service provider via USSD, the user  will  navigate  between  many  levelsof  menus,  make  certain  choices  at  each  level,  or  navigate between several menus. From the server perspective, this can be an asynchronous interaction that could  end  in  a  minute,  or  continue  for  10  minutes  or  more. All  this  while,  the  server  has  to  keep track  of  the  state  of  each  user  (viz.,  when  menu  is  this  user  in,  what  choices  are  made,  on  which content provider is our app waiting, and so on and so forth.</p>

<p>Some of the salient features are:
<ul>
<li>Input/output Plugins: Ability to support arbitrary communication protocols and interfaces via additional input / output interfaces</li>
<li>Asynchronous and multi-threaded</li>
<li>Hot deployment of new /modified applications</li>
</ul></p>

<p>LEAP provides the flexibility and controlled way for developer to create the application by defining the application flows using drag and drop connectors.
<ul>
<li>Develop and test application
</li>
<li>Manage and launch application
</li>
<li>Configuration and Administration
</li>
<li>System requirements
</li>
<li>Dashboards and reports</li>
</ul></p>
<h1 id='document-history'>Document History</h1>
<table><thead>
<tr>
<th>Version</th>
<th>Date</th>
<th>Description</th>
<th>Author</th>
</tr>
</thead><tbody>
<tr>
<td>1</td>
<td>15-Nov-2017</td>
<td><ul><li>Initial draft</li></ul></td>
<td>Yuvaraj k</td>
</tr>
<tr>
<td>2</td>
<td>11-Aug-2018</td>
<td><ul><li>Added HTTP QS Interface</li></ul></td>
<td>Yuvaraj k</td>
</tr>
</tbody></table>
<h1 id='abbreviations-acronyms-amp-definitions'>Abbreviations, Acronyms &amp; Definitions</h1>
<table><thead>
<tr>
<th>Term</th>
<th>Full form</th>
</tr>
</thead><tbody>
<tr>
<td>ACIP</td>
<td>Account Administration Communication Integration Protocol</td>
</tr>
<tr>
<td>API</td>
<td>Application Programming Interface</td>
</tr>
<tr>
<td>CDR</td>
<td>Call Data Records</td>
</tr>
<tr>
<td>DB</td>
<td>Databases</td>
</tr>
<tr>
<td>FLARES</td>
<td>Framework for Launching Applications enabling Rapid Evolution of Services</td>
</tr>
<tr>
<td>GUI</td>
<td>Graphical User Interface</td>
</tr>
<tr>
<td>HTTP</td>
<td>Hypertext Transfer Protocol</td>
</tr>
<tr>
<td>IN</td>
<td>Intelligent Network</td>
</tr>
<tr>
<td>IVR</td>
<td>Interactive Voice Response</td>
</tr>
<tr>
<td>LEAP</td>
<td>Launch Easy Application Platform</td>
</tr>
<tr>
<td>OAM</td>
<td>Operations and Maintenance</td>
</tr>
<tr>
<td>OS</td>
<td>Operating System</td>
</tr>
<tr>
<td>PRD</td>
<td>Product Requirement Document</td>
</tr>
<tr>
<td>SMS</td>
<td>Short Message Services</td>
</tr>
<tr>
<td>SOAP</td>
<td>Simple Object Access Protocol</td>
</tr>
<tr>
<td>TCP/IP</td>
<td>Transmission Control Protocol / Internet Protocol</td>
</tr>
<tr>
<td>TPS</td>
<td>Transactions per Second</td>
</tr>
<tr>
<td>UCIP</td>
<td>User Communication Integration Protocol</td>
</tr>
<tr>
<td>USSD</td>
<td>Unstructured Supplementary Service Data</td>
</tr>
<tr>
<td>VSIP</td>
<td>Voucher Server Communication Integration Protocol</td>
</tr>
</tbody></table>
<h1 id='web-socket-interface-application-simulation'>Web Socket Interface: Application Simulation</h1>
<p>LEAP App simulation page has console window, which inturn uses the Websocket for client and server interactions.</p>

<p>WebSocket is a simple to use, blazing fast, and thoroughly tested WebSocket client and server implementation.</p>

<aside class="notice"><b>NPM Module: </b>is https://www.npmjs.com/package/ws Version: 4.0.0</aside>
<h2 id='design'>Design</h2>
<p><b>Step 1: Pre-Launch settings</b>
<p align="center"><img src="images/AppSimulation-Step1.jpeg" width=700 alt="Workflow Management Design"></p></p>

<p><b>Step 2: Processing module</b>
<p align="center"><img src="images/AppSimulation-Step2.jpeg" width=700 alt="Workflow Management Design"></p></p>
<h2 id='websocket'>Websocket</h2>
<p><b>Following steps for setting web console session.</b></p>

<ul>
<li>
1. Invoke config API to get websocket http://*************:7778/leap_gw/configapi/simulationinfo  
    Response payload:
    {
        "app_url": "http://*************:4508/app_engine/development/",
        "websocket_url": "ws://*************:4508"
    }

</li>
<li>
2. Open new websocket client for "websocket_url" "ws://*************:4508"
</li>
<li>
3. Register the AppId and Invoker by sending following message to the server.

WS_REGISTER|{invokerId}|{appId}

example:
WS_REGISTER|5|1517380984746


Response payload for above request, 

WS_REGISTER|WS_OK|{sessionId}

WS_REGISTER|WS_NOK|App not found
WS_REGISTER|WS_NOK|Unauthorized
WS_REGISTER|WS_NOK|Internal error

example:
WS_REGISTER|WS_OK|b6dba1da-5993-4967-9245-a3bf65dff278
</li>
<li>
4. When registration of console is successful then fire a restful api request for the app simulation launch request.

GET: {app_url}/app_engine/development/{appId}?MSISDN=**********&debugId={sessionId} along with other query parameters.

example:

http://*************:4508/app_engine/development/1517380984746?MSISDN=**********&subscriberNumber=**********&subscriberNumberNAI=**********&transactionCurrency=transactionCurrency&originTransactionID=1&originTimeStamp=********&debugId=b6dba1da-5993-4967-9245-a3bf65dff278 
</li>
<li>
5. Optional: For changing console settings, send following message to server over websocket.

WS_SETTINGS|{sessionId}|{"verbosity": "trace", "pbm": "Y"}

or

WS_SETTINGS|{sessionId}|{"verbosity": "trace"}

or

WS_SETTINGS|{sessionId}|{"pbm": "Y"}

</li>
<li>
6. For selecting next module, send following message to server over websocket.

WS_NEXT|{sessionId}

example: 

WS_NEXT|b6dba1da-5993-4967-9245-a3bf65dff278

</li>
<li>
7. For changing the VARS, send following message to server over websocket.

WS_VARS|{vars}

example:

WS_VARS|{"MSISDN":"**********","subscriberNumber":"**********","subscriberNumberNAI":"**********","transactionCurrency":"transactionCurrency","originTransactionID":"1","originTimeStamp":"********","debugId":"b6dba1da-5993-4967-9245-a3bf65dff278","host":"*************","port":59900,"path":"/jsp/respose.jsp","originNodeType":"EXT","originHostName":"TestClient","dedicatedAccountIDFirst":8,"requestSubDedicatedAccountDetailsFlag":1,"requestFirstAccessibleAndExpiredBalanceAndDateFlag":1,"requestActiveOffersFlag":0,"requestAttributesFlag":0,"negotiatedCapabilities":0,"requestAggregatedProductOfferInformationFlag":0,"messageCapabilityFlag":0,"dedicatedAccountSelection":1,"chargingRequestInformation":""}
</li>
</ul>
<h2 id='register-console'>Register Console</h2>
<p>Use this endpoint to delete application in the system.</p>
<h3 id='http-request'>HTTP Request</h3>
<p><code>GET http://example.com/app_engine/development/:appId</code></p>

<aside class="notice"><b>appId: </b>is application id</aside>
<h3 id='header'>Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='error-codes'>Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>202</td>
<td>Accepted</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>V9004</td>
<td>MSISDN query parameter is missing in the request</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>V9006</td>
<td>Mising some query parameters</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>V9009</td>
<td>Access denied because of missing/wrong credentials</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>V9007</td>
<td>Application not found in App store</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>V9008</td>
<td>Application you are trying to access is not available in this engine</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>V9010</td>
<td>Application is incomplete End module is missing</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>E9000</td>
<td>Application Management Internal Error</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Register Console API!
</aside>
<h1 id='http-s-pull-interface-application-execution'>HTTP(S) Pull Interface: Application Execution</h1><h2 id='query-string-interface'>Query String Interface</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="err">
https://localhost:9002/app_engine/development/1564736997957?MSISDN=919876543210

</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">X-Powered-By</span><span class="p">:</span> <span class="s">Express</span>
<span class="na">Access-Control-Allow-Origin</span><span class="p">:</span> <span class="s">*</span>
<span class="na">Access-Control-Allow-Methods</span><span class="p">:</span> <span class="s">GET,PUT,POST,PATCH,DELETE,OPTIONS</span>
<span class="na">Access-Control-Allow-Headers</span><span class="p">:</span> <span class="s">Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId</span>
<span class="na">X-FRAME-OPTIONS</span><span class="p">:</span> <span class="s">SAMEORIGIN</span>
<span class="na">X-XSS-Protection</span><span class="p">:</span> <span class="s">1;mode=block</span>
<span class="na">X-Content-Type-Options</span><span class="p">:</span> <span class="s">nosniff</span>
<span class="na">Content-Security-Policy</span><span class="p">:</span> <span class="s">script-src 'self'</span>
<span class="na">X-Permitted-Cross-Domain-Policies</span><span class="p">:</span> <span class="s">none</span>
<span class="na">Referrer-Policy</span><span class="p">:</span> <span class="s">no-referrer</span>
<span class="na">Strict-Transport-Security</span><span class="p">:</span> <span class="s">max-age=31536000 ; includeSubDomains</span>
<span class="na">txnId</span><span class="p">:</span> <span class="s">11565513729019</span>
<span class="na">Freeflow</span><span class="p">:</span> <span class="s">FB</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">text/html; charset=utf-8</span>
<span class="na">Content-Length</span><span class="p">:</span> <span class="s">59</span>
<span class="na">ETag</span><span class="p">:</span> <span class="s">W/"3b-bHrx43OLSLTui5JOAH4sbhHo4Bo"</span>
<span class="na">Date</span><span class="p">:</span> <span class="s">Sun, 11 Aug 2019 08:55:29 GMT</span>
<span class="na">Connection</span><span class="p">:</span> <span class="s">keep-alive</span>

Dear Customer, Your request has been successfully processed

</code></pre>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="err">
https://localhost:9002/app_engine/development/1564736997957?

</span></code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad Request</span>
<span class="na">X-Powered-By</span><span class="p">:</span> <span class="s">Express</span>
<span class="na">Access-Control-Allow-Origin</span><span class="p">:</span> <span class="s">*</span>
<span class="na">Access-Control-Allow-Methods</span><span class="p">:</span> <span class="s">GET,PUT,POST,PATCH,DELETE,OPTIONS</span>
<span class="na">Access-Control-Allow-Headers</span><span class="p">:</span> <span class="s">Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId</span>
<span class="na">X-FRAME-OPTIONS</span><span class="p">:</span> <span class="s">SAMEORIGIN</span>
<span class="na">X-XSS-Protection</span><span class="p">:</span> <span class="s">1;mode=block</span>
<span class="na">X-Content-Type-Options</span><span class="p">:</span> <span class="s">nosniff</span>
<span class="na">Content-Security-Policy</span><span class="p">:</span> <span class="s">script-src 'self'</span>
<span class="na">X-Permitted-Cross-Domain-Policies</span><span class="p">:</span> <span class="s">none</span>
<span class="na">Referrer-Policy</span><span class="p">:</span> <span class="s">no-referrer</span>
<span class="na">Strict-Transport-Security</span><span class="p">:</span> <span class="s">max-age=31536000 ; includeSubDomains</span>
<span class="na">txnId</span><span class="p">:</span> <span class="s">11565513866923</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json; charset=utf-8</span>
<span class="na">Content-Length</span><span class="p">:</span> <span class="s">73</span>
<span class="na">ETag</span><span class="p">:</span> <span class="s">W/"49-RiReICB1z/mE/CJ7NpyvL2Ou1SA"</span>
<span class="na">Date</span><span class="p">:</span> <span class="s">Sun, 11 Aug 2019 08:57:46 GMT</span>
<span class="na">Connection</span><span class="p">:</span> <span class="s">keep-alive</span>

<span class="p">{</span><span class="w">
  </span><span class="s2">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"V9004"</span><span class="p">,</span><span class="w">
  </span><span class="s2">"msg"</span><span class="p">:</span><span class="w"> </span><span class="s2">"MSISDN query parameter is missing in the request"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<p>Use this endpoint to execute an application, which is created in LEAP Platform.</p>
<h3 id='http-request-2'>HTTP Request</h3>
<p><code>GET http://example.com/app_engine/:mode/:appId</code></p>

<aside class="notice">
<b>appId:</b> is Application Identifier
</aside>
<h3 id='app-engine-modes'>App Engine Modes</h3>
<table><thead>
<tr>
<th>Mode</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>development</td>
<td>Application execution which supports step-by-step Simulation of an application.</td>
</tr>
<tr>
<td>staging</td>
<td>Staging server for an application for integration test. Pre-Production</td>
</tr>
<tr>
<td>production</td>
<td>Production server for an application for Go-Live.</td>
</tr>
</tbody></table>
<h3 id='http-request-header'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>Basic YWRtaW46YWRtaW4=</td>
<td>Follow below proceedure to generate the token.</td>
</tr>
</tbody></table>
<h3 id='procedure-to-generate-authorization-value'>Procedure to generate Authorization value</h3><h4 id='1-concatenate-the-user-name-with-a-colon-and-the-password-for-example-a-user-name-of-admin-and-a-password-of-admin-becomes-the-following-string'>1. Concatenate the user name with a colon, and the password. For example, a user name of admin, and a password of admin becomes the following string:</h4>
<p>admin:admin</p>
<h4 id='2-encode-this-user-name-and-password-string-in-base64-encoding'>2. Encode this user name and password string in base64 encoding.</h4><h4 id='3-include-this-encoded-user-name-and-password-in-an-http-authorization-basic-header-for-example-with-an-encoded-user-name-of-admin-and-a-password-of-admin-the-following-header-is-created'>3. Include this encoded user name and password in an HTTP Authorization: Basic header. For example, with an encoded user name of admin, and a password of admin, the following header is created:</h4>
<p>Authorization: Basic YWRtaW46YWRtaW4=</p>
<h3 id='http-request-query-parameters'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Description</th>
<th>Required</th>
</tr>
</thead><tbody>
<tr>
<td>MSISDN</td>
<td>Mobile Station International Subscriber Directory Number</td>
<td>Mandatory</td>
</tr>
<tr>
<td>subscriberInput</td>
<td>Current input of the subscriber</td>
<td>Mandatory</td>
</tr>
<tr>
<td>isNewRequest</td>
<td>Identifying whether the request is new free flow request or continuous request</td>
<td>Mandatory</td>
</tr>
<tr>
<td>language</td>
<td>Subscriber language code</td>
<td>Optional</td>
</tr>
<tr>
<td>...</td>
<td>Reset of the parameters defined in application</td>
<td>Mandatory</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameter-isnewrequest'>HTTP Request Query Parameter - isNewRequest</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>0</td>
<td>Indicate process the subscriberInput for existing session</td>
</tr>
<tr>
<td>1</td>
<td>Force to Create new session.</td>
</tr>
</tbody></table>
<h3 id='http-response-header-freeflow'>HTTP Response Header-Freeflow</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>FB</td>
<td>Free flow break, which indicates end of session</td>
</tr>
<tr>
<td>FC</td>
<td>Free flow continue, indicates requires more inputs.</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>OK</td>
<td>-</td>
<td>Success/Failure code defined in Application</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>V9004</td>
<td>MSISDN query parameter is missing in the request</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>V9006</td>
<td>Mising some query parameters</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>V9009</td>
<td>Access denied because of missing/wrong credentials</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>V9007</td>
<td>Application not found in App store</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>V9008</td>
<td>Application you are trying to access is not available in this engine</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>V9010</td>
<td>Application is incomplete End module is missing</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>E9000</td>
<td>Application Management Internal Error</td>
</tr>
</tbody></table>

<aside class="success">
Remember — HTTP(S)-Query string interface API!
</aside>
<h2 id='json-request-response'>JSON Request/Response</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="err">
https://localhost:9002/app_engine/development/1564736997957?

</span></code></pre><pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"MSISDN"</span><span class="p">:</span><span class="w"> </span><span class="s2">"919876543210"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"subscriberInput"</span><span class="p">:</span><span class="w"> </span><span class="s2">"m"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"subscriberType"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w">
    </span><span class="s2">"key1"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Text Value"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"key2"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="s2">"key1"</span><span class="p">:</span><span class="w"> </span><span class="s2">"JSON Value"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="s2">"key3"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
        </span><span class="s2">"Array value"</span><span class="w">
    </span><span class="p">]</span><span class="w">
</span><span class="p">}</span><span class="w">

</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">X-Powered-By</span><span class="p">:</span> <span class="s">Express</span>
<span class="na">Access-Control-Allow-Origin</span><span class="p">:</span> <span class="s">*</span>
<span class="na">Access-Control-Allow-Methods</span><span class="p">:</span> <span class="s">GET,PUT,POST,PATCH,DELETE,OPTIONS</span>
<span class="na">Access-Control-Allow-Headers</span><span class="p">:</span> <span class="s">Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId</span>
<span class="na">X-FRAME-OPTIONS</span><span class="p">:</span> <span class="s">SAMEORIGIN</span>
<span class="na">X-XSS-Protection</span><span class="p">:</span> <span class="s">1;mode=block</span>
<span class="na">X-Content-Type-Options</span><span class="p">:</span> <span class="s">nosniff</span>
<span class="na">Content-Security-Policy</span><span class="p">:</span> <span class="s">script-src 'self'</span>
<span class="na">X-Permitted-Cross-Domain-Policies</span><span class="p">:</span> <span class="s">none</span>
<span class="na">Referrer-Policy</span><span class="p">:</span> <span class="s">no-referrer</span>
<span class="na">Strict-Transport-Security</span><span class="p">:</span> <span class="s">max-age=31536000 ; includeSubDomains</span>
<span class="na">txnId</span><span class="p">:</span> <span class="s">11565519643989</span>
<span class="na">Freeflow</span><span class="p">:</span> <span class="s">FB</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json; charset=utf-8</span>
<span class="na">Content-Length</span><span class="p">:</span> <span class="s">80</span>
<span class="na">ETag</span><span class="p">:</span> <span class="s">W/"50-GwQNSx7BXkplvkz/MfLYWNdM1hk"</span>
<span class="na">Date</span><span class="p">:</span> <span class="s">Sun, 11 Aug 2019 10:34:05 GMT</span>
<span class="na">Connection</span><span class="p">:</span> <span class="s">keep-alive</span>

<span class="p">{</span><span class="w">
    </span><span class="s2">"code"</span><span class="p">:</span><span class="w"> </span><span class="mi">200</span><span class="p">,</span><span class="w">
    </span><span class="s2">"msg"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Dear Customer, Your request has been successfully processed"</span><span class="w">
</span><span class="p">}</span><span class="w">

</span></code></pre>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="err">
https://localhost:9002/app_engine/development/1564736997957?

</span></code></pre><pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"subscriberInput"</span><span class="p">:</span><span class="w"> </span><span class="s2">"m"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"subscriberType"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w">
    </span><span class="s2">"key1"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Text Value"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"key2"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="s2">"key1"</span><span class="p">:</span><span class="w"> </span><span class="s2">"JSON Value"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="s2">"key3"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
        </span><span class="s2">"Array value"</span><span class="w">
    </span><span class="p">]</span><span class="w">
</span><span class="p">}</span><span class="w">

</span></code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad Request</span>
<span class="na">X-Powered-By</span><span class="p">:</span> <span class="s">Express</span>
<span class="na">Access-Control-Allow-Origin</span><span class="p">:</span> <span class="s">*</span>
<span class="na">Access-Control-Allow-Methods</span><span class="p">:</span> <span class="s">GET,PUT,POST,PATCH,DELETE,OPTIONS</span>
<span class="na">Access-Control-Allow-Headers</span><span class="p">:</span> <span class="s">Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId</span>
<span class="na">X-FRAME-OPTIONS</span><span class="p">:</span> <span class="s">SAMEORIGIN</span>
<span class="na">X-XSS-Protection</span><span class="p">:</span> <span class="s">1;mode=block</span>
<span class="na">X-Content-Type-Options</span><span class="p">:</span> <span class="s">nosniff</span>
<span class="na">Content-Security-Policy</span><span class="p">:</span> <span class="s">script-src 'self'</span>
<span class="na">X-Permitted-Cross-Domain-Policies</span><span class="p">:</span> <span class="s">none</span>
<span class="na">Referrer-Policy</span><span class="p">:</span> <span class="s">no-referrer</span>
<span class="na">Strict-Transport-Security</span><span class="p">:</span> <span class="s">max-age=31536000 ; includeSubDomains</span>
<span class="na">txnId</span><span class="p">:</span> <span class="s">11565513866923</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json; charset=utf-8</span>
<span class="na">Content-Length</span><span class="p">:</span> <span class="s">73</span>
<span class="na">ETag</span><span class="p">:</span> <span class="s">W/"49-RiReICB1z/mE/CJ7NpyvL2Ou1SA"</span>
<span class="na">Date</span><span class="p">:</span> <span class="s">Sun, 11 Aug 2019 08:57:46 GMT</span>
<span class="na">Connection</span><span class="p">:</span> <span class="s">keep-alive</span>

<span class="p">{</span><span class="w">
  </span><span class="s2">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"V9004"</span><span class="p">,</span><span class="w">
  </span><span class="s2">"msg"</span><span class="p">:</span><span class="w"> </span><span class="s2">"MSISDN query parameter is missing in the request"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<p>Use this endpoint to execute an application, which is created in LEAP Platform.</p>
<h3 id='http-request-3'>HTTP Request</h3>
<p><code>POST http://example.com/app_engine/:mode/:appId</code></p>

<aside class="notice">
<b>appId:</b> is Application Identifier
</aside>
<h3 id='app-engine-modes-2'>App Engine Modes</h3>
<table><thead>
<tr>
<th>Mode</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>development</td>
<td>Application execution which supports step-by-step Simulation of an application.</td>
</tr>
<tr>
<td>staging</td>
<td>Staging server for an application for integration test. Pre-Production</td>
</tr>
<tr>
<td>production</td>
<td>Production server for an application for Go-Live.</td>
</tr>
</tbody></table>
<h3 id='http-request-header-2'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>Basic YWRtaW46YWRtaW4=</td>
<td>Follow below proceedure to generate the token.</td>
</tr>
</tbody></table>
<h3 id='procedure-to-generate-authorization-value-2'>Procedure to generate Authorization value</h3><h4 id='1-concatenate-the-user-name-with-a-colon-and-the-password-for-example-a-user-name-of-admin-and-a-password-of-admin-becomes-the-following-string-2'>1. Concatenate the user name with a colon, and the password. For example, a user name of admin, and a password of admin becomes the following string:</h4>
<p>admin:admin</p>
<h4 id='2-encode-this-user-name-and-password-string-in-base64-encoding-2'>2. Encode this user name and password string in base64 encoding.</h4><h4 id='3-include-this-encoded-user-name-and-password-in-an-http-authorization-basic-header-for-example-with-an-encoded-user-name-of-admin-and-a-password-of-admin-the-following-header-is-created-2'>3. Include this encoded user name and password in an HTTP Authorization: Basic header. For example, with an encoded user name of admin, and a password of admin, the following header is created:</h4>
<p>Authorization: Basic YWRtaW46YWRtaW4=</p>
<h3 id='http-request-query-parameters-2'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Description</th>
<th>Required</th>
</tr>
</thead><tbody>
<tr>
<td>MSISDN</td>
<td>Mobile Station International Subscriber Directory Number</td>
<td>Mandatory</td>
</tr>
<tr>
<td>subscriberInput</td>
<td>Current input of the subscriber</td>
<td>Mandatory</td>
</tr>
<tr>
<td>isNewRequest</td>
<td>Identifying whether the request is new free flow request or continuous request</td>
<td>Mandatory</td>
</tr>
<tr>
<td>language</td>
<td>Subscriber language code</td>
<td>Optional</td>
</tr>
<tr>
<td>...</td>
<td>Reset of the parameters defined in application</td>
<td>Mandatory</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameter-isnewrequest-2'>HTTP Request Query Parameter - isNewRequest</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>0</td>
<td>Indicate process the subscriberInput for existing session</td>
</tr>
<tr>
<td>1</td>
<td>Force to Create new session.</td>
</tr>
</tbody></table>
<h3 id='http-response-header-freeflow-2'>HTTP Response Header-Freeflow</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>FB</td>
<td>Free flow break, which indicates end of session</td>
</tr>
<tr>
<td>FC</td>
<td>Free flow continue, indicates requires more inputs.</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-2'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>OK</td>
<td>-</td>
<td>Success/Failure code defined in Application</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>V9004</td>
<td>MSISDN query parameter is missing in the request</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>V9006</td>
<td>Mising some query parameters</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>V9009</td>
<td>Access denied because of missing/wrong credentials</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>V9007</td>
<td>Application not found in App store</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>V9008</td>
<td>Application you are trying to access is not available in this engine</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>V9010</td>
<td>Application is incomplete End module is missing</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>E9000</td>
<td>Application Management Internal Error</td>
</tr>
</tbody></table>

<aside class="success">
Remember — HTTP-JSON REST API!
</aside>
<h2 id='cps-xml-request-response'>CPS-XML Request/Response</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="err">
https://localhost:9002/cpsxml

</span></code></pre>
<blockquote>
<p>HTTP Request - Begin Request:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="err">&lt;?xml</span><span class="w"> </span><span class="err">version=</span><span class="s2">"1.0"</span><span class="w"> </span><span class="err">encoding=</span><span class="s2">"UTF-8"</span><span class="err">?&gt;</span><span class="w">
</span><span class="err">&lt;cps-message&gt;</span><span class="w">
   </span><span class="err">&lt;sequence_number&gt;</span><span class="mi">10522</span><span class="err">&lt;/sequence_number&gt;</span><span class="w">
   </span><span class="err">&lt;version&gt;</span><span class="mi">32</span><span class="err">&lt;/version&gt;</span><span class="w">
   </span><span class="err">&lt;service_type&gt;BR&lt;/service_type&gt;</span><span class="w">
   </span><span class="err">&lt;source_addr&gt;</span><span class="mi">51991108426</span><span class="err">&lt;/source_addr&gt;</span><span class="w">
   </span><span class="err">&lt;dest_addr&gt;*</span><span class="mi">97585</span><span class="err">&lt;/dest_addr&gt;</span><span class="w">
   </span><span class="err">&lt;timestamp&gt;</span><span class="mi">2019</span><span class="err">/</span><span class="mi">01</span><span class="err">/</span><span class="mi">03</span><span class="w"> </span><span class="mi">20</span><span class="p">:</span><span class="mi">39</span><span class="p">:</span><span class="mi">42</span><span class="err">&lt;/timestamp&gt;</span><span class="w">
   </span><span class="err">&lt;command_status&gt;</span><span class="mi">0</span><span class="err">&lt;/command_status&gt;</span><span class="w">
   </span><span class="err">&lt;data_coding&gt;</span><span class="mi">0</span><span class="err">&lt;/data_coding&gt;</span><span class="w">
   </span><span class="err">&lt;msg_len&gt;</span><span class="mi">0</span><span class="err">&lt;/msg_len&gt;</span><span class="w">
   </span><span class="err">&lt;msg_content</span><span class="w"> </span><span class="err">/&gt;</span><span class="w">
   </span><span class="err">&lt;msc_gt_addr&gt;</span><span class="mi">51997990091</span><span class="err">&lt;/msc_gt_addr&gt;</span><span class="w">
   </span><span class="err">&lt;LAC&gt;</span><span class="mi">1379</span><span class="err">&lt;/LAC&gt;</span><span class="w">
   </span><span class="err">&lt;CellID&gt;</span><span class="mi">25584</span><span class="err">&lt;/CellID&gt;</span><span class="w">
   </span><span class="err">&lt;IMSI&gt;</span><span class="mi">716101401743485</span><span class="err">&lt;/IMSI&gt;</span><span class="w">
   </span><span class="err">&lt;IMEI</span><span class="w"> </span><span class="err">/&gt;</span><span class="w"> 
   </span><span class="err">&lt;PushID&gt;</span><span class="mf">190103160629267.2074</span><span class="err">&lt;/PushID&gt;</span><span class="w"> </span><span class="err">&lt;App_url&gt;http%</span><span class="mi">3</span><span class="err">A%</span><span class="mi">2</span><span class="err">F%</span><span class="mi">2</span><span class="err">F</span><span class="mf">172.17</span><span class="err">.</span><span class="mf">26.152</span><span class="err">%</span><span class="mi">3</span><span class="err">A</span><span class="mi">20000</span><span class="err">%</span><span class="mi">2</span><span class="err">FNotificacionUSSDServlet%</span><span class="mi">2</span><span class="err">Fmenu.jsp%</span><span class="mi">3</span><span class="err">FMSISDN%</span><span class="mi">3</span><span class="err">D</span><span class="mi">51991108426</span><span class="err">%</span><span class="mi">26</span><span class="err">TXID%</span><span class="mi">3</span><span class="err">D</span><span class="mf">190103160629267.2074</span><span class="err">%</span><span class="mi">26</span><span class="err">CODPROMO%</span><span class="mi">3</span><span class="err">DUP</span><span class="mi">006</span><span class="err">&lt;/App_url&gt;</span><span class="w">
</span><span class="err">&lt;/cps-message&gt;</span><span class="w">

</span></code></pre>
<blockquote>
<p>HTTP Response - Continue Request:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="err">&lt;?xml</span><span class="w"> </span><span class="err">version=</span><span class="s2">"1.0"</span><span class="w"> </span><span class="err">encoding=</span><span class="s2">"UTF-8"</span><span class="err">?&gt;</span><span class="w">
</span><span class="err">&lt;cps-message&gt;</span><span class="w">
    </span><span class="err">&lt;sequence_number&gt;</span><span class="mi">10522</span><span class="err">&lt;/sequence_number&gt;</span><span class="w">
    </span><span class="err">&lt;version&gt;</span><span class="mi">32</span><span class="err">&lt;/version&gt;</span><span class="w">
    </span><span class="err">&lt;service_type&gt;CR&lt;/service_type&gt;</span><span class="w">
    </span><span class="err">&lt;source_addr&gt;</span><span class="mi">51991108426</span><span class="err">&lt;/source_addr&gt;</span><span class="w">
    </span><span class="err">&lt;dest_addr&gt;*</span><span class="mi">97585</span><span class="err">&lt;/dest_addr&gt;</span><span class="w">
    </span><span class="err">&lt;timestamp&gt;</span><span class="mi">2019</span><span class="err">/</span><span class="mi">01</span><span class="err">/</span><span class="mi">25</span><span class="w"> </span><span class="mi">13</span><span class="p">:</span><span class="mi">11</span><span class="p">:</span><span class="mi">44</span><span class="err">&lt;/timestamp&gt;</span><span class="w">
    </span><span class="err">&lt;command_status&gt;</span><span class="mi">0</span><span class="err">&lt;/command_status&gt;</span><span class="w">
    </span><span class="err">&lt;data_coding&gt;</span><span class="mi">0</span><span class="err">&lt;/data_coding&gt;</span><span class="w">
    </span><span class="err">&lt;msg_len&gt;</span><span class="mi">25</span><span class="err">&lt;/msg_len&gt;</span><span class="w">
    </span><span class="err">&lt;msg_content&gt;Welcome</span><span class="w"> </span><span class="err">to</span><span class="w"> </span><span class="err">LEAP</span><span class="w">
        </span><span class="mi">1</span><span class="err">)</span><span class="w"> </span><span class="err">Item</span><span class="w"> </span><span class="mi">1</span><span class="err">&lt;/msg_content&gt;</span><span class="w">
    </span><span class="err">&lt;msc_gt_addr&gt;</span><span class="mi">51997990091</span><span class="err">&lt;/msc_gt_addr&gt;</span><span class="w">
    </span><span class="err">&lt;LAC&gt;</span><span class="mi">1379</span><span class="err">&lt;/LAC&gt;</span><span class="w">
    </span><span class="err">&lt;CellID&gt;</span><span class="mi">25584</span><span class="err">&lt;/CellID&gt;</span><span class="w">
    </span><span class="err">&lt;IMSI&gt;</span><span class="mi">716101401743485</span><span class="err">&lt;/IMSI&gt;</span><span class="w">
    </span><span class="err">&lt;PushID&gt;</span><span class="mf">190103160629267.2074</span><span class="err">&lt;/PushID&gt;</span><span class="w">
</span><span class="err">&lt;/cps-message&gt;</span><span class="w">

</span></code></pre>
<blockquote>
<p>HTTP Request - Continue Action:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="err">&lt;?xml</span><span class="w"> </span><span class="err">version=</span><span class="s2">"1.0"</span><span class="w"> </span><span class="err">encoding=</span><span class="s2">"UTF-8"</span><span class="err">?&gt;</span><span class="w">
</span><span class="err">&lt;cps-message&gt;</span><span class="w">
   </span><span class="err">&lt;sequence_number&gt;</span><span class="mi">10522</span><span class="err">&lt;/sequence_number&gt;</span><span class="w">
   </span><span class="err">&lt;version&gt;</span><span class="mi">32</span><span class="err">&lt;/version&gt;</span><span class="w">
   </span><span class="err">&lt;service_type&gt;CA&lt;/service_type&gt;</span><span class="w">
   </span><span class="err">&lt;source_addr&gt;</span><span class="mi">51991108426</span><span class="err">&lt;/source_addr&gt;</span><span class="w">
   </span><span class="err">&lt;dest_addr&gt;*</span><span class="mi">97585</span><span class="err">&lt;/dest_addr&gt;</span><span class="w">
   </span><span class="err">&lt;timestamp&gt;</span><span class="mi">2019</span><span class="err">/</span><span class="mi">01</span><span class="err">/</span><span class="mi">03</span><span class="w"> </span><span class="mi">20</span><span class="p">:</span><span class="mi">39</span><span class="p">:</span><span class="mi">42</span><span class="err">&lt;/timestamp&gt;</span><span class="w">
   </span><span class="err">&lt;command_status&gt;</span><span class="mi">0</span><span class="err">&lt;/command_status&gt;</span><span class="w">
   </span><span class="err">&lt;data_coding&gt;</span><span class="mi">0</span><span class="err">&lt;/data_coding&gt;</span><span class="w">
   </span><span class="err">&lt;msg_len&gt;</span><span class="mi">0</span><span class="err">&lt;/msg_len&gt;</span><span class="w">
   </span><span class="err">&lt;msg_content&gt;</span><span class="mi">1</span><span class="err">&lt;/msg_content&gt;</span><span class="w">
   </span><span class="err">&lt;msc_gt_addr&gt;</span><span class="mi">51997990091</span><span class="err">&lt;/msc_gt_addr&gt;</span><span class="w">
   </span><span class="err">&lt;LAC&gt;</span><span class="mi">1379</span><span class="err">&lt;/LAC&gt;</span><span class="w">
   </span><span class="err">&lt;CellID&gt;</span><span class="mi">25584</span><span class="err">&lt;/CellID&gt;</span><span class="w">
   </span><span class="err">&lt;IMSI&gt;</span><span class="mi">716101401743485</span><span class="err">&lt;/IMSI&gt;</span><span class="w">
   </span><span class="err">&lt;IMEI</span><span class="w"> </span><span class="err">/&gt;</span><span class="w"> 
   </span><span class="err">&lt;PushID&gt;</span><span class="mf">190103160629267.2074</span><span class="err">&lt;/PushID&gt;</span><span class="w">
   </span><span class="err">&lt;App_url&gt;http%</span><span class="mi">3</span><span class="err">A%</span><span class="mi">2</span><span class="err">F%</span><span class="mi">2</span><span class="err">F</span><span class="mf">172.17</span><span class="err">.</span><span class="mf">26.152</span><span class="err">%</span><span class="mi">3</span><span class="err">A</span><span class="mi">20000</span><span class="err">%</span><span class="mi">2</span><span class="err">FNotificacionUSSDServlet%</span><span class="mi">2</span><span class="err">Fmenu.jsp%</span><span class="mi">3</span><span class="err">FMSISDN%</span><span class="mi">3</span><span class="err">D</span><span class="mi">51991108426</span><span class="err">%</span><span class="mi">26</span><span class="err">TXID%</span><span class="mi">3</span><span class="err">D</span><span class="mf">190103160629267.2074</span><span class="err">%</span><span class="mi">26</span><span class="err">CODPROMO%</span><span class="mi">3</span><span class="err">DUP</span><span class="mi">006</span><span class="err">&lt;/App_url&gt;</span><span class="w">
</span><span class="err">&lt;/cps-message&gt;</span><span class="w">

</span></code></pre>
<blockquote>
<p>HTTP Response - Continue Notification:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="err">&lt;?xml</span><span class="w"> </span><span class="err">version=</span><span class="s2">"1.0"</span><span class="w"> </span><span class="err">encoding=</span><span class="s2">"UTF-8"</span><span class="err">?&gt;</span><span class="w">
</span><span class="err">&lt;cps-message&gt;</span><span class="w">
    </span><span class="err">&lt;sequence_number&gt;</span><span class="mi">10522</span><span class="err">&lt;/sequence_number&gt;</span><span class="w">
    </span><span class="err">&lt;version&gt;</span><span class="mi">32</span><span class="err">&lt;/version&gt;</span><span class="w">
    </span><span class="err">&lt;service_type&gt;CN&lt;/service_type&gt;</span><span class="w">
    </span><span class="err">&lt;source_addr&gt;</span><span class="mi">51991108426</span><span class="err">&lt;/source_addr&gt;</span><span class="w">
    </span><span class="err">&lt;dest_addr&gt;*</span><span class="mi">97585</span><span class="err">&lt;/dest_addr&gt;</span><span class="w">
    </span><span class="err">&lt;timestamp&gt;</span><span class="mi">2019</span><span class="err">/</span><span class="mi">06</span><span class="err">/</span><span class="mi">25</span><span class="w"> </span><span class="mi">13</span><span class="p">:</span><span class="mi">11</span><span class="p">:</span><span class="mi">29</span><span class="err">&lt;/timestamp&gt;</span><span class="w">
    </span><span class="err">&lt;command_status&gt;</span><span class="mi">0</span><span class="err">&lt;/command_status&gt;</span><span class="w">
    </span><span class="err">&lt;data_coding&gt;</span><span class="mi">0</span><span class="err">&lt;/data_coding&gt;</span><span class="w">
    </span><span class="err">&lt;msg_len&gt;</span><span class="mi">23</span><span class="err">&lt;/msg_len&gt;</span><span class="w">
    </span><span class="err">&lt;msg_content&gt;</span><span class="mi">1</span><span class="err">)</span><span class="w"> </span><span class="err">Item</span><span class="w"> </span><span class="mi">2</span><span class="w">
        </span><span class="mi">0</span><span class="w"> </span><span class="err">main</span><span class="w">
        </span><span class="err">b</span><span class="w"> </span><span class="err">back&lt;/msg_content&gt;</span><span class="w">
    </span><span class="err">&lt;msc_gt_addr&gt;</span><span class="mi">51997990091</span><span class="err">&lt;/msc_gt_addr&gt;</span><span class="w">
    </span><span class="err">&lt;LAC&gt;</span><span class="mi">1379</span><span class="err">&lt;/LAC&gt;</span><span class="w">
    </span><span class="err">&lt;CellID&gt;</span><span class="mi">25584</span><span class="err">&lt;/CellID&gt;</span><span class="w">
    </span><span class="err">&lt;IMSI&gt;</span><span class="mi">716101401743485</span><span class="err">&lt;/IMSI&gt;</span><span class="w">
    </span><span class="err">&lt;PushID&gt;</span><span class="mf">190103160629267.2074</span><span class="err">&lt;/PushID&gt;</span><span class="w">
</span><span class="err">&lt;/cps-message&gt;</span><span class="w">
</span></code></pre>
<p>Use this endpoint to execute an application using Continuation Passing Style interface.</p>
<h3 id='http-request-4'>HTTP Request</h3>
<p><code>POST http://example.com/cpsxml</code></p>
<h3 id='http-request-header-3'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Content-Type</td>
<td>text/xml or application/xml</td>
<td>This API Accepts only XML buffer.</td>
</tr>
</tbody></table>
<h3 id='http-request-payload'>HTTP Request - Payload</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>cps-message</td>
<td>Begin Request</td>
</tr>
</tbody></table>
<h3 id='http-request-service-types'>HTTP Request - Service Types</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>BR</td>
<td>Begin Request</td>
</tr>
<tr>
<td>CA</td>
<td>Continue Action</td>
</tr>
<tr>
<td>CNA</td>
<td>Continue Notification Action</td>
</tr>
</tbody></table>
<h3 id='http-response-service-types'>HTTP Response - Service Types</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>CR</td>
<td>Continue Request</td>
</tr>
<tr>
<td>CN</td>
<td>Continue Notification</td>
</tr>
<tr>
<td>EA</td>
<td>End Action</td>
</tr>
</tbody></table>

<aside class="success">
Remember — HTTP-XML CPS API!
</aside>
<h1 id='http-s-push-interface-application-execution'>HTTP(S) Push Interface: Application Execution</h1><h2 id='ussd-push-interface'>USSD Push Interface</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="err">
http://localhost:9004/ussdpush?MSISDN=919876543210&amp;SENDER=135&amp;GATEWAYID=cf2fc100-d628-11e9-b214-074ccf1338ad&amp;USERNAME=leap&amp;PASSWORD=leap123&amp;MESSAGE=This%20is%20URL%20encoded%20string

</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="s">X-Powered-By Express</span>
<span class="s">Access-Control-Allow-Origin *</span>
<span class="s">Access-Control-Allow-Methods GET,PUT,POST,PATCH,DELETE,OPTIONS</span>
<span class="s">Access-Control-Allow-Headers Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId</span>
<span class="s">X-FRAME-OPTIONS SAMEORIGIN</span>
<span class="s">X-XSS-Protection 1;mode=block</span>
<span class="s">X-Content-Type-Options nosniff</span>
<span class="s">Content-Security-Policy script-src 'self'</span>
<span class="s">X-Permitted-Cross-Domain-Policies none</span>
<span class="s">Referrer-Policy no-referrer</span>
<span class="s">Strict-Transport-Security max-age=31536000 ; includeSubDomains</span>
<span class="s">txnId 11568808040749</span>
<span class="s">Content-Type application/json; charset=utf-8</span>
<span class="s">Content-Length 28</span>
<span class="s">ETag W/"1c-TAiME6pO37FIQdTXQjme58ytF/M"</span>
<span class="s">Date Wed, 18 Sep 2019 12:00:40 GMT</span>
<span class="s">Connection keep-alive</span>

{
    "code": "0",
    "msg": "Success"
}

</code></pre>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="err">
http://localhost:9004/ussdpush?

</span></code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad Request</span>
<span class="s">X-Powered-By ?Express</span>
<span class="s">Access-Control-Allow-Origin *</span>
<span class="s">Access-Control-Allow-Methods GET,PUT,POST,PATCH,DELETE,OPTIONS</span>
<span class="s">Access-Control-Allow-Headers Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId</span>
<span class="s">X-FRAME-OPTIONS SAMEORIGIN</span>
<span class="s">X-XSS-Protection 1;mode=block</span>
<span class="s">X-Content-Type-Options ?nosniff</span>
<span class="s">Content-Security-Policy ?script-src 'self'</span>
<span class="s">X-Permitted-Cross-Domain-Policies ?none</span>
<span class="s">Referrer-Policy ?no-referrer</span>
<span class="s">Strict-Transport-Security ?max-age=31536000 ; includeSubDomains</span>
<span class="s">txnId 11568808130017</span>
<span class="s">Content-Type application/json; charset=utf-8</span>
<span class="s">Content-Length ?131</span>
<span class="s">ETag W/"83-2iCoNysGnvtaQgrU7JW3o2dZBnI"</span>
<span class="s">Date Wed, 18 Sep 2019 12:02:10 GMT</span>
<span class="s">Connection keep-alive</span>

{
    "code": "V9006",
    "msg": "Mising some query parameters",
    "queryParams": [
        "MSISDN",
        "SENDER",
        "MESSAGE",
        "GATEWAYID",
        "USERNAME",
        "PASSWORD"
    ]
}
</code></pre>
<p>Use this endpoint to push message content to subscriber, which is created in LEAP Platform.</p>
<h3 id='http-request-5'>HTTP Request</h3>
<p><code>GET http://example.com/ussdpush</code></p>
<h3 id='http-request-query-parameters-3'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Description</th>
<th>Required</th>
</tr>
</thead><tbody>
<tr>
<td>MSISDN</td>
<td>Mobile Station International Subscriber Directory Number</td>
<td>Mandatory</td>
</tr>
<tr>
<td>SENDER</td>
<td>Short code</td>
<td>Mandatory</td>
</tr>
<tr>
<td>MESSAGE</td>
<td>USSD String to be delivered to MSISDN</td>
<td>Mandatory</td>
</tr>
<tr>
<td>GATEWAYID</td>
<td>USSD Gateway account identifier</td>
<td>Mandatory</td>
</tr>
<tr>
<td>USERNAME</td>
<td>USSD Gateway account Username</td>
<td>Mandatory</td>
</tr>
<tr>
<td>PASSWORD</td>
<td>USSD Gateway account password</td>
<td>Mandatory</td>
</tr>
</tbody></table>
<h3 id='http-response-header-freeflow-3'>HTTP Response Header-Freeflow</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>FB</td>
<td>Free flow break, which indicates end of session</td>
</tr>
<tr>
<td>FC</td>
<td>Free flow continue, indicates requires more inputs.</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-3'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>OK</td>
<td>-</td>
<td>Success/Failure code defined in Application</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>V9004</td>
<td>MSISDN query parameter is missing in the request</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>V9006</td>
<td>Mising some query parameters</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>V9013</td>
<td>Invalid shortcode</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>V9009</td>
<td>Access denied because of missing/wrong credentials</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>E9000</td>
<td>Application Management Internal Error</td>
</tr>
</tbody></table>

<aside class="success">
Remember — HTTP(S)-USSD Push Interface API!
</aside>
<h2 id='app-push-interface'>App Push Interface</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="err">
http://localhost:9004/apppush?MSISDN=919876543210&amp;APPID=417af7f0-d626-11e9-aafb-0708775842a5&amp;MESSAGE&amp;USERNAME=leap&amp;PASSWORD=leap123

</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="s">X-Powered-By Express</span>
<span class="s">Access-Control-Allow-Origin *</span>
<span class="s">Access-Control-Allow-Methods GET,PUT,POST,PATCH,DELETE,OPTIONS</span>
<span class="s">Access-Control-Allow-Headers Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId</span>
<span class="s">X-FRAME-OPTIONS SAMEORIGIN</span>
<span class="s">X-XSS-Protection 1;mode=block</span>
<span class="s">X-Content-Type-Options nosniff</span>
<span class="s">Content-Security-Policy script-src 'self'</span>
<span class="s">X-Permitted-Cross-Domain-Policies none</span>
<span class="s">Referrer-Policy no-referrer</span>
<span class="s">Strict-Transport-Security max-age=31536000 ; includeSubDomains</span>
<span class="s">txnId 11568809645948</span>
<span class="s">Content-Type application/json; charset=utf-8</span>
<span class="s">Content-Length 110</span>
<span class="s">ETag W/"6e-9Wejd867rw6UrtFmYEIabODVoTM"</span>
<span class="s">Date Wed, 18 Sep 2019 12:27:25 GMT</span>
<span class="s">Connection keep-alive</span>

{
    "code": "0",
    "msg": "Success"
}

</code></pre>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="err">
http://localhost:9004/apppush?

</span></code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad Request</span>
<span class="s">X-Powered-By Express</span>
<span class="s">Access-Control-Allow-Origin *</span>
<span class="s">Access-Control-Allow-Methods GET,PUT,POST,PATCH,DELETE,OPTIONS</span>
<span class="s">Access-Control-Allow-Headers Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId</span>
<span class="s">X-FRAME-OPTIONS SAMEORIGIN</span>
<span class="s">X-XSS-Protection 1;mode=block</span>
<span class="s">X-Content-Type-Options nosniff</span>
<span class="s">Content-Security-Policy script-src 'self'</span>
<span class="s">X-Permitted-Cross-Domain-Policies none</span>
<span class="s">Referrer-Policy no-referrer</span>
<span class="s">Strict-Transport-Security max-age=31536000 ; includeSubDomains</span>
<span class="s">txnId 11568809645948</span>
<span class="s">Content-Type application/json; charset=utf-8</span>
<span class="s">Content-Length 110</span>
<span class="s">ETag W/"6e-9Wejd867rw6UrtFmYEIabODVoTM"</span>
<span class="s">Date Wed, 18 Sep 2019 12:27:25 GMT</span>
<span class="s">Connection keep-alive</span>

{
    "code": "V9006",
    "msg": "Mising some query parameters",
    "queryParams": [
        "MSISDN",
        "MESSAGE",
        "USERNAME",
        "PASSWORD"
    ]
}
</code></pre>
<p>Use this endpoint to push application content to subscriber, which is created in LEAP Platform.</p>
<h3 id='http-request-6'>HTTP Request</h3>
<p><code>GET http://example.com/apppush</code></p>
<h3 id='http-request-query-parameters-4'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Description</th>
<th>Required</th>
</tr>
</thead><tbody>
<tr>
<td>MSISDN</td>
<td>Mobile Station International Subscriber Directory Number</td>
<td>Mandatory</td>
</tr>
<tr>
<td>MESSAGE</td>
<td>USSD String to be delivered to MSISDN</td>
<td>Mandatory</td>
</tr>
<tr>
<td>APPID</td>
<td>LEAP Application identifierIf this param is present application content will be pushed to subscriber number</td>
<td>Optional</td>
</tr>
<tr>
<td>GATEWAYID</td>
<td>USSD Gateway account Identifier</td>
<td>Mandatory</td>
</tr>
<tr>
<td>USERNAME</td>
<td>USSD Gateway account Username</td>
<td>Mandatory</td>
</tr>
<tr>
<td>PASSWORD</td>
<td>USSD Gateway account password</td>
<td>Mandatory</td>
</tr>
<tr>
<td>SENDERADDRESS</td>
<td>Sender Address</td>
<td>Mandatory</td>
</tr>
<tr>
<td>CAMPAIGNID</td>
<td>Campaign ID</td>
<td>Mandatory</td>
</tr>
<tr>
<td>CAMPAIGNCATEGORY</td>
<td>Campaign Category</td>
<td>Mandatory</td>
</tr>
<tr>
<td>INTERFACETYPE</td>
<td>Campaign Interface Type</td>
<td>Mandatory</td>
</tr>
<tr>
<td>LOCATION</td>
<td>Subscriber Location</td>
<td>Mandatory</td>
</tr>
<tr>
<td>ACCOUNTID</td>
<td>Mapping Push Account Identifier</td>
<td>Mandatory</td>
</tr>
<tr>
<td>USERID</td>
<td>Mapping Push User Identifier</td>
<td>Mandatory</td>
</tr>
</tbody></table>
<h3 id='http-response-header-freeflow-4'>HTTP Response Header-Freeflow</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>FB</td>
<td>Free flow break, which indicates end of session</td>
</tr>
<tr>
<td>FC</td>
<td>Free flow continue, indicates requires more inputs.</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-4'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>OK</td>
<td>-</td>
<td>Success/Failure code defined in Application</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>V9004</td>
<td>MSISDN query parameter is missing in the request</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>V9006</td>
<td>Mising some query parameters</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>V9013</td>
<td>Invalid shortcode</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>V9009</td>
<td>Access denied because of missing/wrong credentials</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>V9007</td>
<td>Application not found in App store</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>V9008</td>
<td>Application you are trying to access is not available in this engine</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>V9010</td>
<td>Application is incomplete End module is missing</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>E9000</td>
<td>Application Management Internal Error</td>
</tr>
</tbody></table>

<aside class="success">
Remember — HTTP(S)-App Push Interface API!
</aside>
<h1 id='leap-cdrs'>LEAP CDRs</h1><h2 id='app-engine-cdr'>App Engine CDR</h2><h3 id='cdr-sample'>CDR Sample</h3>
<style type="text/css">
.tg  {border-collapse:collapse;border-spacing:0;}
.tg td{font-family:Arial, sans-serif;font-size:14px;padding:10px 5px;border-style:solid;border-width:1px;overflow:hidden;word-break:normal;border-color:black;}
.tg th{font-family:Arial, sans-serif;font-size:14px;font-weight:normal;padding:10px 5px;border-style:solid;border-width:1px;overflow:hidden;word-break:normal;border-color:black;}
.tg .tg-0pky{border-color:inherit;text-align:left;vertical-align:top}
.tg .tg-0lax{text-align:left;vertical-align:top}
</style>

<table class="tg">
  
  <tr>
<td>{<br>  "nid": "engine_MCKL-5242_9004_1",<br>  "sid": "11574673702949",<br>  "oid": "51791f20-0f64-11ea-8b99-9195f4afc25f",<br>  "aid": "51791f20-0f64-11ea-8b99-9195f4afc25f",<br>  "uid": "91987643210",<br>  "im": "QUERY_STRING",<br>  "st": 1574673702954,<br>  "et": 1574673703059,<br>  "rt": 92,<br>  "ut": "-1",<br>  "nt": "-1",<br>  "ln": "en",<br>  "ct": "QUERY_STRING",<br>  "s": "0",<br>  "p": [<br>    {<br>      "tid": 11574673702958,<br>      "mid": "37f43bd",<br>      "inf": "ucip",<br>      "mtid": 0.1,<br>      "mst": 1574673702958,<br>      "met": 1574673702963,<br>      "mrt": 5,<br>      "ocode": "0",<br>      "mcode": "0",<br>      "si": null,<br>      "ts": "l1:2|l2:1|l2s1:0|l2s2:1|l2s3:0|l2s4:0|l3:1"<br>    },<br>    {<br>      "tid": 11574673702964,<br>      "mid": "8cdf155",<br>      "inf": "appModules",<br>      "mtid": 3.7,<br>      "mst": 1574673702964,<br>      "met": 1574673703051,<br>      "mrt": 87,<br>      "ocode": "200",<br>      "mcode": "0",<br>      "si": null,<br>      "ts": "l1:3|l2:83|l2s1:0|l2s2:83|l2s3:0|l2s4:0|l3:1"<br>    }<br>  ]<br>}</td>
</tr>
</table>
<h3 id='cdr-field-description'>CDR Field Description</h3>
<style type="text/css">
.tg  {border-collapse:collapse;border-spacing:0;}
.tg td{font-family:Arial, sans-serif;font-size:14px;padding:10px 5px;border-style:solid;border-width:1px;overflow:hidden;word-break:normal;border-color:black;}
.tg th{font-family:Arial, sans-serif;font-size:14px;font-weight:normal;padding:10px 5px;border-style:solid;border-width:1px;overflow:hidden;word-break:normal;border-color:black;}
.tg .tg-0pky{border-color:inherit;text-align:left;vertical-align:top}
.tg .tg-0lax{text-align:left;vertical-align:top}
</style>

<table class="tg">
  <tr>
    <th class="tg-0pky">Field Name</th>
    <th class="tg-0pky">Alias</th>
    <th class="tg-0pky">Data type</th>
    <th class="tg-0pky">Possible Values</th>
    <th class="tg-0pky">Mandatory/Optional (M/O)</th>
    <th class="tg-0lax">Description</th>
  </tr>
  <tr>
    <td class="tg-0lax">Node Identifier</td>
    <td class="tg-0lax">nid</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax">"engine_&lt;hostname&gt;_&lt;port&gt;_&lt;clusterid&gt;"</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Node identifier to know that Session was terminated by which instance</td>
  </tr>
  <tr>
    <td class="tg-0lax">Session Idenfier</td>
    <td class="tg-0lax">sid</td>
    <td class="tg-0lax">Numeric</td>
    <td class="tg-0lax">Generated number</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Unique number generated for LEAP execution Session</td>
  </tr>
  <tr>
    <td class="tg-0lax">Original App Id</td>
    <td class="tg-0lax">oid</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax">A unique ID assigned to an application.</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Application idenfier number denotes from which application session originated</td>
  </tr>
  <tr>
    <td class="tg-0lax">Application Identifier</td>
    <td class="tg-0lax">aid</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax">A unique ID assigned to an application.</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Application identifier with which session is terminated</td>
  </tr>
  <tr>
    <td class="tg-0pky">Subscriber Number<br></td>
    <td class="tg-0pky">uid</td>
    <td class="tg-0pky">Alphanumeric</td>
    <td class="tg-0pky"></td>
    <td class="tg-0pky">M</td>
    <td class="tg-0lax">Query parameter MSISDN that indicates the end</td>
  </tr>
  <tr>
    <td class="tg-0pky">Interface Mode</td>
    <td class="tg-0pky">im</td>
    <td class="tg-0pky">Alphanumeric</td>
    <td class="tg-0pky">QUERY_STRING<br>HTTP_JSON<br><br>CPS_XML<br><br>SMPP<br><br>WEBSOCKET<br><br>NEXMO<br></td>
    <td class="tg-0pky">M</td>
    <td class="tg-0lax">This parameter indicates the Application is being executed through which mode of execution</td>
  </tr>
  <tr>
    <td class="tg-0lax">Start Time</td>
    <td class="tg-0lax">st</td>
    <td class="tg-0lax">Numeric</td>
    <td class="tg-0lax">milliseconds</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Sessios start time in milliseconds</td>
  </tr>
  <tr>
    <td class="tg-0lax">End Time</td>
    <td class="tg-0lax">et</td>
    <td class="tg-0lax">Numeric</td>
    <td class="tg-0lax">milliseconds</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Session end time in milliseconds</td>
  </tr>
  <tr>
    <td class="tg-0lax">Round trip time</td>
    <td class="tg-0lax">rt</td>
    <td class="tg-0lax">Numeric</td>
    <td class="tg-0lax"></td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This value denotes the round trip time of session at run time</td>
  </tr>
  <tr>
    <td class="tg-0lax">Subscriber Type</td>
    <td class="tg-0lax">ut</td>
    <td class="tg-0lax">Numeric</td>
    <td class="tg-0lax">Type of subscriber:<br>1 - Prepaid Subscriber<br>2 - Postpaid Subscriber<br>3 - Hybrid Subscriber</td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">LEAP sends request to IN for getting the subscriber type, whether the subscriber is prepaid subscriber or postpaid subscribers.</td>
  </tr>
  <tr>
    <td class="tg-0lax">Subscriber Network Type</td>
    <td class="tg-0lax">nt</td>
    <td class="tg-0lax">Numeric</td>
    <td class="tg-0lax">Types:<br>0 - Onnet<br>1 - Offnet<br>5-International</td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">LEAP sends request to IN for getting the subscriber network type, whether subscriber belongs to home network or other operator.</td>
  </tr>
  <tr>
    <td class="tg-0lax">Subscriber Language</td>
    <td class="tg-0lax">ln</td>
    <td class="tg-0lax">International Locale</td>
    <td class="tg-0lax">Default: en</td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">LEAP sends request to IN for getting the subscriber preferred or current language set on his/her profile created at IN end.</td>
  </tr>
  <tr>
    <td class="tg-0lax">Status Code</td>
    <td class="tg-0lax">s</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax">E9000</td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Session Status code which denotes the aplication execution final result</td>
  </tr>
  <tr>
    <td class="tg-0lax">Sender Address</td>
    <td class="tg-0lax">sa</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax">Ex:NGAGE</td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the Sender address for App push interface</td>
  </tr>
  <tr>
    <td class="tg-0lax">Campiagn ID</td>
    <td class="tg-0lax">cid</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax"></td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the child process identifer of campaign push for App push interface</td>
  </tr>
  <tr>
    <td class="tg-0lax">Campiagn Interface Type</td>
    <td class="tg-0lax">it</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax"></td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the interface type of campaign push for App push interface</td>
  </tr>
  <tr>
    <td class="tg-0lax">Campaign Category</td>
    <td class="tg-0lax">cc</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax">Ex:Promo, Education etc</td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the location property of campaign for App push interface</td>
  </tr>
  <tr>
    <td class="tg-0lax">Location</td>
    <td class="tg-0lax">lc</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax"></td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the location property of campaign for App push interface</td>
  </tr>
  <tr>
    <td class="tg-0lax">Account Id</td>
    <td class="tg-0lax">acid</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax"></td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the Mapping Push Account Identifier for App push interface</td>
  </tr>
  <tr>
    <td class="tg-0lax">User Id</td>
    <td class="tg-0lax">usid</td>
    <td class="tg-0lax">Alphanumeric</td>
    <td class="tg-0lax"></td>
    <td class="tg-0lax">O</td>
    <td class="tg-0lax">This field denotes the Mapping Push User Identifier for App push interface</td>
  </tr>
   <tr>
    <td class="tg-0lax">Plugin execution Path</td>
    <td class="tg-0lax">p</td>
    <td class="tg-0lax">Array of JSON</td>
    <td class="tg-0lax">
        <table class="tg">
        <tr>
            <th class="tg-0pky">Field Name</th>
            <th class="tg-0pky">Alias</th>
            <th class="tg-0pky">Data type</th>
            <th class="tg-0pky">Mandatory/Optional (M/O)</th>
            <th class="tg-0lax">Description</th>
        </tr>
        <tr>
            <td class="tg-0lax">Transaction ID</td>
            <td class="tg-0lax">tid</td>
            <td class="tg-0lax">Numeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Unique number generated for each transaction</td>
        </tr>
        <tr>
            <td class="tg-0lax">Module ID</td>
            <td class="tg-0lax">mid</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Plugin Identifier</td>
        </tr>
        <tr>
            <td class="tg-0lax">Category ID</td>
            <td class="tg-0lax">inf</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Plugin's Category Identifier</td>
        </tr>
        <tr>
            <td class="tg-0lax">Module Type ID</td>
            <td class="tg-0lax">mtid</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Plugin's Type Identifier</td>
        </tr>
        <tr>
            <td class="tg-0lax">Start Time</td>
            <td class="tg-0lax">mst</td>
            <td class="tg-0lax">Numeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Plugin Execution Start Time in milliseconds</td>
        </tr>
        <tr>
            <td class="tg-0lax">End Time</td>
            <td class="tg-0lax">met</td>
            <td class="tg-0lax">Numeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Plugin Execution End Time in milliseconds</td>
        </tr>
        <tr>
            <td class="tg-0lax">Round Trip Time</td>
            <td class="tg-0lax">mrt</td>
            <td class="tg-0lax">Numeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Actual Plugin Execution Time in milliseconds</td>
        </tr>
        <tr>
            <td class="tg-0lax">Original Plugin Code</td>
            <td class="tg-0lax">ocode</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Actual Plugin Execution status code</td>
        </tr>
        <tr>
            <td class="tg-0lax">Plugin Status Code</td>
            <td class="tg-0lax">mcode</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">M</td>
            <td class="tg-0lax">Plugin status code for reporting</td>
        </tr>
        <tr>
            <td class="tg-0lax">Subscriber Input</td>
            <td class="tg-0lax">si</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">O</td>
            <td class="tg-0lax">User input feeded for this plugin's execution</td>
        </tr>
        <tr>
            <td class="tg-0lax">States Timer</td>
            <td class="tg-0lax">ts</td>
            <td class="tg-0lax">Aplhanumeric</td>
            <td class="tg-0lax">O</td>
            <td class="tg-0lax">Time take at various states of Engine.<br>l1: Plugin settings preparation time<br>l2: Execution time<br>l3: Logic evaluation time</td>
        </tr>
        </table>
    </td>
    <td class="tg-0lax">M</td>
    <td class="tg-0lax">Holds the plugin execution properties in the order of execution</td>
  </tr>
</table>
<h3 id='cdr-processing-call-flow'>CDR Processing Call flow</h3>
<p align="center"><img src="images/LEAP-CDR-Flow.png" width=700 alt="App Engine CDR"></p>
<h3 id='cdr-retention-period'>CDR Retention Period</h3>
<p>The retention period of CDR is 30 days.</p>

<aside class="success">
Remember — CDR Document!
</aside>
<h1 id='errors'>Errors</h1>
<p>The LEAP API uses the following error codes:</p>

<table><thead>
<tr>
<th>Error Code</th>
<th>Meaning</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
</tr>
<tr>
<td>201</td>
<td>Created</td>
</tr>
<tr>
<td>202</td>
<td>Accepted</td>
</tr>
<tr>
<td>204</td>
<td>No Content</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
</tr>
<tr>
<td>413</td>
<td>Request Entity Too Large</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
</tr>
<tr>
<td>501</td>
<td>Not Implemented</td>
</tr>
<tr>
<td>502</td>
<td>Bad Gateway</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
</tr>
<tr>
<td>504</td>
<td>Gateway Timeout</td>
</tr>
</tbody></table>

<table><thead>
<tr>
<th>Internal Error Code</th>
<th>Meaning</th>
</tr>
</thead><tbody>
<tr>
<td>600</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>601</td>
<td>Missing username</td>
</tr>
<tr>
<td>602</td>
<td>Invalid Username or Password</td>
</tr>
<tr>
<td>603</td>
<td>OTP verification failed.</td>
</tr>
<tr>
<td>605</td>
<td>Authorization missing</td>
</tr>
<tr>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>608</td>
<td>Old Password is incorrect</td>
</tr>
<tr>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>610</td>
<td>User Activated Successfully</td>
</tr>
<tr>
<td>611</td>
<td>User Deactivated Successfully</td>
</tr>
<tr>
<td>612</td>
<td>Length of the LoginID exceeded</td>
</tr>
<tr>
<td>613</td>
<td>Length of the firstname exceeded</td>
</tr>
<tr>
<td>614</td>
<td>Length of the email Id exceeded</td>
</tr>
<tr>
<td>615</td>
<td>Missing OTP</td>
</tr>
<tr>
<td>616</td>
<td>Missing password</td>
</tr>
<tr>
<td>617</td>
<td>Missing Old password</td>
</tr>
<tr>
<td>618</td>
<td>Missing New password</td>
</tr>
<tr>
<td>619</td>
<td>Old and New password cannot be same</td>
</tr>
<tr>
<td>620</td>
<td>Password matches with one of the last 5 passwords</td>
</tr>
<tr>
<td>621</td>
<td>User logged out. Cannot issue JWT</td>
</tr>
<tr>
<td>622</td>
<td>User not found</td>
</tr>
<tr>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>624</td>
<td>User Suspended</td>
</tr>
<tr>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>641</td>
<td>User Already Activated</td>
</tr>
<tr>
<td>642</td>
<td>User Already Deactivated</td>
</tr>
<tr>
<td>699</td>
<td>Something went wrong please try later</td>
</tr>
<tr>
<td>700</td>
<td>Service not available</td>
</tr>
<tr>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>702</td>
<td>App Store not reachable</td>
</tr>
<tr>
<td>703</td>
<td>Bad input for size field</td>
</tr>
<tr>
<td>704</td>
<td>Bad input for sortf field</td>
</tr>
<tr>
<td>705</td>
<td>Bad input for order field</td>
</tr>
<tr>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>707</td>
<td>App created successfully</td>
</tr>
<tr>
<td>708</td>
<td>App updated successfully</td>
</tr>
<tr>
<td>709</td>
<td>App deleted successfully</td>
</tr>
<tr>
<td>710</td>
<td>Apps listed successfully</td>
</tr>
<tr>
<td>711</td>
<td>App is not defined</td>
</tr>
<tr>
<td>712</td>
<td>Invalid App Schema</td>
</tr>
<tr>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>714</td>
<td>App id field is missing</td>
</tr>
<tr>
<td>715</td>
<td>App name field is missing</td>
</tr>
<tr>
<td>716</td>
<td>appData field is missing in appData</td>
</tr>
<tr>
<td>717</td>
<td>startId field is missing in appData</td>
</tr>
<tr>
<td>718</td>
<td>modules field is missing in appData</td>
</tr>
<tr>
<td>719</td>
<td>type field is missing in module</td>
</tr>
<tr>
<td>720</td>
<td>coordinates field is missing in module</td>
</tr>
<tr>
<td>721</td>
<td>settings field is missing in module</td>
</tr>
<tr>
<td>722</td>
<td>input field is missing in module</td>
</tr>
<tr>
<td>723</td>
<td>process field is missing in module</td>
</tr>
<tr>
<td>724</td>
<td>output field is missing in module</td>
</tr>
<tr>
<td>725</td>
<td>End module id is not found</td>
</tr>
<tr>
<td>726</td>
<td>Start module is not defined</td>
</tr>
<tr>
<td>727</td>
<td>End module is not defined</td>
</tr>
<tr>
<td>728</td>
<td>Invalid App:id</td>
</tr>
<tr>
<td>729</td>
<td>Invalid App:name</td>
</tr>
<tr>
<td>730</td>
<td>Invalid App:appData</td>
</tr>
<tr>
<td>731</td>
<td>Invalid App:startId</td>
</tr>
<tr>
<td>732</td>
<td>Invalid App:modules</td>
</tr>
<tr>
<td>733</td>
<td>Invalid AppModules:type</td>
</tr>
<tr>
<td>734</td>
<td>Invalid AppModules:coordinates</td>
</tr>
<tr>
<td>735</td>
<td>Invalid AppModules:settings</td>
</tr>
<tr>
<td>736</td>
<td>Invalid AppModules:input</td>
</tr>
<tr>
<td>737</td>
<td>Invalid AppModules:process</td>
</tr>
<tr>
<td>738</td>
<td>Invalid AppModules:output</td>
</tr>
<tr>
<td>739</td>
<td>Resource conflicts, App already exists</td>
</tr>
<tr>
<td>740</td>
<td>Page does not exists</td>
</tr>
<tr>
<td>741</td>
<td>Bad input for status field</td>
</tr>
<tr>
<td>742</td>
<td>Resource conflicts, App no longer exists</td>
</tr>
<tr>
<td>743</td>
<td>Page does not exist</td>
</tr>
<tr>
<td>744</td>
<td>Access forbidden for App</td>
</tr>
<tr>
<td>745</td>
<td>appTemplateId field is missing in request Payload</td>
</tr>
<tr>
<td>746</td>
<td>Resource conflicts, App Template no longer exists</td>
</tr>
<tr>
<td>747</td>
<td>Module undefined</td>
</tr>
<tr>
<td>748</td>
<td>App name should have Minimum 5 characters</td>
</tr>
<tr>
<td>749</td>
<td>App name should not exceed more than 32 characters</td>
</tr>
<tr>
<td>750</td>
<td>App description field MAX limit exceeds</td>
</tr>
<tr>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>752</td>
<td>App owner updated successfully</td>
</tr>
<tr>
<td>753</td>
<td>App name already exists</td>
</tr>
<tr>
<td>754</td>
<td>Incorrect status field</td>
</tr>
<tr>
<td>760</td>
<td>Currently application is not in the state to perform this action</td>
</tr>
<tr>
<td>761</td>
<td>Invalid events</td>
</tr>
<tr>
<td>762</td>
<td>Invalid input</td>
</tr>
<tr>
<td>763</td>
<td>User dont have the permission</td>
</tr>
<tr>
<td>781</td>
<td>Elastic search Error</td>
</tr>
<tr>
<td>782</td>
<td>ESEmptyData</td>
</tr>
<tr>
<td>783</td>
<td>reportTypeError</td>
</tr>
<tr>
<td>784</td>
<td>InfluxDB search error</td>
</tr>
<tr>
<td>785</td>
<td>App Archive request accepted, You receive notification shortly</td>
</tr>
<tr>
<td>800</td>
<td>Plugins internal error</td>
</tr>
<tr>
<td>801</td>
<td>Category is not found</td>
</tr>
<tr>
<td>802</td>
<td>Requested plugin is not available</td>
</tr>
<tr>
<td>803</td>
<td>Not able to connnect to external interface</td>
</tr>
<tr>
<td>804</td>
<td>Settings not found</td>
</tr>
<tr>
<td>805</td>
<td>Query object is not available</td>
</tr>
<tr>
<td>806</td>
<td>Permission is not allowed</td>
</tr>
<tr>
<td>807</td>
<td>Socket time out</td>
</tr>
<tr>
<td>808</td>
<td>Resource not found/Connection refused</td>
</tr>
<tr>
<td>811</td>
<td>Module name is missing</td>
</tr>
<tr>
<td>812</td>
<td>Host is missing</td>
</tr>
<tr>
<td>813</td>
<td>port is missing</td>
</tr>
<tr>
<td>814</td>
<td>path is missing</td>
</tr>
<tr>
<td>815</td>
<td>origin time stamp invalid format</td>
</tr>
<tr>
<td>816</td>
<td>subscriber NAI is not allowed</td>
</tr>
<tr>
<td>817</td>
<td>subscriber number type not correct</td>
</tr>
<tr>
<td>818</td>
<td>Message capability flag type is not correct</td>
</tr>
<tr>
<td>851</td>
<td>WSDL/XSD root upload directory not found</td>
</tr>
<tr>
<td>852</td>
<td>WSDL/XSD upload failed</td>
</tr>
<tr>
<td>853</td>
<td>File extension not supported</td>
</tr>
<tr>
<td>854</td>
<td>Internal error while uploading the file</td>
</tr>
<tr>
<td>855</td>
<td>Filename should be <appName>_<locale>.json format</td>
</tr>
<tr>
<td>856</td>
<td>Unsupported file format is being uploaded</td>
</tr>
<tr>
<td>859</td>
<td>File deletion unsuccesful</td>
</tr>
<tr>
<td>860</td>
<td>Error while performing plugin confirmation step</td>
</tr>
<tr>
<td>861</td>
<td>Plugin Overwriting Error</td>
</tr>
<tr>
<td>862</td>
<td>Request should have either &#39;Yes&#39; or &#39;No&#39; in query</td>
</tr>
<tr>
<td>863</td>
<td>Uploaded files exceed the max size</td>
</tr>
<tr>
<td>870</td>
<td>Error while reading WSDL/XSD directory</td>
</tr>
<tr>
<td>871</td>
<td>Empty directory. No WSDL/XSD to process</td>
</tr>
<tr>
<td>872</td>
<td>Error while parsing the WSDL/XSD</td>
</tr>
<tr>
<td>873</td>
<td>SOAP plugin check error</td>
</tr>
<tr>
<td>874</td>
<td>WSDL directory deletion unsuccessful</td>
</tr>
<tr>
<td>875</td>
<td>Create plugin error</td>
</tr>
<tr>
<td>876</td>
<td>Error in deploy method</td>
</tr>
<tr>
<td>880</td>
<td>SOAP execution failed</td>
</tr>
<tr>
<td>881</td>
<td>SOAP testing failed</td>
</tr>
<tr>
<td>882</td>
<td>SOAP Execution Fault Error</td>
</tr>
<tr>
<td>890</td>
<td>File upload successful</td>
</tr>
<tr>
<td>891</td>
<td>File delete successful</td>
</tr>
<tr>
<td>892</td>
<td>Plugin already exists. Check for user&#39;s confirmation</td>
</tr>
<tr>
<td>893</td>
<td>Plugin deployed successfully</td>
</tr>
<tr>
<td>894</td>
<td>Plugin overwriting successful</td>
</tr>
<tr>
<td>895</td>
<td>Plugin overwriting aborted</td>
</tr>
<tr>
<td>896</td>
<td>Page does not exist</td>
</tr>
<tr>
<td>899</td>
<td>Plugin request execution timeout</td>
</tr>
<tr>
<td>900</td>
<td>Plugin Store Internal error</td>
</tr>
<tr>
<td>901</td>
<td>Invalid plugin ID</td>
</tr>
<tr>
<td>902</td>
<td>built-in plugin cannot be deleted</td>
</tr>
<tr>
<td>903</td>
<td>Plugin deleted successfully</td>
</tr>
<tr>
<td>904</td>
<td>Failed to delete Plugin</td>
</tr>
<tr>
<td>905</td>
<td>Payload is required to Update the Plugin settings</td>
</tr>
<tr>
<td>906</td>
<td>Plugin settings updated successfully</td>
</tr>
<tr>
<td>907</td>
<td>Failed to update Plugin settings</td>
</tr>
<tr>
<td>908</td>
<td>Plugin page does not exists</td>
</tr>
<tr>
<td>909</td>
<td>Plugin not found</td>
</tr>
<tr>
<td>910</td>
<td>Payload is required</td>
</tr>
<tr>
<td>911</td>
<td>Invalid status Field</td>
</tr>
<tr>
<td>912</td>
<td>Plugin Activation is Successful</td>
</tr>
<tr>
<td>913</td>
<td>Plugin Deactivation is Successful</td>
</tr>
<tr>
<td>914</td>
<td>Plugin Activation is Failed</td>
</tr>
<tr>
<td>915</td>
<td>Plugin Deactivation is Failed</td>
</tr>
<tr>
<td>1000</td>
<td>USSD Service created successfully</td>
</tr>
<tr>
<td>1001</td>
<td>USSD Service name field is missing</td>
</tr>
<tr>
<td>1002</td>
<td>Service name should have Minimum 5 characters</td>
</tr>
<tr>
<td>1003</td>
<td>Invalid USSD Service name</td>
</tr>
<tr>
<td>1004</td>
<td>Service name should not exceed more than 32 characters</td>
</tr>
<tr>
<td>1005</td>
<td>Service description field MAX limit exceeds</td>
</tr>
<tr>
<td>1006</td>
<td>Service name already exists</td>
</tr>
<tr>
<td>1007</td>
<td>USSD Shortcode already exists</td>
</tr>
<tr>
<td>1008</td>
<td>USSD Shortcode does not exists</td>
</tr>
<tr>
<td>1009</td>
<td>USSD Shortcode Purged Successfully</td>
</tr>
<tr>
<td>1010</td>
<td>USSD Shortcode Updated Successfully</td>
</tr>
</tbody></table>

      </div>
      <div class="dark-box">
      </div>
    </div>
  </body>
</html>
