const encodings = require("../lib/defs").encodings;

const UCS2Encoder = encodings.UCS2.encode;
const LATIN1Encoder = encodings.LATIN1.encode;
const VALID_SENDER_ADDRESS = "Raaki";

module.exports = [
  {
    id: "SMPP_001",
    name: "MT-TEXT-Single Fragment",
    isConcatenated: false,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991111,
      data_coding: 0,
      short_message: "Hello World"
    },
    expected: {
      command_status: 0
    }
  },
  {
    id: "SMPP_002",
    name: "MT-TEXT-Single Fragment-Invalid Sender Address",
    isConcatenated: false,
    request: {
      source_addr: "SENDER2",
      destination_addr: 919991991112,
      data_coding: 0,
      short_message: "Hello World"
    },
    expected: {
      command_status: 10
    }
  },
  {
    id: "SMPP_003",
    name: "MT-Unicode-Single Fragment",
    isConcatenated: false,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991113,
      data_coding: 8,
      short_message: UCS2Encoder("This ís a Tést")
    },
    expected: {
      command_status: 0
    }
  },
  {
    id: "SMPP_004",
    name: "MT-TEXT-Single Fragment- UTF16 format",
    isConcatenated: false,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991114,
      data_coding: 8,
      short_message: UCS2Encoder("This is simple text message with UTF-16 encoded")
    },
    expected: {
      command_status: 0
    }
  },
  {
    id: "SMPP_005",
    name: "MT-TEXT-Single Fragment-Flash Message",
    isConcatenated: false,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991115,
      data_coding: 16,
      short_message: "This is simple text message flash message"
    },
    expected: {
      command_status: 0
    }
  },
  {
    id: "SMPP_006",
    name: "MT-Unicode-Single Fragment-Flash Message",
    isConcatenated: false,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991116,
      data_coding: 24,
      short_message: UCS2Encoder("This ís a Tést")
    },
    expected: {
      command_status: 0
    }
  },
  {
    id: "SMPP_007",
    name: "GSM 03.38 ASCII charset - Part1 - @£$¥èéùìòç\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞÆæßÉ !\"#¤%&'",
    isConcatenated: false,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991117,
      data_coding: 0,
      short_message: "@£$¥èéùìòç\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞÆæßÉ !\"#¤%&'"
    },
    expected: {
      command_status: 0
    }
  },
  {
    id: "SMPP_008",
    name: "GSM 03.38 ASCII charset - Part2 - ()*+,-./0123456789:;<=>?¡ABCDEFGHIJKLMNOPQRSTUVWXYZ",
    isConcatenated: false,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991118,
      data_coding: 0,
      short_message: "()*+,-./0123456789:;<=>?¡ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    },
    expected: {
      command_status: 0
    }
  },
  {
    id: "SMPP_009",
    name: "GSM 03.38 ASCII charset - Part3 - ÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà",
    isConcatenated: false,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991119,
      data_coding: 0,
      short_message: "ÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà"
    },
    expected: {
      command_status: 0
    }
  },
  {
    id: "SMPP_010",
    name: "GSM 03.38 ASCII charset - Part4 - \f^{}\\[~]|€",
    isConcatenated: false,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991120,
      data_coding: 0,
      short_message: "\f^{}\\[~]|€"
    },
    expected: {
      command_status: 0
    }
  },
  {
    id: "SMPP_011",
    name:
      "GSM 03.38 ASCII charset -Multipart - @£$¥èéùìòç\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞÆæßÉ !\"#¤%&'()*+,-./0123456789:;<=>?¡ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà\f^{}\\[~]|€",
    isConcatenated: true,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991121,
      data_coding: 0,
      short_message:
        "@£$¥èéùìòç\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞÆæßÉ !\"#¤%&'()*+,-./0123456789:;<=>?¡ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà\f^{}\\[~]|€"
    },
    expected: {
      command_status: 0
    }
  },
  {
    id: "SMPP_012",
    name:
      "GSM 03.38 ASCII charset -Multipart - @£$¥èéùìòç\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞÆæßÉ !\"#¤%&'()*+,-./0123456789:;<=>?¡ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà\f^{}\\[~]|€",
    isConcatenated: true,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991122,
      data_coding: 0,
      short_message:
        "@£$¥èéùìòç\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞÆæßÉ !\"#¤%&'()*+,-./0123456789:;<=>?¡ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà\f^{}\\[~]|€"
    },
    expected: {
      command_status: 0
    }
  },
  {
    id: "SMPP_013",
    name: "Unicode - Special characters - ۱۲۳۴۵۶۷۸۹۰",
    isConcatenated: false,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991123,
      data_coding: 8,
      short_message: UCS2Encoder("۱۲۳۴۵۶۷۸۹۰")
    },
    expected: {
      command_status: 0
    }
  },
  {
    id: "SMPP_014",
    name: "MT-TEXT-Single Fragment- UTF16 format - multi fragment",
    isConcatenated: true,
    request: {
      source_addr: VALID_SENDER_ADDRESS,
      destination_addr: 919991991124,
      data_coding: 0,
      short_message: UCS2Encoder(
        "This is simple text message with UTF-16 encoded Whenever you run Mocha at the command line, it will read this file and set a timeout of 5 seconds by default. Another way which may be better depending on your situation"
      )
    },
    expected: {
      command_status: 0
    }
  }
];

function isGSM(text) {
  var REG_EXP = new RegExp("^[@£$¥èéùìòÇç\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞ\x1BÆæßÉ !\"#¤%&'()*+,-./0-9:;<=>?¡A-ZÄÖÑÜ§¿a-zäöñüà\f^{}\\[~]|€]*$");
  let TEXT = new RegExp(
    "^[A-Za-z0-9 \\s+\\r\\n@£$¥èéùìòÇØøÅå\u0394_\u03A6\u0393\u039B\u03A9\u03A0\u03A8\u03A3\u0398\u039EÆæßÉ!\"#¤$%&'()*+,\\-./:;>=<?¡ÄÖÑÜ§¿äöñüà^{}\\\\\\[~\\]|\u20AC]*$"
  );
  return TEXT.test(text);
}
console.log(isGSM("@£$¥èéùìòÇØøÅå"));
console.log(
  isGSM(
    "@£$¥èéùìòç\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞÆæßÉ !\"#¤%&'()*+,-./0123456789:;<=>?¡ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà\f^{}\\[~]|€"
  )
);
