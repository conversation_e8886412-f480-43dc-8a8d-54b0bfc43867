const smpp = require("../index");
const scenarios = require("./scenarios");
const assert = require("assert");

var session;
describe("Bind request", function () {
  before(async function () {
    let bindResponse = await bind();
    console.log(bindResponse);
    if (bindResponse.command_status != 0) {
      assert.fail("Bind failed");
    }
  });

  after(function () {
    if (session) {
      session.unbind((pdu) => {
        console.error("UNBOUND");
        session.destroy((pdu) => {
          console.error("Destroy");
          session = null;
          process.exit(0);
        });
      });
    }
  });

  describe("SMPP Submit_SM", function () {
    scenarios.forEach(function (scenario) {
      if (!scenario.isConcatenated) {
        it("Case:#" + scenario.id + ", Scenario:" + scenario.name, async function () {
          let response = await sendMessage(scenario.request);
          assert.equal(scenario.expected.command_status, response.command_status);
        }).timeout(5000);
      } else {
        it("Case:#" + scenario.id + ", Scenario:" + scenario.name, async function () {
          let response = await sendMultiMessage(scenario.request);
          assert.equal(scenario.expected.command_status, response.command_status);
        }).timeout(60000);
      }
    });
  });
});
const SMPP_HOST = "************";
function bind() {
  return new Promise((resolve) => {
    session = new smpp.Session({
      host: SMPP_HOST, //"************",
      port: 9199,
      responseTimeout: 60000
    });
    session.bind_transmitter(
      {
        system_id: "RaakiUser1",
        password: "Root@123",
        interface_version: 52,
        system_type: ""
      },
      (pdu) => {
        return resolve(pdu);
      }
    );
  });
}

function sendMessage(payload) {
  let request = {
    source_addr: String(payload.source_addr),
    source_addr_ton: 0,
    source_addr_npi: 0,
    destination_addr: String(payload.destination_addr),
    dest_addr_npi: 0,
    dest_addr_ton: 0,
    registered_delivery: 0,
    data_coding: payload.data_coding,
    short_message: payload.short_message
  };
  return new Promise((resolve) => {
    session.submit_sm(request, (pdu) => {
      resolve(pdu);
    });
  });
}

function sendMultiMessage(payload) {
  return new Promise((resolve) => {
    let sequenceNo = 1;
    const parts = getMultiParts(payload.short_message, payload.data_coding == 8 || payload.data_coding == 24);
    let itemsProcessed = 0;

    parts.forEach(async (part) => {
      let obj;
      reqtime = new Date().getTime();
      let udh = Buffer.alloc(6);
      udh.write(String.fromCharCode(0x5), 0); //Length of UDF
      udh.write(String.fromCharCode(0x0), 1); //Indicator for concatenated message
      udh.write(String.fromCharCode(0x3), 2); //  Subheader Length ( 3 bytes)
      udh.write(String.fromCharCode(part.msg_ref_num), 3); //Same reference for all concatenated messages
      udh.write(String.fromCharCode(part.total_segments), 4); //Number of total messages in the concatenation
      udh.write(String.fromCharCode(sequenceNo++), 5); //Sequence number ( used by the mobile to concatenate the split messages)
      obj = {
        source_addr: String(payload.source_addr),
        source_addr_ton: 0,
        source_addr_npi: 0,
        destination_addr: String(payload.destination_addr),
        dest_addr_npi: 0,
        dest_addr_ton: 0,
        registered_delivery: 0,
        data_coding: payload.data_coding,
        esm_class: 0x40,
        short_message: { udh: udh, message: part.payload }
      };
      session.submit_sm(obj, (pdu) => {
        itemsProcessed++;
        obj.fragmentno = itemsProcessed;
        if (parts.length >= itemsProcessed) {
          resolve(pdu);
        }
      });
    });
  });
}
var smsMsgRefNum = 1;

function getMultiParts(content, is_unicode) {
  smsMsgRefNum++;
  if (smsMsgRefNum > 255) smsMsgRefNum = 1;

  let array = splitToArray(content, is_unicode);
  let resultArray = [];
  let i = 1;
  array &&
    array.forEach((buff) => {
      resultArray.push({
        total_segments: array.length,
        segment_seqnum: i,
        payload: buff,
        msg_ref_num: smsMsgRefNum
      });
      i++;
    });
  return resultArray;
}

function splitToArray(content, is_unicode) {
  let array = [];
  // Question: Why maxBytes = 66 in case of unicode?
  // Answer : each character representation takes 2 bytes. Message size cannot go beyond 140 character. So 66x2=132 + 6byte for UDH i.e, upto 138;
  // If go below 66 then number of sms parts will increase; if we go beyond 66 then SMSC will reject the message with 1058 error i.e Carrier ID not found.
  const maxBytes = is_unicode ? 66 : 153;
  if (!is_unicode) {
    let current_length = 0;
    let part = "";
    let chars = content.split("");
    chars.forEach(function (c) {
      if (current_length + charset7bit[c] <= maxBytes) {
        part += c;
        current_length += charset7bit[c];
      } else {
        array.push(part);
        part = "";
        part += c;
        current_length = charset7bit[c];
      }
    });
    if (part) {
      array.push(part);
    }
  } else {
    //convert to string
    let buf = content.toString();

    while (buf.length) {
      let i = buf.lastIndexOf("", maxBytes + 1);
      if (i < 0) i = buf.length;
      let str = buf.slice(0, i); //.toString();
      array.push(toUnicodeBuffer(str));
      buf = buf.slice(i);
    }
  }
  return array;
}

function toUnicodeBuffer(theString) {
  let arr = [];
  for (let i = 0; i < theString.length; i++) {
    let theUnicode = theString.charCodeAt(i).toString(16).toUpperCase();

    while (theUnicode.length < 4) {
      theUnicode = "0" + theUnicode;
    }
    arr.push(parseInt(theUnicode.substring(0, 2), 16));
    arr.push(parseInt(theUnicode.substring(2), 16));
  }
  return Buffer.from(arr);
}

const charset7bit = {
  "@": 1,
  "£": 1,
  $: 1,
  "¥": 1,
  è: 1,
  é: 1,
  ù: 1,
  ì: 1,
  ò: 1,
  Ç: 1,
  "\n": 1,
  Ø: 1,
  ø: 1,
  "\r": 1,
  Å: 1,
  å: 1,
  Δ: 1,
  _: 1,
  Φ: 1,
  Γ: 1,
  Λ: 1,
  Ω: 1,
  Π: 1,
  Ψ: 1,
  Σ: 1,
  Θ: 1,
  Ξ: 1,
  Æ: 1,
  æ: 1,
  ß: 1,
  É: 1,
  " ": 1,
  "!": 1,
  '"': 1,
  "#": 1,
  "¤": 1,
  "%": 1,
  "&": 1,
  "'": 1,
  "(": 1,
  ")": 1,
  "*": 1,
  "+": 1,
  ",": 1,
  "-": 1,
  ".": 1,
  "/": 1,
  0: 1,
  1: 1,
  2: 1,
  3: 1,
  4: 1,
  5: 1,
  6: 1,
  7: 1,
  8: 1,
  9: 1,
  ":": 1,
  ";": 1,
  "<": 1,
  "=": 1,
  ">": 1,
  "?": 1,
  "¡": 1,
  A: 1,
  B: 1,
  C: 1,
  D: 1,
  E: 1,
  F: 1,
  G: 1,
  H: 1,
  I: 1,
  J: 1,
  K: 1,
  L: 1,
  M: 1,
  N: 1,
  O: 1,
  P: 1,
  Q: 1,
  R: 1,
  S: 1,
  T: 1,
  U: 1,
  V: 1,
  W: 1,
  X: 1,
  Y: 1,
  Z: 1,
  Ä: 1,
  Ö: 1,
  Ñ: 1,
  Ü: 1,
  "§": 1,
  "¿": 1,
  a: 1,
  b: 1,
  c: 1,
  d: 1,
  e: 1,
  f: 1,
  g: 1,
  h: 1,
  i: 1,
  j: 1,
  k: 1,
  l: 1,
  m: 1,
  n: 1,
  o: 1,
  p: 1,
  q: 1,
  r: 1,
  s: 1,
  t: 1,
  u: 1,
  v: 1,
  w: 1,
  x: 1,
  y: 1,
  z: 1,
  ä: 1,
  ö: 1,
  ñ: 1,
  ü: 1,
  à: 1,
  "\f": 2,
  "^": 2,
  "{": 2,
  "}": 2,
  "\\": 2,
  "[": 2,
  "~": 2,
  "]": 2,
  "|": 2,
  "€": 2
};
