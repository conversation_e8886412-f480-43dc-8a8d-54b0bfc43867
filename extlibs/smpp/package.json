{"author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {"iconv-lite": "0.x", "safer-buffer": "^2.1.2"}, "deprecated": false, "description": "SMPP client and server implementation in node.js", "devDependencies": {"chai": "^4.2.0", "coveralls": "^3.1.0", "husky": "^4.3.8", "istanbul": "^0.4.5", "mocha": "8.x", "prettier": "^2.2.1"}, "main": "./lib/smpp", "name": "smpp", "scripts": {"cover": "istanbul cover _mocha", "test": "mocha", "prettify": "prettier --ignore-path \"./.prettierignore\" --write \"./**/*.{js,jsx,json}\""}, "version": "1.0.3"}