{"domain": {"type": "qemu", "name": "QEmu-fedora-i686", "uuid": "c7a5fdbd-cdaf-9455-926a-d65c16db1809", "memory": "219200", "currentMemory": "219200", "vcpu": "2", "os": {"type": {"arch": "i686", "machine": "pc", "$t": "hvm"}, "boot": {"dev": "cdrom"}}, "devices": {"emulator": "/usr/bin/qemu-system-x86_64", "disk": [{"type": "file", "device": "cdrom", "source": {"file": "/home/<USER>/boot.iso"}, "target": {"dev": "hdc"}, "readonly": {}}, {"type": "file", "device": "disk", "source": {"file": "/home/<USER>/fedora.img"}, "target": {"dev": "hda"}}], "interface": {"type": "network", "source": {"network": "default"}}, "graphics": {"type": "vnc", "port": "-1"}}, "ah": [{"type": "rare", "foo": "bar", "$t": "cosa1"}, {"type": "normal", "$t": "cosa2"}, "cosa3"]}}