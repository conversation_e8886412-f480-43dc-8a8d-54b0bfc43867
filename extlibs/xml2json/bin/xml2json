#!/usr/bin/env node

const xml2json = require("../index1");
const pkg = require("../package.json");

var xml = "";

const args = process.argv.slice(2);
const arg = args[0];

if (arg == "--version") {
	console.log(pkg.version);
	process.exit(0);
}

process.stdin.on("data", function (data) {
	xml += data;
});

process.stdin.resume();

process.stdin.on("end", function () {
	json = xml2json.toJson(xml);
	process.stdout.write(json + "\n");
});
