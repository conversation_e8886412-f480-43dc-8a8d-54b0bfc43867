{"_from": "xmlrpc@^2.0.0", "_id": "xmlrpc@2.0.0", "_inBundle": false, "_integrity": "sha1-JrLqNHhI0Ciqx+dRS1NRl23j6D0=", "_location": "/xmlrpc", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "xmlrpc@^2.0.0", "name": "xmlrpc", "escapedName": "xmlrpc", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/"], "_resolved": "https://cots.comviva.com:443/api/npm/Global_NPM_Repo/xmlrpc/-/xmlrpc-2.0.0.tgz", "_shasum": "26b2ea347848d028aac7e7514b5351976de3e83d", "_spec": "xmlrpc@^2.0.0", "_where": "/prd/leap/deploy/libs/PluginManager", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/baalexander"}, "bugs": {"url": "https://github.com/baalexander/node-xmlrpc/issues"}, "bundleDependencies": false, "dependencies": {"request": "^2.88.2", "sax": "1.2.x", "xmlbuilder": "15.1.x"}, "deprecated": false, "description": "A pure JavaScript XML-RPC client and server.", "devDependencies": {"vows": "0.8.x"}, "directories": {"lib": "./lib"}, "engines": {"node": ">=0.8", "npm": ">=1.0.0"}, "homepage": "https://github.com/baalexander/node-xmlrpc", "keywords": ["xml-rpc", "xmlrpc", "xml", "rpc"], "license": "MIT", "main": "./lib/xmlrpc.js", "name": "xmlrpc", "preferGlobal": false, "repository": {"type": "git", "url": "git://github.com/baalexander/node-xmlrpc.git"}, "scripts": {"test": "vows 'test/*.js'"}, "version": "2.0.0"}