const url = require("url"),
  Serializer = require("./serializer"),
  Deserializer = require("./deserializer"),
  request = require("request"),
  http = require("http");


// Set the HTTP request headers
const HTTP_HEADERS = {
  "User-Agent": "NodeJS XML-RPC Client",
  "Content-Type": "text/xml",
  "Accept": "text/xml",
  "Accept-Charset": "UTF8",
  "Connection": "Keep-Alive"
};

const keepAliveAgent = new http.Agent(global.httpClientSettings);

const NS_PER_SEC = 1e9;

/**
 * Creates a Client object for making XML-RPC method calls.
 *
 * @constructor
 * @param {Object|String} options - Server options to make the HTTP request to.
 *                                  Either a URI string
 *                                  (e.g. "http://localhost:9090") or an object
 *                                  with fields:
 *   - {String} host              - (optional)
 *   - {Number} port
 *   - {String} url               - (optional) - may be used instead of host/port pair
 *   - {Boolean} cookies          - (optional) - if true then cookies returned by server will be stored and sent back on the next calls.
 * @return {Client}
 */
function Client(options) {

  // Invokes with new if called without
  if (false === (this instanceof Client)) {
    return new Client(options);
  }

  // If a string URI is passed in, converts to URI fields
  if (typeof options === "string") {
    options = url.parse(options);
    options.host = options.hostname;
    options.path = options.pathname;
  }

  if (typeof options.url !== "undefined") {
    let parsedUrl = url.parse(options.url);
    options.host = parsedUrl.hostname;
    options.path = parsedUrl.pathname;
    options.port = parsedUrl.port;
  }

  options.headers = options.headers || {};
  options.timeout = options.timeout || 120000;

  if (options.headers.Authorization == null &&
    options.basic_auth != null &&
    options.basic_auth.user != null &&
    options.basic_auth.pass != null) {
    let auth = options.basic_auth.user + ":" + options.basic_auth.pass;
    options.headers["Authorization"] = "Basic " + Buffer.from(auth).toString("base64");
  }

  for (let attribute in HTTP_HEADERS) {
    if (options.headers[attribute] == null) {
      options.headers[attribute] = HTTP_HEADERS[attribute];
    }
  }

  options.method = "POST";
  this.options = options;

  this.headersProcessors = {
    processors: [],
    composeRequest: function (headers) {
      this.processors.forEach(function (p) { p.composeRequest(headers); });
    },
    parseResponse: function (headers) {
      this.processors.forEach(function (p) { p.parseResponse(headers); });
    }
  };
}

/**
 * Makes an XML-RPC call to the server specified by the constructor's options.
 *
 * @param {String} method     - The method name.
 * @param {Array} params      - Params to send in the call.
 * @param {Function} callback - function(error, value) { ... }
 *   - {Object|null} error    - Any errors when making the call, otherwise null.
 *   - {mixed} value          - The value returned in the method response.
 */
Client.prototype.call = function (req) {
  return new Promise((resolve, reject) => {
    request(req, (error, response, body) => {
      if (error) {

        global.logger.error("Error while submitting xmlPluginRequest: ", error);
        if (error.code == "ESOCKETTIMEDOUT" || error.code == "ETIMEDOUT") {
          reject({ code: 899, msg: "Plugin execution timedout, code:" + error.code, response });
        } else {
          reject({ code: 800, msg: "Plugin execution connection failed, code:" + error.code, response });
        }
      } else {
        return resolve({
          res: response, body
        });
      }
    });
  });
}

Client.prototype.methodCall = async function methodCall(method, params, callback) {

  let startTime = process.hrtime();
  let time, diff;
  let diagnostics = { tid: params[0].originTransactionID, MSISDN: params[0].subscriberNumber };
  try {
    time = process.hrtime();
    let xml = Serializer.serializeMethodCall(method, params, this.options.encoding);
    diff = process.hrtime(time);
    diagnostics.serializerTime = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
    time = process.hrtime();
    let response = await this.call({
      headers: this.options.headers,
      timeout: this.options.timeout || 60000,
      url: "http://" + this.options.host + ":" + this.options.port + this.options.path,
      method: "POST",
      body: xml,
      agent: keepAliveAgent,
      time: true
    });

    diff = process.hrtime(time);
    diagnostics.httpRTT = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();

    diagnostics.elapsedTime = response.res.elapsedTime;
    diagnostics.responseStartTime = response.res.responseStartTime;
    diagnostics.timingStart = response.res.timingStart;
    diagnostics.timings = response.res.timings;
    diagnostics.timingPhases = response.res.timingPhases;

    time = process.hrtime();
    new Deserializer(response.body, (err, result) => {
      if (err) {
        diff = process.hrtime(startTime);
        diagnostics.cbTime = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
        return callback({ ...err, msg: "Plugin execution connection failed, code:" + err.code }, null, diagnostics);
      }
      diff = process.hrtime(time);
      diagnostics.deserializerTime = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
      diff = process.hrtime(startTime);
      diagnostics.cbTime = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
      callback(null, result, diagnostics);
      response = null;
    });
  } catch (error) {
    diff = process.hrtime(startTime);
    diagnostics.cbTime = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
    callback(error, null, diagnostics);
  }
}

module.exports = Client;
