const sax = require("sax"),
  dateFormatter = require("./date_formatter");

const RegexInteger = /^-?\d+$/;

function Deserializer(xml, callback) {
  function cb<PERSON><PERSON>ler(error, result) {
    if (error) {
      callback(error);
    }
    else if (result.length > 1) {
      callback(new Error("Response has more than one param"));
    }
    else if (type !== "methodresponse") {
      callback(new Error("Not a method response"));
    }
    else if (!responseType) {
      callback(new Error("Invalid method response"));
    }
    else {
      callback(null, result[0]);
    }
  };

  let type = null,
    responseType = null,
    stack = [],
    marks = [],
    data = [],
    value = false,
    error = null,
    methodname = null,
    parser = sax.parser(true);

  parser.onopentag = function (node) {
    try {
      let name = node.name.toUpperCase();
      if (name === "ARRAY" || name === "STRUCT") {
        marks.push(stack.length);
      }
      data = [];
      value = (name === "VALUE");
    } catch (error) {
      onError(e);
    }
  };

  parser.onclosetag = function (el) {
    let data1 = data.join("");
    try {
      el = el.toUpperCase();
      switch (el) {
        case "BOOLEAN":
          endBoolean(data1);
          break
        case "INT":
        case "I4":
          endInt(data1);
          break
        case "I8":
          endI8(data1);
          break
        case "DOUBLE":
          endDouble(data1);
          break
        case "STRING":
        case "NAME":
          endString(data1);
          break
        case "ARRAY":
          endArray(data1);
          break
        case "STRUCT":
          endStruct(data1);
          break
        case "BASE64":
          endBase64(data1);
          break
        case "DATETIME.ISO8601":
          endDateTime(data1);
          break
        case "VALUE":
          endValue(data1);
          break
        case "PARAMS":
          endParams(data1);
          break
        case "FAULT":
          endFault(data1);
          break
        case "METHODRESPONSE":
          endMethodResponse(data1);
          break
        case "METHODNAME":
          endMethodName(data1);
          break
        case "METHODCALL":
          endMethodCall(data1);
          break
        case "NIL":
          endNil(data1);
          break
        case "DATA":
        case "PARAM":
        case "MEMBER":
          // Ignored by design
          break
        default:
          onError("Unknown XML-RPC tag \'" + el + "\'");
          break
      }
    } catch (e) {
      onError(e);
    } finally {
      data1 = null;
    }
  };

  parser.ontext = function (text) {
    data.push(text);
  };

  parser.oncdata = function (cdata) {
    data.push(cdata);
  };

  parser.onend = function () {
    if (!error) {
      if (type === null || marks.length) {
        cbHandler(new Error("Invalid XML-RPC message"));
      }
      else if (responseType === "fault") {
        let createFault = function (fault) {
          let error = new Error("XML-RPC fault" + (fault.faultString ? ": " + fault.faultString : ""));
          error.code = fault.faultCode;
          error.faultCode = fault.faultCode;
          error.faultString = fault.faultString;
          return error;
        }
        cbHandler(createFault(stack[0]));
      }
      else {
        cbHandler(undefined, stack);
      }
    }
  };

  parser.onerror = function (msg) {
    if (!error) {
      if (typeof msg === "string") {
        error = new Error(msg);
      }
      else {
        error = msg;
      }
      cbHandler(error);
    }
  };

  function onError(msg) {
    if (!error) {
      if (typeof msg === "string") {
        error = new Error(msg);
      }
      else {
        error = msg;
      }
      cbHandler(error);
    }
  }
  function push(value) {
    stack.push(value);
  }

  function endNil(data) {
    push(null);
    value = false;
  }

  function endBoolean(data) {
    if (data === "1") {
      push(true);
    }
    else if (data === "0") {
      push(false);
    }
    else {
      throw new Error("Illegal boolean value \'" + data + "\'");
    }
    value = false;
  }

  function endInt(data) {
    let value = parseInt(data, 10);
    if (isNaN(value)) {
      throw new Error("Expected an integer but got \'" + data + "\'");
    }
    else {
      push(value);
      value = false;
    }
  }

  function endDouble(data) {
    let value = parseFloat(data);
    if (isNaN(value)) {
      throw new Error("Expected a double but got \'" + data + "\'");
    }
    else {
      push(value)
      value = false;
    }
  }

  function endString(data) {
    push(data);
    value = false;
  }

  function endArray(data) {
    let mark = marks.pop();
    stack.splice(mark, stack.length - mark, stack.slice(mark));
    value = false;
  }

  function endStruct(data) {
    let mark = marks.pop(),
      struct = {},
      items = stack.slice(mark),
      i = 0;

    for (; i < items.length; i += 2) {
      struct[items[i]] = items[i + 1];
    }
    stack.splice(mark, stack.length - mark, struct);
    value = false;
  }

  function endBase64(data) {
    let buffer = Buffer.from(data, "base64");
    push(buffer);
    value = false;
  }

  endDateTime = function (data) {
    let date = dateFormatter.decodeIso8601(data);
    push(date);
    value = false;
  }

  function endI8(data) {
    if (!RegexInteger.test(data)) {
      throw new Error("Expected integer (I8) value but got \'" + data + "\'");
    }
    else {
      endString(data);
    }
  }

  function endValue(data) {
    if (value) {
      endString(data);
    }
  }

  function endParams(data) {
    responseType = "params";
  }

  function endFault(data) {
    responseType = "fault";
  }

  function endMethodResponse(data) {
    type = "methodresponse";
  }

  function endMethodName(data) {
    methodname = data;
  }

  function endMethodCall(data) {
    type = "methodcall";
  }
  parser.write(xml).end();
}

module.exports = Deserializer;
