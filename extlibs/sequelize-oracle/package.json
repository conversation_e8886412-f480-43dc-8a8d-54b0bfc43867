{"_from": "sequelize-oracle", "_id": "sequelize-oracle@4.0.9", "_inBundle": false, "_integrity": "sha1-aGZwfE3E2gz97SOBPIuyVnTb5K0=", "_location": "/sequelize-oracle", "_phantomChildren": {"moment": "2.24.0"}, "_requested": {"type": "tag", "registry": true, "raw": "sequelize-oracle", "name": "sequelize-oracle", "escapedName": "sequelize-oracle", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "http://cots.comviva.com/api/npm/Global_NPM_Repo/sequelize-oracle/-/sequelize-oracle-4.0.9.tgz", "_shasum": "6866707c4dc4da0cfded23813c8bb25674dbe4ad", "_spec": "sequelize-oracle", "_where": "D:\\Workspace\\Leap\\MBS-LEAP-3.0\\pm_fsync", "bundleDependencies": false, "author": "<EMAIL>", "dependencies": {"async": "^3.2.0", "bluebird": "~3.7.2", "deasync": "^0.1.20", "depd": "^2.0.0", "dottie": "^2.0.2", "generic-pool": "3.7.1", "inflection": "^1.12.0", "lodash": "^4.17.15", "moment": "^2.27.0", "moment-timezone": "^0.5.31", "oracledb": "^4.2.0", "shimmer": "1.2.1", "toposort-class": "~1.0.1", "uuid": "~8.2.0", "validator": "^13.1.1"}, "deprecated": false, "description": "Multi dialect ORM for Node.JS/IO.js", "devDependencies": {"chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-datetime": "~1.7.0", "chai-spies": "~1.0.0", "coffee-script": "~1.12.7", "commander": "^5.1.0", "continuation-local-storage": "^3.2.1", "dox": "0.9.0", "git": "^0.1.5", "istanbul": "~0.4.5", "jshint": ">=2.11.1", "lcov-result-merger": "~3.1.0", "mocha": "^8.0.1", "mysql": "~2.18.1", "sinon": "^9.0.2", "sinon-chai": "^3.5.0", "tedious": "^8.3.0", "watchr": "~6.4.0"}, "engines": {"node": ">=0.6.21"}, "keywords": ["mysql", "sqlite", "postgresql", "postgres", "mssql", "orm", "nodejs", "object relational mapper"], "license": "MIT", "main": "index", "name": "sequelize-oracle", "scripts": {"build-docker": "sudo docker-compose build", "docs": "node docs/docs-generator.js", "test": "make all", "test-docker": "sudo docker-compose run sequelize /bin/sh -c \"make all\""}, "version": "4.0.9"}