var colors = require('colors');

const ExpressionEngine = require("../lib/ExecutionContext").ExpressionEngine;

let dataTable = {
  '$response': {
    resultsets: {
      result1: [{
        BUNDLE_NAME: "UnlimiNet Modem Daily"
      }]
    }
  }
};
let macros = new ExpressionEngine({ dataTable });
const msisdn_validator = (max) => {
  let count = 0
  for (let i = 0; i < max; i++) {
    let num = 919876543210 + i;
    let status = macros.eval("_msisdnValidator('" + num + "')");
    if (status) count += 1;
  }
  return Promise.resolve(count)
}

const setVar = (max) => {
  let count = 0
  for (let i = 0; i < max; i++) {
    let num = 919876543210 + i;
    macros.set("$MSISDN", String(num));
    count += 1;
  }
  return Promise.resolve(count)
}

const setGetVar = (max) => {
  let count = 0
  for (let i = 0; i < max; i++) {
    let num1 = 919876543210 + i;
    macros.set("$MSISDN", String(num1));
    let num2 = macros.get("$MSISDN");
    if (num1 = num2) count += 1;
  }
  return Promise.resolve(count)
}

const setEvalExpr = (max) => {
  let count = 0
  for (let i = 0; i < max; i++) {
    let num1 = 919876543210 + i;
    macros.set("$MSISDN", String(num1));
    let num2 = macros.eval("$MSISDN");
    if (num1 = num2) count += 1;
  }
  return Promise.resolve(count)
}

const evalCondition = (max) => {
  let count = 0
  for (let i = 0; i < max; i++) {
    let num1 = 919876543210 + i;
    macros.set("$response.resultsets.result1[0].BUNDLE_NAME", String(num1));
    let expr = [{ "expr": ["$response.resultsets.result1[0].BUNDLE_NAME", "eq", String(num1)] }];
    let result = macros.evalCondition(expr);
    if (result) count += 1;
  }
  return Promise.resolve(count)
}

const functors = {
  setVar, setGetVar, setEvalExpr, evalCondition, msisdn_validator
}


/* _*_*_*_*_*_ Print _*_*_*_*_*/

console.log(colors.green('\n*_*_*_*_*_*_*_*_* Preformance Injection *_*_*_*_*_*_*_*_*'))
const allNameSpace = Object.getOwnPropertyNames(functors)
console.log(colors.green('Property:', allNameSpace))

allNameSpace.forEach((nameSpace) => {
  console.log(colors.green('      ', `typeof functors[${nameSpace}]: `, typeof functors[nameSpace]))
})


/* _*_*_*_*_*_ Preformance Injection _*_*_*_*_*/
allNameSpace.forEach((nameSpace) => {
  var temp = functors[nameSpace]
  functors[nameSpace] = function () {

    /*_*_*_*_ Init _*_*_*/
    var old_time = new Date();
    var m0 = process.memoryUsage()
    var c0 = process.cpuUsage()

    var returnResult = temp.apply(this, arguments);

    /*_*_*_*_ Finished _*_*_*/
    var new_time = new Date();
    var m1 = process.memoryUsage()
    var diffCPU = process.cpuUsage(c0)

    console.log(colors.cyan(`           *_*_*_ function ${nameSpace} _*_*_*`))
    console.log(colors.cyan('           RAM         : ', (m1['rss'] - m0['rss']) / 1048576, 'mb'))
    console.log(colors.cyan('           HeapTotal   : ', (m1['heapTotal'] - m0['heapTotal']) / 1048576, 'mb'))
    console.log(colors.cyan('           HeapUsed    : ', (m1['heapUsed'] - m0['heapUsed']) / 1048576, 'mb'))
    //console.log(colors.cyan('           External    : ', (m1['external'] - m0['external']) / 1048576, 'mb'))
    console.log(colors.cyan('           CPU         : ', (diffCPU.user + diffCPU.system) / 1000000, 's'))
    console.log(colors.cyan('           Spend time  : ', (new_time - old_time), 'ms'))
    return returnResult
  }
})

/* _*_*_*_*_*_ Operation _*_*_*_*_*/
let samples = 100000;
functors.setVar(samples).then(
  (res) => { console.log('      functors.set(' + samples + '):    ', res) }
)
functors.setGetVar(samples).then(
  (res) => { console.log('      functors.setGetVar(' + samples + '):    ', res) }
)
functors.setEvalExpr(samples).then(
  (res) => { console.log('      functors.setEvalExpr(' + samples + '):    ', res) }
)
functors.evalCondition(samples).then(
  (res) => { console.log('      functors.evalCondition(' + samples + '):    ', res) }
)
functors.msisdn_validator(samples).then(
  (res) => { console.log('      functors.msisdn_validator(' + samples + '):    ', res) }
)
