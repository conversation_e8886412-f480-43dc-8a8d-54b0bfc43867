const assert = require('assert');
const ExpressionEngine = require("../index").ExpressionEngine;

let dataTable = {
  '$response': {
    resultsets: {
      result1: [{
        BUNDLE_NAME: "UnlimiNet Modem Daily"
      }]
    }
  }
};

describe('App Macros Suite', function () {
  let macros = new ExpressionEngine({ dataTable });
  describe('#_length()', function () {
    it('should return 11 when the value is _length(\'edfjerfberf\')', function () {
      assert.equal(macros.eval("_length('edfjerfberf')"), 11);
    });
    it('should return 21 when the value is _length($response.resultsets.result1[0].BUNDLE_NAME)', function () {
      assert.equal(macros.eval("_length($response.resultsets.result1[0].BUNDLE_NAME)"), 21);
    });
    it('should return "_length($response.resultsets.result2[0].BUNDLE_NAME)" when the value is _length($response.resultsets.result1[0].BUNDLE_NAME) not defined', function () {
      assert.equal(macros.eval("_length($response.resultsets.result2[0].BUNDLE_NAME)"), "_length($response.resultsets.result2[0].BUNDLE_NAME)");
    });
    it('should return when the value is _length($response.resultsets.result1[0].BUNDLE_NAME) not defined', function () {
      assert.equal(macros.eval("_length($response.resultsets.result2[0].BUNDLE_NAME)"), "_length($response.resultsets.result2[0].BUNDLE_NAME)");
    });
  });
  describe('#Eval Expression()', function () {
    it('should return "UnlimiNet Modem Daily" when the value is $response.resultsets.result1[0].BUNDLE_NAME', function () {
      assert.equal(macros.eval("$response.resultsets.result1[0].BUNDLE_NAME"), "UnlimiNet Modem Daily");
    });
    macros.set("$MSISDN", "9876543210");
    it('G:Expression includes a valid param with valid data|W:eval Expression function is called|T:expression should be successfully evaluated and expression value should be dispalyed', function () {
      assert.equal(macros.eval("$MSISDN"), "9876543210");
    });
    it('G:Expression includes a valid param with valid data|W:eval Expression function is called|T:expression should be successfully evaluated and expression value should be dispalyed', function () {
      assert.equal(macros.eval("$MSISD"), "$MSISD");
    });
  });
});