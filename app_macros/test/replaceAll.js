const ExpressionEngine = require("../index").ExpressionEngine;

let dataTable = {
  "$txnId": **************,
  "$appId": *************,
  "$imode": "HTTP",
  "$imethod": "GET",
  "$MSISDN": ************,
  "$debugId": "d5f465f8-ca9b-4079-8164-e93c5d421f26",
  "$isNewRequest": 1,
  "$language": "en",
  "$contentType": "text",
  "$response": {
    "accountFlagsAfter": {
      "activationStatusFlag": true,
      "negativeBarringStatusFlag": false,
      "serviceFeePeriodExpiryFlag": false,
      "serviceFeePeriodWarningActiveFlag": false,
      "supervisionPeriodExpiryFlag": false,
      "supervisionPeriodWarningActiveFlag": false
    },
    "accountFlagsBefore": {
      "activationStatusFlag": true,
      "negativeBarringStatusFlag": false,
      "serviceFeePeriodExpiryFlag": false,
      "serviceFeePeriodWarningActiveFlag": false,
      "supervisionPeriodExpiryFlag": false,
      "supervisionPeriodWarningActiveFlag": false
    },
    "accountValue1": "2",
    "creditClearanceDate": "2022-06-23T05:30:00.000Z",
    "currency1": "CFA",
    "dedicatedAccountInformation": [
      {
        "dedicatedAccountActiveValue1": "0",
        "dedicatedAccountID": 2,
        "dedicatedAccountUnitType": 1,
        "dedicatedAccountValue1": "0",
        "expiryDate": "9999-12-30T17:30:00.120Z",
        "startDate": "9999-12-30T17:30:00.120Z"
      },
      {
        "dedicatedAccountActiveValue1": "0",
        "dedicatedAccountID": 7,
        "dedicatedAccountUnitType": 1,
        "dedicatedAccountValue1": "0",
        "expiryDate": "9999-12-30T17:30:00.120Z",
        "startDate": "9999-12-30T17:30:00.120Z"
      },
      {
        "dedicatedAccountActiveValue1": "0",
        "dedicatedAccountID": 8,
        "dedicatedAccountUnitType": 1,
        "dedicatedAccountValue1": "0",
        "expiryDate": "9999-12-30T17:30:00.120Z",
        "startDate": "9999-12-30T17:30:00.120Z"
      },
      {
        "dedicatedAccountActiveValue1": "0",
        "dedicatedAccountID": 9,
        "dedicatedAccountUnitType": 1,
        "dedicatedAccountValue1": "0",
        "expiryDate": "9999-12-30T17:30:00.120Z",
        "startDate": "9999-12-30T17:30:00.120Z"
      },
      {
        "dedicatedAccountActiveValue1": "*********",
        "dedicatedAccountID": 10017,
        "dedicatedAccountUnitType": 6,
        "dedicatedAccountValue1": "*********"
      }
    ],
    "languageIDCurrent": 2,
    "offerInformationList": [
      {
        "expiryDate": "9999-12-30T17:30:00.120Z",
        "offerID": 2000,
        "offerType": 0,
        "startDate": "2015-04-17T05:30:00.000Z"
      },
      {
        "expiryDateTime": "2019-09-29T11:45:32.010Z",
        "offerID": 100171,
        "offerState": 0,
        "offerType": 2,
        "productID": 2,
        "startDateTime": "2019-09-28T11:45:32.010Z",
        "usageCounterUsageThresholdInformation": [
          {
            "usageCounterID": 100171,
            "usageCounterValue": "********",
            "usageThresholdInformation": [
              {
                "usageThresholdID": 100171,
                "usageThresholdSource": 3,
                "usageThresholdValue": "*********"
              }
            ]
          }
        ]
      }
    ],
    "originTransactionID": "**************",
    "responseCode": "0",
    "serviceClassCurrent": 48,
    "serviceFeeExpiryDate": "2022-06-23T05:30:00.000Z",
    "serviceRemovalDate": "2022-06-23T05:30:00.000Z",
    "supervisionExpiryDate": "2022-06-23T05:30:00.000Z"
  },
  "$originNodeType": "EXT",
  "$originHostName": "micrwair01",
  "$originTransactionID": **************,
  "expr": [
    "Your",
    "current",
    "balance",
    "is",
    "RWF.$balance",
    "Expiry",
    "on",
    "23Jun2022",
    "SMS",
    0,
    "VOICE",
    0,
    "Mins",
    "Data",
    153.4765625,
    "mbs"
  ],
  "finalExpr": "Your current balance is RWF.$balance Expiry on 23Jun2022 SMS 0 VOICE 0 Mins Data 153.4765625 mbs",
  "$originTimeStamp": 1570438709435,
  "$subscriberNumberNAI": 1,
  "$subscriberNumber": ************,
  "$SMS": 0,
  "$DATA": 153.4765625,
  "$TIME": 0,
  "$balance": "0.02",
  "$exireDate": "23Jun2022",
  "$success": {
    "code": [
      "0",
      200,
      201,
      202,
      203,
      204,
      205,
      206,
      207,
      208,
      209,
      210,
      211,
      212,
      213,
      214,
      215,
      216,
      217,
      218,
      219,
      220,
      221,
      222,
      223,
      224,
      225,
      226,
      "S9000"
    ],
    "message": "Your current balance is RWF.0.02 Expiry on 23Jun2022 SMS 0 VOICE 0 Mins Data 153.4765625 mbs"
  },
  "$customErrors": [
    {
      "code": [],
      "message": ""
    }
  ],
  "$defaultError": {
    "code": "E9000",
    "message": "Dear Customer, Your request cannot be processed now. Please try again later."
  },
  "appId": "*************",
  "defaultError": {
    "code": "E9000",
    "message": "Dear Customer, Your request cannot be processed now. Please try again later."
  },
  "success": {
    "code": [
      "0",
      "200",
      "201",
      "202",
      "203",
      "204",
      "205",
      "206",
      "207",
      "208",
      "209",
      "210",
      "211",
      "212",
      "213",
      "214",
      "215",
      "216",
      "217",
      "218",
      "219",
      "220",
      "221",
      "222",
      "223",
      "224",
      "225",
      "226",
      "S9000"
    ],
    "message": "Your current balance is RWF.$balance Expiry on $exireDate SMS $SMS VOICE $TIME Mins Data $DATA mbs"
  },
  "customErrors": [
    {
      "code": [],
      "message": ""
    }
  ],
  "language": "en"
};

let macros = new ExpressionEngine({ dataTable });

console.log(macros.replaceAllPlaceHolders("Your current balance is RWF $balance Expiry on $exireDate SMS $SMS VOICE $TIME Mins Data $DATA mbs"));
