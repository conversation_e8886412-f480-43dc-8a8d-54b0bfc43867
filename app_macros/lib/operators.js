const operators = Object.freeze({
    eq: {
        op: "eq",
        name: "Equals",
        description: "First operand Equal to Second operand"
    },
    ne: {
        op: "ne",
        name: "Not Equals",
        description: "First operand Not Equal to Second operand"
    },
    gte: {
        op: "gte",
        name: "Greater Than or Equals",
        description: "First operand is greater than or equal to Second operand"
    },
    gt: {
        op: "gt",
        name: "Greater Than",
        description: "First operand is greater than to Second operand"
    },
    lte: {
        op: "lte",
        name: "Less Than or Equals",
        description: "First operand is lesser than or equal to Second operand"
    },
    lt: {
        op: "lt",
        name: "Less Than",
        description: "First operand is lesser than to Second operand"
    },
    contains: {
        op: "contains",
        name: "Contains",
        description: "Left operand contains in Right operand"
    },
    ncontains: {
        op: "ncontains",
        name: "Does Not Contains",
        description: "Left operand does not contains in Right operand"
    },
    and: {
        op: "and",
        name: "And",
        description: "Left operand and Right operand"
    },
    or: {
        op: "or",
        name: "Or",
        description: "Left operand or Right operand"
    }
});

module.exports = operators;
