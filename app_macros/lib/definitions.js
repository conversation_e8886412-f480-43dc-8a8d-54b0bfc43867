const utility = require("utility");
const dateformat = require("dateformat");
const letters = /^[A-Za-z]+$/;
const alphanumeric = /^[A-Za-z0-9]+$/;
const numbers = /^[0-9]+$/;
const lowercaseAlpha = /^[a-z]+$/;
const upperCaseAlpha = /^[A-Z]+$/;
const mValidator = require("msisdn-validator");
const xml2json = require("xml2json");
const DAY = 1000 * 60 * 60 * 24;
const md5 = require("md5");
const crypto = require('crypto');
const jwt = require('jsonwebtoken');
const moment = require('moment');
const NodeRSA = require('node-rsa');

/**
 * <AUTHOR>
 * @description function to get difference bewteen two dates
 * */
function dateDiffInDays(expiryDate) {
  let currentDate = new Date();
  return Math.floor((Date.UTC(expiryDate.getFullYear(), expiryDate.getMonth(), expiryDate.getDate()) - Date.UTC(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate())) / DAY);
}

module.exports = {
  _now: () => {
    return new Date().getTime();
  },
  _uuid: () => {
    return crypto.randomBytes(9).toString('hex'); // Generates a random 18-character string
  },
  _getTxnId: () => {
    return utility.getUniqueTxnId();
  },
  _startDate: (days) => {
    let cur = new Date();
    cur.setTime(cur.getTime() + days * 86400000);
    cur.setHours(24);
    cur.setMinutes(59);
    cur.setSeconds(59);
    return cur;
  },
  _endDate: (days) => {
    let cur2 = new Date();
    cur2.setTime(cur2.getTime() + days * 86400000);
    cur2.setHours(24);
    cur2.setMinutes(59);
    cur2.setSeconds(59);
    return cur2;
  },
  _isEmpty: (str) => {
    if (str == null) { return true; }
    if (str.length == 0) {
      return true;
    }
    return false;
  },
  _getEncryptedCipher: (KEY, text) => {
    const algorithm = 'aes-192-cbc'; // AES-192 needs an explicit IV
    const key = crypto.scryptSync(KEY, 'salt', 24); // Generate a 24-byte key
    const iv = Buffer.alloc(16, 0); // You should use a random IV in real use cases

    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
},

  _md5HashString: (str) => {
    if (str == null) { return false; }
    str = String(str);
    return md5(str);
  },
  _getSHA256: (inputString) => {
    var hash = crypto.createHash('sha256')
      .update(inputString)
      .digest('hex');
    return hash;
  },
  _isAlpha: (str) => {
    if (str == null) { return false; }
    str = String(str);
    return str.match(letters) != null;
  },

  _isNumber: (num) => {
    return !isNaN(Number(num));
  },

  _isAlphanumeric: (str) => {
    if (str == null) { return false; }
    str = String(str);
    return str.match(alphanumeric) != null;
  },

  _isDigit: (str) => {
    if (str == null) { return false; }
    str = String(str);
    return str.match(numbers) != null;
  },

  _isLower: (str) => {
    if (str == null) { return false; }
    str = String(str);
    return str.match(lowercaseAlpha) != null;
  },

  _isUpper: (str) => {
    if (str == null) { return false; }
    str = String(str);
    return str.match(upperCaseAlpha) != null;
  },

  _msisdnValidator: (msisdn) => {
    if (msisdn == null) { return false; }
    let countryCode = global.config && global.config.country || "IN";
    mValidator.init(countryCode);
    return mValidator.validate(msisdn);
  },

  _isInRange: (num, min, max) => {
    return Number(num) >= Number(min) && Number(num) <= Number(max);
  },

  _add: (num1, num2) => {
    return Number(num1) + Number(num2);
  },

  _subtract: (num1, num2) => {
    return Number(num1) - Number(num2);
  },

  _multiply: (num1, num2) => {
    return Number(num1) * Number(num2);
  },

  _divide: (num1, num2) => {
    return (Number(num1) / Number(num2)).toFixed(2);
  },

  _remaider: (num1, num2) => {
    return Number(num1) % Number(num2);
  },

  _and: (num1, num2) => {
    num1 = utility.convert2Primitive(num1);
    num2 = utility.convert2Primitive(num2);
    return num1 && num2;
  },
  _or: (num1, num2) => {
    num1 = utility.convert2Primitive(num1);
    num2 = utility.convert2Primitive(num2);
    return num1 || num2;
  },

  /** Returns a encrypted string using RSA public key */
  _rsaPublicKeyEncrypt: (publicKey, plainText) => {
    var key = new NodeRSA();
    key.importKey(publicKey, 'pkcs8-public');
    key.setOptions({ encryptionScheme: 'pkcs1' });
    var encryptedString = key.encrypt(plainText, 'base64');
    return encryptedString;
  },

  /** Returns a decrypted text using RSA public key */
  _rsaPrivateKeyDecrypt: (privateKey, encryptedString) => {
    var key = new NodeRSA();
    key.importKey(privateKey, 'pkcs8-private');
    key.setOptions({ encryptionScheme: 'pkcs1' });
    var decryptedString = key.decrypt(encryptedString, 'utf-8');
    return decryptedString;
  },

  /** Returns a date as a string value. */
  _date2DateString: (args) => {
    return new Date(args).toDateString();
  },
  /** Returns a time as a string value. */
  _date2TimeString: (args) => {
    return new Date(args).toTimeString();
  },
  /** Returns a value as a string value appropriate to the host environment's current locale. */
  _date2LocaleString: (args) => {
    return new Date(args).toLocaleString();
  },
  /** Returns a date as a string value appropriate to the host environment's current locale. */
  _date2LocaleDateString: (args) => {
    return new Date(args).toLocaleDateString();
  },
  /** Returns a time as a string value appropriate to the host environment's current locale. */
  _date2LocaleTimeString: (args) => {
    return new Date(args).toLocaleTimeString();
  },
  /** Gets the time value in milliseconds. */
  _getTime: (args) => {
    return new Date(args).getTime();
  },
  /** Gets the year, using local time. */
  _getFullYear: (args) => {
    return new Date(args).getFullYear();
  },
  /** Gets the year using Universal Coordinated Time (UTC). */
  _getUTCFullYear: (args) => {
    return new Date(args).getUTCFullYear();
  },
  /** Gets the month, using local time. */
  _getMonth: (args) => {
    return new Date(args).getMonth() + 1;
  },
  /** Gets the month of a Date object using Universal Coordinated Time (UTC). */
  _getUTCMonth: (args) => {
    return new Date(args).getUTCMonth();
  },
  /** Gets the day-of-the-month, using local time. */
  _getDate: (args) => {
    return new Date(args).getDate();
  },
  /** Gets the day-of-the-month, using Universal Coordinated Time (UTC). */
  _getUTCDate: (args) => {
    return new Date(args).toDateString();
  },
  /** Gets the day of the week, using local time. */
  _getDay: (args) => {
    return new Date(args).getDay();
  },
  /** Gets the day of the week using Universal Coordinated Time (UTC). */
  _getUTCDay: (args) => {
    return new Date(args).getUTCDay();
  },
  /** Gets the hours in a date, using local time. */
  _getHours: (args) => {
    return new Date(args).getHours();
  },
  /** Gets the hours value in a Date object using Universal Coordinated Time (UTC). */
  _getUTCHours: (args) => {
    return new Date(args).getUTCHours();
  },
  /** Gets the minutes of a Date object, using local time. */
  _getMinutes: (args) => {
    return new Date(args).getMinutes();
  },
  /** Gets the minutes of a Date object using Universal Coordinated Time (UTC). */
  _getUTCMinutes: (args) => {
    return new Date(args).getUTCMinutes();
  },
  /** Gets the seconds of a Date object, using local time. */
  _getSeconds: (args) => {
    return new Date(args).getSeconds();
  },
  /** Gets the seconds of a Date object using Universal Coordinated Time (UTC). */
  _getUTCSeconds: (args) => {
    return new Date(args).getUTCSeconds();
  },
  /** Gets the milliseconds of a Date, using local time. */
  _getMilliseconds: (args) => {
    return new Date(args).getMilliseconds();
  },
  /** Gets the milliseconds of a Date object using Universal Coordinated Time (UTC). */
  _getUTCMilliseconds: (args) => {
    return new Date(args).getUTCMilliseconds();
  },
  /** Gets the difference in minutes between the time on the local computer and Universal Coordinated Time (UTC). */
  _getTimezoneOffset: (args) => {
    return new Date(args).getTimezoneOffset();
  },

  _generateJwtToken: (payload, secret, options) => {
    try {
      let token;
      token = jwt.sign(payload, secret, options);
      return token;
    }
    catch (err) {
      global.logger.error("Error while generating JWT token", err);
    }
  },
  /**
      * Returns the character at the specified index.
      * @param pos The zero-based index of the desired character.
      */
  _charAt: (str, pos) => {
    return str.charAt(pos);
  },

  /**
    * Returns the Unicode value of the character at the specified location.
    * @param index The zero-based index of the desired character. If there is no character at the specified index, NaN is returned.
    */
  _charCodeAt: (str, index) => {
    return str.charCodeAt(index);
  },

  /**
    * Returns a string that contains the concatenation of two or more strings.
    * @param strings The strings to append to the end of the string.
    */
  _concat: (...strings) => {
    let res = "";
    for (let i = 0; i < strings.length; i++) {
      res += strings[i];
    }
    return res;
  },

  /**
    * Returns the position of the first occurrence of a substring.
    * @param searchString The substring to search for in the string
    * @param position The index at which to begin searching the String object. If omitted, search starts at the beginning of the string.
    */
  _indexOf: (searchString, position) => {
    return searchString.indexOf(position);
  },

  /**
    * Returns the last occurrence of a substring in the string.
    * @param searchString The substring to search for.
    * @param position The index at which to begin searching. If omitted, the search begins at the end of the string.
    */
  _lastIndexOf: (searchString, position) => {
    return searchString.indexOf(position);
  },

  /**
    * Matches a string with a regular expression, and returns an array containing the results of that search.
    * @param regexp A variable name or string literal containing the regular expression pattern and flags.
    */
  _match: (str, expr) => {
    return str.match(expr);
  },

  /**
    * Replaces text in a string, using a regular expression or search string.
    * @param searchValue A string to search for.
    * @param replaceValue A string containing the text to replace for every successful match of searchValue in this string.
    */
  _replace: (str, searchValue, replaceValue) => {
    return str.replace(searchValue, replaceValue);
  },


  /**
    * Finds the first substring match in a regular expression search.
    * @param regexp The regular expression pattern and applicable flags.
    */
  _search: (str, searchValue) => {
    return str.search(searchValue);
  },

  /**
    * Returns a section of a string.
    * @param start The index to the beginning of the specified portion of stringObj.
    * @param end The index to the end of the specified portion of stringObj. The substring includes the characters up to, but not including, the character indicated by end.
    * If this value is not specified, the substring continues to the end of stringObj.
    */
  _slice: (str, start, end) => {
    return str.slice(start, end);
  },

  /**
    * Returns the substring at the specified location within a String object.
    * @param start The zero-based index number indicating the beginning of the substring.
    * @param end Zero-based index number indicating the end of the substring. The substring includes the characters up to, but not including, the character indicated by end.
    * If end is omitted, the characters from start through the end of the original string are returned.
    */
  _substring: (str, start, end) => {
    return String(str).slice(start, end);
  },
  _stringify: (str) => {
    return JSON.stringify(str);
  },
  _length: (str) => {
    return String(str).length;
  },
  _toNumber: (str) => {
    return Number(str);
  },
  /** Converts all the alphabetic characters in a string to lowercase. */
  _toLowerCase: (str) => {
    return str.toLowerCase();
  },

  /** Converts all alphabetic characters to lowercase, taking into account the host environment's current locale. */
  _toLocaleLowerCase: (str) => {
    return str.toLocaleLowerCase();
  },

  /** Converts all the alphabetic characters in a string to uppercase. */
  _toUpperCase: (str) => {
    return str.toUpperCase();
  },

  /** Returns a string where all alphabetic characters have been converted to uppercase, taking into account the host environment's current locale. */
  _toLocaleUpperCase: (str) => {
    return str.toLocaleUpperCase();
  },

  /** Removes the leading and trailing white space and line terminator characters from a string. */
  _trim: (str) => {
    return str.trim();
  },
  /** Removes the trailing white space and line terminator characters from a string. */
  _trimLeft: (str) => {
    return str.trimLeft();
  },
  _trimRight: (str) => {
    return str.trimRight();
  },
  /**
    * Returns true if the sequence of elements of searchString converted to a String is the
    * same as the corresponding elements of this object (converted to a String) starting at
    * position. Otherwise returns false.
    */
  _startsWith: (str, searchString) => {
    return str.startsWith(searchString);
  },
  /**
    * Returns true if the sequence of elements of searchString converted to a String is the
    * same as the corresponding elements of this object (converted to a String) starting at
    * endPosition – length(this). Otherwise returns false.
    */
  _endsWith: (str, searchString) => {
    return str.endsWith(searchString);
  },

  _parseDedicatedAccount: (info, accid) => {
    try {
      for (let i = 0; i < info.length; i++) {
        let da = info[i];
        if (da.dedicatedAccountID == accid) {
          return da;
        }
      }
    } catch (error) {
    }
    return {
      "dedicatedAccountActiveValue1": "0",
      "dedicatedAccountID": -1,
      "dedicatedAccountUnitType": -1,
      "dedicatedAccountValue1": "0",
      "expiryDate": "",
      "offerID": -1,
      "startDate": ""
    };
  },

  _parseDedicatedOfferID: (info, offerID) => {
    try {
      for (let i = 0; i < info.length; i++) {
        let daOffer = info[i];
        if (daOffer.offerID == offerID) {
          return daOffer;
        }
      }
    } catch (error) {

    }
    return {
      "dedicatedAccountActiveValue1": "0",
      "dedicatedAccountID": -1,
      "dedicatedAccountUnitType": -1,
      "dedicatedAccountValue1": "0",
      "expiryDate": "",
      "offerID": -1,
      "startDate": ""
    };
  },

  _parseOfferInformationList: (offers, offerID) => {
    try {
      for (let i = 0; i < offers.length; i++) {
        let offer = offers[i];
        if (offer.offerID == offerID) {
          return offer;
        }
      }
    } catch (error) {

    }
    return {
      "expiryDate": "9999-12-30T21:00:00.120Z",
      "offerID": -1,
      "offerType": 0,
      "startDate": "9999-12-30T21:00:00.120Z"
    };
  },

  _checkOfferIDExists: (offers, offerID) => {
    let flag = false;
    try {
      let arr = [], count = 0;
      if (offerID != null && typeof offerID == "string") {
        arr = offerID.split(",");
      } else {
        arr.push(String(offerID));
      }
      for (let i = 0; i < offers.length; i++) {
        let offer = offers[i];
        if (arr.includes(String(offer.offerID))) {
          let expDate = new Date(offer.expiryDate || offer.expiryDateTime);
          flag = (expDate.getTime() >= new Date().getTime());
          count++;
        }
      }
      if (arr.length > 0 && arr.length == count) {
        flag = true;
      }
    } catch (error) {
    }
    return flag;
  },

  _checkUsageCounterValue: (offers, offerID, ucID) => {
    try {
      let retJSON = {};
      retJSON["offerID"] = offerID;
      retJSON["ucID"] = ucID;
      let tempUCValue = 0;
      let tempUTValue = 0;
      let tempExpiryDate = "";
      let j = 0;
      for (let i = 0; i < offers.length; i++) {
        if (offers[i].offerID == offerID) {
          let expDate = new Date(offers[i].expiryDate || offers[i].expiryDateTime);
          if (expDate.getTime() >= new Date().getTime()) {
            if (j == 0) {
              tempExpiryDate = offers[i].expiryDateTime;
              j++;
            } else {
              let expDate1 = new Date(offers[i].expiryDate || offers[i].expiryDateTime);
              let expDate2 = new Date(tempExpiryDate);
              if (expDate1 > expDate2) {
                tempExpiryDate = offers[i].expiryDateTime;
              }
            }
            if (offers[i].usageCounterUsageThresholdInformation) {
              for (let j = 0; j < offers[i].usageCounterUsageThresholdInformation.length; j++) {
                if (ucID == offers[i].usageCounterUsageThresholdInformation[j].usageCounterID) {
                  tempUCValue += Number(offers[i].usageCounterUsageThresholdInformation[j].usageCounterValue);
                }
              }
            }
            if (offers[i].attributeInformationList) {
              for (let j = 0; j < offers[i].attributeInformationList.length; j++) {
                var s = offers[i].attributeInformationList[j].attributeName.substr(3, 6)
                if (parseInt(s, 10) == parseInt(ucID, 10)) {
                  tempUTValue += Number(offers[i].attributeInformationList[j].attributeValueString);
                }

                else if (offers[i].attributeInformationList[j].attributeName.substr(3, 8) == ucID) {
                  tempUTValue += Number(offers[i].attributeInformationList[j].attributeValueString);
                }

                else if (offers[i].attributeInformationList[j].attributeName.substr(0, 6) == ucID) {
                  tempUTValue += Number(offers[i].attributeInformationList[j].attributeValueString);
                }
              }
            } else if (offers[i].usageCounterUsageThresholdInformation) {
              for (let j = 0; j < offers[i].usageCounterUsageThresholdInformation.length; j++) {
                if (ucID == offers[i].usageCounterUsageThresholdInformation[j].usageCounterID) {
                  tempUTValue += Number(offers[i].usageCounterUsageThresholdInformation[j].usageThresholdInformation[0].usageThresholdValue);
                }
              }
            }
          }
        }
      }

      retJSON["expiryDateTime"] = tempExpiryDate;
      if (tempUCValue > 0 && tempUTValue > 0) {
        retJSON["ucValue"] = tempUTValue - tempUCValue;
        return retJSON;
      }
      else if (tempUCValue >= 0 && tempUTValue > 0) {
        retJSON["ucValue"] = tempUTValue - tempUCValue;
        return retJSON;
      }
      else if (tempUCValue > 0) {
        retJSON["ucValue"] = tempUCValue;
        return retJSON;
      } else if (tempUTValue > 0) {
        retJSON["ucValue"] = tempUTValue;
        return retJSON;
      }
    } catch (error) {
    }
    return {
      "offerID": -1,
      "ucID": -1,
      "ucValue": 0,
      "expiryDateTime": ""
    };
  },

  _contains: (element, elementsList) => {
    let flag = false;
    try {
      let arr = []
      if (elementsList != null && typeof elementsList == 'string') {
        arr = elementsList.split(',')
      } else {
        arr.push(String(elementsList))
      }

      if (arr.includes(String(element))) {
        flag = true;
      }
    } catch (error) {
    }
    return flag;
  },
  _date2customformat: (dt, format) => {
    let res;
    if (format == null) {
      format = "isoDateTime";
    }
    if (dt == null) {
      res = new Date();
    } else {
      res = new Date(dt);
    }
    return dateformat(res, format);
  },
  _regexValidator: (regex, str) => {
    try {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(regex, str);
      }
      if (regex != null && str != null) { return new RegExp(regex).test(str); }
    } catch (e) {
      global.logger.error(e);
    }
    return false;
  },
  _xml2json: (body) => {
    return JSON.parse(xml2json.toJson(body));
  },
  _accumlateDAValue: (info, accid) => {
    let finalDedicatedAccountValue = 0;
    let finalExpiryDate = new Date();
    try {
      for (let i = 0; i < info.length; i++) {
        let da = info[i];
        if ((da.offerID == accid) && ((da.expiryDateTime.getTime()) > (new Date().getTime()))) {
          let tempDA = da.dedicatedAccountInformation[0].dedicatedAccountValue1;
          finalDedicatedAccountValue = (finalDedicatedAccountValue * 1) + (tempDA * 1);

          if (da.expiryDateTime.getTime() > finalExpiryDate.getTime()) {
            finalExpiryDate = new Date(module.exports._date2customformat(da.expiryDateTime))
          }
        }
      }
    } catch (error) {
      //ignore
    }

    return {
      "dedicatedAccountID": accid,
      "dedicatedAccountValue1": finalDedicatedAccountValue,
      "expiryDateTime": finalExpiryDate
    };
  },
  _calculateAccumulatorValue: (accumulatorInformation, accid) => {
    try {
      if (accumulatorInformation)
        for (let i = 0; i < accumulatorInformation.length; i++) {
          let ua = accumulatorInformation[i];
          if (ua.accumulatorID == accid) {
            return ua;
          }
        }
    } catch (e) {
      global.logger.error(e);
    }
    return {
      "accumulatorID": -1,
      "accumulatorStartDate": "9999-12-30T21:00:00.120Z",
      "accumulatorValue": 0
    };
  },
  _getNoOfDaysBetween: (dateString) => {
    if (dateString == null) return 0;
    if (typeof dateString == "string") dateString = new Date(dateString);
    let currentDate = new Date();
    let result = Math.ceil((Date.UTC(dateString.getFullYear(), dateString.getMonth(), dateString.getDate()) - Date.UTC(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate())) / DAY) + 1;
    return result;
  },
  /**
    * Returns the duration at the specified language within a String object.
    * @params {String} durNum // number of duration
    * @params {String} durationType // type of duration format like day, days, hour, hours
    * @params {String} lang // language to convert into specific lang like day - joure(in french), hours - ora(in malagasy)
  **/
  _formatDuration: (durNum, durationType, lang) => {
    //macros to generate duration( number of 'day, days, hour, hours') in the specific language.
    try {
      let result;

      if (lang == "mg") {
        if (durationType == "days" || durationType == "day") {
          result = durNum + " andro";
        } else {
          result = durNum + " ora";
        }
      } else {
        //converting the duration( number of 'day, days, hour, hours')in specific language.
        result = moment.duration(Number(durNum), durationType).locale(lang).humanize();
      }
      let res1 = result.split(" ");

      /**
        * split the result to check whether it contain like 'a,an,un,une' and convert it to numberic 1.
        * converted '1 day' -- 'un jour' (in french language)
        * converted '1 day' -- 'a day' (in english language)
        * converted '1 hour' -- 'an hour' (in english language)
        * converted '1 hour' -- 'une heure' (in french language)
        * for that split the result and put the condition
      **/
      if (res1[0] == "a" || res1[0] == "an" || res1[0] == "un" || res1[0] == "une") {
        result = 1 + " " + res1[1];
      }
      return result; //returning the result
    } catch (err) {
      global.logger.error("Error while processing _formatDuration macros == ", err);
    }
  }
};
