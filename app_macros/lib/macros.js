/**
 * Set of macros supported by the module.Macros are keywords that starts with "_" and implements  * string, Date and Math functions.
 */
const _macros = Object.freeze({
  "_rsaPublicKeyEncrypt": {
    "description": "Encrypts given text with the given public key",
    "args": {
      "publicKey": "Public key to encrypt the data",
      "plainText": "Text to be encrypted"
    }
  },
  "_rsaPrivateKeyDecrypt": {
    "description": "Encrypts given encrypted text with the given private key",
    "args": {
      "privateKey": "Private key to decrypt the data",
      "encryptedString": "Text to be decrypted"
    }
  },
  "_toLowerCase": {
    "description": "Converts uppercase string into lowercase",
    "args": {
      "input_string": "The input string"
    }
  },
  "_toUpperCase": {
    "description": "Converts lowercase string into uppercase",
    "args": {
      "input_string": "The input string"
    }
  },
  "_startDate": {
    "description": "Gives start date for the given number of days",
    "args": {
      "input_string": "Number of days"
    }
  },
  "_endDate": {
    "description": "Gives end date based on the number of input days",
    "args": {
      "input_string": "Number of days"
    }
  },
  "_substring": {
    "description": "Extracts the characters from a string between two specified indices and returns a new substring",
    "args": {
      "input_string": "Input string",
      "start_index": "The starting index of the substring.First character is at index 0.",
      "end_index": "The position (up to, but not including) where to end the extraction."
    }
  },
  "_md5HashString": {
    "description": "Converts string to md5 Hash String",
    "args": {
      "input_string": "The input string"
    }
  },
  "_getEncryptedCipher": {
    "description": "Converts string to Encrypted text using Public key",
    "args": {
      "input_string1": "The input string",
      "input_string2": "The input string"
    }
  },
  "_encryptedCipher": {
    "description": "Converts string to Encrypted text using Public key",
    "args": {
      "input_string1": "The input string",
      "input_string2": "The input string"
    }
  },
  "_trim": {
    "description": "Removes whitespaces/set of characters from both the sides of the string",
    "args": {
      "input_string": "The input string",
      "left_substring": "Set of characters to be deleted from the left",
      "right_substring": "Set of characters to be deleted from the right"
    }
  },
  "_trimLeft": {
    "description": "Removes whitespaces from the left side of the string",
    "args": {
      "input_string": "The input string",
      "left_substring": "Set of characters to be deleted from the left"
    }
  },
  "_trimRight": {
    "description": "Removes whitespaces from the right side of the string",
    "args": {
      "input_string": "The input string",
      "right_substring": "Set of characters to be deleted from the right"
    }
  },
  "_stringify": { // json.stringify
    "description": "Converts an input/object to string",
    "args": {
      "input": "The input to be converted into string"
    }
  },
  "_toNumber": {
    "description": "Converts an input/object to a number",
    "args": {
      "input": "The input string/object to be converted into number"
    }
  },
  "_length": {
    "description": "Returns the length of the given string",
    "args": {
      "input_string": "The input string"
    }
  },
  "_isEmpty": {
    "description": "Checks if the string is empty. Returns true if the length of the string is zero,otherwise false.",
    "args": {
      "input_string": "The input string"
    }
  },
  "_isAlpha": {
    "description": "Checks whether a string contains only alphabets or not",
    "args": {
      "input_string": "The input string"
    }
  },
  "_isNumeric": {
    "description": "Checks whether a string contains only number or not",
    "args": {
      "input_string": "The input string"
    }
  },
  "_isAlphanumeric": {
    "description": "Checks whether a string contains only alphabet or number",
    "args": {
      "input_string": "The input string"
    }
  },
  "_isDigit": {
    "description": "Checks whether a character is a digit",
    "args": {
      "input_string": "The input character"
    }
  },
  "_isLower": {
    "description": "Checks whether a passed string is in lowercase letters or not",
    "args": {
      "input_string": "The input string"
    }
  },
  "_isUpper": {
    "description": "Checks whether a passed string is in uppercase letters or not",
    "args": {
      "input_string": "The input string"
    }
  },
  "_startsWith": {
    "description": "Checks whether a string starts with a given substring",
    "args": {
      "input_string": "The input string",
      "start_substring": "The substring with which string should start"
    }
  },
  "_endsWith": {
    "description": "Checks whether a string ends with a given substring",
    "args": {
      "input_string": "The input string",
      "end_substring": "The substring with which string should end"
    }
  },
  "_now": {
    "description": "Returns the number of milliseconds since 1970/01/01",
    "functor": "now",
    "this": "Date"
  },
  "_uuid": {
    "description": "Returns the unique transactionId Alphanumeric"
  },
  "_getTxnId": {
    "description": "Returns the unique transactionId numeric"
  },
  "_getDate": {
    "description": "Returns the day of the month(from 0-31)",
    "args": {
      "dateString": "The timestamp from which date needs to be extracted", // optional
      "milliseconds": "Time in milliseconds for which date needs to be extracted" // optional
    }
  },
  "_getDay": {
    "description": "Returns the day of the week(from 0-6)",
    "args": {
      "dateString": "The timestamp from which date needs to be extracted", // optional
      "milliseconds": "Time in milliseconds for which date needs to be extracted" // optional
    }
  },
  "_getFullYear": {
    "description": "Returns the year,according to the local time or given timestamp",
    "args": {
      "dateString": "The timestamp from which date needs to be extracted", // optional
      "milliseconds": "Time in milliseconds for which date needs to be extracted" // optional
    }
  },
  "_getHours": {
    "description": "Returns the hour(from 0-23), according to the local time or given timestamp",
    "args": {
      "dateString": "The timestamp from which date needs to be extracted", // optional
      "milliseconds": "Time in milliseconds for which date needs to be extracted" // optional
    }
  },
  "_getMinutes": {
    "description": "Returns the minute(from 0-59), according to the local time or given timestamp",
    "args": {
      "dateString": "The timestamp from which date needs to be extracted", // optional
      "milliseconds": "Time in milliseconds for which date needs to be extracted" // optional
    }
  },
  "_getMonth": {
    "description": "Returns the month(from 0-11), according to the local time or given timestamp ",
    "args": {
      "dateString": "The timestamp from which date needs to be extracted", // optional
      "milliseconds": "Time in milliseconds for which date needs to be extracted" // optional
    }
  },
  "_getSeconds": {
    "description": "Returns the second(from 0-59), according to the local time or given timestamp",
    "args": {
      "dateString": "The timestamp from which date needs to be extracted", // optional
      "milliseconds": "Time in milliseconds for which date needs to be extracted" // optional
    }
  },
  "_getMilliseconds": {
    "description": "Returns the milliseconds(from 0-999), according to the local time or given timestamp",
    "args": {
      "dateString": "The timestamp from which date needs to be extracted", // optional
      "milliseconds": "Time in milliseconds for which date needs to be extracted" // optional
    }
  },
  "_getTime": {
    "description": "Returns the number of milliseconds since 1970/01/01",
    "args": {
      "dateString": "The timestamp from which date needs to be extracted", // optional
      "milliseconds": "Time in milliseconds for which date needs to be extracted" // optional
    }
  },
  "_add": {
    "description": "Adds the two given numbers",
    "args": {
      "operand1": "First operand to add",
      "operand2": "Second operand to add"
    }
  },
  "_substract": {
    "description": "Subtracts the two given numbers",
    "args": {
      "operand1": "First operand to subtract",
      "operand2": "Second operand to subtract"
    }
  },
  "_multiply": {
    "description": "Multiplies the two given numbers",
    "args": {
      "operand1": "First operand to multiply",
      "operand2": "Second operand to multiply"
    }
  },
  "_divide": {
    "description": "Divides the two given numbers",
    "args": {
      "operand1": "First operand to divide",
      "operand2": "Second operand to divide"
    }
  },
  "_isInRange": {
    "description": "Checks whether the given number is in the given range or not.",
    "args": {
      "Input": "Number to check the range for.",
      "min": "Minimum value in the range",
      "max": "Maximun value in the range"
    }
  },
  "_getTimestamp": {
    "description": "Returns the current timestamp"
  },
  "_msisdnValidator": {
    "description": "Validates whether an MSISDN is correct or not",
    "args": {
      "MSISDN": "String consisting of the MSISDN number"
    }
  },
  "_parseDedicatedAccount": {
    "description": "Parse the Dedicated account information Array",
    "args": {
      "dedicatedAccountInformation": "Array of dedicatedAccountInformation",
      "dedicatedAccountID": "dedicatedAccountID for filter"
    }
  },
  "_parseDedicatedOfferID": {
    "description": "Parse the Dedicated account information for offerID",
    "args": {
      "dedicatedAccountInformation": "Array of dedicatedAccountInformation",
      "dedicatedOfferID": "dedicatedOfferID for filter"
    }
  },
  "_parseOfferInformationList": {
    "description": "Parse the Offer information Array",
    "args": {
      "offerInformationList": "Array of offerInformation",
      "offerID": "offerID for filter"
    }
  },
  "_checkOfferIDExists": {
    "description": "Parse the OfferList and Validate OfferID exists in list",
    "args": {
      "offerInformationList": "Array of offerInformation",
      "offerID": "offerID for filter"
    }
  },
  "_contains": {
    "description": "To check wether the given given element exists in the given lists",
    "args": {
      "element": "element to check",
      "elementsList": "elements list"
    }
  },
  "_date2customformat": {
    "description": "Date custom formatter",
    "args": {
      "date": "Date string",
      "format": "Date format"
    }
  },
  "_regexValidator": {
    "description": "Regex Validator",
    "args": {
      "regex": "Regex string",
      "str": "String for validation against regex"
    }
  },
  "_xml2json": {
    "description": "XML to JSON convertor",
    "args": {
      "str": "xml string buffer"
    }
  },
  "_accumlateDAValue": {
    "description": "Looping the dedicatedAccountValue for offerID",
    "args": {
      "dedicatedAccountInformation": "Array of dedicatedAccountInformation",
      "OfferID": "OfferID for filter"
    }
  },
  "_getNoOfDaysBetween": {
    "description": "Returns the day of days from the current system time",
    "args": {
      "dateString": "The timestamp from which date needs to be extracted"
    }
  },
  "_and": {
    "description": "Returns the logical AND value of the provided arguments",
    "args": {
      "num1": "Argument 1 for logical AND",
      "num2": "Argument 2 for logical AND"
    }
  },
  "_calculateAccumulatorValue": {
    "description": "Returns accumulator value",
    "args": {
      "accumulatorInformation": "Provides the length of the accumulator information",
      "accid": "Accumulator Id "
    }
  },
  "_charAt": {
    "description": "Returns character at a particular position of a string",
    "args": {
      "str": "String from which character has to be returned",
      "pos": "Position at which the character will be returned"
    }
  },
  "_charCodeAt": {
    "description": "Returns the Unicode value of the character at the specified location",
    "args": {
      "str": "String from which unicode value of the character will be returned",
      "index": "Index at which the character's unicode value will be returned"
    }
  },
  "_checkUsageCounterValue": {
    "description": "",
    "args": {
      "offers": "",
      "offerId": "",
      "ucID": ""
    }
  },
  "_concat": {
    "description": "Returns a string that contains the concatenation of two or more strings.",
    "args": {
      "str": "Strings to be concatenated"
    }
  },
  "_date2DateString": {
    "description": "Returns a date as a string value.",
    "args": {
      "args": "Argument to be converted in the DateString"
    }
  },
  "_date2LocaleDateString": {
    "description": " Returns a date as a string value appropriate to the host environment's current locale.",
    "args": {
      "args": "Argument to be converted in LocaleDateString"
    }
  },
  "_date2LocaleString": {
    "description": "Returns a value as a string value appropriate to the host environment's current locale",
    "args": {
      "args": "Argument to be converted in LocaleString"
    }
  },
  "_date2LocaleTimeString": {
    "description": "Returns a time as a string value appropriate to the host environment's current locale",
    "args": {
      "args": "Argument to be converted in LocaleTimeString"
    }
  },
  "_date2TimeString": {
    "description": "Returns a time as a string value.",
    "args": {
      "args": "Argument to be converted into TimeString"
    }
  },
  "_generateJwtToken": {
    "description": "Returns a JWT token",
    "args": {
      "payload": "Payload for the jwt token",
      "secret": "Secret key for jwt token generation",
      "options": "Additional options to be passed in jwt sign function"
    }
  },
  "_getTimezoneOffset": {
    "description": "Gets the difference in minutes between the time on the local computer and Universal Coordinated Time (UTC)",
    "args": {
      "args": "Argument for which returns the difference is to be calculated"
    }
  },
  "_getUTCDate": {
    "description": "Gets the day-of-the-month, using Universal Coordinated Time (UTC)",
    "args": {
      "args": "Argument for which the Date String is returned"
    }
  },
  "_getUTCDay": {
    "description": "Gets the day of the week using Universal Coordinated Time (UTC)",
    "args": {
      "args": "Argument for which the day of the week is returned"
    }
  },
  "_getUTCFullYear": {
    "description": "Gets the year using Universal Coordinated Time (UTC)",
    "args": {
      "args": "Argument for which the year is returned"
    }
  },
  "_getUTCHours": {
    "description": "Gets the hours value in a Date object using Universal Coordinated Time (UTC)",
    "args": {
      "args": "Argument for which the hours value is returned in Date object"
    }
  },
  "_getUTCMilliseconds": {
    "description": "Gets the milliseconds of a Date object using Universal Coordinated Time (UTC)",
    "args": {
      "args": "Argument for which the milliseconds of a Date object is returned"
    }
  },
  "_getUTCMinutes": {
    "description": "Gets the minutes of a Date object using Universal Coordinated Time (UTC)",
    "args": {
      "args": "Argument for which minutes of a Date object is returned"
    }
  },
  "_getUTCMonth": {
    "description": "Gets the month of a Date object using Universal Coordinated Time (UTC)",
    "args": {
      "args": "Argument for which month of a Date object is returned"
    }
  },
  "_getUTCSeconds": {
    "description": "Gets the seconds of a Date object using Universal Coordinated Time (UTC)",
    "args": {
      "args": "Argument for which seconds of a Date object is returned"
    }
  },
  "_indexOf": {
    "description": "Returns the position of the first occurrence of a substring",
    "args": {

    }
  },
  "_isNumber": {
    "description": "",
    "args": {
      "searchString": "The substring to search for in the string",
      "position": "The index at which to begin searching the String object. If omitted, search starts at the beginning of the string"
    }
  },
  "_lastIndexOf": {
    "description": "Returns the last occurrence of a substring in the string",
    "args": {
      "searchString": "The substring to search for",
      "position": "The index at which to begin searching. If omitted, the search begins at the end of the string"
    }
  },
  "_match": {
    "description": "Matches a string with a regular expression, and returns an array containing the results of that search",
    "args": {
      "str": "String from which a expression is to be matched",
      "expr": "Expression to be matched in the string"
    }
  },
  "_or": {
    "description": "Returns logical OR of the given numbers",
    "args": {
      "num1": "Number 1 to be logically OR",
      "num2": "Number 2 to be logically OR"
    }
  },
  "_remaider": {
    "description": "Returns the remainder after dividing num1 by num2",
    "args": {
      "num1": "Number 1 for which remainder is to be returned",
      "num2": "Number 2 from which the num1 will be divided"
    }
  },
  "_replace": {
    "description": "Replaces text in a string, using a regular expression or search string",
    "args": {
      "str": "String in which replacement is to be done",
      "searchValue": "Search value in the string",
      "replaceValue": "Value to be replaced in the string"
    }
  },
  "_search": {
    "description": "Finds the first substring match in a regular expression search",
    "args": {
      "str": "String in which substring is to be searched",
      "searchValue": "Search Value for searching in the string"
    }
  },
  "_slice": {
    "description": "",
    "args": {

    }
  },
  "_subtract": {
    "description": "Returns a section of a string",
    "args": {
      "str": "String from which a subsection has to be return",
      "start": "The index to the beginning of the specified portion of stringObj",
      "end": "The index to the end of the specified portion of stringObj. The substring includes the characters up to, but not including, the character indicated by end"
    }
  },
  "_toLocaleLowerCase": {
    "description": "Converts all alphabetic characters to lowercase, taking into account the host environment's current locale",
    "args": {
      "str": "String which has to be converted to Lower Case"
    }
  },
  "_toLocaleUpperCase": {
    "description": "Returns a string where all alphabetic characters have been converted to uppercase, taking into account the host environment's current locale",
    "args": {
      "str": "String which has to be converted to Upper Case"
    }
  },
  "_formatDuration": {
    "description": "Returns a duration to specified lamguage",
    "args": {
      "durNum": "Number of duration",
      "durationType": "Type of duration like day., days, hour or hours convert to particular language",
      "lang": "Particular language in which string text need to be converted"
    }
  }
});

module.exports.macros = _macros;