/**
 *
 */
const utility = require("utility");
const allowedChar = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ.[]_";
const util = require("util");
const Notation = require("notation");

const vm = require("vm");
const definitions = require("./definitions");
const operators = require("./operators");
const macros_arr = Object.keys(definitions);
const sleep = require("wait-sync");

const RESERVED_KEYWORDS = ["$menu"];
const SymbolDolar = "$";

/**
 * @class ExpressionEngine exposes a set of methods for the following:
 *  1. @method set - Setting key, value pairs.
 *  2. @method eval - Evaluation of expression containing macros, params, string literal and
 *      numbers.e.g. :"_substring("HelloWorld",5,10)"
 *  3. @method evalCondition - Evaluation of expr,join Array.Here, expr is an array of 3 elements
 *      in which 1st & 3rd element are compared according to the operator specified in the 2nd
 *      element.And join specifies the boolean operator using which one or more expressions are
 *      evaluated.
 *
 */
class ExpressionEngine {

  constructor(opts) {
    let data = opts && opts.dataTable || {};
    data = {
      ...data,
      ...definitions
    }
    this.context = Notation.create(data);
    this.vmcontext = vm.createContext(this.context.value);
  }

  /**
   * @method set allows to set key, value pairs in current context's data table.These key value
   * pairs can be used in the evaluation of the expression.
   * @param {*} key
   * @param {*} value
   */
  set(key, value) {
    try {
      if (key == null) {
        return;
      }
      if (!key.startsWith(SymbolDolar)) { key = SymbolDolar + key; }
      if (key == null || RESERVED_KEYWORDS.includes(key)) {
        return;
      }
      this.context.set(key, value);
    } catch (e2) {
      //ignore
    }
  }

  setJson(params) {
    Object.keys(params).forEach(key => {
      this.set(key, params[key]);
    });
  }

  get(key) {
    if (!key.startsWith(SymbolDolar)) { key = SymbolDolar + key; }
    return this.context.get(key);
  }

  del(key) {
    if (!key.startsWith(SymbolDolar)) { key = SymbolDolar + key; }
    return this.context.remove(key);
  }

  getDataTable() {
    return this.context.value;
  }
  /**
   * Evaluates the expression Array of the form:
   * [
   *  { "expr": ["leap", "eq", "_substring("myleap",2,5)"] },
   *  { "join": "OR" },
   *  { "expr": ["$MSISDN", "eq", "+919934297977"] }
   * ];
   *
   * @param {*} array
   */
  evalCondition(array) {
    let result = [];
    for (let i = 0; i < array.length; i++) {
      if (array[i].hasOwnProperty("expr")) {
        result[i] = this.resolveCondition(array[i].expr);
        if (result[i] == undefined) {
          result[i] = false;
        }
        //Return err in json format if any err while parsing.
        if (result[i].hasOwnProperty("errorDesc")) {
          return result[i].errorDesc;
        }
      } else {
        result[i] = array[i].join;
      }
    }

    for (let j = 1; j < result.length - 1; j = j + 2) {
      result[j - 1] = utility.convert2Primitive(result[j - 1]);
      result[j + 1] = utility.convert2Primitive(result[j + 1]);
      if (result[j].toUpperCase() == "AND") {
        result[j + 1] = (result[j - 1] && result[j + 1]);
      } else if (result[j].toUpperCase() == "OR") {
        result[j + 1] = (result[j - 1] || result[j + 1]);
      } else {
        result[j + 1] = false;
      }
    }
    return result[result.length - 1];
  }

  evalScript(session, box, wsconsole, verbose) {
    let script = session.getContext().getOutputScripts();
    let sandbox = {}, context;
    let result = { responseCode: "S9000", headers: {}, vars: {}, immediateResponse: false };
    try {
      sandbox = Object.assign(sandbox, box, this.context.value);
      if (script.includes("util."))
        sandbox.util = util;

      if (script.includes("require"))
        sandbox.require = require;

      if (script.includes("sleep"))
        sandbox.sleep = sleep;

      if (script.includes("logger."))
        sandbox.logger = {
          trace: (...message) => {
            if (global.logger.isTraceEnabled()) {
              session.emit(wsconsole, verbose.TRACE, ...message);
            }
          },
          debug: (...message) => {
            if (global.logger.isDebugEnabled()) {
              session.emit(wsconsole, verbose.DEBUG, ...message);
            }
          },
          info: (...message) => {
            if (global.logger.isInfoEnabled()) {
              session.emit(wsconsole, verbose.INFO, ...message);
            }
          },
          warn: (...message) => {
            session.emit(wsconsole, verbose.WARN, ...message);
          },
          error: (...message) => {
            session.emit(wsconsole, verbose.ERROR, ...message);
          }
        };

      if (script.includes("console."))
        sandbox.console = {
          log: (message) => {
            if (global.logger.isTraceEnabled()) {
              session.emit(wsconsole, verbose.TRACE, message);
            }
          },
          error: (message) => {
            session.emit(wsconsole, verbose.ERROR, message);
          }
        };

      if (script.includes("setResponseCode"))
        sandbox.setResponseCode = function (code) {
          result.responseCode = code;
        };

      if (script.includes("setHeader"))
        sandbox.setHeader = function (key, value) {
          result.headers[key] = value;
        };

      if (script.includes("setVariable"))
        sandbox.setVariable = function (key, value) {
          result.vars[key] = value;
        };

      if (script.includes("setMultiAccessCodes"))
        sandbox.setMultiAccessCodes = function (value) {
          result.multiAccessCodes = value;
        };

      if (script.includes("sendImmediateResponse"))
        sandbox.sendImmediateResponse = function (code) {
          result.responseCode = code;
          result.immediateResponse = true;
        };
      context = vm.createContext(sandbox);
      new vm.Script(script + "\n nextModuleId=main();")
        .runInContext(context);
      result.nextModuleId = sandbox.nextModuleId;
    } catch (e) {
      global.logger.error("Exception in evalScript", e);
      return new Error(e);
    } finally {
      sandbox = null;
      context = null;
    }
    return result;
  }

  /**
   * Evaluates the inputted expression and returns its value.
   * @param {*} expression
   */
  eval(expr) {
    let expression;
    try {
      expression = utility.convert2Primitive(expr);
      if (typeof expr == 'string' && typeof expression == 'number' && expr.localeCompare(expression.toString()) != 0)
        expression = expr;
      if (typeof expression == "string") {
        if (expression.startsWith("0") || expression.trim().length == 0) {
          return expr;
        }
        expression = String(expression);
        if (expressionHasMacros(expression)) {
          this.context.set("expr", []);
          try {
            new vm.Script("finalExpr=" + expression)
              .runInContext(this.vmcontext);
          } catch (e) {
            this.context.set("finalExpr", expression);
            if (expression.includes("$") || expression.includes("_")) {
              let expr = expression.split(" ");
              for (let i = 0; i < expr.length; i++) {
                try {
                  if (expr[i] != "this") {
                    new vm.Script("expr[" + i + "]=" + expr[i])
                      .runInContext(this.vmcontext);
                  } else {
                    this.context.value['expr'][i] = expr[i];
                  }
                } catch (e1) {
                  this.context.value['expr'][i] = expr[i];
                }
              }
            }
            this.context.set("finalExpr", this.context.get("expr").join(" "));
          }
          expression = this.context.get("finalExpr");
        }
        if (expressionHasMacros(expression)) {
          expression = this.replaceAllPlaceHolders(expression);
        }
      }
      if (!expression && String(expression).length == 0) {
        expression = expr;
      }
    } catch (error) {
      global.logger.error("Exception in eval", error);
    }
    return expression;
  }

  /**
   * Takes an expr Array, resolves it, and returns a boolean result.
   * @param {*} condition e.g.- ["leap", "eq", "_substring("myleap",2,5)"]
   */
  resolveCondition(condition) {
    let result = [];
    for (let j = 0; j < condition.length; j++) {
      try {
        if ((j % 2) == 0) {
          if (condition[j] == "true") {
            result[j] = true;
          } else if (condition[j] == "false") {
            result[j] = false;
          } else {
            result[j] = this.eval(condition[j]);
          }
        } else {
          result[j] = condition[j];
        }
      } catch (error) {
        global.logger.error("Exception in resolveCondition", error);
        result[j] = condition[j];
      }
    }
    let res = resolveOperators(result);
    return res;
  }

  /**
  * Replace All Place Holders in a string
  *
  * @param  {String} message
  * @param  {Object} json
  * @return {String}
  */
  replaceAllPlaceHolders(message) {
    try {
      let type = typeof utility.convert2Primitive(message);
      switch (type) {
        case "string": {
          if (message.length == 0) return message;
          if (message.startsWith("_parse") || message.startsWith("_check")) {
            return message;
          }
          let endIndex;
          let startIndex = message.indexOf("$");
          let flag = (startIndex > -1);
          for (let i = startIndex; i < message.length; i++) {
            if (flag && i != startIndex && !allowedChar.includes(message[i])) {
              endIndex = i;
              break;
            }
          }
          if (endIndex == null) {
            endIndex = message.length;
          }
          let tempKey = message.substring(startIndex, endIndex);
          let newvalue = this.resolveParam(tempKey);
          if (newvalue == null) {
            if (tempKey.startsWith("$") && tempKey.endsWith(".")) {
              tempKey = message.substring(startIndex, endIndex - 1);
              newvalue = this.resolveParam(tempKey);
              if (newvalue == null) {
                return message;
              } else {
                endIndex = endIndex - 1;
              }
            } else {
              return message;
            }
          }

          if (startIndex == 0 && endIndex == message.length) {
            message = newvalue;
          } else {
            message = message.substring(0, startIndex) + newvalue + message.substring(endIndex, message.length);
          }

          if (typeof message == "string" && message.indexOf("$") > -1) {
            message = this.replaceAllPlaceHolders(message);
          }
        }
          break;
        case "object": {
          let keys = Object.keys(message);
          for (let i = 0; i < keys.length; i++) {
            let key = keys[i];
            message[key] = this.replaceAllPlaceHolders(message[key]);
          }
        }
          break;
        default:
          return message;
      }

    } catch (error) {
      //global.logger.error("Exception in replaceAllPlaceHolders", error);
    }
    return message;
  }


  /**
   * Clears the dataTable & destroys the parser's object.
   */
  destroy() {
    this.context = null;
    this.vmcontext = null;
  }
  /**
   * Looks up in the data table and resolves the given param
   * @param {*} key
   */
  resolveParam(key) {
    let value = null;
    try {
      if (key == null) return null;
      value = this.context.get(key);
    } catch (error) {
      // ignore
    }
    return value;
  }
}

module.exports.definitions = definitions;
module.exports.ExpressionEngine = ExpressionEngine;

function resolveOperators(inputArray) {
  inputArray[0] = utility.convert2Primitive(inputArray[0]);
  inputArray[2] = utility.convert2Primitive(inputArray[2]);

  switch (inputArray[1]) {
    case operators.eq.op:
      return inputArray[0] == inputArray[2];
    case operators.ne.op:
      return inputArray[0] != inputArray[2];
    case operators.gte.op:
      return inputArray[0] >= inputArray[2];
    case operators.gt.op:
      return inputArray[0] > inputArray[2];
    case operators.lte.op:
      return inputArray[0] <= inputArray[2];
    case operators.lt.op:
      return inputArray[0] < inputArray[2];
    case operators.contains.op:
      return (inputArray[0].includes(inputArray[2])) || (inputArray[2].includes(inputArray[0]));
    case operators.ncontains.op:
      return !(inputArray[0].includes(inputArray[2])) || (inputArray[2].includes(inputArray[0]));
    case operators.and.op:
      return inputArray[0] && inputArray[2];
    case operators.or.op:
      return inputArray[0] || inputArray[2];
    default:
      break;
  }
  return false;
}

function expressionHasMacros(expr) {
  let flag = false;
  try {
    if (expr.includes("$")) return true;
    if (expr.includes("_"))
      for (let i = 0; i < macros_arr.length; i++) {
        if (expr.includes(macros_arr[i])) {
          flag = true;
          break;
        }
      }
  } catch (error) {
    //ignore
  }
  return flag;
}


function isBuffer(obj) {
  return obj != null && obj.constructor != null &&
    typeof obj.constructor.isBuffer === "function" && obj.constructor.isBuffer(obj)
}

function unflatten(target, opts) {
  opts = opts || {};

  let delimiter = opts.delimiter || ".";
  let overwrite = opts.overwrite || false;
  let result = {};

  let isbuffer = isBuffer(target);
  if (isbuffer || Object.prototype.toString.call(target) !== "[object Object]") {
    return target;
  }

  // safely ensure that the key is
  // an integer.
  function getkey(key) {
    let parsedKey = Number(key);

    return (
      isNaN(parsedKey) ||
      key.indexOf(".") !== -1 ||
      opts.object
    ) ? key
      : parsedKey;
  }

  let sortedKeys = Object.keys(target).sort(function (keyA, keyB) {
    return keyA.length - keyB.length;
  });

  sortedKeys.forEach(function (key) {
    let split = key.split(delimiter);
    let key1 = getkey(split.shift());
    let key2 = getkey(split[0]);
    let recipient = result;

    while (key2 !== undefined) {
      let type = Object.prototype.toString.call(recipient[key1])
      let isobject = (
        type === "[object Object]" ||
        type === "[object Array]"
      );

      // do not write over falsey, non-undefined values if overwrite is false
      if (!overwrite && !isobject && typeof recipient[key1] !== "undefined") {
        return;
      }

      if ((overwrite && !isobject) || (!overwrite && recipient[key1] == null)) {
        recipient[key1] = (
          typeof key2 === "number" &&
            !opts.object ? [] : {}
        );
      }

      recipient = recipient[key1];
      if (split.length > 0) {
        key1 = getkey(split.shift());
        key2 = getkey(split[0]);
      }
    }

    recipient[key1] = unflatten(target[key], opts);
  });

  return result;
}
