"use strict";
/**
 * Entry point file for macro functors.
 *
 * Errors and corresponding error codes thrown by this module.
 * 701  -   <PERSON><PERSON><PERSON> while setting key value pair in data table.
 * 702  -   <PERSON>rror in parsing the expression.
 * 703  -   Error in expression evaluation.
 *
 * Error codes can accessed using error.errorCode property of the error object.
 * Whereas error description message can b e accessed using error.errorDesc property of
 * the error object.
 *
 */

const Macros = require("./lib/macros");
const ExecutionContext = require("./lib/ExecutionContext");
module.exports = {
    ...ExecutionContext,
    ...Macros
};
