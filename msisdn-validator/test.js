const msisdnValidator = require('./lib/msisdn_validator');
msisdnValidator.init("CG");


let msisdn = "069242343";
let validationStatus = msisdnValidator.validate(msisdn);
console.log("Entered msisdn %s is %s ", msisdn, validationStatus);

msisdn = "242069242343";
validationStatus = msisdnValidator.validate(msisdn);
console.log("Entered msisdn %s is %s ", msisdn, validationStatus);

msisdn = "069242343";
validationStatus = msisdnValidator.validate(msisdn);
console.log("Entered msisdn %s is %s ", msisdn, validationStatus);

msisdn = "+242069242343";
validationStatus = msisdnValidator.validate(msisdn);
console.log("Entered msisdn %s is %s ", msisdn, validationStatus);

msisdn = "24269242343";
validationStatus = msisdnValidator.validate(msisdn);
console.log("Entered msisdn %s is %s ", msisdn, validationStatus);


msisdn = "123";
validationStatus = msisdnValidator.validate(msisdn);
console.log("Entered msisdn %s is %s ", msisdn, validationStatus);
