var colors = require('colors');

const _msisdnValidator = require("./lib/msisdn_validator");
const msisdn_validator = (max) => {
  let count = 0
  for (let i = 0; i < max; i++) {
    let num = 919876543210 + i;
    let status = _msisdnValidator.validate(String(num));
    if (status) count += 1;
  }
  return Promise.resolve(count)
}

const class1 = {
  msisdn_validator
}

/* _*_*_*_*_*_ Print _*_*_*_*_*/
console.log(colors.green('\n*_*_*_*_*_*_*_*_* Preformance Injection *_*_*_*_*_*_*_*_*'))
const allNameSpace = Object.getOwnPropertyNames(class1)
console.log(colors.green('Property:', allNameSpace))

allNameSpace.forEach((nameSpace) => {
  console.log(colors.green('      ', `typeof class1[${nameSpace}]: `, typeof class1[nameSpace]))
})

/* _*_*_*_*_*_ Preformance Injection _*_*_*_*_*/
allNameSpace.forEach((nameSpace) => {
  var temp = class1[nameSpace]
  class1[nameSpace] = function () {

    /*_*_*_*_ Init _*_*_*/
    var old_time = new Date();
    var m0 = process.memoryUsage()
    var c0 = process.cpuUsage()

    var returnResult = temp.apply(this, arguments);

    /*_*_*_*_ Finished _*_*_*/
    var new_time = new Date();
    var m1 = process.memoryUsage()
    var diffCPU = process.cpuUsage(c0)

    console.log(colors.cyan(`           *_*_*_ function ${nameSpace} _*_*_*`))
    console.log(colors.cyan('           RAM         : ', (m1['rss'] - m0['rss']) / 1048576, 'mb'))
    console.log(colors.cyan('           HeapTotal   : ', (m1['heapTotal'] - m0['heapTotal']) / 1048576, 'mb'))
    console.log(colors.cyan('           HeapUsed    : ', (m1['heapUsed'] - m0['heapUsed']) / 1048576, 'mb'))
    console.log(colors.cyan('           External    : ', (m1['external'] - m0['external']) / 1048576, 'mb'))
    console.log(colors.cyan('           CPU         : ', (diffCPU.user + diffCPU.system) / 1000000, 's'))
    console.log(colors.cyan('           Spend time  : ', (new_time - old_time), 'ms'))
    return returnResult
  }
})

/* _*_*_*_*_*_ Operation _*_*_*_*_*/
class1.msisdn_validator(500000).then(
  (res2) => { console.log('      class1.msisdn_validator(5000):    ', res2) }
)
