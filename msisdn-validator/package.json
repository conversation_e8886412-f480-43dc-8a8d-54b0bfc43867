{"name": "msisdn-validator", "version": "2.0.4", "description": "Validates any inputted msisdn.", "main": "./lib/msisdn_validator.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["msisdn", "msisdn-validator"], "publishConfig": {"registry": "http://cots.comviva.com/api/npm/Comviva-Npm-Repo/"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"copy-to": "^2.0.1", "log4js": "6.3.0", "utility": "file:../utility"}, "devDependencies": {"colors": "^1.4.0", "csvtojson": "^2.0.10"}}