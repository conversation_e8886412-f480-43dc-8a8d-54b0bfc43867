const ccMap = require("../res/countryCodeMap");
const countryData = require("../res/countryData.json");
const log4js = require("log4js");
const defaultLogger = log4js.getLogger("msisdn_validator");
const logger = global.logger || defaultLogger;
const cluster = require("cluster");

var previousID = 0;
var countryCode = global.config && global.config.country || "IN";

module.exports = {
    init: (country) => {
        if (country != null) {
            countryCode = country;
        }
    },
    validate: (msisdn) => {

        if (msisdn == null) {
            return false;
        }
        let tid, flag = false;
        msisdn = String(msisdn).replace(/[\s]+/g, '');
        tid = getUniqueTxnId();
        if (logger.isTraceEnabled()) {
            logger.isTraceEnabled("TID:" + tid + ", MacroReq: _msisdnValidator(" + msisdn + ")");
        }
        let cCode;
        if (!isNaN(Number(msisdn))) {
            if (msisdn.slice(0, 1) === "+") {
                msisdn = msisdn.slice(1, msisdn.length);
            }
            msisdn = String(msisdn);
            if (msisdn.length == 3) return true;
            for (let i = 4; i > 0; i--) {
                let status = checkMap(msisdn, i);
                if (status === true) {
                    cCode = msisdn.substring(0, i);
                    //True - If msisdn is valid, false if invalid.
                    flag = true;
                    break;
                }
            }
            //after searching complete map,no match found -case where country code is not mentioned
            if (!cCode) {
                if (countryData.hasOwnProperty(countryCode)) {
                    cCode = countryData[countryCode].PhoneCode;
                    msisdn = cCode + msisdn;
                }
            }
            if (cCode == null) {
                cCode = "";
            }
            if (ccMap.hasOwnProperty(cCode)) {
                flag = ccMap[cCode].NSN.includes(msisdn.length - cCode.length);
            }

        }
        if (logger.isTraceEnabled()) {
            logger.isTraceEnabled("TID:" + tid + ", MacroRes: _msisdnValidator(" + msisdn + ") = " + flag);
        }
        return flag;
    },

    removeCC: (msisdn) => {

        if (msisdn == null) {
            return null;
        }
        let flag = msisdn;
        msisdn = String(msisdn).replace(/[\s]+/g, '');
        if (!isNaN(Number(msisdn))) {
            if (msisdn.slice(0, 1) === "+") {
                msisdn = msisdn.slice(1, msisdn.length);
            }
            msisdn = String(Number(msisdn));
            for (let i = 4; i > 0; i--) {
                if (checkMap(msisdn, i)) {
                    flag = msisdn.substring(i);
                    break;
                }
            }

        } else {
            return null;
        }
        return flag;
    }
};

function getUniqueTxnId() {
    let currentID = new Date().getTime();
    if (currentID <= previousID) {
        currentID = previousID + 1;
    }
    previousID = currentID;
    if (cluster.isWorker) {
        currentID = cluster.worker.id + "" + currentID;
    } else {
        currentID = "1" + currentID;
    }
    return Number(currentID);
}

/**
 * This method takes the given MSISDN string,slices the country code and checks for it and the corresponding NSN in the country code map.
 * @param {*} msisdn -The input string
 * @param {*} index -end index of the string upto which extraction should be done.
 */
function checkMap(msisdn, index) {
    let cc_actual = msisdn.slice(0, index);
    let nsn_actual = msisdn.length - index;
    if (ccMap.hasOwnProperty(cc_actual)) {
        return ccMap[cc_actual].NSN.includes(nsn_actual);
    }
}
