/**
 * Dialing prefix to country code mapping.
 */
const dp_to_cc = {
    "00": [93, 355, 213, 376, 244, 54, 374, 297, 247, 672, 43, 994, 973, 880, 32, 501, 229, 975, 591, 387, 267, 55, 673, 359, 226, 95, 257, 237, 238, 236, 235, 86, 269, 243, 242, 682, 506, 225, 385, 357, 420, 45, 246, 253, 593, 20, 503, 240, 291, 372, 251, 500, 298, 679, 358, 33, 262, 594, 689, 241, 220, 49, 233, 350, 30, 299, 590, 502, 224, 245, 509, 39, 504, 36, 354, 91, 870, 98, 964, 353, 972, 39, 962, 254, 686, 850, 965, 996, 856, 371, 961, 266, 231, 218, 423, 370, 352, 853, 389, 261, 265, 60, 960, 223, 356, 596, 222, 230, 52, 373, 377, 382, 212, 258, 264, 674, 977, 31, 599, 687, 64, 505, 227, 683, 47, 968, 92, 507, 675, 595, 51, 63, 48, 351, 974, 40, 250, 290, 508, 685, 378, 239, 966, 221, 381, 248, 232, 421, 386, 677, 252, 27, 34, 94, 249, 597, 268, 46, 41, 963, 255, 670, 228, 690, 676, 290, 216, 90, 1649, 688, 256, 380, 971, 44, 598, 678, 58, 84, 681, 967, 260, 263],
    "011": [1684, 1264, 1268, 1868, 1340, 1242, 1246, 1441, 1284, 1, 1345, 1767, 1809, 1829, 1473, 1671, 1876, 692, 691, 1664, 1670, 680, 1869, 1758, 1784],
    "0011": [61],
    "001": [592, 852, 62, 82, 976, 66, 855, 65],
    "002": [82, 886],
    "012": [972],
    "10": [81],
    "013": [972],
    "014": [972],
    "810": [375, 7, 992, 993, 998],
    "007": [855, 57],
    "008": [62, 65],
    "1YZ0": [56],
    "99X": [358],
    "009": [57, 234],
    "005": [57],
    "119": [53],
    "8": [995]
};
module.exports = dp_to_cc;
