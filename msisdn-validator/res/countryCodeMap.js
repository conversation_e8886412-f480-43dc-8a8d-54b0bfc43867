/**
 * Country code map
 * NSN -the number of digits after the country code excluding any trunk code or access code.
 * @URL:https://en.wikipedia.org/wiki/List_of_mobile_phone_number_series_by_country
 */
const cc = {
    "1": { NSN: [10, 7], MobilePrefix: [] },
    "7": { NSN: [10, 11], MobilePrefix: [] },
    "91": { NSN: [10], MobilePrefix: [] },
    "20": { NSN: [10, 11, 12], MobilePrefix: [] },
    "27": { NSN: [9], MobilePrefix: [] },
    "32": { NSN: [9], MobilePrefix: [] },
    "31": { NSN: [9], MobilePrefix: [] },
    "30": { NSN: [10], MobilePrefix: [] },
    "33": { NSN: [9], MobilePrefix: [] },
    "34": { NSN: [9], MobilePrefix: [] },
    "36": { NSN: [9], MobilePrefix: [] },
    "39": { NSN: [9, 10, 12, 13], MobilePrefix: [] },
    "40": { NSN: [9], MobilePrefix: [] },
    "41": { NSN: [9], MobilePrefix: [] },
    "43": { NSN: [9, 10, 11], MobilePrefix: [] },
    "44": { NSN: [10], MobilePrefix: [] },
    "45": { NSN: [8], MobilePrefix: [] },
    "46": { NSN: [7, 9], MobilePrefix: [] },
    "47": { NSN: [8], MobilePrefix: [] },
    "48": { NSN: [9], MobilePrefix: [] },
    "49": { NSN: [10, 11], MobilePrefix: [] },
    "51": { NSN: [9], MobilePrefix: [] },
    "52": { NSN: [10, 12], MobilePrefix: [] },
    "53": { NSN: [8], MobilePrefix: [] },
    "54": { NSN: [12], MobilePrefix: [] },
    "55": { NSN: [11], MobilePrefix: [] },
    "56": { NSN: [8, 9], MobilePrefix: [] },
    "57": { NSN: [10], MobilePrefix: [] },
    "58": { NSN: [7, 10], MobilePrefix: [] },
    "60": { NSN: [9], MobilePrefix: [] },
    "61": { NSN: [9], MobilePrefix: [] },
    "62": { NSN: [9, 10, 11], MobilePrefix: [] },
    "63": { NSN: [10], MobilePrefix: [] },
    "64": { NSN: [8, 9, 10], MobilePrefix: [] },
    "65": { NSN: [8], MobilePrefix: [] },
    "66": { NSN: [9], MobilePrefix: [] },
    "81": { NSN: [10], MobilePrefix: [] },
    "82": { NSN: [10], MobilePrefix: [] },
    "84": { NSN: [9, 10], MobilePrefix: [] },
    "86": { NSN: [11], MobilePrefix: [] },
    "90": { NSN: [7, 11], MobilePrefix: [] },
    "92": { NSN: [10], MobilePrefix: [] },
    "93": { NSN: [9], MobilePrefix: [] },
    "94": { NSN: [7, 9], MobilePrefix: [] },
    "95": { NSN: [8, 9, 10], MobilePrefix: [] },
    "98": { NSN: [10], MobilePrefix: [] },
    "211": { NSN: [9], MobilePrefix: [] },
    "212": { NSN: [9], MobilePrefix: [] },
    "213": { NSN: [9], MobilePrefix: [] },
    "216": { NSN: [8], MobilePrefix: [] },
    "218": { NSN: [9, 10], MobilePrefix: [] },
    "220": { NSN: [7], MobilePrefix: [] },
    "221": { NSN: [9], MobilePrefix: [] },
    "222": { NSN: [8], MobilePrefix: [] },
    "223": { NSN: [8], MobilePrefix: [] },
    "224": { NSN: [9], MobilePrefix: [] },
    "225": { NSN: [7], MobilePrefix: [] },
    "226": { NSN: [8], MobilePrefix: [] },
    "227": { NSN: [8], MobilePrefix: [] },
    "228": { NSN: [8], MobilePrefix: [] },
    "229": { NSN: [8], MobilePrefix: [] },
    "230": { NSN: [8], MobilePrefix: [] },
    "231": { NSN: [7, 8, 9], MobilePrefix: [] },
    "232": { NSN: [8], MobilePrefix: [] },
    "233": { NSN: [9], MobilePrefix: [] },
    "234": { NSN: [8, 10], MobilePrefix: [] },
    "235": { NSN: [6, 8], MobilePrefix: [] },
    "236": { NSN: [8], MobilePrefix: [] },
    "237": { NSN: [9], MobilePrefix: [] },
    "238": { NSN: [7], MobilePrefix: [] },
    "239": { NSN: [7], MobilePrefix: [] },
    "240": { NSN: [9], MobilePrefix: [] },
    "241": { NSN: [7], MobilePrefix: [] },
    "242": { NSN: [9], MobilePrefix: [] },
    "243": { NSN: [9], MobilePrefix: [] },
    "244": { NSN: [9], MobilePrefix: [] },
    "245": { NSN: [9], MobilePrefix: [] },
    "246": { NSN: [7], MobilePrefix: [] },
    "247": { NSN: [], MobilePrefix: [] },
    "248": { NSN: [7], MobilePrefix: [] },
    "249": { NSN: [9], MobilePrefix: [] },
    "250": { NSN: [9], MobilePrefix: [] },
    "251": { NSN: [9], MobilePrefix: [] },
    "252": { NSN: [7, 8], MobilePrefix: [] },
    "253": { NSN: [8], MobilePrefix: [] },
    "254": { NSN: [9], MobilePrefix: [] },
    "255": { NSN: [9], MobilePrefix: [] },
    "256": { NSN: [9], MobilePrefix: [] },
    "257": { NSN: [8], MobilePrefix: [] },
    "258": { NSN: [9], MobilePrefix: [] },
    "259": { NSN: [], MobilePrefix: [] },
    "260": { NSN: [9], MobilePrefix: [] },
    "261": { NSN: [9], MobilePrefix: [] },
    "262": { NSN: [9, 12], MobilePrefix: [] },
    "263": { NSN: [9], MobilePrefix: [] },
    "264": { NSN: [9, 10], MobilePrefix: [] },
    "265": { NSN: [9], MobilePrefix: [] },
    "266": { NSN: [8], MobilePrefix: [] },
    "267": { NSN: [8], MobilePrefix: [] },
    "268": { NSN: [8], MobilePrefix: [] },
    "269": { NSN: [7], MobilePrefix: [] },
    "290": { NSN: [4], MobilePrefix: [] },
    "291": { NSN: [7], MobilePrefix: [] },
    "297": { NSN: [7], MobilePrefix: [] },
    "298": { NSN: [5, 6], MobilePrefix: [] },
    "299": { NSN: [6], MobilePrefix: [] },
    "350": { NSN: [8], MobilePrefix: [] },
    "351": { NSN: [9], MobilePrefix: [] },
    "352": { NSN: [9], MobilePrefix: [] },
    "353": { NSN: [9], MobilePrefix: [] },
    "354": { NSN: [7], MobilePrefix: [] },
    "355": { NSN: [7, 9], MobilePrefix: [] },
    "356": { NSN: [8], MobilePrefix: [] },
    "357": { NSN: [6, 8], MobilePrefix: [] },
    "358": { NSN: [10, 9], MobilePrefix: [] },
    "359": { NSN: [8, 9], MobilePrefix: [] },
    "370": { NSN: [8, 9], MobilePrefix: [] },
    "371": { NSN: [8], MobilePrefix: [] },
    "372": { NSN: [8], MobilePrefix: [] },
    "373": { NSN: [8], MobilePrefix: [] },
    "374": { NSN: [8], MobilePrefix: [] },
    "375": { NSN: [9, 11], MobilePrefix: [] },
    "376": { NSN: [6], MobilePrefix: [] },
    "377": { NSN: [9], MobilePrefix: [] },
    "378": { NSN: [8], MobilePrefix: [] },
    "380": { NSN: [9], MobilePrefix: [] },
    "381": { NSN: [9], MobilePrefix: [] },
    "382": { NSN: [8], MobilePrefix: [] },
    "385": { NSN: [9], MobilePrefix: [] },
    "386": { NSN: [8], MobilePrefix: [] },
    "387": { NSN: [8], MobilePrefix: [] },
    "389": { NSN: [8], MobilePrefix: [] },
    "420": { NSN: [9], MobilePrefix: [] },
    "421": { NSN: [9], MobilePrefix: [] },
    "423": { NSN: [9], MobilePrefix: [] },
    "500": { NSN: [5], MobilePrefix: [] },
    "501": { NSN: [6, 7], MobilePrefix: [] },
    "502": { NSN: [8], MobilePrefix: [] },
    "503": { NSN: [8], MobilePrefix: [] },
    "504": { NSN: [8], MobilePrefix: [] },
    "505": { NSN: [8], MobilePrefix: [] },
    "506": { NSN: [8], MobilePrefix: [] },
    "507": { NSN: [8], MobilePrefix: [] },
    "508": { NSN: [6], MobilePrefix: [] },
    "509": { NSN: [8], MobilePrefix: [] },
    "590": { NSN: [9, 12], MobilePrefix: [] },
    "591": { NSN: [8], MobilePrefix: [] },
    "592": { NSN: [7], MobilePrefix: [] },
    "593": { NSN: [9], MobilePrefix: [] },
    "594": { NSN: [9, 12], MobilePrefix: [] },
    "595": { NSN: [9], MobilePrefix: [] },
    "596": { NSN: [9, 12], MobilePrefix: [] },
    "597": { NSN: [7], MobilePrefix: [] },
    "598": { NSN: [8], MobilePrefix: [] },
    "599": { NSN: [7, 8], MobilePrefix: [] },
    "670": { NSN: [8], MobilePrefix: [] },
    "672": { NSN: [6], MobilePrefix: [] },
    "673": { NSN: [7], MobilePrefix: [] },
    "674": { NSN: [7], MobilePrefix: [] },
    "675": { NSN: [7], MobilePrefix: [] },
    "676": { NSN: [7], MobilePrefix: [] },
    "677": { NSN: [7], MobilePrefix: [] },
    "678": { NSN: [7], MobilePrefix: [] },
    "679": { NSN: [7], MobilePrefix: [] },
    "680": { NSN: [7], MobilePrefix: [] },
    "681": { NSN: [6], MobilePrefix: [] },
    "682": { NSN: [5], MobilePrefix: [] },
    "683": { NSN: [4], MobilePrefix: [] },
    "685": { NSN: [6], MobilePrefix: [] },
    "686": { NSN: [5, 8], MobilePrefix: [] },
    "687": { NSN: [6, 8], MobilePrefix: [] },
    "688": { NSN: [6], MobilePrefix: [] },
    "689": { NSN: [8], MobilePrefix: [] },
    "690": { NSN: [4], MobilePrefix: [] },
    "691": { NSN: [7], MobilePrefix: [] },
    "692": { NSN: [7], MobilePrefix: [] },
    "850": { NSN: [10], MobilePrefix: [] },
    "852": { NSN: [8], MobilePrefix: [] },
    "853": { NSN: [8], MobilePrefix: [] },
    "855": { NSN: [9, 8], MobilePrefix: [] },
    "856": { NSN: [10], MobilePrefix: [] },
    "880": { NSN: [10], MobilePrefix: [] },
    "886": { NSN: [9], MobilePrefix: [] },
    "960": { NSN: [7], MobilePrefix: [] },
    "961": { NSN: [7, 8], MobilePrefix: [] },
    "962": { NSN: [9], MobilePrefix: [] },
    "963": { NSN: [9], MobilePrefix: [] },
    "964": { NSN: [10], MobilePrefix: [] },
    "965": { NSN: [8], MobilePrefix: [] },
    "966": { NSN: [9], MobilePrefix: [] },
    "967": { NSN: [9], MobilePrefix: [] },
    "968": { NSN: [8], MobilePrefix: [] },
    "970": { NSN: [9], MobilePrefix: [] },
    "971": { NSN: [9], MobilePrefix: [] },
    "972": { NSN: [9], MobilePrefix: [] },
    "973": { NSN: [8], MobilePrefix: [] },
    "974": { NSN: [8], MobilePrefix: [] },
    "975": { NSN: [8], MobilePrefix: [] },
    "976": { NSN: [8], MobilePrefix: [] },
    "977": { NSN: [10], MobilePrefix: [] },
    "992": { NSN: [10], MobilePrefix: [] },
    "993": { NSN: [9], MobilePrefix: [] },
    "994": { NSN: [9], MobilePrefix: [] },
    "995": { NSN: [9], MobilePrefix: [] },
    "996": { NSN: [9], MobilePrefix: [] },
    "998": { NSN: [10], MobilePrefix: [] },
    "1264": { NSN: [10], MobilePrefix: [] },
    "1242": { NSN: [10], MobilePrefix: [] },
    "1473": { NSN: [10], MobilePrefix: [] },
    "1671": { NSN: [10], MobilePrefix: [] },
    "1670": { NSN: [10], MobilePrefix: [] },
    "1340": { NSN: [10], MobilePrefix: [] },
    "1284": { NSN: [10], MobilePrefix: [] },
    "1767": { NSN: [10], MobilePrefix: [] },
    "1876": { NSN: [10], MobilePrefix: [] },
    "1721": { NSN: [10], MobilePrefix: [] },
    "1268": { NSN: [10], MobilePrefix: [] },
    "1246": { NSN: [10], MobilePrefix: [] },
    "1345": { NSN: [10], MobilePrefix: [] },
    "1664": { NSN: [10], MobilePrefix: [] },
    "1784": { NSN: [10], MobilePrefix: [] },
    "1684": { NSN: [10], MobilePrefix: [] },
    "1441": { NSN: [10], MobilePrefix: [] },
    "1869": { NSN: [10], MobilePrefix: [] },
    "1649": { NSN: [10], MobilePrefix: [] },
    "1758": { NSN: [10], MobilePrefix: [] },
    "1868": { NSN: [10], MobilePrefix: [] }
};
module.exports = cc;
