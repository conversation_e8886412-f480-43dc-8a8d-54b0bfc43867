const csv = require('csvtojson')

const path = require('path');
const fs = require('fs');
const csvFilePath = path.resolve(__dirname, 'countrycode.csv');
console.log(csvFilePath);
csv()
  .fromFile(csvFilePath)
  .then((jsonObj) => {
    let schema = {};
    jsonObj.forEach(item => {
      let key = item.ISO2;
      delete item.ISO2;
      item.LanguageCodes = item.LanguageCodes.split(",");
      item.Languages = item.Languages.split(",");
      schema[key] = item;
    });
    fs.writeFileSync("countryData.json", JSON.stringify(schema, undefined, 2));
  });
