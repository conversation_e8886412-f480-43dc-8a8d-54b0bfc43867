const influx = require("influx");
const os = require("os");
var client;
module.exports = {
  init: () => {
    client = getClient();
  },

  writeDataPoints: async (measurement, json) => {
    try {
      let cli = getClient();
      if (cli != null) {
        await cli.writePoints([{
          measurement: measurement,
          tags: { host: os.hostname() },
          fields: json
        }]);
      }
    } catch (e) {
      global.logger.error("resetting client", e);
      client = null;
    }
  }
};

function getClient() {
  if (client == null) {
    client = new influx.InfluxDB({
      host: global.config.influxDB.host,
      port: global.config.influxDB.port,
      database: global.config.influxDB.database,
      username: global.config.influxDB.username,
      password: global.config.influxDB.password
    });
  }
  return client;
}
