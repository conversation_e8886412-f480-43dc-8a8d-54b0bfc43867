/**
 * provides KPI"s for LEAP App executor updates the all kpi info into redis cache
 * every second
 */
const path = require("path");
const fs = require("fs");
const events = require("events");

const mkdirp = require("mkdirp");
const common = require("common");
const os = require("os");
const influxCli = require("./influxdbcli");
const flatten = require("flat");

const SUCCESS = "success";
const FAIL = "fail";

const redis_man = common.redis_man;
const REDIS_KEY = "KPI_QUEUE";

const KPI_KEYS = Object.freeze({
  interface_req: "inf_req",
  interface_res: "inf_res",
  listsStore: "lists",
  appstore: "db",
  sessionstore: "sst",
  appCache: "ast",
  cdrQ: "log2queue",
  ussddrQ: "drqueue",
  module: "mod_hits",
  tps: "app_hits",
  plugin_stats: "plugin_stats",
  app_stats: "app_stats"
});

const mFriction = 1048576;

function getHeapStats() {
  try {
    let m1 = process.memoryUsage();
    return {
      nid: global.nodeid,
      cid: global.clusterId,
      pid: process.pid,
      host: os.hostname(),
      component: global.componentName,
      mode: global.mode,
      rss: m1.rss / mFriction,
      heapTotal: m1.heapTotal / mFriction,
      heapUsed: m1.heapUsed / mFriction,
      external: m1.external / mFriction
    };
  } catch (error) {
    global.logger.error(error);
  }
  return null;
}

let props = {
  isApigwKpiEnabled: false,
  isAppengineKpiEnabled: false,
  thresholdHeapMemory: 500,
  isMapped: false,
  kpiPath: "",
  interval: 60000,
  isRedis: false,
  isFile: false
};

const app_stats = {};

const plugin_stats = {};

const counters = {
  globalTPS: 0,
  app_tps_stats: {},
  app_module_codes: { success: 0, fail: 0 },
  categories: 0,
  interfaces: {},
  apps: {}
};

var leapKPI;

class KeyPerformaceIndicator extends events.EventEmitter {

  constructor() {
    super();
    counters.globalTPS = 0;
    counters.apps = {};
    counters.app_tps_stats = {};
    counters.app_module_codes = { success: 0, fail: 0 }
    counters.categories = {};
    leapKPI = {};
    props.isMapped = false;
    props.isApigwKpiEnabled = false;
    props.isAppengineKpiEnabled = false;

    this.on(KPI_KEYS.interface_req, incrementInrterfaceReq);
    this.on(KPI_KEYS.interface_res, incrementInrterfaceRes);
    this.on(KPI_KEYS.appstore, incrementDatabaseKpiCount);
    this.on(KPI_KEYS.sessionstore, incrementSessionStoreKpiCount);
    this.on(KPI_KEYS.appCache, incrementAppCacheKpiCount);
    this.on(KPI_KEYS.cdrQ, incrementLogQueueKpiCount);
    this.on(KPI_KEYS.ussddrQ, incrementDRQueueKpiCount);
    this.on(KPI_KEYS.module, incrementCategoryKpiCount);
    this.on(KPI_KEYS.tps, incrementAppWiseKpiCount);
    this.on(KPI_KEYS.plugin_stats, incrementPluginWiseKpiCount);
  }

  async init(opts) {
    try {
      if (opts && opts.enabled) {
        props.isApigwKpiEnabled = (opts.enabled == true && opts.api_gw_kpi_enabled == true);
        props.isAppengineKpiEnabled = (opts.enabled == true && opts.app_engine_kpi_enabled == true);
        props.interval = opts.options && opts.options.frequency && opts.options.frequency * 1000 || 60000;
        console.log("KPI INTERVAL:", props.interval, "ms");
        if (opts.policy == "redis") {
          props.isRedis = true;
          redis_man.init({
            key: REDIS_KEY,
            config: opts.options.redis,
            oid: "kpi_queue_conn"
          });
        } else if (opts.policy == "file") {
          props.isFile = true;
          props.kpiPath = opts.options.file || path.resolve("/logs/leap/kpi/");
        } else if (opts.policy == "influx") {
          props.isInflux = true;
          influxCli.init();
        } else {
          console.error("Unknown KPI policy. ");
        }
      } else {
        props.isApigwKpiEnabled = false;
        props.isAppengineKpiEnabled = false;
        console.error("Skipping feature 'kpi', as it is not enabled.");
        return false;
      }
      if (opts.thresholdHeapMemory != null) {
        props.thresholdHeapMemory = Number(opts.thresholdHeapMemory);
      }
      if (props.isApigwKpiEnabled == true) {
        await apigwKPI();
      }
      if (props.isAppengineKpiEnabled == true) {
        await appengineKPI();
      }
      return true;
    } catch (error) {
      global.logger.error("Failed to init KPI", error);
    }
    return false;
  }
}

module.exports = new KeyPerformaceIndicator();
module.exports.KEYS = KPI_KEYS;

/*
 * function to update kpi counters into redis after an interval of 1 sec and
 * reset the counters back to 0 after updating in redis
 */
async function apigwKPI() {
  try {
    let iat = (new Date().getTime() / 1000).toFixed();
    let stats = getHeapStats();
    if (stats != null) {
      stats.iat = iat;
      if (stats.heapUsed > props.thresholdHeapMemory) {
        global.gc();
      }
    }
    let COMPONENT_NAME;
    if (global.nodeid != null && global.nodeid.length > 0) {
      COMPONENT_NAME = global.nodeid;
    }
    if (COMPONENT_NAME) {
      if (Object.keys(leapKPI).length > 0) {
        await writeDataPoint("apigw", leapKPI, COMPONENT_NAME + ".kpi");
      }
      await writeDataPoint("heapstats", stats, COMPONENT_NAME + ".stats");
    }
  } catch (error) {
    console.error(error);
  }
  setTimeout(apigwKPI, props.interval);
}

async function appengineKPI() {

  try {
    let iat = (new Date().getTime() / 1000).toFixed();
    let stats = getHeapStats();
    if (stats != null) {
      stats.iat = iat;
      if (stats.heapUsed > props.thresholdHeapMemory) {
        global.gc();
      }
    }

    let data = {
      nid: global.nodeid,
      iat: iat,
      inf: counters.interfaces,
      app_tps_stats: counters.app_tps_stats,
      apps: counters.apps,
      cat: counters.categories,
      tps: counters.globalTPS,
      codes: counters.app_module_codes,
      pecounter: global.pecounter || 0
    };
    let cname;
    if (global.nodeid != null && global.nodeid.length > 0) {
      cname = global.nodeid;
    }
    if (cname != null) {
      await writeDataPoint("tail", data, cname + ".tps");
      await writeDataPoint("heapstats", stats, cname + ".stats");
      await writeDataPoint("plugin_stats", plugin_stats, cname + ".pstats");
      await writeDataPoint("app_stats", app_stats, cname + ".appstats");
    }
  } catch (error) {
    global.logger.error(error);
  }
  setTimeout(appengineKPI, 1000);
}

function incrementInrterfaceReq(type) {
  if (!counters.interfaces.hasOwnProperty(KPI_KEYS.interface_req)) {
    counters.interfaces[KPI_KEYS.interface_req] = { [type]: 0 }
  }
  if (!counters.interfaces[KPI_KEYS.interface_req].hasOwnProperty(type)) {
    counters.interfaces[KPI_KEYS.interface_req][type] = 0
  }
  counters.interfaces[KPI_KEYS.interface_req][type]++;
}

function incrementInrterfaceRes(type) {
  if (!counters.interfaces.hasOwnProperty(KPI_KEYS.interface_res)) {
    counters.interfaces[KPI_KEYS.interface_res] = { [type]: 0 }
  }
  if (!counters.interfaces[KPI_KEYS.interface_res].hasOwnProperty(type)) {
    counters.interfaces[KPI_KEYS.interface_res][type] = 0
  }
  counters.interfaces[KPI_KEYS.interface_res][type]++;
}


function incrementDatabaseKpiCount(start, end, type) {
  incrementType(KPI_KEYS.appstore, start, end, type);
}

function incrementSessionStoreKpiCount(start, end, type) {
  incrementType(KPI_KEYS.sessionstore, start, end, type);
}

function incrementAppCacheKpiCount(start, end, type) {
  incrementType(KPI_KEYS.appCache, start, end, type);
}

function incrementLogQueueKpiCount(start, end, type) {
  incrementType("log_queue", start, end, type);
}

function incrementDRQueueKpiCount(start, end, type) {
  incrementType("dr_queue", start, end, type);
}

function incrementType(kpi, start, end, type) {
  try {
    if (props.isApigwKpiEnabled === true) {
      let respTime = end - start;
      if (!leapKPI.hasOwnProperty(kpi)) {
        leapKPI[kpi] = {};
      }
      if (!leapKPI[kpi].hasOwnProperty(type)) {
        leapKPI[kpi][type] = {
          hits: 0,
          respTime: 0
        };
      }
      leapKPI[kpi][type].hits += 1;
      leapKPI[kpi][type].respTime += respTime;
    }
  } catch (error) {
    global.logger.error(error);
  }
}

async function writeDataPoint(measurement, data, filename) {
  if (data == null) return;
  if (props.isRedis) {
    let connection = await redis_man.getConnection(REDIS_KEY);
    connection.lpush(filename, JSON.stringify(data));
  }
  else if (props.isFile) {
    let filepath = path.join(props.kpiPath, filename);
    if (props.isMapped == false) {
      mkdirp(props.kpiPath).then(result => {
        props.isMapped = true;
      });
    }
    fs.writeFileSync(filepath, JSON.stringify(data));
  } else if (props.isInflux) {
    influxCli.writeDataPoints(measurement, flatten(data));
  }
}


function incrementPluginWiseKpiCount(data) {
  let key = data.aid + ";" + data.mid + ";" + data.mname + ";" + data.code;
  if (!plugin_stats.hasOwnProperty(key)) {
    plugin_stats[key] = 0;
  }
  plugin_stats[key]++;
}


function incrementAppWiseKpiCount(data) {
  let key = data.aid + ";" + data.code;
  if (!app_stats.hasOwnProperty(key)) {
    app_stats[key] = 0;
  }
  app_stats[key]++;

  if (!counters.apps.hasOwnProperty(data.aid)) {
    counters.apps[data.aid] = { hits: 0, codes: {} };
    counters.apps[data.aid].codes[data.code] = 0;
  }
  counters.apps[data.aid].hits++;
  counters.apps[data.aid].codes[data.code]++;
}

function incrementCategoryKpiCount(data) {
  try {
    if (typeof data == "object") {
      if (data.mname != "appStart")
        counters.globalTPS++;
      let mid = Number(String(data.mid).split(".")[0]).toFixed();

      if (isNaN(mid)) {
        mid = -1;
      }
      if (!counters.app_tps_stats.hasOwnProperty(data.aid)) {
        counters.app_tps_stats[data.aid] = 0;
      }
      counters.app_tps_stats[data.aid]++;
      if (global.categories.hasOwnProperty(mid)) {
        if (!counters.categories.hasOwnProperty(global.categories[mid].type)) {
          counters.categories[global.categories[mid].type] = 0;
        }
        counters.categories[global.categories[mid].type]++;
        if (data.mcode == 0) {
          counters.app_module_codes[SUCCESS]++;
        } else {
          counters.app_module_codes[FAIL]++;
        }
      }
    }
  } catch (error) {
    //ignore
  } finally {
    data = null;
  }
}
