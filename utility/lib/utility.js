"use strict";

const copy = require("copy-to");

copy(require("./function"))
  .and(require("./polyfill"))
  .and(require("./optimize"))
  .and(require("./crypto"))
  .and(require("./number"))
  .and(require("./string"))
  .and(require("./array"))
  .and(require("./json"))
  .and(require("./date"))
  .and(require("./object"))
  .and(require("./type"))
  .and(require("./web"))
  .and(require("./cross_origin"))
  .and(require("./print"))
  .and(require("./transaction"))
  .and(require("./checksum"))
  .and(require("./hash"))
  .and(require("./file"))
  .to(module.exports);
