"use strict";

const fs = require("fs");
const path = require("path");
const logger = global.logger;

exports.rmdir = (dirPath) => {
  deleteFolderRecursive(dirPath);
};

function deleteFolderRecursive(cp) {
  let p = path.resolve(cp);
  try {
    if (fs.existsSync(p)) {
      // Read all files and directories in the current directory
      fs.readdirSync(p).forEach(function (file) {
        let curPath = path.join(p, file); // Use path.join for better cross-platform compatibility
        if (fs.lstatSync(curPath).isDirectory()) { // recurse
          deleteFolderRecursive(curPath);
        } else { // delete file
          fs.unlinkSync(curPath);
        }
      });

      // Now delete the directory itself
      try {
        fs.rmdirSync(p);
      } catch (e) {
        logger.error(`Failed to remove directory: ${p}, Error: ${e.message}`);
      }
    } else {
      logger.error("Folder does not exist: " + p);
    }
  } catch (e) {
    logger.error(`Error processing folder: ${p}, Error: ${e.message}`);
  }
}
