"use strict";

const JWT = require("jwt-simple");
const ipRangeCheck = require("ip-range-check");

/**
 * Extract Authorization header (JWT) from request.
 * @param {JSON Object} req, HTTP Rest API request.
 * @return {String}
 */
exports.getJWT = (req) => {
  return req.get("Authorization").slice(7);
};

/**
 * Decodes the JWT into a readable JSON
 * @param {JWT} jwt, JWT(JSON Web Token).
 * @param {String} secret, Auth secret.
 * @return {JSON Object}
 */
exports.decodeJWT = (jwt, secret) => {
  return JWT.decode(jwt, secret);
};

/**
 * Middleware which sets the Cross-Origin Resource Sharing headers to the HTTP response object.
 * @param {JSON Object} res, HTTP Response object.
 * @return {JSON Object}
 * Ref: https://www.owasp.org/index.php/OWASP_Secure_Headers_Project#tab=Headers
 */

exports.set_cors_headers = (req, res, next) => {
  let security = global.config && global.config.security;
  if (security && security.enabled && security.cors != null) {
    let whitelist_origins = Object.values(security.cors)
    if (whitelist_origins.indexOf(req.headers.origin) == -1) {
      return res.status(401).json({ code: 401, msg: "Access Denied: Unknown Origin" });
    }
  }
  res.header("Access-Control-Allow-Origin", req.headers.origin);
  res.header("Access-Control-Allow-Methods", "GET,POST");
  res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, txnId, sessionid, debugId");
  res.header("X-FRAME-OPTIONS", "SAMEORIGIN");
  res.header("X-XSS-Protection", "1;mode=block");
  res.header("X-Content-Type-Options", "nosniff");
  res.header("Content-Security-Policy", "script-src 'self'");
  res.header("X-Permitted-Cross-Domain-Policies", "none");
  res.header("Referrer-Policy", "no-referrer");
  res.header("Strict-Transport-Security", "max-age=31536000 ; includeSubDomains");
  next();
};

exports.isIPWhiteListed = (req, res, next) => {
  let security = global.config && global.config.security;
  if (security && security.enabled && security.allowedIPs != null) {
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Check Requested IP:%s for whitelist", req.ip);
    }
    let whitelist_ips = Object.values(security.allowedIPs);
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("whitelisted IPS:", whitelist_ips);
    }
    let isInRange = ipRangeCheck(req.ip, whitelist_ips);
    if (isInRange == false) {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("This IP Address %s is not in range:", req.ip, isInRange);
      }
      return res.status(403).json({ code: 403, msg: "Access Denied: Your IP address is not whitelisted, please contact your Admintrator!!!" });
    }
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("This IP Address %s is in range:", req.ip, isInRange);
    }
  }
  next();
};

exports.filterHttpMethods = (req, res, next) => {
  let security = global.config && global.config.security;
  if (security && security.enabled && security.allowedRestMethods && !security.allowedRestMethods.includes(req.method)) {
    return res.status(405).json({ code: 405, msg: "Method not allowed." });
  }
  next();
}
