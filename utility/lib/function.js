"use strict";

const assert = require("assert");

/**
 * A empty function.
 *
 * @return {Function}
 * @public
 */
exports.noop = () => { };

/**
 * Get a function parameter's names.
 *
 * @param {Function} func
 * @param {Boolean} [useCache], default is true
 * @return {Array} names
 */
exports.getParamNames = (func, cache) => {
  let type = typeof func;
  assert(type === "function", 'The "func" must be a function. Received type "' + type + '"');

  cache = cache !== false;
  if (cache && func.__cache_names) {
    return func.__cache_names;
  }
  let str = func.toString();
  let names = str.slice(str.indexOf("(") + 1, str.indexOf(")")).match(/([^\s,]+)/g) || [];
  func.__cache_names = names;
  return names;
};
