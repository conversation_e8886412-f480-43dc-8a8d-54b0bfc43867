"use strict";

const crypto = require('crypto');

exports.MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER || Math.pow(2, 53) - 1;
exports.MIN_SAFE_INTEGER = -exports.MAX_SAFE_INTEGER;
let MAX_SAFE_INTEGER_STR = exports.MAX_SAFE_INTEGER_STR = String(exports.MAX_SAFE_INTEGER);
let MAX_SAFE_INTEGER_STR_LENGTH = MAX_SAFE_INTEGER_STR.length;

/**
 * Detect a number string can safe convert to Javascript Number.
 *
 * @param {String} s number format string, like `"123"`, `"-1000123123123123123123"`
 * @return {Boolean}
 */
exports.isSafeNumberString = (s) => {
  if (s[0] === "-") {
    s = s.substring(1);
  }
  if (s.length < MAX_SAFE_INTEGER_STR_LENGTH ||
    (s.length === MAX_SAFE_INTEGER_STR_LENGTH && s <= MAX_SAFE_INTEGER_STR)) {
    return true;
  }
  return false;
};

/**
 * Convert string to Number if string in safe Number scope.
 *
 * @param {String} s number format string.
 * @return {Number|String} success will return Number, otherise return the original string.
 */
exports.toSafeNumber = (s) => {
  if (typeof s === "number") {
    return s;
  }

  return exports.isSafeNumberString(s) ? Number(s) : s;
};

/**
 * Produces a random integer between the inclusive `lower` and `upper` bounds.
 *
 * @param {Number} lower The lower bound.
 * @param {Number} upper The upper bound.
 * @return {Number} Returns the random number.
 */
exports.random = (lower, upper) => {
  if (lower === undefined && upper === undefined) {
    return 0;
  }
  if (upper === undefined) {
    upper = lower;
    lower = 0;
  }
  if (lower > upper) {
    [lower, upper] = [upper, lower];
  }

  // Generate a secure random number between 0 and upper - lower
  const range = upper - lower;
  const randomBytes = crypto.randomBytes(4).readUInt32LE(0); // Get a 32-bit random number
  const randomInRange = Math.floor((randomBytes / 0xffffffff) * range);

  return lower + randomInRange;
};

exports.bytes2Units = (bytes) => {
  if (bytes >= 1073741824) {
    bytes = (bytes / 1073741824).toFixed(2) + " GB";
  } else if (bytes >= 1048576) {
    bytes = (bytes / 1048576).toFixed(2) + " MB";
  } else if (bytes >= 1024) {
    bytes = (bytes / 1024).toFixed(2) + " KB";
  } else if (bytes > 1) {
    bytes = bytes + " bytes";
  } else if (bytes === 1) {
    bytes = bytes + " byte";
  } else {
    bytes = "0 byte";
  }
  return bytes;
};
