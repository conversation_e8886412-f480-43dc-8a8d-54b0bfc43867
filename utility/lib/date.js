"use strict";
const MONTHS = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

// only set once.
let tzo = -new Date().getTimezoneOffset();
let dif = tzo >= 0 ? '+' : '-';
function pad(num) {
  let norm = Math.floor(Math.abs(num));
  return (norm < 10 ? '0' : '') + norm;
}

let TIMEZONE = " " + dif + pad(tzo / 60) + pad(tzo % 60);

/**
 * Access log format date. format: `moment().format("DD/MMM/YYYY:HH:mm:ss ZZ")`
 *
 * @return {String}
 */
exports.accessLogDate = (d) => {
  // 16/Apr/2013:16:40:09 +0800
  d = d || new Date();
  let date = d.getDate();
  if (date < 10) {
    date = "0" + date;
  }
  let hours = d.getHours();
  if (hours < 10) {
    hours = "0" + hours;
  }
  let mintues = d.getMinutes();
  if (mintues < 10) {
    mintues = "0" + mintues;
  }
  let seconds = d.getSeconds();
  if (seconds < 10) {
    seconds = "0" + seconds;
  }
  return date + "/" + MONTHS[d.getMonth()] + "/" + d.getFullYear() +
    ":" + hours + ":" + mintues + ":" + seconds + TIMEZONE;
};

/**
 * Normal log format date. format: `moment().format("YYYY-MM-DD HH:mm:ss.SSS")`
 *
 * @return {String}
 */
exports.logDate = exports.YYYYMMDDHHmmssSSS = (d, msSep) => {
  if (typeof d === "string") {
    // logDate(msSep)
    msSep = d;
    d = new Date();
  } else {
    // logDate(d, msSep)
    d = d || new Date();
  }
  let date = d.getDate();
  if (date < 10) {
    date = "0" + date;
  }
  let month = d.getMonth() + 1;
  if (month < 10) {
    month = "0" + month;
  }
  let hours = d.getHours();
  if (hours < 10) {
    hours = "0" + hours;
  }
  let mintues = d.getMinutes();
  if (mintues < 10) {
    mintues = "0" + mintues;
  }
  let seconds = d.getSeconds();
  if (seconds < 10) {
    seconds = "0" + seconds;
  }
  let milliseconds = d.getMilliseconds();
  if (milliseconds < 10) {
    milliseconds = "00" + milliseconds;
  } else if (milliseconds < 100) {
    milliseconds = "0" + milliseconds;
  }
  return d.getFullYear() + "-" + month + "-" + date + " " +
    hours + ":" + mintues + ":" + seconds + (msSep || ".") + milliseconds;
};

/**
 * `moment().format("YYYY-MM-DD HH:mm:ss")` format date string.
 *
 * @return {String}
 */
exports.YYYYMMDDHHmmss = (d, options) => {
  d = d || new Date();
  if (!(d instanceof Date)) {
    d = new Date(d);
  }

  let dateSep = "-";
  let timeSep = ":";
  if (options) {
    if (options.dateSep) {
      dateSep = options.dateSep;
    }
    if (options.timeSep) {
      timeSep = options.timeSep;
    }
  }
  let date = d.getDate();
  if (date < 10) {
    date = "0" + date;
  }
  let month = d.getMonth() + 1;
  if (month < 10) {
    month = "0" + month;
  }
  let hours = d.getHours();
  if (hours < 10) {
    hours = "0" + hours;
  }
  let mintues = d.getMinutes();
  if (mintues < 10) {
    mintues = "0" + mintues;
  }
  let seconds = d.getSeconds();
  if (seconds < 10) {
    seconds = "0" + seconds;
  }
  return d.getFullYear() + dateSep + month + dateSep + date + " " +
    hours + timeSep + mintues + timeSep + seconds;
};

/**
 * `moment().format("YYYY-MM-DD")` format date string.
 *
 * @return {String}
 */
exports.YYYYMMDD = (d, sep) => {
  if (typeof d === "string") {
    // YYYYMMDD(sep)
    sep = d;
    d = new Date();
  } else {
    // YYYYMMDD(d, sep)
    d = d || new Date();
    if (typeof sep !== "string") {
      sep = "-";
    }
  }
  let date = d.getDate();
  if (date < 10) {
    date = "0" + date;
  }
  let month = d.getMonth() + 1;
  if (month < 10) {
    month = "0" + month;
  }
  return d.getFullYear() + sep + month + sep + date;
};

/**
 * return datetime struct.
 *
 * @return {Object} date
 *  - {Number} YYYYMMDD, 20130401
 *  - {Number} H, 0, 1, 9, 12, 23
 */
exports.datestruct = (now) => {
  now = now || new Date();
  return {
    YYYYMMDD: now.getFullYear() * 10000 + (now.getMonth() + 1) * 100 + now.getDate(),
    H: now.getHours()
  };
};

/**
 * Get Unix's timestamp in seconds.
 * @return {Number}
 */
exports.timestamp = (t) => {
  if (t) {
    let v = t;
    if (typeof v === "string") {
      v = Number(v);
    }
    if (String(t).length === 10) {
      v *= 1000;
    }
    return new Date(v);
  }
  return Math.round(new Date().getTime() / 1000);
};

/**
 *
 * Get the last char to decide if it's one of:
 * m - minutes
 * h - hours
 * d - days
 * w - weeks
 * M - months (note: capital M)
 */
exports.toSeconds = (timeval) => {
  let timeUnit = timeval.slice(-1);
  let val = Number(timeval.slice(0, -1));
  switch (timeUnit) {
    case "m": return val * 60;
    case "h": return val * 3600;
    case "d": return val * 3600 * 24;
    case "w": return val * 3600 * 24 * 7;
    case "M": return val * 3600 * 24 * 7 * 30; //approx
    default:
      if (isDigit(timeUnit)) { return timeval; }
      else { throw new Error("Invalid time unit."); }
  }
};

/**
 * Returns a unix timestamp, for a given future time in time units
 **/
exports.timeFuture = (duration) => {
  let distance = exports.toSeconds(duration);
  let now = (new Date().getTime() / 1000);
  return Math.round(now + distance);
};

function isDigit(ch) {
  return ("0123456789".indexOf(ch) !== -1);
}
