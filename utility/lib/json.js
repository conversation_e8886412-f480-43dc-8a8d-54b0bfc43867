"use strict";

const fs = require("mz/fs");
const path = require("path");
const mkdirp = require("mkdirp");
const zlib = require("zlib");

exports.strictJSONParse = function (str) {
  let obj = JSON.parse(str);
  if (!obj || typeof obj !== "object") {
    throw new Error("JSON string is not object");
  }
  return obj;
};

exports.readJSONSync = function (filepath) {
  if (!fs.existsSync(filepath)) {
    throw new Error(filepath + " is not found");
  }
  return JSON.parse(fs.readFileSync(filepath));
};

exports.writeJSONSync = function (filepath, str, options) {
  options = options || {};
  if (!("space" in options)) {
    options.space = 2;
  }

  mkdirp.sync(path.dirname(filepath));
  if (typeof str === "object") {
    str = JSON.stringify(str, options.replacer, options.space) + "\n";
  }

  fs.writeFileSync(filepath, str);
};

exports.readJSON = function (filepath) {
  return fs.exists(filepath)
    .then(function (exists) {
      if (!exists) {
        throw new Error(filepath + " is not found");
      }
      return fs.readFile(filepath);
    })
    .then(function (buf) {
      return JSON.parse(buf);
    });
};

exports.writeJSON = function (filepath, str, options) {
  options = options || {};
  if (!("space" in options)) {
    options.space = 2;
  }

  if (typeof str === "object") {
    str = JSON.stringify(str, options.replacer, options.space) + "\n";
  }

  return mkdir(path.dirname(filepath))
    .then(function () {
      return fs.writeFile(filepath, str);
    });
};

exports.deflateJSON = function (data, jsonsupport) {
  if (data == null)
    return Promise.resolve(data);

  if (typeof data == "object") {
    data = JSON.stringify(data);
  }
  global.logger.trace("deflateJSON jsonsupport::", jsonsupport);
  if (jsonsupport) {
    return Promise.resolve(JSON.parse(data));
  }
  return new Promise(resolve => {
    zlib.deflate(data, (err, buffer) => {
      if (!err) {
        resolve(buffer.toString("base64"));
      } else {
        resolve(data);
      }
    });
  });
}

exports.unzipJSON = function (data, jsonsupport) {
  if (data == null)
    return Promise.resolve(data);

  return new Promise(resolve => {

    global.logger.trace("unzipJSON jsonsupport::", jsonsupport);

    if (jsonsupport) {
      if (typeof data == "string") {
        return resolve(JSON.parse(data));
      } else {
        return resolve(data);
      }
    }
    zlib.unzip(Buffer.from(data, "base64"), (err, buffer) => {
      if (!err) {
        buffer = JSON.parse(buffer);
        resolve(buffer);
      } else {
        resolve(JSON.parse(data));
      }
    });
  });
}


function mkdir(dir) {
  return new Promise(function (resolve, reject) {
    mkdirp(dir, function (err) {
      if (err) {
        return reject(err);
      }
      resolve();
    });
  });
}
