"use strict";

/**
 * Array random slice with items count.
 * @param {Array} arr
 * @param {Number} num, number of sub items.
 * @return {Array}
 */
const crypto = require('crypto');

exports.randomSlice = (arr, num) => {
  if (!num || num >= arr.length) {
    return arr.slice();
  }

  // Generate a secure random index
  const randomIndex = () => {
    const randomBytes = crypto.randomBytes(4); // 4 bytes (32 bits)
    const randomValue = randomBytes.readUInt32BE(0) % arr.length; // Get a value within the bounds of the array length
    return randomValue;
  };

  let index = randomIndex();
  let a = [];
  for (let i = 0, j = index; i < num; i++) {
    a.push(arr[j++]);
    if (j === arr.length) {
      j = 0; // Wrap around
    }
  }
  return a;
};

/**
 * Remove one exists element from an array
 * @param {Array} arr
 * @param  {Number} index - remove element index
 * @return {Array} the array instance
 */
exports.spliceOne = (arr, index) => {
  if (index < 0) {
    index = arr.length + index;
    // still negative, not found element
    if (index < 0) {
      return arr;
    }
  }

  // don't touch
  if (index >= arr.length) {
    return arr;
  }

  for (let i = index, k = i + 1, n = arr.length; k < n; i += 1, k += 1) {
    arr[i] = arr[k];
  }
  arr.pop();
  return arr;
};

function getSortOrder(prop) {
  if (prop.includes(".")) {
    const arr = prop.split(".");
    const key = arr[0];
    const field = arr[1];
    return (a, b) => {
      try {
        if (Number(a[key][field]) > Number(b[key][field])) {
          return 1;
        } else if (Number(a[key][field]) < Number(b[key][field])) {
          return -1;
        }
      } catch (error) {
        // ignore
      }
      return 0;
    };
  } else {
    return (a, b) => {
      try {
        if (Number(a[prop]) > Number(b[prop])) {
          return 1;
        } else if (Number(a[prop]) < Number(b[prop])) {
          return -1;
        }
      } catch (error) {
        // ignore
      }
      return 0;
    };
  }
}

exports.sortArray = (array, sortField) => {
  return array.sort(getSortOrder(sortField));
};
