"use strict";
const crypto = require('crypto');

exports.randomString = (length, charSet) => {
  length = length || 16;
  charSet = charSet || "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  const result = [];

  // Generate secure random bytes and convert them to indices in charSet
  const randomBytes = crypto.randomBytes(length);
  for (let i = 0; i < length; i++) {
    const index = randomBytes[i] % charSet.length; // Modulo operation to keep within charSet bounds
    result.push(charSet[index]);
  }

  return result.join("");
};

/**
 * split string to array
 * @param  {String} str
 * @param  {String} [sep] default is ","
 * @return {Array}
 */
exports.split = (str, sep) => {
  str = str || "";
  sep = sep || ",";
  let items = str.split(sep);
  let needs = [];
  for (let i = 0; i < items.length; i++) {
    let s = items[i].trim();
    if (s.length > 0) {
      needs.push(s);
    }
  }
  return needs;
};
// always optimized
exports.splitAlwaysOptimized = (...args) => {
  let str = "";
  let sep = ",";
  if (args.length === 1) {
    str = args[0] || "";
  } else if (args.length === 2) {
    str = args[0] || "";
    sep = args[1] || ",";
  }
  let items = str.split(sep);
  let needs = [];
  for (let i = 0; i < items.length; i++) {
    let s = items[i].trim();
    if (s.length > 0) {
      needs.push(s);
    }
  }
  return needs;
};


/**
 * Replace string
 *
 * @param  {String} str
 * @param  {String|RegExp} substr
 * @param  {String|Function} newSubstr
 * @return {String}
 */
exports.replace = (str, substr, newSubstr) => {
  let replaceFunction = newSubstr;
  if (typeof replaceFunction !== "function") {
    replaceFunction = () => {
      return newSubstr;
    };
  }
  return str.replace(substr, replaceFunction);
};

function replaceAll(str, term, replacement) {
  term = term.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  return str.replace(new RegExp(term, "g"), replacement);
}

/**
 * Replace string
 *
 * @param  {String} str
 * @param  {String|RegExp} substr
 * @param  {String|Function} newSubstr
 * @return {String}
 */
exports.replaceAll = replaceAll;

// original source https://github.com/nodejs/node/blob/v7.5.0/lib/_http_common.js#L300
/**
 * True if val contains an invalid field-vchar
 *  field-value    = *( field-content / obs-fold )
 *  field-content  = field-vchar [ 1*( SP / HTAB ) field-vchar ]
 *  field-vchar    = VCHAR / obs-text
 *
 * checkInvalidHeaderChar() is currently designed to be inlinable by v8,
 * so take care when making changes to the implementation so that the source
 * code size does not exceed v8"s default max_inlined_source_size setting.
 **/
let validHdrChars = [
  0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, // 0 - 15
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // 16 - 31
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 32 - 47
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 48 - 63
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 64 - 79
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 80 - 95
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 96 - 111
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, // 112 - 127
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 128 ...
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  // ... 255
];

/**
 * Replace invalid http header characters with replacement
 *
 * @param  {String} val
 * @param  {String|Function} replacement - can be `function(char)`
 * @return {Object}
 */
exports.replaceInvalidHttpHeaderChar = (val, replacement) => {
  replacement = replacement || " ";
  let invalid = false;

  if (!val || typeof val !== "string") {
    return {
      val: val,
      invalid: invalid,
    };
  }

  let replacementType = typeof replacement;
  let chars;
  for (let i = 0; i < val.length; ++i) {
    if (!validHdrChars[val.charCodeAt(i)]) {
      // delay create chars
      chars = chars || val.split("");
      if (replacementType === "function") {
        chars[i] = replacement(chars[i]);
      } else {
        chars[i] = replacement;
      }
    }
  }

  if (chars) {
    val = chars.join("");
    invalid = true;
  }

  return {
    val: val,
    invalid: invalid,
  };
};

/**
 * Detect invalid http header characters in a string
 *
 * @param {String} val
 * @return {Boolean}
 */
exports.includesInvalidHttpHeaderChar = (val) => {
  if (!val || typeof val !== "string") {
    return false;
  }

  for (let i = 0; i < val.length; ++i) {
    if (!validHdrChars[val.charCodeAt(i)]) {
      return true;
    }
  }

  return false;
};


const charset7bit = { '@': 1, '£': 1, '$': 1, '¥': 1, 'è': 1, 'é': 1, 'ù': 1, 'ì': 1, 'ò': 1, 'Ç': 1, "\n": 1, 'Ø': 1, 'ø': 1, "\r": 1, 'Å': 1, 'å': 1, 'Δ': 1, '_': 1, 'Φ': 1, 'Γ': 1, 'Λ': 1, 'Ω': 1, 'Π': 1, 'Ψ': 1, 'Σ': 1, 'Θ': 1, 'Ξ': 1, 'Æ': 1, 'æ': 1, 'ß': 1, 'É': 1, ' ': 1, '!': 1, '"': 1, '#': 1, '¤': 1, '%': 1, '&': 1, "'": 1, '(': 1, ')': 1, '*': 1, '+': 1, ',': 1, '-': 1, '.': 1, '/': 1, '0': 1, '1': 1, '2': 1, '3': 1, '4': 1, '5': 1, '6': 1, '7': 1, '8': 1, '9': 1, ':': 1, ';': 1, '<': 1, '=': 1, '>': 1, '?': 1, '¡': 1, 'A': 1, 'B': 1, 'C': 1, 'D': 1, 'E': 1, 'F': 1, 'G': 1, 'H': 1, 'I': 1, 'J': 1, 'K': 1, 'L': 1, 'M': 1, 'N': 1, 'O': 1, 'P': 1, 'Q': 1, 'R': 1, 'S': 1, 'T': 1, 'U': 1, 'V': 1, 'W': 1, 'X': 1, 'Y': 1, 'Z': 1, 'Ä': 1, 'Ö': 1, 'Ñ': 1, 'Ü': 1, '§': 1, '¿': 1, 'a': 1, 'b': 1, 'c': 1, 'd': 1, 'e': 1, 'f': 1, 'g': 1, 'h': 1, 'i': 1, 'j': 1, 'k': 1, 'l': 1, 'm': 1, 'n': 1, 'o': 1, 'p': 1, 'q': 1, 'r': 1, 's': 1, 't': 1, 'u': 1, 'v': 1, 'w': 1, 'x': 1, 'y': 1, 'z': 1, 'ä': 1, 'ö': 1, 'ñ': 1, 'ü': 1, 'à': 1, "\f": 2, '^': 2, '{': 2, '}': 2, '\\': 2, '[': 2, '~': 2, ']': 2, '|': 2, '€': 2 };

exports.isUnicode = (content) => {
  const chars = content.split("");
  for (let i = 0, length = chars.length; i < length; i++) {
    let c = chars[i];
    if (!charset7bit[c])
      return true;
  }
  return false;
}

exports.getTotalLengthGSM = (content) => {
  const chars = content.split("");
  let char_length = 0;
  chars.forEach(function (c) {
    char_length += charset7bit[c];
  });
  return char_length;
}

exports.charset7bit = charset7bit;
