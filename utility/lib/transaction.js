"use strict";
const { v4: uuidv4 } = require('uuid');
const cluster = require("cluster");
const nodeId = Number(process.env.LEAP_NODE_ID) || "";
let previousID = 0;
let lastDuplicate = 0;

exports.getUniqueTxnId = () => {
  // Generate a UUID
  let uuid = uuidv4();

  // Take a substring to get a UUID between 16 and 32 characters
  let shortenedUUID = uuid.replace(/-/g, '').slice(0, 32); // Adjust length as needed

  return shortenedUUID;
};

exports.setStartTime = (req, res, next) => {
  try {
    req.startTime = new Date().getTime();
    res.header("startTime", req.startTime);
  } catch (error) {
    //ignore
  }
  next();
};

exports.setTxnId = (req, res, next) => {
  try {
    if (req.query.transactionid != null && !isNaN(req.query.transactionid)) {
      req.txnId = req.query.transactionid;
    } else {
      req.txnId = exports.getUniqueTxnId();
    }
    res.header("txnId", req.txnId);
  } catch (error) {
    //ignore
  }
  next();
};