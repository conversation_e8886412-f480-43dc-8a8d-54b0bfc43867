"use strict";

exports.isObject = (value) => {
  return (null !== value && typeof value === typeof {} && !exports.isArray(value));
};

exports.isNumber = (value) => {
  return !exports.isArray(value) && (value - parseFloat(value) + 1) >= 0;
};

exports.isArray = (value) => {
  return (value instanceof Array);
};

exports.isString = (value) => {
  return (typeof value === typeof "");
};

exports.isNull = (value) => {
  return (null === value);
};

exports.isBoolean = (value) => {
  return (value === true || value === false);
};

exports.toObject = (arr) => {
  let rv = {};
  for (let i = 0; i < arr.length; ++i) { rv[i] = arr[i]; }
  return rv;
};

exports.oneIsNull = (v1, v2) => {
  return ((v1 === null && v2 !== null) || (v1 !== null && v2 === null));
};

exports.isUndefined = (val) => {
  return (null === val || typeof val === "undefined");
};

exports.isFunction = (fn) => {
  return (typeof fn === "function");
};

exports.isEqual = (v1, v2) => {
  if (typeof v1 !== typeof v2 || exports.oneIsNull(v1, v2)) {
    return false;
  }

  if (typeof v1 === typeof "" || typeof v1 === typeof 0) {
    return v1 === v2;
  }

  let _isEqual = true;

  if (typeof v1 === typeof {}) {
    let compare = (value1, value2) => {
      for (let i in value1) {
        if (!value2.hasOwnProperty(i)) {
          _isEqual = false;
          break;
        }

        if (exports.isObject(value1[i])) {
          compare(value1[i], value2[i]);
        } else if (typeof value1[i] === typeof "") {
          if (value1[i] !== value2[i]) {
            _isEqual = false;
            break;
          }
        }
      }
    };

    compare(v1, v2);
  }

  return _isEqual;
};

exports.getType = (data) => {
  if (exports.isNull(data)) {
    return null;
  } else if (exports.isObject(data)) {
    return "object";
  } else if (exports.isArray(data)) {
    return "array";
  } else if (exports.isBoolean(data)) {
    return "boolean";
  } else if (exports.isString(data)) {
    return "string";
  } else if (exports.isNumber(data)) {
    return "number";
  }
};

exports.isPrimitive = (obj) => {
  let type = typeof obj;
  if (type === "undefined" || type === "number" || type === "boolean") {
    return true;
  }
  return false;
};

exports.convert2Primitive = (param) => {
  let type = typeof param;
  if (type === "undefined" || type === "number" || type === "boolean") {
    return param;
  }
  let temp;
  if (type === "string") {
    temp = param.trim();
    if (temp.startsWith("0") || temp.length === 0) { return String(param); }
    if (temp === "true" || temp === "false") {
      return Boolean(temp);
    }
  }
  temp = Number(param);
  if (!isNaN(temp)) {
    return temp;
  }
  return param;
};
