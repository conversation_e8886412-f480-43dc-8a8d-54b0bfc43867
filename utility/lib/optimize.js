"use strict";

/**
 * optimize try catch
 * @param {Function} fn
 * @return {Object}
 *   - {Error} error
 *   - {Mix} value
 */
exports.try = (fn) => {
  let res = {
    error: undefined,
    value: undefined
  };

  try {
    res.value = fn();
  } catch (err) {
    res.error = err instanceof Error
      ? err
      : new Error(err);
  }

  return res;
};


/**
 * @description Deal with typescript
 */
exports.UNSTABLE_METHOD = {
  try: exports.try,
};


/**
 * avoid if (a && a.b && a.b.c)
 * @param {Object} obj
 * @param {...String} keys
 * @return {Object}
 */
exports.dig = (obj, ...keys) => {
  if (!obj) {
    return;
  }
  console.log(obj);

  if (keys.length === 0) {
    return obj;
  }

  let value = obj[keys[0]];
  console.log(value);

  for (let i = 1; i < keys.length; i++) {
    if (!value) {
      break;
    }
    value = value[keys[i]];
  }

  return value;
};


/**
 * optimize arguments to array
 * @param {Arguments} args
 * @return {Array}
 */
exports.argumentsToArray = (args) => {
  let res = new Array(args.length);
  for (let i = 0; i < args.length; i++) {
    res[i] = args[i];
  }
  return res;
};
