"use strict";

/**
 * Module dependencies
 */
const crypto = require("crypto");
const fs = require("fs");

/**
 * Checksum
 * a digit representing the sum of the correct digits in a piece of stored or transmitted digital data, against which later comparisons can be made to detect errors in the data.
 */
exports.checksum = (filename, options) => {
  return new Promise((resolve, reject) => {

    if (!options) {
      options = {};
    }
    if (!options.algorithm) {
      options.algorithm = "sha1";
    }
    if (!options.encoding) {
      options.encoding = "hex";
    }

    fs.stat(filename, (err, stat) => {
      if (!err && !stat.isFile()) {
        err = new Error("Not a file");
      }
      if (err) {
        return reject(err);
      }

      let hash = crypto.createHash(options.algorithm);
      let fileStream = fs.createReadStream(filename);

      if (!hash.write) {
        fileStream.on("data", data => {
          hash.update(data);
        });
        fileStream.on("end", () => {
          resolve(hash.digest(options.encoding));
        });
      } else {

        hash.setEncoding(options.encoding);
        fileStream.pipe(hash, { end: false });

        fileStream.on("end", () => {
          hash.end();
          resolve(hash.read());
        });
      }
    });
  });
};
