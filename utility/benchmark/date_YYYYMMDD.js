/**
 * Copyright(c) node-modules and other contributors.
 * MIT Licensed
 *
 * Authors: <AUTHORS>
 */

"use strict";

/**
 * Module dependencies.
 */

// http://jsperf.com/access-log-date-format

var utils = require('../');
var moment = require('moment');
var Benchmark = require('benchmark');
var suite = new Benchmark.Suite();

console.log('parseInt(moment().format("YYYYMMDD"), 10): %j', parseInt(moment().format('YYYYMMDD'), 10));
console.log('utils.datestruct().YYYYMMDD: %j', utils.datestruct().YYYYMMDD);
console.log('new Date().toString(): %j', new Date().toString());
console.log('------------------------');

suite
  .add("parseInt(moment().format('YYYYMMDD'), 10)", function () {
    const dateValue = parseInt(moment().format('YYYYMMDD'), 10);
    return dateValue;
  })
  .add('utils.datestruct().YYYYMMDD', function () {
    const dateValue = utils.datestruct().YYYYMMDD;
    return dateValue;
  })
  .add('new Date().toString()', function () {
    const dateString = new Date().toString();
    return dateString;
  })
  // add listeners
  .on('cycle', function (event) {
    console.log(String(event.target));
  })
  .on('complete', function () {
    console.log('Fastest is ' + this.filter('fastest').pluck('name'));
  })
  .run({ async: false });
