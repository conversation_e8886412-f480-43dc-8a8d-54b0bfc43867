{"name": "utility", "version": "2.0.13", "description": "Comviva's Generic Utility - A collection of useful utilities.", "main": "lib/utility.js", "files": ["lib", "index.d.ts"], "scripts": {"test": "npm run lint && npm run test-local", "test-ts": "npm run test-local-ts", "test-local": "ava test/**/*.test.js", "test-local-ts": "ava-ts test_ts/**/*.test.ts", "test-cov": "nyc ava test/**/*.test.js && nyc report --reporter=lcov", "lint": "jshint .", "ci": "npm run lint && npm run test-cov && npm run test-ts", "autod": "autod -w --prefix '^' -e benchmark", "test-optimized": "node --allow-natives-syntax --trace_opt --trace_deopt test/optimized.js", "contributor": "git-contributor"}, "author": "<EMAIL>", "license": "ISC", "dependencies": {"bluebird": "^3.7.2", "codecov": "^3.7.0", "colors": "^1.4.0", "copy-to": "^2.0.1", "escape-html": "^1.0.3", "ip-range-check": "^0.2.0", "jwt-simple": "^0.5.6", "mkdirp": "^1.0.4", "mz": "^2.7.0", "unescape": "^1.0.1"}, "devDependencies": {"@types/escape-html": "1.0.0", "@types/node": "^14.0.18", "autod": "*", "ava": "^3.10.0", "ava-ts": "^0.25.2", "beautify-benchmark": "*", "benchmark": "^2.1.4", "contributors": "*", "git-contributor": "^1.0.10", "jshint": "*", "moment": "^2.27.0", "nyc": "15", "object-assign": "^4.1.1", "optimized": "^1.2.0", "rimraf": "^3.0.2", "ts-node": "^8.10.2", "typescript": "^3.9.6"}, "homepage": "http://blrgitlab.comviva.com/mbs/MBS-Generics/tree/master/utility", "repository": {"type": "git", "url": "http://blrgitlab.comviva.com/mbs/MBS-Generics.git", "web": "http://blrgitlab.comviva.com/mbs/MBS-Generics/tree/master/utility"}, "keywords": ["utility", "com-utility", "comviva-utils", "util", "utils", "sha256", "sha1", "hash", "hex"], "publishConfig": {"registry": "http://cots.comviva.com/api/npm/Comviva-Npm-Repo/"}, "engines": {"node": ">= 10.13.0"}}