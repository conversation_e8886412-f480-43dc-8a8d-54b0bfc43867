1.0.2 / 2019-04-19
==================

  * fix for color npm package

1.0.1 / 2019-04-19
==================

  * feat: add includesInvalidHttpHeaderChar() to detect invalid char
  * test: add url test for replaceInvalidHttpHeader<PERSON>har
  * chore: remove unused comments
  * feat: replacement support function format

1.0.0 / 2019-04-07
==================

  * benchmark: update arguments to array
  * chore: add doc
  * feat: add utility.argumentsToArray
  * chore: add david-dm status badge
  * deps: remove unuse mm module
  * test: use ava and nyc instead of mocha and istanbul
  * bench: add string and tpl string benchmark
  * YYYYMMDDHHmmss() support custom dateSep
  * support `YYYYMMDD(d, sep)`
  * add YYYYMMDDHHmmss benchmark
  * add sha1 to readme
  * add random string benchmark
  * utils.YYYYMMDDHHmmssSSS(','); // '2013-04-17 14:43:02,674'
  * support var map = util.map({a: 1})
  * add sha1()
  * remove config from scripts
  * add randomString() and has() (@dead-horse)
  * install from cnpm
  * add YYYYMMDD()
  * support sub object md5
  * support timestamp string
  * parse timestamp
  * hash object
  * add npm image
  * utils.setImmediate()
  * fix test case
  * add number utils: toSafeNumber()
  * add utils.YYYYMMDDHHmmss()
  * add base64 format md5
  * add timestamp()
  * move getIP() to address, fixed #2
  * utils.getParamNames(): get a function parameters names
  * fixed interface name wrong on liunx
  * add getIP()
  * add more test cases
  * Safe encodeURIComponent and decodeURIComponent
  * add randomSlice() fixed #1
  * fixed timezone +0000 and test cases
  * utils.logDate(); // '2013-04-17 14:43:02.674'
  * faster accesslogDate() impl
  * add benchmark
  * add accessLogDate()
  * add hmac()
  * update copyright year
  * update md5 readme
  * add base64 encode and urlsafe base64 encode
  * add html escape()
  * update makefile

0.0.1 / 2018-12-13 
==================

  * first commit
