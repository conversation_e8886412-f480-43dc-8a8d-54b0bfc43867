# required metadata
sonar.projectKey=my:MBS_MBS-LEAP-3.0
sonar.projectName=MBS_MBS-LEAP-3.0
sonar.projectVersion=3.0.0

# path to source directories (required)
sonar.sources=./
sonar.scm.url=scm:svn:http://buildmanager:<EMAIL>/mbs/MBS-LEAP-3.0.git
sonar.exclusions=leap_ui/**/*.js,**/node_modules/

sonar.language=js
# skip build wrapper for C,CPP projects
sonar.cfamily.build-wrapper-output.bypass=true
 
# Encoding of the source code
sonar.sourceEncoding=UTF-8
sonar.my.property=value