---
title: LEAP GW API Reference

toc_footers:
  - <a href='http://localhost:9000'>Login to Studio</a>

includes:
  - errors

search: true
---

# Introduction
```javascript

let request = require('request');
request('http://example.com/leap_gw/configapi', function (error, response, body) {
  console.log('error:', error); // Print the error if one occurred
  console.log('statusCode:', response && response.statusCode); // Print the response status code if a response was received
  console.log('body:', body); // Print the JSON for the LEAP gateway configapi.
});

```

<b>Welcome to the LEAP Gateway API!</b>

This part of the document deals with defining a set of HTTP REST api that are meant for consumption by LEAP GUI. LEAP UI consists of features such as user login, user management, apps listing, apps creation etc. All these GUI functions need data coming from the platform by way of AJAX requests. These API are classified and defined here..

<aside class="notice">
<b>Internationalization support:</b>
<p>The caller may pass the <b>Accept-Language</b> header, to indicate the language preference for receiving response message string. Wherever applicable, LEAP platform will try to provide error messages (if any) in that preferred language. Note that this is a preference only, and NOT binding upon the platform to deliver responses in that specific language. The platform will do its best effort to provide in the specified language and if and only if such a language support is available.
</p>
</aside>

<b>RESTful API</b>
<p>
A RESTful API is an application program interface (API) that uses HTTP requests to GET, PUT, POST and DELETE data.

A RESTful API -- also referred to as a RESTful web service -- is based on representational state transfer (REST) technology, an architectural style and approach to communications often used in web services development.

REST technology is generally preferred to the more robust Simple Object Access Protocol (SOAP) technology because REST leverages less bandwidth, making it more suitable for internet usage. An API for a website is code that allows two software programs to communicate with each another . The API spells out the proper way for a developer to write a program requesting services from an operating system or other application.
 
The REST used by browsers can be thought of as the language of the internet. With cloud use on the rise, APIs are emerging to expose web services. REST is a logical choice for building APIs that allow users to connect and interact with cloud services. RESTful APIs are used by such sites as Amazon, Google, LinkedIn and Twitter.
<p>

#Document History

Version	| Date	| Description |	Author 
--------| ------| ----------- | ------
1 | 15-Nov-2017 | <ul><li>Initial draft</li></ul> | Ravindranath
2 | 14-Dec-2017	| <ul><li>Added API for forgot/reset password</li><li>Added API for refresh token</li><li>Revised the response payloads for all API. Now the response payload carries a business specific error code, where applicable</li></ul> | swathi.s, Yuvaraj K, Umesh Joge
3 | 30-Dec-2017	| <ul><li>Updated the API for App listing, introducing filtering based on status.<p>Query param: status</p><p>Range: 0-9</p></li></ul> | Yuvaraj K 
4 | 04-Jan-2018	| <ul><li>Added API for Create new App.</li><li>Added API for Update existing App</li><li>Added API for App deletion.</li> | Yuvaraj K 
5 | 05-Jan-2018	| <ul><b>Added Config API</b><li>passwordstrength</li><li>appstates</li><li>operators</li><li>languages</li> | Yuvaraj K 
6 | 08-Jan-2018	| <ul><li>Added API for Retrieve list of App Templates</li></ul> | Yuvaraj K 
7 | 09-Jan-2018	| <ul><li>Added the Response Headers for Pagination for list of Apps</li></ul> | Yuvaraj K 
8 | 11-Jan-2018 | <ul><li>Updated the Response Payload for Pagination for list of Apps</li></ul> | Yuvaraj K 
9 | 12-Jan-2018	| <ul><li>Added API for Purge an application</li></ul> | Yuvaraj K 
10 | 15-Jan-2018 | <ul><li>Added API for Create an App using Template</li></ul> | Yuvaraj K 
11 | 16-Jan-2018 | <ul><li>Added response payloads for Change Password</li></ul> | Yuvaraj K 
12 | 18-Jan-2018 | <ul><li>Updated Refresh Token API</li></ul> | swathi.s 
13 | 18-Jan-2018 | <ul><li>Added Config API for macros</li></ul> | Zeba Karishma 
14 | 23-Jan-2018 | <ul><li>Updated SendPasswordOTP, verifyOTP APIs.</li></ul> | swathi.s 
15 | 24-Jan-2018 | <ul><li>Updated the reponse payload for all APIs with respect to JWT expiry status</li></ul> | Yuvaraj K 
16 | 26-Jan-2018 | <ul><li>APIs prefixed with /leap_gw</li></ul> | Yuvaraj K 
17 | 05-Feb-2018 | <ul><li>Create and Save App content type and JSON body error response payload</li></ul> | Yuvaraj K 
18 | 06-Feb-2018 | <ul><li>Added App Simulation Config API</li><li>Added App Simulation API</li></ul> | Yuvaraj K 
19 | 14-Feb-2018 | <ul><li>Added secure OTP for reset password.</li><li>Added purge user API</li></ul>| swathi.s 
20 | 01-Mar-2018 | <ul><li>Database plugin API</li></ul> | Umesh Joge 
21 | 05-Mar-2018 | <ul><li>Updated response payload of App module validation API</li></ul> | Umesh Joge 
22 | 05-Mar-2018 | <ul><li>Updated MySQL request and response payload in Database plugin API</li></ul> | Umesh Joge 
23 | 21-May-2018 | <ul><li>Updated SOAP upload/deploy API</li></ul> | Aditya Prateek 
24 | 21-May-2018 | <ul><li>Added work flow API's</li></ul> | Umesh Joge 
25 | 28-May-2018 | <ul><li>Updated SOAP API and included testConnection API</li></ul> | Aditya Prateek 
26 | 08-June-2018 | <ul><li>Added appsOverview API for IT admin overview page</li></ul> | Zeba Karishma 
27 | 08-June-2018 | <ul><b>Added role APIs to leapGW</b><li>createRole</li><li>list Roles</li><li>list role by roleName</li><li>getUsersByRole</li><li>getUserIdsByRole</li></ul> | Zeba Karishma 
28 | 26-June-2018 |	<ul><b>Added Marketing Admin report API's</b><li>Overview-Summary</li><li>Overview-Top Used Apps</li><li>Overview-Top Success Apps</li><li>Overview-Top Fail Apps</li><li>Top20Apps - Highest Active Users</li><li>Top20Apps - Highest success</li><li>Top20Apps - Highest Transactions</li><li>AppStats - Module wise transactions</li><li>AppStats - Total Active users</li><li>AppStats - Total transactions</li><li>AppStats - Average response time</li><li>InterfaceStats - Total Transactions</li><li>InterfaceStats - Total applications</li></ul> | Umesh Joge
29 | 14-Jul-2018 | <ul><li>Plugin Category listing</li><li>Plugin stats listing</li><li>Retrieve plugin settings schema with data</li><li>Delete custom plugin</li><li>Activate/Deactivate Plugin</li></ul> | Yuvaraj K	
30 | 20-Jul-2018 | <ul><li>Added Audit Trail API</li></ul> | Zeba Karishma	
31 | 12-Sep-2018 | <ul><li>Added App Clone API</li></ul> | Yuvaraj K	
32 | 19-Mar-2019 | <ul><li>Added USSD Service APIs</li><li>Added USSD Gateway configuration APIs</li></ul> | Yuvaraj K	
33 | 03-May-2019 | <ul><b>Added ACL APIs</b><li>List ACL</li><li>Create ACL</li><li>Update ACL</li><li>Delete ACL</li><li>Upload ACL file</li><li>Append ACL file</li><li>Delete files on cancel</li><li>Delete a specific file</li><li>Link List to App</li><li>Unlink List from App</li></ul> | Yuvaraj K, Rohit Jaiswal, Vrinda Agarwal
34 | 03-May-2019 | <ul><b>My Download APIs</b><li>List Downloadable files</li><li>Download file</li></ul> | Yuvaraj K, Rohit Jaiswal, Vrinda Agarwal

# User Management

## Login

> HTTP Request Payload:

```json
{
    "username": "leap",
    "password": "leap@123"
}
```

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.wkR6bu27zqZiG-6xB-1F6niyCAdkhaiT9QeJJ8uRKRQ",
    "code": 200,
    "msg": "Success"
}
```

> Failure Response Payload:

```http
HTTP/1.1 400 Bad Request
Content-Type: application/json
{ 
  "code": 602,
  "msg" : "Invalid username or password"
}
```

Use this endpoint to authenticate a user with a login credential.

### HTTP Request

`POST http://example.com/leap_gw/users/authenticate`

### HTTP Response Error codes

Code | Status | Description
--------- | ------- | -----------
200 | Login is successful | JWT (JSON Web Token) will be obtainined in response body. { "accessToken": <JWT>}
400 | Bad Request | response payload:{"code":400,"msg":["data should have required property 'username'","data should have required property 'password'"]}
401 | Unauthorized | Invalid username or password
500 | Internal Server Error | Any internal or unhandled error

<aside class="success">
Remember — Login API!
</aside>

## Logout

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
  "code": 0,
  "msg": "success"
}
```

> Failure Response Payload:

```http
HTTP/1.1 403 Forbidden
Content-Type: application/json
{
  "code": 623,
  "msg": "JWT blacklisted"
}
```

Use this endpoint to logout a user.

### HTTP Request

`GET http://example.com/leap_gw/users/logout`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Description
--------- | ------- | -----------
200 | Logout is successful | response payload:{ "code": 0, "msg": "success"}
400 | Forbidden | If the user has already logged out but same access token is tried to be accessed.
500 | Internal Server Error | Any internal or unhandled error

<aside class="success">
Remember — Logout API!
</aside>


## Refreshtoken

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.wkR6bu27zqZiG-6xB-1F6niyCAdkhaiT9QeJJ8uRKRQ"
}
```

> Failure Response Payload:

```http
HTTP/1.1 403 Forbidden
Content-Type: application/json
{
  "code": 623,
  "msg": "JWT blacklisted"
}
```

Use this endpoint to refresh an Access Token using the Refresh Token you got during authorization.

### HTTP Request

`GET http://example.com/leap_gw/refreshToken`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | response payload:{ "accessToken": "<token>"}
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Conflict | 623 | JWT blacklisted
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Refresh token API!
</aside>

## Generate OTP

> HTTP Request Payload:

```json
{
    "email":"<EMAIL>"
}
```

> Failure Response Payload:

```http
HTTP/1.1 403 Forbidden
Content-Type: application/json
{
  "code": 623,
  "msg": "JWT blacklisted"
}
```

Use this endpoint to generate the OTP for a user, when user forgets the password. OTP will be sent to users e-mail address.

### HTTP Request

`POST http://example.com/leap_gw/sendPasswordOTP`

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad Request | 601 | Missing 'username' property in request payload
400 | Bad Request | 622 | User is not found in system.
400 | Bad Request | 623 | Request is sent too soon.
400 | Bad Request | 624 | Maximum attempt Limit reached.
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Generate OTP API!
</aside>


## Verify OTP

> HTTP Request Payload:

```json
{
    "otp": "715734",
    "newToken": true //Default:false. Allows to send new token after OTP verification.
}
```

> Failure Response Payload:

```http
HTTP/1.1 403 Forbidden
Content-Type: application/json
{
  "code": 623,
  "msg": "JWT blacklisted"
}
```

Use this endpoint to verify the OTP.

### HTTP Request

`POST http://example.com/leap_gw/verifyOTP`

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
409 | Conflict | 603 | OTP Verification failed
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Verify OTP API!
</aside>

## Reset Password

> HTTP Request Payload:

```json
{
    "password": "Leap@1234",
    "otp": "715734"
}
```

> Failure Response Payload:

```http
HTTP/1.1 400 Bad request
Content-Type: application/json
{
  "code": 601,
  "msg": "Missing 'username' property in request payload."
}
```

```http
HTTP/1.1 400 Bad request
Content-Type: application/json
{
  "code": 602,
  "msg": "Invalid 'username' property in request payload."
}
```

```http
HTTP/1.1 400 Bad request
Content-Type: application/json
{
  "code": 604,
  "msg": "Password criteria unmet."
}
```

```http
HTTP/1.1 409 Conflict
Content-Type: application/json
{
  "code": 605,
  "msg": "Unrecognized username"
}
```

Use this endpoint to reset the password for a user.

### HTTP Request

`POST http://example.com/leap_gw/resetPassword`

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad request | 601 | Missing 'username' property in request payload.
400 | Bad request | 602 | Invalid 'username' property in request payload.
400 | Bad request | 604 | Password criteria unmet.
409 | Conflict | 605 | Unrecognized username
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Reset Password API!
</aside>

## Change Password

> HTTP Request Payload:

```json
{
    "oldPassword": "",
    "password": ""
}
```

> Failure Response Payload:

```http
HTTP/1.1 400 Bad request
Content-Type: application/json
{
  "code": 601,
  "msg": "Missing 'username' property in request payload."
}
```

Use this endpoint to change the password for a user.

### HTTP Request

`POST http://example.com/leap_gw/users/changePassword/update`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad request | 604 | Password criteria unmet
400 | Bad request | 608 | Old Password is incorrect
400 | Bad request | 617 | Missing 'oldPassword' property in request payload.
400 | Bad request | 618 | Missing new 'password' property in request payload.
400 | Bad request | 619 | Old and New password cannot be same.
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
409 | Conflict | 620 | Password matches with one of the last 5 passwords
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Change Password API!
</aside>

## Create User Without Password

> HTTP Request Payload:

```json
{
    "loginId": "leap",
    "firstName": "leap",
    "lastName" : "Surname",
    "contactNo": "919090909090",    //Optional
    "email": "<EMAIL>",
    "activationLink":"http://159.89.175.116/create-password?", //mandatory
    "role":"app-developer", // mkt-admin or it-admin or app-developer
    "tags" : { "lang" : "en" }      //Optional, lang:Default English
}
```

> Failure Response Payload:

```http
HTTP/1.1 400 Bad request
Content-Type: application/json
{
  "code": 400,
  "msg": [
    "data.firstName should match pattern \"^[A-Za-z]{2,}$\"",
    "data.lastName should match pattern \"^[A-Za-z]{2,}$\"",
    "data.email should match pattern \"^[a-zA-Z0-9]+[a-zA-Z0-9._-]*[a-zA-Z0-9]+@[a-zA-Z0-9]+[a-zA-Z0-9._-]*[a-zA-Z0-9]+.[a-zA-Z]{2,5}$\"",
    "data.contactNo should match pattern \"^[0-9]{6,}$\"",
    "data should have required property 'role'"
  ]
}
```

```http
HTTP/1.1 400 Bad request
Content-Type: application/json
{
    "code": 400,
    "msg": [
      "data should have required property 'password'",
      "data should have required property 'firstName'",
      "data should have required property 'email'",
      "data should have required property 'role'",
      "data should have required property 'loginId'",
      "data should have required property 'username'",
      "data should match exactly one schema in oneOf"
    ]
}
```

Use this endpoint to create user without the password.

### HTTP Request

`POST http://example.com/leap_gw/users/createUserWithoutPassword`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Create User Without Password API!
</aside>

## Update User Settings

> HTTP Request Payload:

```json
{
  "firstName": "",
  "lastName": "",
  "contactNo": "",
  "tags" : { "lang" : "en" }    //Optional, lang:Default English
}
```

> Failure Response Payload:

```http
HTTP/1.1 400 Bad request
Content-Type: application/json
{
  "code": 613,
  "msg": "Length of the firstname exceeded"
}
```

Use this endpoint to update the user settings.

### HTTP Request

`POST http://example.com/leap_gw/users/updateUser/:userId/update`

<aside class="notice"><b>userId: </b>is user id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 603 | Length of the firstname exceeded
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Update User Settings API!
</aside>

## List Users

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": "success",
    "data": {
        "pageSize": 10,
        "currentPage": 1,
        "totalUsers": 3,
        "totalPages": 1,
        "userList": [ ]
    },
    "limit": 10,
    "offset": 0
}
```

Use this endpoint to list the users in the system.

### HTTP Request

`GET http://example.com/leap_gw/users`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
token | null | A Search string.
page | 1 | Page number.
size | 10 | Number of records per page.
sortf | null | Name of the field on which to sorting to be applied. Possible values: username or email or createdAt or updatedAt
order | asc | Sorting order applied for above filed. Possible values: asc or desc
status | null | Filter the users by user activation status. Choose from active or inactive or both(comma separated).
startTime | null | Time (in miliseconds) from which user is to be listed.
endTime | null | Time (in milliseconds) to which user is to be listed.
app_count | false | Computes the total apps under each of the users, if set to true. Possible values: true or false

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 603 | Length of the firstname exceeded
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — List Users API!
</aside>

## User Profile

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "id": 1,
    "loginId": "",
    "firstName": "",
    "lastName": "",
    "status": "1",
    "email": "",
    "contactNo": "919090909092",
    "activationDt": "2018-12-20T07:00:23.000Z",
    "role": "app-developer",
    "createdBy": 1,
    "modifiedBy": 1,
    "createdAt": "2018-12-20T19:00:23.000Z",
    "updatedAt": "2018-12-20T19:00:23.000Z"
}
```

Use this endpoint to retrieve the user profile information from the system.

### HTTP Request

`GET http://example.com/leap_gw/users/:userId`

<aside class="notice"><b>userId: </b>is user id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 603 | Length of the firstname exceeded
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — User Profile API!
</aside>

## Delete User

Use this endpoint to retrieve the user profile information from the system.

### HTTP Reques

`POST http://example.com/leap_gw/users/deleteUser/:userId/purge/delete`

<aside class="notice"><b>userId: </b>is user id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 603 | Length of the firstname exceeded
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Delete User API!
</aside>

## Create Role

> HTTP Request Payload:

```json
{
    "roleName": "",
    "perms": ""
}
```

```http
HTTP/1.1 409 Bad request
Content-Type: application/json
{
  "code": 633,
  "msg": "Role already exists"
}
```

Use this endpoint to create a new role in the system.

### HTTP Request

`POST http://example.com/leap_gw/roles/createRole`

<aside class="notice"><b>userId: </b>is user id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 603 | Length of the firstname exceeded
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Create Role API!
</aside>

## List Roles

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
[
    {
        "roleName": "app-developer",
        "description": null
    },
    {
        "roleName": "it-admin",
        "description": null
    },
    {
        "roleName": "mkt-admin",
        "description": null
    }
]
```

Use this endpoint to list the roles in the system.

### HTTP Request

`GET http://example.com/leap_gw/roles`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 603 | Length of the firstname exceeded
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — List Roles API!
</aside>

## List UserID by Roles

> HTTP Request Payload:

```json
{
    "roles":["root","app-developer","it-admin","mkt-admin"]
}
```

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "root": [
        1,
        2
    ],
    "app-developer": [
        5,
        6
    ],
    "it-admin": [
        3
    ],
    "mkt-admin": [
        4
    ]
}
```

Use this endpoint to list the roles in the system.

### HTTP Request

`POST http://example.com/leap_gw/roles/getUserIdsByRole`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 603 | Length of the firstname exceeded
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — List UserID by Roles API!
</aside>

## Activate User

> HTTP Request Payload:

```json
{
    "status":1 //status =1 corresponds to activating the user
}
```

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 610,
    "msg": "User Activated Successfully"
}
```

Use this endpoint to Activate the user.

### HTTP Request

`POST http://example.com/leap_gw/users/activate/:userId/update`

<aside class="notice"><b>userId: </b>is user id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 641 | User Already Activated
400 | Bag Request | 754 | Incorrect status field
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Activate User API!
</aside>

## Deactivate User

> HTTP Request Payload:

```json
{
    "status":2 //status =1 corresponds to activating the user
}
```

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 611,
    "msg": "User Deactivated Successfully"
}
```

Use this endpoint to Activate the user.

### HTTP Request

`POST http://example.com/leap_gw/users/deactivate/:userId/update`

<aside class="notice"><b>userId: </b>is user id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 641 | User Already Activated
400 | Bag Request | 754 | Incorrect status field
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Deactivate User API!
</aside>


## User Creation Template

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "required": [
        "activationLink",
        "email",
        "firstName",
        "role"
    ],
    "oneOf": [
        {
            "required": [
                "loginId"
            ]
        },
        {
            "required": [
                "username"
            ]
        }
    ],
    "properties": {
        "loginId": {
            "type": "string",
            "description": "This field allows alphanumeric characters, hyphen, dot and underscore.MinLength = 2",
            "maxLength": 32,
            "pattern": "[A-Za-z0-9_.-]{2,}$"
        },
        "username": {
            "type": "string",
            "description": "This field allows alphanumeric characters, hyphen, dot and underscore.MinLength = 2",
            "maxLength": 32,
            "pattern": "[A-Za-z0-9_.-]{2,}$"
        },
        "activationLink": {
            "type": "string",
            "description": "Base url to which unique activation endpoint can be attached."
        },
        "firstName": {
            "type": "string",
            "description": "This field allows alphabets only.MinLength = 2",
            "maxLength": 32,
            "pattern": "[A-Za-z]{2,}$"
        },
        "middleName": {
            "type": "string",
            "description": "This field allows alphabets only.MinLength = 2",
            "maxLength": 32,
            "pattern": "[A-Za-z]{2,}$"
        },
        "lastName": {
            "type": "string",
            "description": "This field allows alphabets only.MinLength = 2",
            "maxLength": 32,
            "pattern": "[A-Za-z]{2,}$"
        },
        "email": {
            "type": "string",
            "description": "Valid email id.",
            "maxLength": 128,
            "format": "email",
            "pattern": "^[a-zA-Z0-9._-]{2,}@[a-zA-Z0-9._-]{2,}.[a-zA-Z]{2,5}$"
        },
        "contactNo": {
            "type": "string",
            "description": "This field allows digits only.",
            "maxLength": 24,
            "pattern": "[0-9]{6,}$"
        },
        "role": {
            "type": "string",
            "description": "RoleName of the role to be assigned to the user."
        }
    }
}
```

Use this endpoint to get JSON template for user creation form.

### HTTP Request

`GET http://example.com/leap_gw/users/template/createUser`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — User Creation Template API!
</aside>

# Application Management

## List Apps

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 200,
    "msg": "Apps listed successfully",
    "data": {
        "pageSize": 1,
        "totalApps": 1,
        "totalPages": 1,
        "currentPage": 1,
        "appList": [
            {
                "id": "1548840798135",
                "name": "135 NPP Value Menu App",
                "desc": "135 NPP Value Menu App",
                "status": "6",
                "owner": 5,
                "createdBy": 5,
                "modifiedBy": 5,
                "createdAt": "2019-01-30 15:03:18",
                "updatedAt": "2019-04-13 22:57:25"
            }
        ]
    }
}
```

Use this endpoint to list the apps in the system.

### HTTP Request

`GET http://example.com/leap_gw/apps`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
token | null | A Search string.
page | 1 | Page number.
size | 10 | Number of records per page.
sortf | null | Name of the field on which to sorting to be applied. Possible values: name or status or createdAt or updatedAt
order | asc | Sorting order applied for above filed. Possible values: asc or desc
status | null | Filter the apps by status codes.  Status codes are from 0 to 9 can be used or comma separated codes.
user | null | Filter the apps by user id(Owner's of app)

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 703 | Bad input for size field
400 | Bag Request | 704 | Bad input for sortf field
400 | Bag Request | 705 | Bad input for order field
400 | Bag Request | 740 | Bad input for page field
400 | Bag Request | 743 | Page does not exists
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

### HTTP Response Headers

Parameter  | Description
--------- | -----------
totalApps | Total Apps availble for the filter.
firstPage | First Page number of the results for the filter.
prevPage | Previous Page number from current page.
currPage | Current page number, Input provided by App developer.
nextPage | Next Page number to current page.
lastPage | Last Page number of the results for the filter.
pageSize | Number of Apps Per page.

<aside class="success">
Remember — List Apps API!
</aside>

## List Application Templates

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 200,
    "msg": "Apps listed successfully",
    "data": [
        {
            "id": "1543681814395",
            "name": "Balance Enquiry",
            "desc": "App template to demontrate the usage of UCIP module in the application"
        },
        {
            "id": "1543681814396",
            "name": "Call Me Back",
            "desc": "App template to demontrate the Call me back application"
        }
    ]
}
```

Use this endpoint to list the application templates in the system.

### HTTP Request

`GET http://example.com/leap_gw/api/app/templates`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — List Application Templates API!
</aside>

## Create Application

> HTTP Request Payload:

```json
{
   "name":"Test App",
   "desc":"Test Automation"
}
```

Use this endpoint to create application in the system.

### HTTP Request

`POST http://example.com/leap_gw/apps`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
201 | Created | 201 | 
400 | Bad Request | 711 | Payload missing
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 715 | App name field is missing
400 | Bad Request | 729 | Invalid App name
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
409 | Conflict | 753 | App name already exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Create Application API!
</aside>

## Clone App from template

> HTTP Request Payload:

```json
{
   "name": "New App",
   "desc": "New App using template",
   "appTemplateId": 1514430879843
}
```

Use this endpoint to create application in the system.

### HTTP Request

`POST http://example.com/leap_gw/api/templates/clone`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
201 | Created | 201 | 
400 | Bad Request | 711 | Payload missing
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 715 | App name field is missing
400 | Bad Request | 729 | Invalid App name
400 | Bad Request | 745 | App template id is missing
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
409 | Conflict | 739 | Resource conflicts, App already exists
409 | Conflict | 746 | Resource conflicts, App Template no longer exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Clone App from template API!
</aside>

## Clone App from existing App

> HTTP Request Payload:

```json
{
   "name": "New App",
   "desc": "New App using existing application"
}
```

Use this endpoint to create application by cloning from existing application.

### HTTP Request

`POST http://example.com/leap_gw/apps/clone/:appId`

<aside class="notice"><b>appId: </b>is application id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
201 | Created | 201 | 
400 | Bad Request | 711 | Payload missing
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 715 | App name field is missing
400 | Bad Request | 729 | Invalid App name
400 | Bad Request | 748 | App name should have Minimum 5 characters
400 | Bad Request | 749 | App name should not exceed more than 32 characters
400 | Bad Request | 750 | App description field MAX limit exceeds
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
409 | Conflict | 739 | Resource conflicts, App already exists
409 | Conflict | 746 | Resource conflicts, App Template no longer exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Clone App from existing App API!
</aside>

## Update Application

> HTTP Request Payload:

```json
{
  "name": "Test App Name",
  "desc": "",
  "appData": { }
}
```

Use this endpoint to update application in the system.

### HTTP Request

`POST http://example.com/leap_gw/apps/:appId/update`

<aside class="notice"><b>appId: </b>is application id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad Request | 711 | Payload missing
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 715 | App name field is missing
400 | Bad Request | 729 | Invalid App name
400 | Bad Request | 745 | App template id is missing
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
409 | Conflict | 739 | Resource conflicts, App no longer exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Update Application API!
</aside>

## Delete Application

Use this endpoint to delete application in the system.

### HTTP Request

`POST http://example.com/leap_gw/apps/:appId/delete`

<aside class="notice"><b>appId: </b>is application id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 739 | Resource conflicts, App no longer exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Delete Application API!
</aside>

## Retrieve Application

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "id": "1555470891917",
    "name": "Hello World",
    "desc": "",
    "status": "0",
    "owner": 5,
    "createdBy": 5,
    "modifiedBy": 5,
    "createdAt": "2019-04-17 08:44:51",
    "updatedAt": "2019-04-17 08:45:31",
    "appData": {
        "id": "0b004c8d-1ced-4454-928c-db0d7e9812f3",
        "offsetX": 0,
        "offsetY": 0,
        "zoom": 100,
        "gridSize": 0,
        "links": [
            {
                "id": "d7fcbc602785",
                "type": "default",
                "selected": false,
                "source": "9bfa38fe-81d7-4bde-9e13-cf115132306c",
                "sourcePort": "87ef79b986e1",
                "target": "d35c73d5-e454-4721-bf21-1f18b8b732fe",
                "targetPort": "19863763216b",
                "points": [
                    {
                        "id": "8ea677e1-8e29-4758-ab62-0acc5c157f11",
                        "selected": false,
                        "x": 0,
                        "y": 0
                    },
                    {
                        "id": "b8bc48f1-9c36-4102-a158-3eb9345bc903",
                        "selected": false,
                        "x": 0,
                        "y": 0
                    }
                ],
                "color": "link__conditions",
                "extras": {}
            }
        ],
        "startId": "2745140",
        "endId": "eec4e35",
        "modules": {
            "2745140": {
                "settings": {
                    "auth_required": false,
                    "username": "leap",
                    "password": "leap123",
                    "immediateResponseFlag": false,
                    "method": "GET",
                    "samplePayload": "{\"MSISDN\": \"919876543210\",\n\"subscriberInput\": \"919876543210\",\"query\": {\"subscriberType\": 1,\"contentType\": \"text\"}",
                    "contentType": "application/json",
                    "aparty": "MSISDN",
                    "params": [
                        "MSISDN"
                    ],
                    "freeflow": "Freeflow",
                    "title": "App Start"
                },
                "input": {},
                "process": {},
                "output": {
                    "conditions": {
                        "eec4e35_200": {
                            "statement": [
                                {
                                    "expr": [
                                        "ok",
                                        "eq",
                                        "ok"
                                    ]
                                }
                            ],
                            "fallbackcode": "200",
                            "isActive": true
                        }
                    },
                    "codeActive": false,
                    "customCode": "// Custom code panel\n// main function is the default method executed after processing current module\nfunction main(){\n  return \"\";// return end moduleId\n}"
                },
                "conditionsMap": [
                    "eec4e35_200"
                ],
                "menuNavigationOptions": {},
                "coordinates": {
                    "id": "d35c73d5-e454-4721-bf21-1f18b8b732fe",
                    "type": "pallete",
                    "selected": false,
                    "x": 373.84375,
                    "y": 80,
                    "extras": {},
                    "ports": [
                        {
                            "id": "19863763216b",
                            "type": "pallete",
                            "selected": false,
                            "name": "default_node-in",
                            "parentNode": "d35c73d5-e454-4721-bf21-1f18b8b732fe",
                            "links": [
                                "d7fcbc602785"
                            ],
                            "in": true,
                            "color": "link__conditions"
                        }
                    ],
                    "nodeData": {
                        "title": "App Start",
                        "name": "App Start",
                        "id": "2745140",
                        "isEditable": true,
                        "canDelete": false,
                        "status": "",
                        "moduleType": "appStart"
                    }
                },
                "type": "appStart",
                "typeId": "0.1",
                "name": "appStart"
            },
            "eec4e35": {
                "settings": {
                    "title": "App End"
                },
                "input": {},
                "process": {
                    "success": {
                        "code": [
                            "0",
                            "200-226",
                            "S9000"
                        ],
                        "message": "Dear Customer, Your request has been successfully processed"
                    },
                    "customErrors": [
                        {
                            "code": [],
                            "message": ""
                        }
                    ],
                    "defaultError": {
                        "code": "E9000",
                        "message": "Dear Customer, Your request cannot be processed now. Please try again later."
                    },
                    "staticMessage": {
                        "code": false,
                        "message": "Thanks for using LEAP-USSD Services"
                    }
                },
                "output": {
                    "conditions": {}
                },
                "conditionsMap": [],
                "menuNavigationOptions": {},
                "type": "appEnd",
                "typeId": "0.2",
                "coordinates": {
                    "id": "9bfa38fe-81d7-4bde-9e13-cf115132306c",
                    "type": "pallete",
                    "selected": false,
                    "x": 380.84375,
                    "y": 309,
                    "extras": {},
                    "ports": [
                        {
                            "id": "87ef79b986e1",
                            "type": "pallete",
                            "selected": false,
                            "name": "default_node-out",
                            "parentNode": "9bfa38fe-81d7-4bde-9e13-cf115132306c",
                            "links": [
                                "d7fcbc602785"
                            ],
                            "in": false,
                            "color": "link__conditions"
                        }
                    ],
                    "nodeData": {
                        "title": "App End",
                        "name": "App End",
                        "id": "eec4e35",
                        "isEditable": true,
                        "canDelete": false,
                        "status": "",
                        "moduleType": "appEnd"
                    }
                },
                "name": "appEnd"
            }
        },
        "errors": {
            "2745140": [],
            "eec4e35": []
        }
    },
    "code": 200,
    "supportedLanguages": "English"
}
```

Use this endpoint to register application for simulation.

### HTTP Request

`GET http://example.com/samvaadak/apps/:appId`

<aside class="notice"><b>appId: </b>is application id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
202 | Success | - | 
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 739 | Resource conflicts, App no longer exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Delete Application API!
</aside>

## Assign Application

> HTTP Request Payload:

```json
{
    "assignTo":2//userId of the developer to which app is to be assigned.
    // Use value as -1 to unassign an application from a developer.
}
```

Use this endpoint to assign application to app-developer.

### HTTP Request

`PATCH http://example.com/leap_gw/api/:appId/assign`

<aside class="notice"><b>appId: </b>is application id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 739 | Resource conflicts, App no longer exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Assign Application API!
</aside>

# Workflow Management

<b>Workflow Management Design.</b>
<p align="center"><img src="/images/workflowdesign.png" width=700 alt="Workflow Management Design"></p>

## Route Application

> HTTP Request Payload:

```json
{
    "comment": "Routing for approval"
}
```

Use this endpoint to route the application to next level user for approval.

### HTTP Request

`POST http://example.com/leap_gw/workflow/:appId/:action`

<aside class="notice"><b>appId: </b>is application id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### Workflow actions
No. | Description | Owner | Action | Action Description | Start State | End State | Notify
--------- |--------- |--------- |--------- |--------- |--------- |--------- |--------- 
1 | Submit to Marketing Admin for Get Approval | app-developer | SUBMIT | Submits for Get Approval | Draft | ApprovalPending | mkt-admin
2 | Simulate and validate the application | mkt-admin | VIEW | View application | ApprovalPending | ApprovalPending | NA
3 | Withdraw application | app-developer | WITHDRAW | Withdraw for Rework | ApprovalPending | Draft | mkt-admin
4 | If Any changes/issues, Marketing Admin Rejects application | mkt-admin | REJECT | Rejects for rework | ApprovalPending | Draft | app-developer
5 | If No Changes/issues, Marketing Admin route to IT Admin as approved for staging | mkt-admin | STAGING | Approved for staging | ApprovalPending | Staging | app-developer,it-admin
6 | If Any issues, IT Admin Reject application | it-admin | REJECT | Reject for rework | Staging | Draft | app-developer,mkt-admin
7 | If Any issues, IT Admin Reject application | it-admin | STAGE | Routes for staging | Staging | Staged | app-developer,mkt-admin
8 | If Any issues, Marketing Admin will Reject application | mkt-admin | REJECT | Rejects for rework | Staged | Draft | app-developer,it-admin
9 | If everything is fine, Marketing Admin will Launch the application to Production | mkt-admin | LAUNCH | Launch for production | Staged | Launched | app-developer,it-admin
10 | Withdraw application from production by making Retire | mkt-admin | RETIRE | Withdraw from production | Launched | Retired | app-developer,it-admin
11 | Launch for production  | mkt-admin | LAUNCH | Launch from production | ApprovalPending | Launched | app-developer,it-admin

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 760 | Application is not in the state to perform the action
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Route Application API!
</aside>

## Retrieve Application History

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": 1
}
```

Use this endpoint to the retrieve the History of an Application.

### HTTP Request

`GET http://example.com/leap_gw/apps/apphistory/:appId`

<aside class="notice"><b>appId: </b>is application id</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 760 | Application is not in the state to perform the action
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Retrieve Application History API!
</aside>

# Notification Management

## Get Notification Count

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": 1
}
```

Use this endpoint to the count of un-read notifications.

### HTTP Request

`GET http://example.com/leap_gw/notification/count`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 760 | Application is not in the state to perform the action
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Get Notification Count API!
</aside>

## List Notifications

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": "Success",
    "data": {
        "pageSize": 1,
        "totalEvents": 1,
        "totalPages": 1,
        "currentPage": 1,
        "eventList": [
            {
                "msg": "DefaultMktAdmin launched the 135 NPP Value Menu App app",
                "data": {
                    "id": 2,
                    "appId": "1548840798135",
                    "appName": "135 NPP Value Menu App",
                    "action": "8",
                    "comment": null,
                    "is_read": "0",
                    "performer": "4",
                    "createdAt": "2019-04-13 22:57:25",
                    "updatedAt": "2019-04-13 22:57:25"
                }
            }
        ]
    }
}
```

Use this endpoint to list the notifications.

### HTTP Request

`GET http://example.com/leap_gw/notification`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 760 | Application is not in the state to perform the action
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — List Notifications API!
</aside>

## Mark Notifications Read

> HTTP Request Payload:

```json
{
    "events": [1,2,5,7,10] //Array of event ID's to update status
}
```

Use this endpoint to update the notification status.

### HTTP Request

`POST http://example.com/leap_gw/notification/update`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 760 | Application is not in the state to perform the action
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Mark Notifications Read API!
</aside>

## Delete Notifications

> HTTP Request Payload:

```json
{
    "events": [1,2,5,7,10] //Array of event ID's to update status
}
```

Use this endpoint to delete the notifications.

### HTTP Request

`POST http://example.com/leap_gw/notification/delete`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 760 | Application is not in the state to perform the action
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Delete Notifications API!
</aside>

# Plugin Management

## List Palettes

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
[
    {
        "category": {
            "type": "appModules",
            "name": "App Modules",
            "id": "0",
            "expand": true
        },
        "modules": [
            {
                "category": "appModules",
                "id": "0.1",
                "type": "appStart",
                "name": "App Start"
            },
            {
                "category": "appModules",
                "id": "0.2",
                "type": "appEnd",
                "name": "App End"
            },
            {
                "category": "appModules",
                "id": "0.3",
                "type": "appMenu",
                "name": "Menu",
                "description": "Menu design module"
            },
            {
                "category": "appModules",
                "id": "0.4",
                "type": "appConfig",
                "name": "App Config"
            },
            {
                "category": "appModules",
                "id": "0.5",
                "type": "codeModule",
                "name": "Code Module"
            },
            {
                "category": "appModules",
                "id": "0.6",
                "type": "appRedirect",
                "name": "App Redirect"
            }
        ],
        "id": "0"
    },
    {
        "category": {
            "type": "channelModules",
            "name": "Channel Modules",
            "id": "1",
            "expand": true
        },
        "modules": [
            {
                "category": "channelModules",
                "id": "1.1",
                "type": "sms",
                "name": "SMS Push",
                "description": "This module is used to push the message using smsc"
            },
            {
                "category": "channelModules",
                "id": "1.2",
                "type": "http",
                "name": "HTTP Push",
                "description": "HTTP Channel functions as a request– response protocol in the client– server computing mode"
            },
            {
                "category": "channelModules",
                "id": "1.3",
                "type": "email",
                "name": "e-mails Push",
                "description": "HTTP Channel functions as a request– response protocol in the client– server computing mode"
            }
        ],
        "id": "1"
    },
    {
        "category": {
            "type": "databases",
            "name": "Database Modules",
            "id": "2",
            "expand": true
        },
        "modules": [
            {
                "category": "databases",
                "id": "2.1",
                "type": "mysql",
                "name": "MySQL"
            },
            {
                "category": "databases",
                "id": "2.2",
                "type": "oracle",
                "name": "Oracle DB"
            },
            {
                "category": "databases",
                "id": "2.3",
                "type": "mariadb",
                "name": "MariaDB"
            }
        ],
        "id": "2"
    },
    {
        "category": {
            "type": "ucip",
            "name": "UCIP v5 Modules",
            "id": "3",
            "description": "UCIP(User Communication Interface Protocol) is intended for user self services such as Adjustments, Account Refill, and Account Enquiries and to extract account details. UCIP is an IP-based protocol used for integration towards the AIR server from the external application. UCIP is an XML over HTTP based protocol, which makes it easy to integrate with a central integration point within a network. The protocol supports both session as well as event based clients. A UCIP request is sent to one of the AIR servers within the network and for redundancy purposes it is required to have N+1 AIR system in the network.",
            "expand": false
        },
        "modules": [
            {
                "category": "ucip",
                "id": "3.1",
                "type": "GeneralUpdate",
                "name": "General Update",
                "description": "The message GeneralUpdate is used by external system to adjust offers, account balances, accumulators, service class and more in a single transaction"
            },
            {
                "category": "ucip",
                "id": "3.2",
                "type": "GetAccountDetails",
                "name": "Get Account Details",
                "description": "The GetAccountDetails message is used to obtain account information in order to validate and tailor the user communication"
            },
            {
                "category": "ucip",
                "id": "3.3",
                "type": "GetAccountManagementCounters",
                "name": "Get Account Management Counters",
                "description": "The message GetAccountManagementCounters will return account management counters"
            },
            {
                "category": "ucip",
                "id": "3.4",
                "type": "GetAccountServiceFeeData",
                "name": "Get Account Service Fee Data",
                "description": "The GetAccountServiceFeeData message is used to fetch service fee data tied to an account"
            },
            {
                "category": "ucip",
                "id": "3.5",
                "type": "GetAccumulators",
                "name": "Get Accumulators",
                "description": "The message GetAccumulators is used to obtain accumulator values and (optional) start and end dates related to those accumulators"
            },
            {
                "category": "ucip",
                "id": "3.6",
                "type": "GetAllowedServiceClasses",
                "name": "Get AllowedService Classes",
                "description": "The GetAllowedServiceClasses message is used to fetch a list of service classes the subscriber is allowed to change to"
            },
            {
                "category": "ucip",
                "id": "3.7",
                "type": "GetBalanceAndDate",
                "name": "Get Balance and Date",
                "description": "The message GetBalanceAndDate is used to perform a balance enquiry on the account associated with a specific subscriber identity"
            },
            {
                "category": "ucip",
                "id": "3.8",
                "type": "GetFaFList",
                "name": "Get FaF List",
                "description": "The GetFaFList message is used to fetch the list of Family and Friends numbers with attached FaF indicators"
            },
            {
                "category": "ucip",
                "id": "3.9",
                "type": "GetOffers",
                "name": "Get Offers",
                "description": "The message GetOffers will return a list of offers currently assigned to an account"
            },
            {
                "category": "ucip",
                "id": "3.10",
                "type": "GetRefillOptions",
                "name": "Get Refill Options",
                "description": "This message GetRefillOptions is used to fetch the refill options"
            },
            {
                "category": "ucip",
                "id": "3.11",
                "type": "GetUsageThresholdsAndCounters",
                "name": "Get Usage Thresholds And Counters",
                "description": "The message GetUsageThresholdsAndCounters is used to fetch the active usage counters and thresholds for a subscriber"
            },
            {
                "category": "ucip",
                "id": "3.12",
                "type": "Refill",
                "name": "Refill",
                "description": "The message Refill is used to apply a refill from an administrative system to a prepaid account associated with a specific subscriber identity"
            },
            {
                "category": "ucip",
                "id": "3.13",
                "type": "UpdateAccountDetails",
                "name": "Update Account Details",
                "description": "The message UpdateAccountDetails is used to update the account information"
            },
            {
                "category": "ucip",
                "id": "3.14",
                "type": "UpdateBalanceAndDate",
                "name": "Update Balance And Date",
                "description": "The message UpdateBalanceAndDate is used by external system to adjust balances, start dates and expiry dates on the main account and the dedicated accounts"
            },
            {
                "category": "ucip",
                "id": "3.15",
                "type": "UpdateCommunityList",
                "name": "Update Community List",
                "description": "The message UpdateCommunityList set or updates the list of communities which the account belong to"
            },
            {
                "category": "ucip",
                "id": "3.16",
                "type": "UpdateFaFList",
                "name": "Update FaF List",
                "description": "The message UpdateFaFList is used to update the Family and Friends list for either the account or subscriber"
            },
            {
                "category": "ucip",
                "id": "3.17",
                "type": "UpdateOffer",
                "name": "Update Offer",
                "description": "The UpdateOffer message will assign a new offer or update an existing offer to an account"
            },
            {
                "category": "ucip",
                "id": "3.18",
                "type": "UpdateServiceClass",
                "name": "Update Service Class",
                "description": "This message UpdateServiceClass is used to update the service class (SC) for the subscriber"
            },
            {
                "category": "ucip",
                "id": "3.19",
                "type": "UpdateSubscriberSegmentation",
                "name": "Update Subscriber Segmentation",
                "description": "The message UpdateSubscriberSegmentation is used in order set or update the accountGroupID and serviceOffering parameters which are used for subscriber segmentation"
            },
            {
                "category": "ucip",
                "id": "3.20",
                "type": "UpdateUsageThresholdsAndCounters",
                "name": "Update Usage Thresholds And Counters",
                "description": "The message UpdateUsageThresholdsAndCounters is used to personalize a usage threshold for a subscriber by setting a value other than the default value, either an individual value for a subscriber or an individual value for a provider shared by all consumers"
            }
        ],
        "id": "3"
    },
    {
        "category": {
            "type": "acip",
            "name": "ACIP v5 Modules",
            "id": "4",
            "description": "ACIP(Administrative Communication Interface Protocol) is an IP-based protocol used for integration towards the AIR server from the external administrative application. ACIP is an XML over HTTP based protocol, which makes it easy to integrate with a central integration point within a network. The protocol supports both session as well as event based clients. An ACIP request is sent to one of the AIR servers within the network and for redundancy purposes it is required to have N+1 AIR system in the network. ",
            "expand": false
        },
        "modules": [
            {
                "category": "acip",
                "id": "4.1",
                "type": "AddPeriodicAccountManagementData",
                "name": "Add Periodic Account Management Data",
                "description": "The message AddPeriodicAccountManagementData adds periodic account management data to a subscriber"
            },
            {
                "category": "acip",
                "id": "4.2",
                "type": "DeleteAccumulators",
                "name": "Delete Accumulators",
                "description": "This message is intended to remove one or more accumulators identified by their accumulatorID"
            },
            {
                "category": "acip",
                "id": "4.3",
                "type": "DeleteDedicatedAccounts",
                "name": "Delete Dedicated Accounts",
                "description": "This message is intended to remove one or more dedicated accounts identified by their dedicatedAccountID"
            },
            {
                "category": "acip",
                "id": "4.4",
                "type": "DeleteOffer",
                "name": "Delete Offer",
                "description": "The message DeleteOffer is used to disconnect an offer assigned to an account"
            },
            {
                "category": "acip",
                "id": "4.5",
                "type": "DeletePeriodicAccountManagementData",
                "name": "Delete Periodic Account Management Data",
                "description": "The message DeletePeriodicAccountManagementData deletes periodic account management evaluation data for a subscriber"
            },
            {
                "category": "acip",
                "id": "4.6",
                "type": "DeleteSubscriber",
                "name": "Delete Subscriber",
                "description": "The message DeleteSubscriber performs a deletion of subscriber and account "
            },
            {
                "category": "acip",
                "id": "4.7",
                "type": "DeleteTimeRestriction",
                "name": "Delete Time Restriction",
                "description": "This message removes any number of time restrictions. If no identifier is given all existing restrictions will be deleted"
            },
            {
                "category": "acip",
                "id": "4.8",
                "type": "DeleteUsageThreshold",
                "name": "Delete Usage Threshold",
                "description": "The message DeleteUsageThresholds removes a personal or common usage threshold from a subscriber"
            },
            {
                "category": "acip",
                "id": "4.9",
                "type": "GetCapabilities",
                "name": "Get Capabilities",
                "description": "The message GetCapabilities is used to fetch available capabilities"
            },
            {
                "category": "acip",
                "id": "4.10",
                "type": "GetPromotionCounters",
                "name": "Get Promotion Counters",
                "description": "The message GetPromotionCounters will return the current accumulated values used as base for the calculation of when to give a promotion and when to progress a promotion plan"
            },
            {
                "category": "acip",
                "id": "4.11",
                "type": "GetPromotionPlans",
                "name": "Get Promotion Plans",
                "description": "The message GetPromotionPlans will return the promotion plan allocated to the subscribers account"
            },
            {
                "category": "acip",
                "id": "4.12",
                "type": "GetTimeRestriction",
                "name": "Get Time Restriction",
                "description": "This message retrieves time restrictions. Any number of time restriction IDs can be specified for retrieval. If no IDs are requested all the time restriction will be returned"
            },
            {
                "category": "acip",
                "id": "4.13",
                "type": "InstallSubscriber",
                "name": "InstallSubscriber",
                "description": "The message InstallSubscriber performs an installation of a subscriber with relevant account and subscriber data"
            },
            {
                "category": "acip",
                "id": "4.14",
                "type": "LinkSubordinateSubscriber",
                "name": "Link Subordinate Subscriber",
                "description": "The message LinkSubordinateSubscriber will link a previously installed subscriber to another subscriber's account"
            },
            {
                "category": "acip",
                "id": "4.15",
                "type": "ManageSubscriber",
                "name": "Manage Subscriber",
                "description": "The message ManageSubscriber will be used to manage single subscribers"
            },
            {
                "category": "acip",
                "id": "4.16",
                "type": "RunPeriodicAccountManagement",
                "name": "Run Periodic Account Management",
                "description": "The message RunPeriodicAccountManagement executes an on demand periodic account management evaluation"
            },
            {
                "category": "acip",
                "id": "4.17",
                "type": "UpdateAccountManagementCounters",
                "name": "Update Account Management Counters",
                "description": "The message UpdateAccountManagementCounters will modify account management counters"
            },
            {
                "category": "acip",
                "id": "4.18",
                "type": "UpdateAccumulators",
                "name": "Update Accumulators",
                "description": "The message UpdateAccumulators performs an adjustment to the counter values of the chosen accumulators"
            },
            {
                "category": "acip",
                "id": "4.19",
                "type": "UpdateCommunicationID",
                "name": "Update CommunicationID",
                "description": "The communication ID change operation changes the Communication ID"
            },
            {
                "category": "acip",
                "id": "4.20",
                "type": "UpdateMidCycleRerating",
                "name": "Update Mid Cycle Rerating",
                "description": "The UpdateMidCycleRerating operation changes the rerating status of the account"
            },
            {
                "category": "acip",
                "id": "4.21",
                "type": "UpdatePeriodicAccountManagementData",
                "name": "Update Periodic Account Management Data",
                "description": "The message UpdatePeriodicAccountManagementData changes periodic account management data for a subscriber"
            },
            {
                "category": "acip",
                "id": "4.22",
                "type": "UpdatePromotionCounters",
                "name": "Update Promotion Counters",
                "description": "The message UpdatePromotionCounters give access to modify the counters used in the calculation when to give a promotion or promotion plan progression"
            },
            {
                "category": "acip",
                "id": "4.23",
                "type": "UpdatePromotionPlan",
                "name": "Update Promotion Plan",
                "description": "The message UpdatePromotionPlan can Add, Set or Delete a promotion plan allocation to an account"
            },
            {
                "category": "acip",
                "id": "4.24",
                "type": "UpdateRefillBarring",
                "name": "Update Refill Barring",
                "description": "The message UpdateRefillBarring either bar or clear the subscriber when attempting refills"
            },
            {
                "category": "acip",
                "id": "4.25",
                "type": "UpdateSubDedicatedAccounts",
                "name": "Update Sub-Dedicated Accounts",
                "description": "The message UpdateSubDedicatedAccounts is used by external system to adjust balances, start dates and expiry dates on the sub dedicated accounts"
            },
            {
                "category": "acip",
                "id": "4.26",
                "type": "UpdateTemporaryBlocked",
                "name": "Update Temporary Blocked",
                "description": "The message UpdateTemporaryBlocked set or clear the temporary blocked status on a subscriber"
            },
            {
                "category": "acip",
                "id": "4.27",
                "type": "UpdateTimeRestriction",
                "name": "Update Time Restriction",
                "description": "This message handles both creation and updates to time restrictions. If a restriction id is given that does not exist the restriction will be created"
            }
        ],
        "id": "4"
    },
    {
        "category": {
            "type": "miscellaneous",
            "name": "Miscellaneous Modules",
            "id": "5",
            "expand": true
        },
        "modules": [
            {
                "category": "miscellaneous",
                "id": "5.1",
                "type": "OnNetOffNet",
                "name": "OnNetOffNet Check"
            },
            {
                "category": "miscellaneous",
                "id": "5.2",
                "type": "CheckUtilization",
                "name": "Utilization Check"
            },
            {
                "category": "miscellaneous",
                "id": "5.3",
                "type": "CSVOperations",
                "name": "CSV Operations"
            },
            {
                "category": "miscellaneous",
                "id": "5.4",
                "type": "xmlPullInterface",
                "name": "XML PULL Interface",
                "description": "HTTP Channel functions as a request– response protocol in the client– server computing mode"
            },
            {
                "category": "miscellaneous",
                "id": "5.5",
                "type": "dbill",
                "name": "dbill",
                "description": "Dbill module"
            }
        ],
        "id": "5"
    },
    {
        "category": {
            "type": "vsip",
            "name": "VSIP_v5 Modules",
            "id": "6",
            "description": "VSIP(Voucher Communication Interface Protocol) is intended to be used for integration with voucher administrative systems other than Charging System. VSIP is an RPC style protocol wherein each request-response consists of XML messages sent over HTTP. This makes it easy to integrate with a central integration point within a network. The protocol supports a wide variety of administrative and refill related services. A VSIP request is sent to one of the redundant Voucher Servers.",
            "expand": false
        },
        "modules": [
            {
                "category": "vsip",
                "id": "6.1",
                "type": "GetVoucherDetails",
                "name": "Get Voucher Details",
                "description": "The message GetVoucherDetails is used in order to obtain detailed information on an individual voucher"
            },
            {
                "category": "vsip",
                "id": "6.2",
                "type": "GetVoucherHistory",
                "name": "Get Voucher History",
                "description": "The message GetVoucherHistory is used to get historical information for a voucher including information about voucher state changes performed for a specific voucher"
            },
            {
                "category": "vsip",
                "id": "6.3",
                "type": "UpdateVoucherState",
                "name": "Update Voucher State"
            },
            {
                "category": "vsip",
                "id": "6.4",
                "type": "LoadVoucherCheck",
                "name": "Load Voucher Check",
                "description": "The message LoadVoucherCheck is used to check if the vouchers in a serial number range are loaded into the database"
            },
            {
                "category": "vsip",
                "id": "6.5",
                "type": "GenerateVoucher",
                "name": "Generate Voucher",
                "description": "The message GenerateVoucher is used to schedule a generate voucher task"
            },
            {
                "category": "vsip",
                "id": "6.6",
                "type": "GetGenerateVoucherTaskInfo",
                "name": "Get Generate Voucher TaskInfo",
                "description": "The message GetGenerateVoucherTaskInfo is used to retrieve information about a GenerateVoucher task"
            },
            {
                "category": "vsip",
                "id": "6.7",
                "type": "LoadVoucherBatchFile",
                "name": "Load Voucher Batch File",
                "description": "The message LoadVoucherBatchFile is used to schedule the loading of a batch file"
            },
            {
                "category": "vsip",
                "id": "6.8",
                "type": "GetLoadVoucherBatchFileTaskInfo",
                "name": "Get Load Voucher Batch File TaskInfo",
                "description": "The message GetLoadVoucherBatchFileTaskInfo is used to retrieve information about a LoadVoucherBatchFile"
            },
            {
                "category": "vsip",
                "id": "6.9",
                "type": "GetVoucherBatchFilesList",
                "name": "Get Voucher Batch Files List",
                "description": "The message GetVoucherBatchFilesList is used to get a list of all generated batch files"
            },
            {
                "category": "vsip",
                "id": "6.10",
                "type": "ChangeVoucherState",
                "name": "Change Voucher State",
                "description": "The message ChangeVoucherState message is used to schedule a task to change the state of vouchers"
            },
            {
                "category": "vsip",
                "id": "6.11",
                "type": "GetChangeVoucherStateTaskInfo",
                "name": "Get Change Voucher State TaskInfo",
                "description": "The message GetChangeVoucherStateTaskInfo message is used to retrieve information about a ChangeVoucherState task"
            },
            {
                "category": "vsip",
                "id": "6.12",
                "type": "PurgeVouchers",
                "name": "Purge Vouchers",
                "description": "The message PurgeVouchers is used to schedule a purge voucher task"
            },
            {
                "category": "vsip",
                "id": "6.13",
                "type": "GetPurgeVouchersTaskInfo",
                "name": "Get Purge Vouchers TaskInfo",
                "description": "The message GetPurgeVouchersTaskInfo message is used to return information about a PurgeVoucherTask"
            },
            {
                "category": "vsip",
                "id": "6.14",
                "type": "DeleteGenerateVoucherTask",
                "name": "Delete Generate Voucher Task",
                "description": "The DeleteGenerateVoucherTask message is used to delete a task"
            },
            {
                "category": "vsip",
                "id": "6.14",
                "type": "ReserveVoucher",
                "name": "Reserve Voucher",
                "description": "This message is used to reserve a voucher. The message represents the start of a refill transaction."
            },
            {
                "category": "vsip",
                "id": "6.15",
                "type": "DeleteLoadVoucherBatchTask",
                "name": "Delete Load Voucher Batch Task",
                "description": "The DeleteLoadVoucherBatchTask message is used to delete a task"
            },
            {
                "category": "vsip",
                "id": "6.15",
                "type": "EndReservation",
                "name": "End Reservation",
                "description": "This message is used to reserve a voucher. The message represents the start of a refill transaction."
            },
            {
                "category": "vsip",
                "id": "6.16",
                "type": "DeleteChangeVoucherStateTask",
                "name": "Delete Change Voucher State Task",
                "description": "The DeleteChangeVoucherStateTask message is used to delete a task"
            },
            {
                "category": "vsip",
                "id": "6.17",
                "type": "GenerateVoucherDetailsReport",
                "name": "Generate Voucher Details Report",
                "description": "The GenerateVoucherDetailsReport message is used to schedule a report file of all vouchers in a specified batch"
            },
            {
                "category": "vsip",
                "id": "6.18",
                "type": "GetGenerateVoucherDetailsReportTaskInfo",
                "name": "Get Generate Voucher Details Report TaskInfo",
                "description": "The GetGenerateVoucherDetailsReportTaskInfo message is used to return information about a GenerateVoucherDetailsReport Task"
            },
            {
                "category": "vsip",
                "id": "6.19",
                "type": "GenerateVoucherDistributionReport",
                "name": "Generate Voucher Distribution Report",
                "description": "The GenerateVoucherDistributionReport message is used to create a voucher distribution report file either for a batch or for all vouchers in the database"
            },
            {
                "category": "vsip",
                "id": "6.20",
                "type": "GetGenerateVoucherDistributionReportTaskInfo",
                "name": "Get Generate Voucher Distribution Report TaskInfo",
                "description": "The GetGenerateVoucherDistributionReportTaskInfo message is used to return information about a GenerateVoucherDistributionReport task"
            },
            {
                "category": "vsip",
                "id": "6.21",
                "type": "GenerateVoucherUsageReport",
                "name": "Generate Voucher Usage Report",
                "description": "The GenerateVoucherUsageReport message is used to schedule a report file of all vouchers that was used within a specified time frame"
            },
            {
                "category": "vsip",
                "id": "6.22",
                "type": "GetGenerateVoucherUsageReportTaskInfo",
                "name": "Get Generate Voucher Usage Report TaskInfo",
                "description": "The GetGenerateVoucherUsageReportTaskInfo message is used to return information about a specific or all GenerateVoucherUsageReport tasks"
            },
            {
                "category": "vsip",
                "id": "6.23",
                "type": "ChangeVoucherAttribute",
                "name": "Change Voucher Attribute",
                "description": "The message ChangeVoucherAttribute message is used to change (update) user-defined attributes for vouchers that are already loaded into the database"
            },
            {
                "category": "vsip",
                "id": "6.24",
                "type": "ChangeEncryptionKey",
                "name": "Change Encryption Key",
                "description": "The message ChangeEncryptionKey is used to change the encryption key for the default cipher profile in the Voucher Server"
            },
            {
                "category": "vsip",
                "id": "6.24",
                "type": "GetChangeVoucherAttributeTaskInfo",
                "name": "Get Change Voucher Attribute TaskInfo",
                "description": "The message GetChangeVoucherAttributeTaskInfo message is used to retrieve information about a ChangeVoucherAttribute task"
            }
        ],
        "id": "6"
    },
    {
        "category": {
            "type": "Mobiquity",
            "name": "Mobiquity Modules",
            "id": "7",
            "expand": true
        },
        "modules": [
            {
                "category": "Mobiquity",
                "id": "7.0",
                "type": "TransactionEnquiry",
                "name": "Transaction Enquiry",
                "description": "The API Mobiquity shall reverse the transaction amount and charges"
            },
            {
                "category": "Mobiquity",
                "id": "7.1",
                "type": "MerchantPaymentWithPin",
                "name": "Merchant Payment With Pin",
                "description": "The API can be used by 3rd party to initiate merchant payment where the AM Customer is entering the AM PIN on 3rd party portal"
            },
            {
                "category": "Mobiquity",
                "id": "7.2",
                "type": "TransactionReversal",
                "name": "Transaction Reversal",
                "description": "The API Mobiquity shall reverse the transaction amount and charges"
            }
        ],
        "id": "7"
    }
]
```

Use this endpoint to list the palettes.

### HTTP Request

`GET http://example.com/leap_gw/palette`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
category | all | category is the name of the palette category. If specified, the api will only return sub items under the matching category. If not specified, returns all.

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 801 | Category is not found
409 | Conflict | 763 | User dont have the permission
500 | Internal Server Error | 800 | Internal error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — List Palettes API!
</aside>

## Get Plugin Metainfo
> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "typeId": "0.1",
    "name": "appStart",
    "title": "App Start",
    "type": "object",
    "required": [
        "name",
        "coordinates",
        "settings",
        "process",
        "output"
    ],
    "properties": {
        "name": {
            "description": "Name Of the Module",
            "title": "Module name",
            "type": "string",
            "minLength": 1
        },
        "coordinates": {
            "description": "Coordinates Of the Module",
            "title": "Coordinates",
            "type": "object",
            "properties": {
                "id": {
                    "description": "Coordinate ID",
                    "title": "Coordinate ID",
                    "type": "string",
                    "minLength": 1
                },
                "type": {
                    "description": "Coordinate type",
                    "title": "Coordinate Type",
                    "type": "string",
                    "minLength": 1
                },
                "ports": {
                    "description": "Coordinate ports",
                    "title": "Coordinate ports",
                    "type": "array",
                    "minItems": 1
                },
                "nodedata": {
                    "description": "Node data",
                    "title": "Node data",
                    "type": "object",
                    "properties": {
                        "title": {
                            "description": "The title of node",
                            "title": "Module title",
                            "type": "string"
                        },
                        "name": {
                            "description": "The name of node",
                            "title": "Module Name",
                            "type": "string"
                        },
                        "id": {
                            "description": "The Id of node",
                            "title": "Module ID",
                            "type": "string"
                        }
                    }
                }
            }
        },
        "settings": {
            "description": "Setting properties Of the Module",
            "title": "Settings",
            "type": "object",
            "required": [
                "method",
                "aparty"
            ],
            "oneOf": [
                {
                    "properties": {
                        "method": {
                            "enum": [
                                "POST"
                            ]
                        }
                    },
                    "required": [
                        "contentType",
                        "samplePayload"
                    ]
                },
                {
                    "properties": {
                        "method": {
                            "enum": [
                                "GET"
                            ]
                        }
                    },
                    "required": [
                        "contentType"
                    ]
                }
            ],
            "properties": {
                "auth_required": {
                    "description": "Basic Authentication",
                    "title": "Basic Authentication",
                    "type": "boolean",
                    "hint": "checkBox",
                    "default": true
                },
                "username": {
                    "description": "Username",
                    "title": "Username",
                    "type": "string",
                    "default": "leap"
                },
                "password": {
                    "description": "Password",
                    "title": "Password",
                    "type": "string",
                    "hint": "password",
                    "default": "leap123"
                },
                "immediateResponseFlag": {
                    "description": "Immdediate Response Required",
                    "title": "Immdediate Response Required",
                    "type": "boolean",
                    "hint": "checkBox",
                    "default": false
                },
                "method": {
                    "description": "HTTP Request Method",
                    "title": "HTTP Request Method",
                    "default": "GET",
                    "hint": "radio",
                    "enum": [
                        "GET",
                        "POST"
                    ]
                },
                "samplePayload": {
                    "description": "Request payload",
                    "title": "Sample payload",
                    "hint": "textArea",
                    "type": "string",
                    "default": "{\"MSISDN\": \"919876543210\",\n\"subscriberInput\": \"919876543210\",\"query\": {\"subscriberType\": 1,\"contentType\": \"text\"}"
                },
                "contentType": {
                    "description": "Content Type to send in request header Of the App Module",
                    "title": "Content-Type",
                    "default": "application/json",
                    "enum": [
                        "application/json"
                    ]
                },
                "aparty": {
                    "description": "A Party Query String Parameter",
                    "title": "A Party Query String Param",
                    "type": "string",
                    "default": "MSISDN"
                },
                "params": {
                    "description": "Array of output params",
                    "title": "App Request params",
                    "type": "array",
                    "default": [
                        "MSISDN"
                    ]
                },
                "freeflow": {
                    "description": "App Response header",
                    "title": "Response Header-Freeflow",
                    "type": "string",
                    "default": "Freeflow"
                }
            }
        },
        "input": {
            "description": "Input parameters",
            "title": "Input",
            "type": "object"
        },
        "process": {
            "description": "Setting properties Of the Module",
            "title": "Process",
            "type": "object"
        },
        "output": {
            "description": "The output params",
            "type": "object",
            "customCode": "// Custom code panel\n// main function is the default method executed after processing current module\nfunction main(){\n  return \"\";// return end moduleId\n}",
            "codeActive": true
        }
    },
    "category": "appmodules"
}
```

Use this endpoint to get the plugin metainfo.

### HTTP Request

`GET http://example.com/leap_gw/plugins/:pluginId`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### PluginId

PluginId  | Category 
--------- | ------- 
appStart | appModules
appEnd | appModules
appMenu | appModules
appConfig | appModules
codeModule | appModules
appRedirect | appModules
sms | channelModules
http | channelModules
email | channelModules
mysql | databases
oracle | databases
mariadb | databases
OnNetOffNet | miscellaneous
CheckUtilization | miscellaneous
CSVOperations | miscellaneous
xmlPullInterface | miscellaneous
dbill | miscellaneous
TransactionEnquiry | Mobiquity
MerchantPaymentWithPin | Mobiquity
TransactionReversal | Mobiquity

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 801 | Category is not found
409 | Conflict | 763 | User dont have the permission
500 | Internal Server Error | 800 | Internal error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Get Plugin Metainfo API!
</aside>

## Upload SOAP Plugin files

> HTTP Request Payload:

```http
Body > “form-data”

Key: “file”

Type: file

Value: File to be uploaded
```

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{ 
    "code": 0, 
    "msg": "File upload successful"
}
```

Use this endpoint to upload the SOAP files(xsd/wsdl files).

### HTTP Request

`POST http://example.com/leap_gw/plugins/soap`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
404 | Not Found | 851 | WSDL/XSD root upload directory not found
403 | Forbidden | 625 | Resource Permission Denied
406 | Not Acceptable | 853 | File extension not supported
409 | Conflicts | 892 | Plugin already exists. Check for user's confirmation
500 | Internal Server Error | 854 | Internal error while uploading the file
500 | Internal Server Error | 870 | Error while reading WSDL/XSD directory
500 | Internal Server Error | 871 | Empty directory. No WSDL/XSD to process
500 | Internal Server Error | 872 | Error while parsing the WSDL/XSD
500 | Internal Server Error | 873 | SOAP plugin check error
500 | Internal Server Error | 874 | WSDL directory deletion unsuccessful
500 | Internal Server Error | 875 | Create plugin error
500 | Internal Server Error | 876 | Error in deploy method
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Upload SOAP Plugin files API!
</aside>

## Deploy SOAP Plugin

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{ 
    "code": 0, 
    "msg": "Plugin deployed successfully"
}
```

Use this endpoint to deploy the SOAP plugon for uploaded SOAP files(xsd/wsdl files).

### HTTP Request

`GET http://example.com/leap_gw/plugins/soap/deploy`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
404 | Not Found | 851 | WSDL/XSD root upload directory not found
403 | Forbidden | 625 | Resource Permission Denied
406 | Not Acceptable | 853 | File extension not supported
409 | Conflicts | 892 | Plugin already exists. Check for user's confirmation
500 | Internal Server Error | 854 | Internal error while uploading the file
500 | Internal Server Error | 870 | Error while reading WSDL/XSD directory
500 | Internal Server Error | 871 | Empty directory. No WSDL/XSD to process
500 | Internal Server Error | 872 | Error while parsing the WSDL/XSD
500 | Internal Server Error | 873 | SOAP plugin check error
500 | Internal Server Error | 874 | WSDL directory deletion unsuccessful
500 | Internal Server Error | 875 | Create plugin error
500 | Internal Server Error | 876 | Error in deploy method
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Deploy SOAP Plugin API!
</aside>

## Confirm Plugin Deploy

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{ 
    "code": 0, 
    "msg": "Plugin overwriting successful"
}
```

```http
HTTP/1.1 200 OK
Content-Type: application/json
{ 
    "code": 0, 
    "msg": "Plugin overwriting aborted"
}
```

Use this endpoint to overwrite the existing plugin with user's confirmation.

### HTTP Request

`GET http://example.com/leap_gw/plugins/soap/confirm`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
ans | null | Confirmation yes or no

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Bad Request | 862 | Request should have either 'Yes' or 'No' in query
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
404 | Not Found | 851 | WSDL/XSD root upload directory not found
403 | Forbidden | 625 | Resource Permission Denied
406 | Not Acceptable | 853 | File extension not supported
409 | Conflicts | 892 | Plugin already exists. Check for user's confirmation
500 | Internal Server Error | 860 | Error while performing plugin confirmation step
500 | Internal Server Error | 861 | Plugin Overwriting Error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Confirm Plugin Deploy API!
</aside>

## List Plugin Stats

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "data": {
        "pageSize": 10,
        "totalPlugins": 22,
        "totalPages": 3,
        "currentPage": 1,
        "pluginList": [
            {
                "id": "0.1",
                "category": "appModules",
                "name": "appStart",
                "description": "",
                "type": 0,
                "status": 1,
                "typeDesc": "Built-In",
                "statusDesc": "Active"
            },
            {
                "id": "0.2",
                "category": "appModules",
                "name": "appEnd",
                "description": "",
                "type": 0,
                "status": 1,
                "typeDesc": "Built-In",
                "statusDesc": "Active"
            },
            {
                "id": "0.3",
                "category": "appModules",
                "name": "appMenu",
                "description": "Menu design module",
                "type": 0,
                "status": 1,
                "typeDesc": "Built-In",
                "statusDesc": "Active"
            },
            {
                "id": "0.4",
                "category": "appModules",
                "name": "appConfig",
                "description": "",
                "type": 0,
                "status": 1,
                "typeDesc": "Built-In",
                "statusDesc": "Active"
            },
            {
                "id": "0.5",
                "category": "appModules",
                "name": "codeModule",
                "description": "",
                "type": 0,
                "status": 1,
                "typeDesc": "Built-In",
                "statusDesc": "Active"
            },
            {
                "id": "1.1",
                "category": "channelModules",
                "name": "sms",
                "description": "This module is used to push the message using smsc",
                "type": 0,
                "status": 1,
                "typeDesc": "Built-In",
                "statusDesc": "Active"
            },
            {
                "id": "1.2",
                "category": "channelModules",
                "name": "http",
                "description": "HTTP Channel functions as a request– response protocol in the client– server computing mode",
                "type": 0,
                "status": 1,
                "typeDesc": "Built-In",
                "statusDesc": "Active"
            },
            {
                "id": "2.1",
                "category": "databases",
                "name": "mysql",
                "description": "",
                "type": 0,
                "status": 1,
                "typeDesc": "Built-In",
                "statusDesc": "Active"
            },
            {
                "id": "2.2",
                "category": "databases",
                "name": "oracle",
                "description": "",
                "type": 0,
                "status": 1,
                "typeDesc": "Built-In",
                "statusDesc": "Active"
            },
            {
                "id": "2.3",
                "category": "databases",
                "name": "mariadb",
                "description": "",
                "type": 0,
                "status": 1,
                "typeDesc": "Built-In",
                "statusDesc": "Active"
            }
        ]
    }
}
```

Use this endpoint to list the plugin settings.

### HTTP Request

`GET http://example.com/leap_gw/pm`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
token | null | A Search string.
page | 1 | Page number.
size | 10 | Number of records per page.
type | all | Type of Plugins that is Custom or built-in. Possible values: 0 or 1.
sortf | null | Name of the field on which to sorting to be applied. Possible values: id or name or status or category or description
order | asc | Sorting order applied for above filed. Possible values: asc or desc
status | null | Plugin status value 0(InActive) or 1(Active), if param is negative or if it is undefined. API retrieve all records.

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 908 | Page does not exists
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 900 | Plugin - Internal error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

### HTTP Response Headers

Parameter  | Description
--------- | -----------
totalPlugins | Total plugins availble for the filter.
firstPage | First Page number of the results for the filter.
prevPage | Previous Page number from current page.
currPage | Current page number, Input provided by IT Admin.
nextPage | Next Page number to current page.
lastPage | Last Page number of the results for the filter.
pageSize | Number of plugins Per page.

<aside class="success">
Remember — List Plugin Stats API!
</aside>

## Get Plugin Settings

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "typeId": "6",
    "pluginName": "All",
    "category": "vsip",
    "testRequired": false,
    "url": null,
    "required": [
        "dev",
        "prod"
    ],
    "properties": {
        "dev": {
            "description": "Setting properties Of the Module",
            "title": "Settings",
            "type": "object",
            "required": [
                "host",
                "port",
                "path"
            ],
            "properties": {
                "host": {
                    "description": "The hostname of the VSIP",
                    "title": "Host",
                    "default": "127.0.0.1",
                    "type": "string",
                    "format": "ipv4"
                },
                "port": {
                    "description": "The port number of the VSIP",
                    "title": "Port",
                    "default": 3306,
                    "type": "integer",
                    "minimum": 1024,
                    "maximum": 65535
                },
                "timeout": {
                    "description": "The time interval defines the Request timeout in millis",
                    "title": "Request Timeout in ms",
                    "default": 10000,
                    "type": "integer"
                },
                "path": {
                    "description": "Path of the VSIP client",
                    "title": "Path",
                    "type": "string",
                    "minLength": 1
                },
                "username": {
                    "description": "User Name to connect VSIP client",
                    "title": "User name",
                    "type": "string",
                    "minLength": 1
                },
                "password": {
                    "description": "Password to connect VSIP client",
                    "title": "Password",
                    "hint": "password",
                    "type": "string",
                    "minLength": 1
                },
                "Content-Type": {
                    "description": "Content Type to send in request header",
                    "title": "Content type",
                    "default": "text/xml",
                    "enum": [
                        "text/xml",
                        "application/json",
                        "text/json"
                    ],
                    "minLength": 1
                },
                "Accept-Charset": {
                    "description": "Content Type to send in request header",
                    "title": "Accept charset",
                    "default": "UTF-8",
                    "enum": [
                        "US-ASCII",
                        "UTF-8",
                        "UTF-32",
                        "UTF-64"
                    ],
                    "minLength": 1
                },
                "User-Agent": {
                    "description": "The extrenal user agent to send in the HTTP request header",
                    "title": "User agent",
                    "type": "string",
                    "minLength": 1
                },
                "maxRetry": {
                    "description": "The Max number of retries to connect to the IN",
                    "title": "maxRetry",
                    "default": 1,
                    "type": "integer"
                },
                "retryInterval": {
                    "description": "The time interval betwwen retries to connect to the IN in millis",
                    "title": "retryInterval",
                    "default": 3000,
                    "type": "integer"
                }
            }
        },
        "prod": {
            "description": "Setting properties Of the Module",
            "title": "Settings",
            "type": "object",
            "required": [
                "host",
                "port",
                "path"
            ],
            "properties": {
                "host": {
                    "description": "The hostname of the VSIP",
                    "title": "Host",
                    "default": "127.0.0.1",
                    "type": "string",
                    "format": "ipv4"
                },
                "port": {
                    "description": "The port number of the VSIP",
                    "title": "Port",
                    "default": 3306,
                    "type": "integer",
                    "minimum": 1024,
                    "maximum": 65535
                },
                "timeout": {
                    "description": "The time interval defines the Request timeout in millis",
                    "title": "Request Timeout in ms",
                    "default": 10000,
                    "type": "integer"
                },
                "path": {
                    "description": "Path of the VSIP client",
                    "title": "Path",
                    "type": "string",
                    "minLength": 1
                },
                "username": {
                    "description": "User Name to connect VSIP client",
                    "title": "User name",
                    "type": "string",
                    "minLength": 1
                },
                "password": {
                    "description": "Password to connect VSIP client",
                    "title": "Password",
                    "hint": "password",
                    "type": "string",
                    "minLength": 1
                },
                "Content-Type": {
                    "description": "Content Type to send in request header",
                    "title": "Content type",
                    "default": "text/xml",
                    "enum": [
                        "text/xml",
                        "application/json",
                        "text/json"
                    ],
                    "minLength": 1
                },
                "Accept-Charset": {
                    "description": "Content Type to send in request header",
                    "title": "Accept charset",
                    "default": "UTF-8",
                    "enum": [
                        "US-ASCII",
                        "UTF-8",
                        "UTF-32",
                        "UTF-64"
                    ],
                    "minLength": 1
                },
                "User-Agent": {
                    "description": "The extrenal user agent to send in the HTTP request header",
                    "title": "User agent",
                    "type": "string",
                    "minLength": 1
                },
                "maxRetry": {
                    "description": "The Max number of retries to connect to the IN",
                    "title": "maxRetry",
                    "default": 1,
                    "type": "integer"
                },
                "retryInterval": {
                    "description": "The time interval betwwen retries to connect to the IN in millis",
                    "title": "retryInterval",
                    "default": 3000,
                    "type": "integer"
                }
            }
        }
    }
}
```

Use this endpoint to get the plugin settings.

### HTTP Request

`GET http://example.com/leap_gw/pm/settings/:id`

<aside class="notice"><b>id: </b>is plugin id or category id.</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 908 | Page does not exists
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 900 | Plugin - Internal error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachabl

<aside class="success">
Remember — Get Plugin Settings API!
</aside>

## Update Plugin Settings

> HTTP Request Payload:

```json
{
 "dev": {
  "host": "*************",
  "port": 8080,
  "path": "/mock/vsip",
  "username": "test",
  "password": "test",
  "User-Agent": "test"
 },
 "prod": {
  "host": "localhost",
  "port": 2144,
  "path": "/vsip/ferfer",
  "username": "test",
  "password": "test",
  "User-Agent": "test"
 }
}
```

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": "Plugin settings updated successfully"
}
```

> Failure Response Payload:

```http
HTTP/1.1 400 Bad Request
Content-Type: application/json
{
    "code": 907,
    "msg": [
        {
            "parameter": "host",
            "path": "dev",
            "severity": "error",
            "msg": "should have required property 'host'"
        },
        {
            "parameter": "host",
            "path": "prod",
            "severity": "error",
            "msg": "should have required property 'host'"
        }
    ]
}
```

Use this endpoint to update the plugin settings.

### HTTP Request

`POST http://example.com/leap_gw/pm/settings/:id`

<aside class="notice"><b>id: </b>is plugin id or category id.</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 905 | Payload is required to Update the Plugin settings
400 | Bag Request | 907 | JSON Array of errors
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 909 | Plugin not found
500 | Internal Server Error | 900 | Plugin - Internal error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Update Plugin Settings API!
</aside>

## Delete Custom Plugin

Use this endpoint to delete the plugin/category.

### HTTP Request

`POST http://example.com/leap_gw/pm/:id/delete`

<aside class="notice"><b>id: </b>is plugin id or category id.</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 901 | Invalid plugin id
400 | Bag Request | 902 | built-in plugin cannot be deleted
400 | Bag Request | 904 | Plugin delete failed
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 909 | Plugin not found
500 | Internal Server Error | 900 | Plugin - Internal error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Delete Custom Plugin API!
</aside>

## Activate/Deactivate Plugin

> HTTP Request Payload:

```json
{
    "status": 1 // 1 - active, 0 - Deactive
}
```

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": "Plugin Activation is Successful"
}
```

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": "Plugin Deactivation is Successful"
}
```

Use this endpoint to update the plugin/category status.

### HTTP Request

`POST http://example.com/leap_gw/pm/:id/update`

<aside class="notice"><b>id: </b>is plugin id or category id.</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 910 | Payload is required
400 | Bag Request | 911 | Invalid status Field
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 914 | Plugin Activation is Failed
404 | Not Found | 915 | Plugin Deactivation is Failed
500 | Internal Server Error | 900 | Plugin - Internal error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Activate/Deactivate Plugin API!
</aside>

## List Plugin Categories

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
[
    "appModules",
    "channelModules",
    "databases",
    "ucip",
    "acip",
    "miscellaneous",
    "vsip",
    "Mobiquity",
    "SOAP_soaptopupsuite_soaptopupsuiteSoapBinding"
]
```
Use this endpoint to list the categories.

### HTTP Request

`GET http://example.com/leap_gw/pm/categories`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 900 | Plugin - Internal error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — List Plugin Categories API!
</aside>

# USSD Services - Shortcodes

## List Services

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 200,
    "msg": "USSD Shortcode Listed Successfully",
    "data": {
        "pageSize": 0,
        "totalShortcodes": 0,
        "totalPages": null,
        "currentPage": 1,
        "ussdServiceList": []
    }
}
```

Use this endpoint to list the USSD Services in the system.

### HTTP Request

`GET http://example.com/leap_gw/ussd/sc`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
token | null | A Search string.
page | 1 | Page number.
size | 10 | Number of records per page.
sortf | null | Name of the field on which to sorting to be applied. Possible values: name or status or createdAt or updatedAt
order | asc | Sorting order applied for above filed. Possible values: asc or desc
status | null | Filter the servies by status codes.  Status codes are 0 or 1, also it can be comma separated codes like 0,1
user | null | Filter the servies by user id(Owner's of USSD Service)
startTime | null | Time (in miliseconds) from which user is to be listed.
endTime | null | Time (in milliseconds) to which user is to be listed.

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 703 | Bad input for size field
400 | Bag Request | 704 | Bad input for sortf field
400 | Bag Request | 705 | Bad input for order field
400 | Bag Request | 740 | Bad input for page field
400 | Bag Request | 743 | Page does not exists
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

### HTTP Response Headers

Parameter  | Description
--------- | -----------
totalShortcodes | Total Services availble for the filter.
firstPage | First Page number of the results for the filter.
prevPage | Previous Page number from current page.
currPage | Current page number, Input provided by IT Admin.
nextPage | Next Page number to current page.
lastPage | Last Page number of the results for the filter.
pageSize | Number of Services Per page.

<aside class="success">
Remember — List Services API!
</aside>

## Create Service

> HTTP Request Payload:

```json
{
    "shortcode": "153",
    "name":"USSD Service3",
    "desc": "refrerr",
    "status": 1,
    "options": {
    	"KEY1": "VALUE",
    	"KEY2": "VALUE"
    },
    "appId": 1550396075330
}
```

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": "USSD Service created successfully",
    "id": 1555650655614
}
```

Use this endpoint to create the USSD Service.

### HTTP Request

`POST http://example.com/leap_gw/ussd/sc`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 1006 | Service name already exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Create Service API!
</aside>

## Update Service

> HTTP Request Payload:

```json
{
    "name":"USSD Service3",
    "desc": "refrerr",
    "options": {
    	"KEY1": "VALUE",
    	"KEY2": "VALUE"
    },
    "appId": 1550396075330
}
```

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": "USSD Shortcode Updated Successfully"
}
```

Use this endpoint to update the USSD Service.

### HTTP Request

`POST http://example.com/leap_gw/ussd/sc/:shortcode/update`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 1006 | Service name already exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Update Service API!
</aside>

## Delete Service

Use this endpoint to update the USSD Service.

### HTTP Request

`POST http://example.com/leap_gw/ussd/sc/:shortcode/delete`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 1008 | USSD Shortcode does not exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Delete Service API!
</aside>

# USSD Services - Gateway Linkings

## List Gateway Configurations

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 200,
    "msg": "USSD Gateway Listed Successfully",
    "data": {
        "pageSize": 0,
        "totalGateways": 0,
        "totalPages": null,
        "currentPage": 1,
        "ussdGWList": []
    }
}
```

Use this endpoint to list the USSD Gateway Configurations in the system.

### HTTP Request

`GET http://example.com/leap_gw/ussd/gw`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
token | null | A Search string.
page | 1 | Page number.
size | 10 | Number of records per page.
sortf | null | Name of the field on which to sorting to be applied. Possible values: name or status or createdAt or updatedAt
order | asc | Sorting order applied for above filed. Possible values: asc or desc
status | null | Filter the servies by status codes.  Status codes are 0 or 1, also it can be comma separated codes like 0,1
user | null | Filter the servies by user id(Owner's of USSD Gateway config)
startTime | null | Time (in miliseconds) from which user is to be listed.
endTime | null | Time (in milliseconds) to which user is to be listed.

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 703 | Bad input for size field
400 | Bag Request | 704 | Bad input for sortf field
400 | Bag Request | 705 | Bad input for order field
400 | Bag Request | 740 | Bad input for page field
400 | Bag Request | 743 | Page does not exists
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

### HTTP Response Headers

Parameter  | Description
--------- | -----------
totalgateways | Total USSD Gateways configs availble for the filter.
firstPage | First Page number of the results for the filter.
prevPage | Previous Page number from current page.
currPage | Current page number, Input provided by IT Admin.
nextPage | Next Page number to current page.
lastPage | Last Page number of the results for the filter.
pageSize | Number of USSD Gateways configs Per page.

<aside class="success">
Remember — List Gateway Configurations API!
</aside>

## Create Gateway Configuration

> HTTP Request Payload:

```json
{
    "name": "auto_gw1",
    "type": 0,
    "ip": "***********",
    "port": 4545,
    "user": "LC135",
    "pass": "LC135",
    "enquiry_link_interval": 10000,
    "no_of_channels": 1,
    "system_type": 0,
    "interface_type": 2,
    "version": 4,
    "buffer_size": 0,
    "parameters_order": "SERVICE_STRING||IMSI||MSISDN||MSC||MCC||MNC||LAC||CI",
    "url": "smpp://localhost:2775",
    "maxUssdMessageCharacters": 182
  }
```

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": "USSD Gateway Configuration created successfully",
    "id": 1555650655614
}
```

Use this endpoint to create the USSD Gateway Configuration.

### HTTP Request

`POST http://example.com/leap_gw/ussd/gw`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 1006 | Gateway Config name already exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Create Gateway Configuration API!
</aside>

## Update Gateway Configuration

> HTTP Request Payload:

```json
{
    "name": "auto_gw1",
    "type": 0,
    "ip": "***********",
    "port": 4545,
    "user": "LC135",
    "pass": "LC135",
    "enquiry_link_interval": 10000,
    "no_of_channels": 1,
    "system_type": 0,
    "interface_type": 2,
    "version": 4,
    "buffer_size": 0,
    "parameters_order": "SERVICE_STRING||IMSI||MSISDN||MSC||MCC||MNC||LAC||CI",
    "url": "smpp://localhost:2775",
    "maxUssdMessageCharacters": 182
  }
```

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": "USSD Gateway Configuration Updated Successfully"
}
```

Use this endpoint to update the USSD Gateway Configuration.

### HTTP Request

`POST http://example.com/leap_gw/ussd/gw/:gatewayId/update`

<aside class="notice"><b>gatewayId: </b>is USSD Gateway configuration id.</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | App Not found in AppStore
409 | Conflict | 1006 | Gateway Configuration name already exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Update Gateway Configuration API!
</aside>

## Delete Gateway Configuration

Use this endpoint to delete the USSD Gateway Configuration.

### HTTP Request

`POST http://example.com/leap_gw/ussd/gw/:gatewayId/delete`

<aside class="notice"><b>gatewayId: </b>is USSD Gateway configuration id.</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad Request | 713 | Invalid JSON body
400 | Bad Request | 751 | Invalid Content Type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 706 | Gateway Configuration found in AppStore
409 | Conflict | 1008 | Gateway Configuration does not exists
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Delete Gateway Configuration API!
</aside>

# USSD Services - Access Control List

## List ACL

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 200,
    "msg": "ACL listed successfully",
    "data": {
        "pageSize": 1,
        "totalACLs": 1,
        "totalPages": 1,
        "currentPage": 1,
        "list": [
            {
                "id": "1548840798135",
                "name": "My LIST 1",
                "type": "MSISDN",
                "status": "Completed", // Possible values: inprogress/completed
                "totalRecords": 1000,
                "createdBy": 5,
                "modifiedBy": 5,
                "createdAt": "2019-01-30 15:03:18",
                "updatedAt": "2019-04-13 22:57:25"
            }
        ]
    }
}
```

Use this endpoint to list the Access Control lists in the system.

### HTTP Request

`GET http://example.com/leap_gw/ussd/acl`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
token | null | A Search string.
page | 1 | Page number.
size | 10 | Number of records per page.
sortf | null | Name of the field on which to sorting to be applied. Possible values: name or type or createdAt or updatedAt
order | asc | Sorting order applied for above filed. Possible values: asc or desc

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 930 | Bad input for page field
400 | Bag Request | 931 | Bad input for size field
400 | Bag Request | 932 | Bad input for sortf field
400 | Bag Request | 933 | Bad input for order field
400 | Bag Request | 934 | Page does not exists
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 999 | Internal Error
503 | Service Unavailable | 609 | Auth Server is down

### HTTP Response Headers

Parameter  | Description
--------- | -----------
totalACL | Total ACL availble for the filter.
firstPage | First Page number of the results for the filter.
prevPage | Previous Page number from current page.
currPage | Current page number, Input provided by IT Admin.
nextPage | Next Page number to current page.
lastPage | Last Page number of the results for the filter.
pageSize | Number of ACL Per page.

<aside class="success">
Remember — List ACLs API!
</aside>

## Create ACL

> HTTP Request Payload

```json
{
    "name" : "My List 1",
    "type" : "MSISDN", // Possible values example: MSISDN, IMSI, IMEI, etc.
    "searchKey": "MSISDN",
    "uuid": 2143567854321
}
```

> Success Response Payload

```http 
HTTP/1.1 200 Success
Content-Type: application/json
{
	"code" : 0,
	"msg" : "List created successfully",
    "id": 2345678909876543
}
```

Use this endpoint to create Access Control List using predfined List type.

### HTTP Request

`POST http://example.com/leap_gw/ussd/acl`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### Error Codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | 0 | List created successfully
400 | Bad Request | 935 | Invalid AC List name
400 | Bad Request | 936 | Minimum characters for list name atleast 5.
400 | Bad Request | 937 | Max length exceeds for list name
400 | Bad Request | 938 | Missing type
400 | Bad Request | 939 | Invalid List Type
400 | Bad Request | 940 | Missing Search KEY
400 | Bad Request | 941 | Invalid Search KEY
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
409 | Conflict | 942 | List name already exists
500 | Internal Server Error | 999 | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Create ACL API!
</aside>

## Update ACL

> HTTP Request Payload

```json
{
    "name" : "My List 12",
    "searchKey": "msisdn",
    "uuid": ************
}
```

> Success Response Payload

```http 
HTTP/1.1 200 Success
Content-Type: application/json
{
	"code" : 0,
	"msg" : "List updated successfully"
}
```

Use this endpoint to update the Access Control List properties like name and searchKey.

### HTTP Request

`POST http://example.com/leap_gw/ussd/acl/:listId/update`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### Error Codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | 0 | List updated successfully
400 | Bad Request | 935 | Invalid AC List name
400 | Bad Request | 936 | Minimum characters for list name atleast 5.
400 | Bad Request | 937 | Max length exceeds for list name
400 | Bad Request | 940 | Missing Search KEY
400 | Bad Request | 941 | Invalid Search KEY
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
409 | Conflict | 942 | List name already exists
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Update ACL API!
</aside>

## Delete ACL

> Success Response Payload

```http 
HTTP/1.1 200 Success
Content-Type: application/json
{
	"code" : 0,
	"msg" : "List deleted successfully"
}
```

> Failure Response Payload

```http 
HTTP/1.1 409 Conflict
Content-Type: application/json
{
	"code" : 944,
	"msg" : "List used by applications",
    "mappings": {
        "12345678": ["bwvee23", "qwer324"], // Application ID and array of module ID
        "1234563456": ["bwvewere", "rthyjy324"]
    }
}
```

Use this endpoint to delete the Access Control List from system.

### HTTP Request

`POST http://example.com/leap_gw/ussd/acl/:listId/delete`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### Error Codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | 0 | List deleted successfully
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 943 | List not found
409 | Conflict | 944 | List used by applications
500 | Internal Server Error | 999 | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Delete ACL API!
</aside>

## Upload ACL file

> HTTP Request Payload:

```http
Body > “form-data”
Key: “file”
Type: file
Value: File to be uploaded
```

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{ 
    "code": 0, 
    "msg": "File uploaded successful"
}
```	

Use this endpoint to upload the files(.csv files) for Access control list.

### HTTP Request

`POST http://example.com/leap_gw/ussd/acl/upload/:uuid`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | 0 | File uploaded successfully
400 | Bad Request | 945 | File name is too large.
400 | Bad Request | 946 | Malicious file uploaded by user.
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
406 | Not Acceptable | 947 | File extension not supported.
409 | Conflict | 948 | temp directory not found/permission.
413 | Payload Too Large | 949 | Maximum file size allowed in X mb.
429 | Too Many Requests | 950 | Minimum disksapce is there in System.
429 | Too Many Requests| 951 | Too many files uploaded in a given amount of time.
500 | Internal Server Error | 999 | Internal error while uploading the file
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Upload ACL file API!
</aside>

## Cancel list creation

> Success Response Payload:

```http
HTTP/1.1 200 Success
Content-Type: application/json
{ 
    "code": 0, 
    "msg": "Successfully deleted the files for :uuid"
}
```	

Use this endpoint to delete the uploaded files for :uuid generated.

### HTTP Request

`POST http://example.com/leap_gw/ussd/acl/cancel/:uuid/delete`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | 0 | Successfully deleted the files for :uuid
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
409 | Conflict | 953 | :uuid files cannot be deleted at this moment.
500 | Internal Server Error | 999 | Unable to delete the files/folder due to permission.
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Cancel list creation API!
</aside>

## Link ACL 

> HTTP Request Payload:

```json
[{
    "menu_item_id" : "String",
    "list_id": "uuid"
}]
```

> Success Response Payload:

```http
HTTP/1.1 200 Success
Content-Type: application/json
{ 
    "code": 0, 
    "msg": "Successfully linked acl list"
}
```	

Use this endpoint to link ACL (listid) to a Menu (menuId).

### HTTP Request

`POST http://example.com/leap_gw/ussd/acl/link/:appId/:mid`

<aside class="notice"><b>appId: </b>is application identifier</aside>
<aside class="notice"><b>mid: </b>is module identifier</aside>

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | 0 | Successfully linked acl list
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 963 | File not found
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 1020 | ACL Store is unreachable

<aside class="success">
Remember — Link ACL to Menu API!
</aside>

## Download Master/Error file

Use this endpoint to download the Master/Error file of :listId.

### HTTP Request

`GET http://example.com/leap_gw/ussd/acl/download/:type/:listId`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | 0 | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 943 | List not found.
404 | Not Found | 954 | List is empty
409 | Conflict | 956 | You cannot download file at this moment.
409 | Conflict | 957 | download link expired.
500 | Internal Server Error | 999 | Unable to delete the files/folder due to permission.
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Download Master/Error file API!
</aside>

# My Downloads

## List Downloadable files

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 200,
    "msg": "files listed successfully",
    "data": {
        "pageSize": 1,
        "totalFiles": 1,
        "totalPages": 1,
        "currentPage": 1,
        "list": [
            {
                "id": "1548840798135",
                "name": "ERROR_213456bwgf.csv",
                "createdAt": "2019-01-30 15:03:18",
                "expiryTime": "2019-04-13 22:57:25"
            }
        ]
    }
}
```

Use this endpoint to list the downloadable files in the system.

### HTTP Request

`GET http://example.com/leap_gw/md`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
token | null | A Search string.
page | 1 | Page number.
size | 10 | Number of records per page.
sortf | null | Name of the field on which to sorting to be applied. Possible values: name or createdAt or expiryTime
order | asc | Sorting order applied for above filed. Possible values: asc or desc

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 930 | Bad input for page field
400 | Bag Request | 931 | Bad input for size field
400 | Bag Request | 932 | Bad input for sortf field
400 | Bag Request | 933 | Bad input for order field
400 | Bag Request | 934 | Page does not exists
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 999 | Internal Error
503 | Service Unavailable | 609 | Auth Server is down

### HTTP Response Headers

Parameter  | Description
--------- | -----------
totalFiles | Total files availble for the filter.
firstPage | First Page number of the results for the filter.
prevPage | Previous Page number from current page.
currPage | Current page number, Input provided by IT Admin.
nextPage | Next Page number to current page.
lastPage | Last Page number of the results for the filter.
pageSize | Number of Files Per page.

<aside class="success">
Remember — List ACLs API!
</aside>

## Download the file

Use this endpoint to download th file from LEAP system.

### HTTP Request

`GET http://example.com/leap_gw/md/:id`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | 0 | 
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
404 | Not Found | 958 | File not found.
409 | Conflict | 957 | download link expired.
500 | Internal Server Error | 999 | Unable to delete the files/folder due to permission.
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Download the file API!
</aside>

# Reports Management

## MktAdmin Overview

> Success Response Payload: Summary Report

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": {
        "totalUsers": 9,
        "totalApps": 4
    }
}
```

> Success Response Payload: Top Used Apps Report

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": [
        {
            "appId": "app1",
            "appName": "Balance Transfer",
            "count": 4
        },
        {
            "appId": "app2",
            "appName": "FAF",
            "count": 3
        },
        {
            "appId": "app3",
            "appName": "SMS",
            "count": 1
        }
    ]
}
```

> Success Response Payload: Top Success Apps Report

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": [
        {
            "appId": "app1",
            "appName": "Balance Transfer",
            "count": 3
        },
        {
            "appId": "app2",
            "appName": "FAF",
            "count": 1
        },
        {
            "appId": "app3",
            "appName": "SMS",
            "count": 1
        }
    ]
}
```

> Success Response Payload: Top Failed Apps Report

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": [
        {
            "appId": "app2",
            "appName": "FAF",
            "count": 2
        },
        {
            "appId": "app1",
            "appName": "Balance Transfer",
            "count": 1
        }
    ]
}
```

> Success Response Payload: Top 20 Apps Report

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": [
        {
            "appId": "app1",
            "appName": "Balance Transfer",
            "count": 4
        },
        {
            "appId": "app2",
            "appName": "FAF",
            "count": 3
        },
        {
            "appId": "app3",
            "appName": "SMS",
            "count": 1
        }
    ]
}
```

Use this endpoint to Overview page details for Marketing Admin user dashboard.

### HTTP Request

`GET http://example.com/leap_gw/reports/overview/:reportType`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
reportType | null | Please refer bellow table for values
startTime | null | This parameter indicates start time to fetch overview report
endTime | null | This parameter indicates end time to fetch overview report

reportType | Description
--------- | -----------
summary | This report type gives the overview summary report
topusedapps | This report type gives the overview summary report
topsuccapps | This report type gives the overview top success apps report
topfailapps | This report type gives the overview top failed apps report
top20apps | This report type gives the overview top 20 apps report

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bad Request | 783 | invalid report type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 780 | Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable
503 | Service Unavailable | 781 | Elastic search error
503 | Service Unavailable | 782 | Elastic search empty data

<aside class="success">
Remember — MktAdmin Overview API!
</aside>

## TOP 20 Apps

> Success Response Payload: Top 20 Apps With Highest Users

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": [
        {
            "appId": "app3",
            "appName": "SMS",
            "count": 1
        },
        {
            "appId": "app1",
            "appName": "Balance Transfer",
            "count": 4
        },
        {
            "appId": "app2",
            "appName": "FAF",
            "count": 3
        }
    ]
}
```

> Success Response Payload: Top 20 Apps With Highest Success

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": [
        {
            "appId": "app3",
            "appName": "SMS",
            "count": 1
        },
        {
            "appId": "app1",
            "appName": "Balance Transfer",
            "count": 3
        },
        {
            "appId": "app2",
            "appName": "FAF",
            "count": 1
        }
    ]
}
```

> Success Response Payload: Top 20 Apps With Highest Transactions

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": [
        {
            "appId": "app3",
            "appName": "SMS",
            "count": 1
        },
        {
            "appId": "app1",
            "appName": "Balance Transfer",
            "count": 4
        },
        {
            "appId": "app2",
            "appName": "FAF",
            "count": 3
        }
    ]
}
```

Use this endpoint to list the Top 20 apps.

### HTTP Request

`GET http://example.com/leap_gw/reports/top20apps/:reportType`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
reportType | null | Please refer bellow table for values
startTime | 24 back hours time | This parameter indicates start time to fetch overview report
endTime | current system time | This parameter indicates end time to fetch overview report

reportType | Description
--------- | -----------
users | This report type gives the Top 20 Applications with highest number of users(Sorted by latest launched)
success | This report type gives the Top 20 Applications with highest number of success(Sorted by latest launched)
total | This report type gives the Top 20 Applications with highest number of transactions(Sorted by latest launched)

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - |
400 | Bad Request | 783 | invalid report type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 780 | Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable
503 | Service Unavailable | 781 | Elastic search error
503 | Service Unavailable | 782 | Elastic search empty data

<aside class="success">
Remember — TOP 20 Apps API!
</aside>

## Application Wise Stats

> Success Response Payload: AppStats Module Wise Transactions

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": {
        "1528405200000": {
            "0.1": 2,
            "12.11": 2,
            "3.1": 1
        },
        "1528653600000": {
            "0.1": 1,
            "3.7": 1,
            "6.11": 1
        },
        "1528696800000": {
            "0.1": 1,
            "3.1": 1,
            "5.1": 1
        }
    }
}
```

> Success Response Payload: AppStats Active Users

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": {
        "1528405200000": 2,
        "1528653600000": 1,
        "1528696800000": 1
    }
}
```

> Success Response Payload: AppStats Total Transactions

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
    "code": 0,
    "msg": {
        "1528405200000": {
            "total": 2,
            "success": 2,
            "failure": 0
        },
        "1528653600000": {
            "total": 1,
            "success": 1,
            "failure": 0
        },
        "1528696800000": {
            "total": 1,
            "success": 0,
            "failure": 1
        }
    }
}
```

> Success Response Payload: AppStats Average Response Time

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": {
        "1528405200000": "3821017517500.00",
        "1528653600000": "4585966221000.00",
        "1528696800000": "4586095821000.00"
    }
}
```

Use this endpoint to get the reports for selected application.

### HTTP Request

`GET http://example.com/leap_gw/reports/appstats/:reportType`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
reportType | null | Please refer bellow table for values
startTime | 24 back hours time | This parameter indicates start time to fetch overview report
endTime | current system time | This parameter indicates end time to fetch overview report
appId | This parameter indicates the selected Application id and this parameter is mandatory
interval | This parameter indicates the interval to show the data

reportType | Description
--------- | -----------
modulewisetrans | This report type gives the Total transactions for the selected app grouped by Interfaces.
activeusers | This report type gives the total active users foe the selected App
apptotaltrans | This report type gives the total transaction for the selected App
appsavgres | This report type gives the average response time for the selected App
apperrorcodes | This report type gives top 10 status codes count for the selected App

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - |
400 | Bad Request | 783 | invalid report type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 780 | Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable
503 | Service Unavailable | 781 | Elastic search error
503 | Service Unavailable | 782 | Elastic search empty data

<aside class="success">
Remember — Application Wise Stats API!
</aside>

## Interface Wise Stats

> Success Response Payload: Interface Total Transactions

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": {
        "1528398000000": {
            "total":1,
            "success": 1,
            "fail": 0
        },
        "1528405200000": {
            "total":3,
            "success": 2,
            "fail": 1
        },
        "1528416000000": {
            "total":1,
            "success": 1,
            "fail": 0
        },
        "1528653600000": {
            "total":1,
            "success": 1,
            "fail": 0
        }
    }
}
```

> Success Response Payload: Interface Total Apps

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 0,
    "msg": {
        "1528399800000": 1,
        "1528407000000": 2,
        "1528414200000": 1,
        "1528655400000": 1
    }
}
```

Use this endpoint to get the reports for selected interface.

### HTTP Request

`GET http://example.com/leap_gw/reports/interfacestats/:reportType`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
reportType | null | Please refer bellow table for values
startTime | 24 back hours time | This parameter indicates start time to fetch report
endTime | current system time | This parameter indicates end time to fetch report
moduleName | This parameter indicates the selected modulename and this parameter is mandatory
interval | This parameter indicates the interval to show the data

reportType | Description
--------- | -----------
interfaceavgtrans | This report type gives the average transactions per second
interfaceavgres | This report type gives the average response time
interfacetotaltrans | This report type gives the total transaction for the selected Interface
interfacetotalapps | This report type gives the total applications the selected Interface

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - |
400 | Bad Request | 783 | invalid report type
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | 780 | Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable
503 | Service Unavailable | 781 | Elastic search error
503 | Service Unavailable | 782 | Elastic search empty data

<aside class="success">
Remember — Interface Wise Stats API!
</aside>

## Audit Trails

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
[
    {
        "t": "2018-07-05T19:55:36.000Z",
        "usr": "TeamLeap",
        "mtd": "PATCH",
        "aid": "1538395620058",
        "an": "FriendsAndFamily",
        "url": "/leap_gw/api/1538395620058/assign",
        "action": "assigned",
        "desc": "TeamLeap assigned FriendsAndFamily"
    },
    {
        "t": "2018-07-05T18:55:36.000Z",
        "usr": "TeamLeap",
        "mtd": "POST",
        "aid": "1538395620058",
        "an": "FriendsAndFamily",
        "url": "/leap_gw/workflow/1538395620058/ApproveForStaging",
        "action": "approved for staging",
        "desc": "TeamLeap approved for staging FriendsAndFamily"
    },
    {
        "t": "2018-07-05T18:15:36.000Z",
        "usr": "TeamLeap",
        "mtd": "POST",
        "aid": "1538395620058",
        "an": "FriendsAndFamily",
        "url": "/leap_gw/workflow/1538395620058/LAUNCH",
        "action": "launched",
        "desc": "TeamLeap launched FriendsAndFamily"
    },
    {
        "t": "2018-07-05T18:05:36.000Z",
        "usr": "TeamLeap",
        "mtd": "POST",
        "aid": "1538395620058",
        "an": "FriendsAndFamily",
        "url": "/leap_gw/workflow/1538395620058/RETIRE",
        "action": "retired",
        "desc": "TeamLeap retired FriendsAndFamily"
    },
    {
        "t": "2018-07-05T17:55:36.000Z",
        "usr": "TeamLeap",
        "mtd": "POST",
        "aid": "1538395620058",
        "an": "FriendsAndFamily",
        "url": "/leap_gw/workflow/1538395620058/ApproveForStaging",
        "action": "approved for staging",
        "desc": "TeamLeap approved for staging FriendsAndFamily"
    },
    {
        "t": "2018-07-05T17:45:36.000Z",
        "usr": "TeamLeap",
        "mtd": "POST",
        "aid": "1538395620058",
        "an": "FriendsAndFamily",
        "url": "/leap_gw/workflow/1538395620058/STAGE",
        "action": "staged",
        "desc": "TeamLeap staged FriendsAndFamily"
    },
    {
        "t": "2018-07-05T16:55:36.000Z",
        "usr": "TeamLeap",
        "mtd": "POST",
        "aid": "1538395620058",
        "an": "FriendsAndFamily",
        "url": "/leap_gw/workflow/1538395620058/WITHDRAW",
        "action": "withdrew",
        "desc": "TeamLeap withdrew FriendsAndFamily"
    },
    {
        "t": "2018-07-05T16:45:36.000Z",
        "usr": "TeamLeap",
        "mtd": "POST",
        "aid": "1538395620058",
        "an": "FriendsAndFamily",
        "url": "/leap_gw/workflow/1538395620058/REJECT",
        "action": "rejected",
        "desc": "TeamLeap rejected FriendsAndFamily"
    },
    {
        "t": "2018-07-05T14:58:36.000Z",
        "usr": "TeamLeap",
        "mtd": "POST",
        "aid": "1538395620058",
        "an": "FriendsAndFamily",
        "url": "/leap_gw/workflow/1538395620058/SUBMIT",
        "action": "submitted",
        "desc": "TeamLeap submitted FriendsAndFamily"
    },
    {
        "t": "2018-07-05T14:55:36.000Z",
        "usr": "root",
        "mtd": "POST",
        "aid": "10.0",
        "an": "PluginName",
        "url": "/leap_gw/plugins/soap/10.0/deactivate",
        "action": "deactivated",
        "desc": "root deactivated PluginName"
    },
    {
        "t": "2018-07-05T14:50:36.000Z",
        "usr": "root",
        "mtd": "POST",
        "aid": "7",
        "an": "LeapDemo2",
        "url": "/leap_gw/users/updateUser/7/update",
        "action": "updated",
        "desc": "root updated LeapDemo2"
    },
    {
        "t": "2018-07-05T13:55:36.000Z",
        "usr": "1",
        "mtd": "POST",
        "aid": "2",
        "an": "LeapDemo2",
        "url": "/leap_gw/users/createUserWithoutPassword",
        "action": "created",
        "desc": "1 created LeapDemo2"
    },
    {
        "t": "2018-07-05T13:55:36.000Z",
        "usr": "root",
        "mtd": "POST",
        "aid": "2",
        "an": "LeapDemo2",
        "url": "/leap_gw/users/deactivate/2",
        "action": "deactivated",
        "desc": "root deactivated LeapDemo2"
    },
    {
        "t": "2018-07-05T13:55:36.000Z",
        "usr": "LeapDemo2",
        "mtd": "POST",
        "aid": "1528395620058",
        "an": "JustForGags",
        "url": "/leap_gw/apps",
        "action": "created",
        "desc": "LeapDemo2 created JustForGags"
    },
    {
        "t": "2018-07-05T13:55:36.000Z",
        "usr": "root",
        "mtd": "POST",
        "aid": "10.0",
        "an": "PluginName",
        "url": "/leap_gw/plugins/soap",
        "action": "uploaded",
        "desc": "root uploaded PluginName"
    },
    {
        "t": "2018-07-05T13:55:36.000Z",
        "usr": "root",
        "mtd": "POST",
        "aid": "3",
        "an": "InvalidUser",
        "url": "/leap_gw/users/deleteUser/3/purge",
        "action": "deleted",
        "desc": "root deleted InvalidUser"
    },
    {
        "t": "2018-07-05T13:55:36.000Z",
        "usr": "root",
        "mtd": "POST",
        "aid": "2",
        "an": "LeapDemo2",
        "url": "/leap_gw/users/createUser",
        "action": "created",
        "desc": "root created LeapDemo2"
    },
    {
        "t": "2018-07-05T13:55:36.000Z",
        "usr": "root",
        "mtd": "POST",
        "aid": "",
        "an": "",
        "url": "/leap_gw/users/authenticate",
        "action": "logged in.",
        "desc": "root logged in. "
    },

]
```

Use this endpoint to list the audit trails in the system.

### HTTP Request

`GET http://example.com/leap_gw/reports/auditTrail`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
token | null | A Search string.
page | 1 | Page number.
size | 10 | Number of records per page.
sortf | null | Name of the field on which to sorting to be applied. Possible values: username or email or createdAt or updatedAt
order | asc | Sorting order applied for above filed. Possible values: asc or desc
status | null | Filter the users by user activation status. Choose from active or inactive or both(comma separated).
startTime | null | Time (in miliseconds) from which user is to be listed.
endTime | null | Time (in milliseconds) to which user is to be listed.
user_filter | null | the username to get the audit trail for a particular user.

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 603 | Length of the firstname exceeded
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Audit Trails API!
</aside>

## ITAdmin Overview

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "totalUsers": 4,
    "totalApps": 7,
    "appsStatus": {
        "Draft": 7,
        "ApprovalPending": 0,
        "ScheduledforLaunch": 0,
        "Staged": 0,
        "Launched": 0,
        "Retired": 0,
        "Deleted": 0
    },
    "unassignedApps": 5,
    "assignedApps": 2
}
```

Use this endpoint to get the details for IT admin dashboard.

### HTTP Request

`GET http://example.com/leap_gw/dashboard/stats`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 603 | Length of the firstname exceeded
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — ITAdmin Overview API!
</aside>

##  Dashboard Apps Listing

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "code": 200,
    "msg": "Apps listed successfully",
    "data": {
        "pageSize": 7,
        "totalApps": 7,
        "totalPages": 1,
        "currentPage": 1,
        "appList": [
            {
                "id": "1528395620058",
                "name": "JustForGags222",
                "desc": "fkernferkfnerkf",
                "status": "0",
                "owner": "Unknown User",
                "createdBy": "root ",
                "modifiedBy": 1,
                "createdAt": "2018-06-07T06:20:20.000Z",
                "updatedAt": "2018-06-07T18:20:20.000Z"
            },
            {
                "id": "1528395884601",
                "name": "JustForGags22256",
                "desc": "fkernferkfnerkf",
                "status": "0",
                "owner": "Unknown User",
                "createdBy": "root ",
                "modifiedBy": 1,
                "createdAt": "2018-06-07T06:24:44.000Z",
                "updatedAt": "2018-06-07T18:24:44.000Z"
            },
            {
                "id": "1528395899691",
                "name": "callMeback",
                "desc": "fkernferkfnerkf",
                "status": "4",
                "owner": "Unassigned",
                "createdBy": "root ",
                "modifiedBy": 1,
                "createdAt": "2018-06-07T06:24:59.000Z",
                "updatedAt": "2018-06-11T14:37:33.000Z"
            },
            {
                "id": "1528395907967",
                "name": "callMeback67788",
                "desc": "fkernferkfnerkf",
                "status": "2",
                "owner": "DefaultAppDev User",
                "createdBy": "root ",
                "modifiedBy": 1,
                "createdAt": "2018-06-07T06:25:07.000Z",
                "updatedAt": "2018-06-07T18:25:07.000Z"
            },
            {
                "id": "1528395925838",
                "name": "friendsAndFamily",
                "desc": "fkernferkfnerkf",
                "status": "0",
                "owner": "Unknown User",
                "createdBy": "root ",
                "modifiedBy": 1,
                "createdAt": "2018-06-07T06:25:25.000Z",
                "updatedAt": "2018-06-07T18:25:25.000Z"
            },
            {
                "id": "1528395986628",
                "name": "BalanceEnquiry",
                "desc": "fkernferkfnerkf",
                "status": "0",
                "owner": "Unknown User",
                "createdBy": "root ",
                "modifiedBy": 1,
                "createdAt": "2018-06-07T06:26:26.000Z",
                "updatedAt": "2018-06-07T18:26:26.000Z"
            },
            {
                "id": "1528396026063",
                "name": "LanguageChange",
                "desc": "fkernferkfnerkf",
                "status": "4",
                "owner": "Unknown User",
                "createdBy": "root ",
                "modifiedBy": 1,
                "createdAt": "2018-06-07T06:27:06.000Z",
                "updatedAt": "2018-06-07T18:27:06.000Z"
            }
        ]
    }
}
```

Use this endpoint to list all the apps in the platform with required details for IT admin.

### HTTP Request

`GET http://example.com/leap_gw/dashboard/`

### HTTP Request Header

Key | Value| Description
--------- | ------- | -----------
Authorization | bearer {JWT Token} | Authorization token

### HTTP Request Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
token | null | A Search string.
page | 1 | Page number.
size | 10 | Number of records per page.
sortf | null | Name of the field on which to sorting to be applied. Possible values: username or email or createdAt or updatedAt
order | asc | Sorting order applied for above filed. Possible values: asc or desc
startTime | null | Time (in miliseconds) from which app is to be listed(filtered by created date).
endTime | null | Time (in milliseconds) to which useappr is to be listed(filtered by created date).

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
400 | Bag Request | 603 | Length of the firstname exceeded
401 | Unauthorized | 605 | Authorization header is missing
401 | Unauthorized | 606 | JWT invalid
401 | Unauthorized | 607 | JWT expired
403 | Forbidden | 623 | JWT blacklisted
403 | Forbidden | 625 | Resource Permission Denied
500 | Internal Server Error | - | Any internal or unhandled error
503 | Service Unavailable | 609 | Auth Server is down

<aside class="success">
Remember — Dashboard Apps Listing API!
</aside>

# Others

## List Config APIs

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "/leap_gw/configapi/passwordstrength": "Get the regular expression to validate the Strength of Password",
    "/leap_gw/configapi/appstates": "Get App Status Code information",
    "/leap_gw/configapi/macros": "Get list of MACROS supported in platform",
    "/leap_gw/configapi/operators": "Get list of Operators supported in platform",
    "/leap_gw/configapi/languages": "Get list of Languges supported in platform"
}
```

Use this endpoint to list the Config APIS in the system.

### HTTP Request

`GET http://example.com/leap_gw/configapi`

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — List Config APIs API!
</aside>

## Password Strength

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "description": "The password string must contain at least 1 lowercase alphabetical character, must contain at least 1 uppercase alphabetical character, must contain at least 1 numeric character, must contain at least one special character, but we are escaping reserved RegEx characters to avoid conflict, The string must be 8 characters or longer",
    "expression": "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})"
}
```

Use this endpoint to get the regular expression to validate the Strength of Password.

### HTTP Request

`GET http://example.com/leap_gw/configapi/passwordstrength`

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Password Strength API!
</aside>

## Application States

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "Draft": {
        "code": 0,
        "description": "Draft"
    },
    "ApprovalPending": {
        "code": 1,
        "description": "Approval Pending"
    },
    "ApprovedForStaging": {
        "code": 2,
        "description": "Approved for Staging"
    },
    "ScheduledforStaging": {
        "code": 3,
        "description": "Scheduled for Staging"
    },
    "ScheduledforLaunch": {
        "code": 4,
        "description": "Scheduled for Launch"
    },
    "Staged": {
        "code": 5,
        "description": "Staged"
    },
    "Launched": {
        "code": 6,
        "description": "Launched"
    },
    "Retired": {
        "code": 7,
        "description": "Retired"
    }
}
```

Use this endpoint to get Application Status Code information.

### HTTP Request

`GET http://example.com/leap_gw/configapi/appstates`

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — Application States API!
</aside>

## List Macros

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "_toLowerCase": {
        "description": "Converts uppercase string into lowercase",
        "args": {
            "input_string": "The input string"
        },
        "functor": "toLowerCase",
        "this": true
    },
    "_toUpperCase": {
        "description": "Converts lowercase string into uppercase",
        "args": {
            "input_string": "The input string"
        },
        "functor": "toUpperCase",
        "this": true
    },
    "_substring": {
        "description": "Extracts the characters from a string between two specified indices and returns a new substring",
        "args": {
            "input_string": "Input string",
            "start_index": "The starting index of the substring.First character is at index 0.",
            "end_index": "The position (up to, but not including) where to end the extraction."
        },
        "functor": "substring",
        "this": true
    },
    "_trim": {
        "description": "Removes whitespaces/set of characters from both the sides of the string",
        "args": {
            "input_string": "The input string",
            "left_substring": "Set of characters to be deleted from the left",
            "right_substring": "Set of characters to be deleted from the right"
        },
        "functor": "trim",
        "this": true
    },
    "_trimLeft": {
        "description": "Removes whitespaces from the left side of the string",
        "args": {
            "input_string": "The input string",
            "left_substring": "Set of characters to be deleted from the left"
        },
        "functor": "trimLeft",
        "this": true
    },
    "_trimRight": {
        "description": "Removes whitespaces from the right side of the string",
        "args": {
            "input_string": "The input string",
            "right_substring": "Set of characters to be deleted from the right"
        },
        "functor": "trimRight",
        "this": true
    },
    "_stringify": {
        "description": "Converts an input/object to string",
        "args": {
            "input": "The input to be converted into string"
        },
        "functor": "toString",
        "this": true
    },
    "_toNumber": {
        "description": "Converts an input/object to a number",
        "args": {
            "input": "The input string/object to be converted into number"
        },
        "functor": "Number",
        "this": false
    },
    "_length": {
        "description": "Returns the length of the given string",
        "args": {
            "input_string": "The input string"
        },
        "functor": "length",
        "this": true
    },
    "_isEmpty": {
        "description": "Checks if the string is empty. Returns true if the length of the string is zero,otherwise false.",
        "args": {
            "input_string": "The input string"
        },
        "functor": "isEmpty",
        "this": true
    },
    "_isAlpha": {
        "description": "Checks whether a string contains only alphabets or not",
        "args": {
            "input_string": "The input string"
        },
        "functor": "isAlpha",
        "this": true
    },
    "_isNumeric": {
        "description": "Checks whether a string contains only number or not",
        "args": {
            "input_string": "The input string"
        },
        "functor": "isNumber",
        "this": true
    },
    "_isAlphanumeric": {
        "description": "Checks whether a string contains only alphabet or number",
        "args": {
            "input_string": "The input string"
        },
        "functor": "isAlphanumeric",
        "this": true
    },
    "_isDigit": {
        "description": "Checks whether a character is a digit",
        "args": {
            "input_string": "The input character"
        },
        "functor": "isDigit",
        "this": true
    },
    "_isLower": {
        "description": "Checks whether a passed string is in lowercase letters or not",
        "args": {
            "input_string": "The input string"
        },
        "functor": "isLower",
        "this": true
    },
    "_isUpper": {
        "description": "Checks whether a passed string is in uppercase letters or not",
        "args": {
            "input_string": "The input string"
        },
        "functor": "isUpper",
        "this": true
    },
    "_startsWith": {
        "description": "Checks whether a string starts with a given substring",
        "args": {
            "input_string": "The input string",
            "start_substring": "The substring with which string should start"
        },
        "functor": "startsWith",
        "this": true
    },
    "_endsWith": {
        "description": "Checks whether a string ends with a given substring",
        "args": {
            "input_string": "The input string",
            "end_substring": "The substring with which string should end"
        },
        "functor": "endsWith",
        "this": true
    },
    "_now": {
        "description": "Returns the number of milliseconds since 1970/01/01",
        "functor": "now",
        "this": "Date"
    },
    "_uuid": {
        "description": "Returns the transactionId",
        "functor": "getUuid",
        "this": false
    },
    "_getDate": {
        "description": "Returns the day of the month(from 0-31)",
        "args": {
            "dateString": "The timestamp from which date needs to be extracted",
            "milliseconds": "Time in milliseconds for which date needs to be extracted"
        },
        "functor": "getDate",
        "this": true
    },
    "_getDay": {
        "description": "Returns the day of the week(from 0-6)",
        "args": {
            "dateString": "The timestamp from which date needs to be extracted",
            "milliseconds": "Time in milliseconds for which date needs to be extracted"
        },
        "functor": "getDay",
        "this": true
    },
    "_getFullYear": {
        "description": "Returns the year,according to the local time or given timestamp",
        "args": {
            "dateString": "The timestamp from which date needs to be extracted",
            "milliseconds": "Time in milliseconds for which date needs to be extracted"
        },
        "functor": "getFullYear",
        "this": true
    },
    "_getHours": {
        "description": "Returns the hour(from 0-23), according to the local time or given timestamp",
        "args": {
            "dateString": "The timestamp from which date needs to be extracted",
            "milliseconds": "Time in milliseconds for which date needs to be extracted"
        },
        "functor": "getHours",
        "this": true
    },
    "_getMinutes": {
        "description": "Returns the minute(from 0-59), according to the local time or given timestamp",
        "args": {
            "dateString": "The timestamp from which date needs to be extracted",
            "milliseconds": "Time in milliseconds for which date needs to be extracted"
        },
        "functor": "getMinutes",
        "this": true
    },
    "_getMonth": {
        "description": "Returns the month(from 0-11), according to the local time or given timestamp ",
        "args": {
            "dateString": "The timestamp from which date needs to be extracted",
            "milliseconds": "Time in milliseconds for which date needs to be extracted"
        },
        "functor": "getMonth",
        "this": true
    },
    "_getSeconds": {
        "description": "Returns the second(from 0-59), according to the local time or given timestamp",
        "args": {
            "dateString": "The timestamp from which date needs to be extracted",
            "milliseconds": "Time in milliseconds for which date needs to be extracted"
        },
        "functor": "getSeconds",
        "this": true
    },
    "_getMilliseconds": {
        "description": "Returns the milliseconds(from 0-999), according to the local time or given timestamp",
        "args": {
            "dateString": "The timestamp from which date needs to be extracted",
            "milliseconds": "Time in milliseconds for which date needs to be extracted"
        },
        "functor": "getMilliseconds",
        "this": true
    },
    "_getTime": {
        "description": "Returns the number of milliseconds since 1970/01/01",
        "args": {
            "dateString": "The timestamp from which date needs to be extracted",
            "milliseconds": "Time in milliseconds for which date needs to be extracted"
        },
        "functor": "getTime",
        "this": true
    },
    "_add": {
        "description": "Adds the two given numbers",
        "args": {
            "operand1": "First operand to add",
            "operand2": "Second operand to add"
        },
        "functor": "add",
        "this": false
    },
    "_substract": {
        "description": "Subtracts the two given numbers",
        "args": {
            "operand1": "First operand to subtract",
            "operand2": "Second operand to subtract"
        },
        "functor": "subtract",
        "this": false
    },
    "_multiply": {
        "description": "Multiplies the two given numbers",
        "args": {
            "operand1": "First operand to multiply",
            "operand2": "Second operand to multiply"
        },
        "functor": "multiply",
        "this": false
    },
    "_divide": {
        "description": "Divides the two given numbers",
        "args": {
            "operand1": "First operand to divide",
            "operand2": "Second operand to divide"
        },
        "functor": "divide",
        "this": false
    },
    "_isInRange": {
        "description": "Checks whether the given number is in the given range or not.",
        "args": {
            "Input": "Number to check the range for.",
            "min": "Minimum value in the range",
            "max": "Maximun value in the range"
        },
        "functor": "checkRange",
        "this": false
    },
    "_getTimestamp": {
        "description": "Returns the current timestamp",
        "functor": "getTimestamp",
        "this": true
    },
    "_msisdnValidator": {
        "description": "Validates whether an MSISDN is correct or not",
        "args": {
            "MSISDN": "String consisting of the MSISDN number"
        },
        "functor": "msisdnValidator",
        "this": true
    },
    "_parseDedicatedAccount": {
        "description": "Parse the Dedicated account information Array",
        "args": {
            "dedicatedAccountInformation": "Array of dedicatedAccountInformation",
            "dedicatedAccountID": "dedicatedAccountID for filter"
        },
        "functor": "parseDedicatedAccount",
        "this": true
    },
    "_parseDedicatedOfferID": {
        "description": "Parse the Dedicated account information for offerID",
        "args": {
            "dedicatedAccountInformation": "Array of dedicatedAccountInformation",
            "dedicatedOfferID": "dedicatedOfferID for filter"
        },
        "functor": "parseDedicatedOfferID",
        "this": true
    },
    "_parseOfferInformationList": {
        "description": "Parse the Offer information Array",
        "args": {
            "offerInformationList": "Array of offerInformation",
            "offerID": "offerID for filter"
        },
        "functor": "parseOfferInformationList",
        "this": true
    },
    "_checkOfferIDExists": {
        "description": "Parse the OfferList and Validate OfferID exists in list",
        "args": {
            "offerInformationList": "Array of offerInformation",
            "offerID": "offerID for filter"
        },
        "functor": "checkOfferIDExists",
        "this": true
    },
    "_contains": {
        "description": "To check wether the given given element exists in the given lists",
        "args": {
            "element": "element to check",
            "elementsList": "elements list"
        },
        "functor": "contains",
        "this": true
    },
    "_date2customformat": {
        "description": "Date custom formatter",
        "args": {
            "date": "Date string",
            "format": "Date format"
        },
        "functor": "date2customformat",
        "this": true
    },
    "_regexValidator": {
        "description": "Regex Validator",
        "args": {
            "regex": "Regex string",
            "str": "String for validation against regex"
        },
        "functor": "regexValidator",
        "this": true
    },
    "_xml2json": {
        "description": "XML to JSON convertor",
        "args": {
            "str": "xml string buffer"
        },
        "functor": "xml2json",
        "this": true
    },
    "_accumlateDAValue": {
        "description": "Looping the dedicatedAccountValue for offerID",
        "args": {
            "dedicatedAccountInformation": "Array of dedicatedAccountInformation",
            "OfferID": "OfferID for filter"
        },
        "functor": "_accumlateDAValue",
        "this": true
    },
    "_getNoOfDaysBetween": {
        "description": "Returns the day of days from the current system time",
        "args": {
            "dateString": "The timestamp from which date needs to be extracted"
        },
        "functor": "_getNoOfDaysBetween",
        "this": true
    }
}
```

Use this endpoint to list of MACROS supported in platform.

### HTTP Request

`GET http://example.com/leap_gw/configapi/macros`

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — List Macros API!
</aside>

## List Operators

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "eq": {
        "op": "eq",
        "name": "Equals",
        "description": "First operand Equal to Second operand"
    },
    "ne": {
        "op": "ne",
        "name": "Not Equals",
        "description": "First operand Not Equal to Second operand"
    },
    "gte": {
        "op": "gte",
        "name": "Greater Than or Equals",
        "description": "First operand is greater than or equal to Second operand"
    },
    "gt": {
        "op": "gt",
        "name": "Greater Than",
        "description": "First operand is greater than to Second operand"
    },
    "lte": {
        "op": "lte",
        "name": "Less Than or Equals",
        "description": "First operand is lesser than or equal to Second operand"
    },
    "lt": {
        "op": "lt",
        "name": "Less Than",
        "description": "First operand is lesser than to Second operand"
    },
    "contains": {
        "op": "contains",
        "name": "Contains",
        "description": "Left operand contains in Right operand"
    },
    "ncontains": {
        "op": "ncontains",
        "name": "Does Not Contains",
        "description": "Left operand does not contains in Right operand"
    },
    "and": {
        "op": "and",
        "name": "And",
        "description": "Left operand and Right operand"
    },
    "or": {
        "op": "or",
        "name": "Or",
        "description": "Left operand or Right operand"
    }
}
```

Use this endpoint to list of Operators supported in platform.

### HTTP Request

`GET http://example.com/leap_gw/configapi/operators`

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — List Operators API!
</aside>

## List Languages

> Success Response Payload:

```http
HTTP/1.1 200 OK
Content-Type: application/json
{
    "te": {
        "locale": "te",
        "description": "Telugu"
    },
    "ta": {
        "locale": "ta",
        "description": "Tamil"
    },
    "hi": {
        "locale": "hi",
        "description": "Hindi"
    },
    "kn": {
        "locale": "kn",
        "description": "Kannada"
    },
    "en_US": {
        "locale": "en_US",
        "description": "English - United States"
    },
    "en": {
        "locale": "en",
        "description": "English"
    },
    "en_IN": {
        "locale": "en_IN",
        "description": "English - India"
    }
}
```

Use this endpoint to list of Languages supported in platform.

### HTTP Request

`GET http://example.com/leap_gw/configapi/languages`

### HTTP Response Error codes

Code | Status | Internal Code |Description
--------- | ------- | ------- | -----------
200 | Success | - | 
500 | Internal Server Error | 701 | Application Management Internal Error
503 | Service Unavailable | 609 | Auth Server is down
503 | Service Unavailable | 702 | App Store not reachable

<aside class="success">
Remember — List Languages API!
</aside>
