--
-- <PERSON><PERSON><PERSON><PERSON>omviva-GNOC-LEAP-MIB.my
-- MIB generated by Comviva Technologies Limited.
-- Wednesday, September19, 2018 at 05:06:47
--

	Ma<PERSON><PERSON>Comviva-GNOC-LEAP-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			comviva, alarmObjects			
				FROM Comviva			
			Integer32, OBJECT-TYPE, OBJECT-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI;
	
	
--
-- Node definitions
--
	
		-- *******.4.1.19338.0.1
		exitStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmObjects 1 }

		
		-- *******.4.1.19338.0.2
		alarmString OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmObjects 2 }

		
		-- *******.4.1.19338.0.3
		misc OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmObjects 3 }

		
		-- *******.4.1.19338.0.4
		alarmType OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmObjects 4 }

		
		-- *******.4.1.19338.0.5
		alarmStatus OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmObjects 5 }
		
		-- *******.4.1.19338.126
		leap OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Leap Product"
			::= { comviva 126 }

		
		-- *******.4.1.19338.126.1
		apigw OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"API GW"
			::= { leap 1 }


		
		-- *******.4.1.19338.126.1.1
		apiGw NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"API GW is up and running"
			::= { apigw 1 }

		
		-- *******.4.1.19338.126.1.2
		apigwConfigserverConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"APIGW-Config Server connectivity"
			::= { apigw 2 }

		
		-- *******.4.1.19338.126.1.3
		apigwAppStoreConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"APIGW-AppStore DB connection"
			::= { apigw 3 }

		
		-- *******.4.1.19338.126.1.4
		apigwAuthserverConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"APIGW-AuthServer connection "
			::= { apigw 4 }

		
		-- *******.4.1.19338.126.1.5
		apigwLoginSessionstoreConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"APIGW-Login Session store connection"
			::= { apigw 5 }

		
		-- *******.4.1.19338.126.1.6
		apigwPmserverConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"APIGW-PMFSYNC-Server connection"
			::= { apigw 6 }
		
		-- *******.4.1.19338.126.2
		appengine OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"APP Engine"
			::= { leap 2 }


		
		-- *******.4.1.19338.126.2.1
		appEngine NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"APP-ENGINE is up and running"
			::= { appengine 1 }

		
		-- *******.4.1.19338.126.2.2
		appEngineConfigserverConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"AppExec-Config Server connection"
			::= { appengine 2 }

		
		-- *******.4.1.19338.126.2.3
		appEngineAppStoreConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"AppExec-AppStore DB connection"
			::= { appengine 3 }

		
		-- *******.4.1.19338.126.2.4
		appEngineSessionstoreConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"AppExec-Session store connection"
			::= { appengine 4 }

		
		-- *******.4.1.19338.126.2.5
		appEnginePmserverConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"AppExec-PMFSYNC-Server connection"
			::= { appengine 5 }

		
		-- *******.4.1.19338.126.2.6
		appEngineCdrqueueConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"AppExec-logQueue connection"
			::= { appengine 6 }

		
		-- *******.4.1.19338.126.2.7
		appEngineTpsqueueConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"AppExec-TPSQueue connection"
			::= { appengine 7 }
		
		-- *******.4.1.19338.126.3
		pmfsync OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"PM FSYNC"
			::= { leap 3 }


		
		-- *******.4.1.19338.126.3.1
		pmFsync NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"PM-FSYNC is up and running"
			::= { pmfsync 1 }

		
		-- *******.4.1.19338.126.3.2
		pmfsyncConfigserverConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"PMSERV-Config Server connection"
			::= { pmfsync 2 }

		
		-- *******.4.1.19338.126.3.3
		pmfsyncPmstoreConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"PMSERV-PluginStore DB connection"
			::= { pmfsync 3 }

		
		-- *******.4.1.19338.126.3.4
		pmfsyncEventqConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"PMSERV-Event Queue connection"
			::= { pmfsync 4 }

		
		-- *******.4.1.19338.126.3.5
		pmcliEventqConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"PMCLI-Event Queue connection"
			::= { pmfsync 5 }
		
		-- *******.4.1.19338.126.4
		pmStore OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Plugin Manager"
			::= { leap 4 }


		
		-- *******.4.1.19338.126.4.1
		pmstore NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"Plugin Store is up and running"
			::= { pmStore 1 }

		
		-- *******.4.1.19338.126.4.2
		pmChlSmsConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"Channel Module - SMS connection"
			::= { pmStore 2 }

		
		-- *******.4.1.19338.126.4.3
		pmChlHttpConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"Channel Module - HTTP connection"
			::= { pmStore 3 }

		
		-- *******.4.1.19338.126.4.4
		pmDbMysqlConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"Database Module - MySQL connection"
			::= { pmStore 4 }

		
		-- *******.4.1.19338.126.4.5
		pmDbOracleConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"Database Module - Oracle connection"
			::= { pmStore 5 }

		
		-- *******.4.1.19338.126.4.6
		pmDbMariadbConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"Database Module - mariadb connection"
			::= { pmStore 6 }

		
		-- *******.4.1.19338.126.4.7
		pmUcipConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"UCIP Module connection"
			::= { pmStore 7 }

		
		-- *******.4.1.19338.126.4.8
		pmAcipConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"ACIP Module connection"
			::= { pmStore 8 }

		
		-- *******.4.1.19338.126.4.9
		pmVsipConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"VSIP Module connection"
			::= { pmStore 9 }

		
		-- *******.4.1.19338.126.4.10
		pmSoapConn NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"SOAP Module connection"
			::= { pmStore 10 }
		
		-- *******.4.1.19338.126.5
		authServer OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"AUTH-SERV "
			::= { leap 5 }


		
		-- *******.4.1.19338.126.5.1
		authserver NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"AUTH-SERV is up and running"
			::= { authServer 1 }
		
		-- *******.4.1.19338.126.6
		appstore OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"App-Store"
			::= { leap 6 }


		
		-- *******.4.1.19338.126.6.1
		appStore NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"App Store is up and running"
			::= { appstore 1 }
		
		-- *******.4.1.19338.126.7
		mockServer OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Mock Server"
			::= { leap 7 }


		
		-- *******.4.1.19338.126.7.1
		mockserver NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"MOCK-SERV is up and running"
			::= { mockServer 1 }
		
		-- *******.4.1.19338.126.8
		loginsessionstore OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Login session store"
			::= { leap 8 }


		
		-- *******.4.1.19338.126.8.1
		loginSessionStore NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"Login Session store is up and running"
			::= { loginsessionstore 1 }
		
		-- *******.4.1.19338.126.9
		usersessionstore OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"User Session store"
			::= { leap 9 }


		
		-- *******.4.1.19338.126.9.1
		userSessionStore NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"User session store is up and running"
			::= { usersessionstore 1 }
		
		-- *******.4.1.19338.126.10
		cdrqueue OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"CDR Queue"
			::= { leap 10 }


		
		-- *******.4.1.19338.126.10.1
		cdrQueue NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"CDR Queue is up and running"
			::= { cdrqueue 1 }
		
		-- *******.4.1.19338.126.11
		whiteBoard OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Event Queue"
			::= { leap 11 }


		
		-- *******.4.1.19338.126.11.1
		whiteboard NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"Event queue is up and running"
			::= { whiteBoard 1 }
		
		-- *******.4.1.19338.126.12
		tpsservice OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"tpsAgent"
			::= { leap 12 }


		
		-- *******.4.1.19338.126.12.1
		tpsAgent NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"tpsAgent is up and running"
			::= { tpsservice 1 }
		
		-- *******.4.1.19338.126.13
		cdrservice OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"cdrsweeper"
			::= { leap 13 }


		
		-- *******.4.1.19338.126.13.1
		cdrsweeper NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"cdrsweeper is up and running"
			::= { cdrservice 1 }
		
		-- *******.4.1.19338.126.14
		duservice OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Du Service"
			::= { leap 14 }


		
		-- *******.4.1.19338.126.14.1
		duService NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"duService is up and running"
			::= { duservice 1 }
		
		-- *******.4.1.19338.126.15
		guiservice OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"GUI Service"
			::= { leap 15 }


		
		-- *******.4.1.19338.126.15.1
		gui NOTIFICATION-TYPE
			OBJECTS { exitStatus, alarmString, misc, alarmType, alarmStatus
				 }
			STATUS current
			DESCRIPTION 
				"gui is up and running"
			::= { guiservice 1 }

		
	
	END

--
-- MahindraComviva-GNOC-LEAP-MIB.my
--
