--
-- Comviva.my
-- MIB generated by MG-SOFT Visual MIB Builder 2010 Version 8.0  Build 410
-- Monday, April 08, 2013 at 11:29:24
--

	Comviva DEFINITIONS ::= BEGIN
 
		IMPORTS
			enterprises, Integer32, OBJECT-TYPE, MODULE-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.19338
		comviva MODULE-IDENTITY 
			LAST-UPDATED "201208211836Z"		-- August 21, 2012 at 18:36 GMT
			ORGANIZATION 
				"Comviva Technologies Limited"
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"This is a generic Comviva MIB describing the objects used for GNOC"
			::= { enterprises 19338 }

		
	
--
-- Node definitions
--
	
		-- *******.4.1.19338.0
		alarmObjects OBJECT IDENTIFIER ::= { comviva 0 }

		
		-- *******.4.1.19338.0.1
		exitStatus OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmObjects 1 }

		
		-- *******.4.1.19338.0.2
		alarmString OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmObjects 2 }

		
		-- *******.4.1.19338.0.3
		misc OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmObjects 3 }

		
		-- *******.4.1.19338.0.4
		alarmType OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmObjects 4 }

		
		-- *******.4.1.19338.0.5
		alarmStatus OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmObjects 5 }

		
	
	END

--
-- Comviva.my
--
