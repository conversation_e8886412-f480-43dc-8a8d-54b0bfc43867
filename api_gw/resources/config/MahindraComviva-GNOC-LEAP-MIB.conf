#
#
#
#
MIB: MahindraComviva-GNOC-LEAP-MIB (file:/usr/share/snmp/mibs/MahindraComviva-GNOC-LEAP-MIB.my) converted on Tue Nov  5 09:17:56 2019 using snmpttconvertmib v1.3
#
#
#
EVENT apiGw .*******.4.1.19338.126.1.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

API GW is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT apigwConfigserverConn .*******.4.1.19338.126.1.2 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

APIGW-Config Server connectivity
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT apigwAppStoreConn .*******.4.1.19338.126.1.3 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

APIGW-AppStore DB connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT apigwAuthserverConn .*******.4.1.19338.126.1.4 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

APIGW-AuthServer connection 
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT apigwLoginSessionstoreConn .*******.4.1.19338.126.1.5 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

APIGW-Login Session store connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT apigwPmserverConn .*******.4.1.19338.126.1.6 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

APIGW-PMFSYNC-Server connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT appEngine .*******.4.1.19338.126.2.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

APP-ENGINE is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT appEngineConfigserverConn .*******.4.1.19338.126.2.2 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

AppExec-Config Server connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT appEngineAppStoreConn .*******.4.1.19338.126.2.3 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

AppExec-AppStore DB connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT appEngineSessionstoreConn .*******.4.1.19338.126.2.4 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

AppExec-Session store connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT appEnginePmserverConn .*******.4.1.19338.126.2.5 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

AppExec-PMFSYNC-Server connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT appEngineCdrqueueConn .*******.4.1.19338.126.2.6 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

AppExec-logQueue connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT appEngineTpsqueueConn .*******.4.1.19338.126.2.7 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

AppExec-TPSQueue connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmFsync .*******.4.1.19338.126.3.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

PM-FSYNC is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmfsyncConfigserverConn .*******.4.1.19338.126.3.2 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

PMSERV-Config Server connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmfsyncPmstoreConn .*******.4.1.19338.126.3.3 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

PMSERV-PluginStore DB connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmfsyncEventqConn .*******.4.1.19338.126.3.4 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

PMSERV-Event Queue connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmcliEventqConn .*******.4.1.19338.126.3.5 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

PMCLI-Event Queue connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmstore .*******.4.1.19338.126.4.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

Plugin Store is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmChlSmsConn .*******.4.1.19338.126.4.2 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

Channel Module - SMS connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmChlHttpConn .*******.4.1.19338.126.4.3 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

Channel Module - HTTP connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmDbMysqlConn .*******.4.1.19338.126.4.4 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

Database Module - MySQL connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmDbOracleConn .*******.4.1.19338.126.4.5 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

Database Module - Oracle connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmDbMariadbConn .*******.4.1.19338.126.4.6 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

Database Module - mariadb connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmUcipConn .*******.4.1.19338.126.4.7 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

UCIP Module connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmAcipConn .*******.4.1.19338.126.4.8 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

ACIP Module connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmVsipConn .*******.4.1.19338.126.4.9 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

VSIP Module connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT pmSoapConn .*******.4.1.19338.126.4.10 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

SOAP Module connection
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT authserver .*******.4.1.19338.126.5.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

AUTH-SERV is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT appStore .*******.4.1.19338.126.6.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

App Store is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT mockserver .*******.4.1.19338.126.7.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

MOCK-SERV is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT loginSessionStore .*******.4.1.19338.126.8.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

Login Session store is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT userSessionStore .*******.4.1.19338.126.9.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

User session store is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT cdrQueue .*******.4.1.19338.126.10.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

CDR Queue is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT whiteboard .*******.4.1.19338.126.11.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

Event queue is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT tpsAgent .*******.4.1.19338.126.12.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

tpsAgent is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT cdrsweeper .*******.4.1.19338.126.13.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

cdrsweeper is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT duService .*******.4.1.19338.126.14.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

duService is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
#
#
#
EVENT gui .*******.4.1.19338.126.15.1 "Status Events" Normal
FORMAT $*
EXEC /prd/nms/www/snmp/submit_check_result $r $o $N $1 "$2" "$3" $4 $5 "$*"
SDESC

GUI is up and running
Variables:
  1: exitStatus
  2: alarmString
  3: misc
  4: alarmType
  5: alarmStatus
EDESC
