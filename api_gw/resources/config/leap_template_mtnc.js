module.exports = {
    system: {
        settings: {
            global: {
                country: "CG",
                timezone: "Africa/Brazzaville",
                authSecret: "ThereAreNoGodDamnSecrets",
                certificates: {
                    key: "/prd/leap/config/server.key",
                    cert: "/prd/leap/config/server.crt",
                    pem: "/prd/leap/config/server.pem"
                },
                kpi: {
                    enabled: true,
                    policy: "file",
                    options: {
                        frequency: 1,
                        redis: { host: "************", port: 6384, db: 0, password: "leap123" },
                        file: "/data/leap/kpi"
                    }
                },
                oam: {
                    enabled: true,
                    project: "LEAP",
                    options: {
                        oamServerIp: "127.0.0.1",
                        oamServerPort: 4545,
                        appId: ".1",
                        oidPrefix: "*******.1.19338.126",
                        oidMap: {
                            apigw: ["1.1", "APIGW is up and running", "APIGW is down", "Warning Message", "Unknown Message"],
                            apigw_configserver_conn: ["1.2", "APIGW-Config Server connection established", "APIGW-Config Server connectivity is down", "Warning Message", "Unknown Message"],
                            apigw_app_store_conn: ["1.3", "APIGW-AppStore DB connection established", "APIGW-AppStore DB connectivity is down", "Warning Message", "Unknown Message"],
                            apigw_authserver_conn: ["1.4", "APIGW-AuthServer connection established", "APIGW-AuthServer connectivity is down", "Warning Message", "Unknown Message"],
                            apigw_login_sessionstore_conn: ["1.5", "APIGW-Login Session store connection established", "APIGW-Login Session store connectivity is down", "Warning Message", "Unknown Message"],
                            apigw_pmserver_conn: ["1.6", "APIGW - PMFSYNC - Server connection established", "APIGW - PMFSYNC - Server connectivity is down", "Warning Message", "Unknown Message"],
                            app_engine: ["2.1", "APP-ENGINE is up and running", "APP-ENGINE is down", "Warning Message", "Unknown Message"],
                            app_engine_configserver_conn: ["2.2", "AppExec-Config Server connection established", "AppExec-Config Server connectivity is down", "Warning Message", "Unknown Message"],
                            app_engine_app_store_conn: ["2.3", "AppExec-AppStore DB connection established", "AppExec-AppStore DB connectivity is down", "Warning Message", "Unknown Message"],
                            app_engine_sessionstore_conn: ["2.4", "AppExec-Session store connection established", "AppExec-Session store connectivity is down", "Warning Message", "Unknown Message"],
                            app_engine_pmserver_conn: ["2.5", "AppExec-PMFSYNC-Server connection established", "APIGW-PMFSYNC-Server connectivity is down", "Warning Message", "Unknown Message"],
                            app_engine_cdrqueue_conn: ["2.6", "AppExec-logQueue connection established", "AppExec logQueue connectivity is down", "Warning Message", "Unknown Message"],
                            app_engine_tpsqueue_conn: ["2.7", "AppExec-TPSQueue connection established", "AppExec-TPSQueue connectivity is down", "Warning Message", "Unknown Message"],
                            pm_fsync: ["3.1", "PM-FSYNC is up and running", "PM-FSYNC is down", "Warning Message", "Unknown Message"],
                            pmfsync_configserver_conn: ["3.2", "PMSERV-Config Server connection established", "PMSERV-Config Server connectivity is down", "Warning Message", "Unknown Message"],
                            pmfsync_pmstore_conn: ["3.3", "PMSERV-PluginStore DB connection established", "PMSERV-PluginStore DB connectivity is down", "Warning Message", "Unknown Message"],
                            pmfsync_eventq_conn: ["3.4", "PMSERV-Event Queue connection established", "PMSERV-Event Queue connectivity is down", "Warning Message", "Unknown Message"],
                            pmcli_eventq_conn: ["3.5", "PMCLI-Event Queue connection established", "PMCLI-Event Queue connectivity is down", "Warning Message", "Unknown Message"],
                            pmstore: ["4.1", "Plugin Store is up and running", "Plugin Store is down", "Warning Message", "Unknown Message"],
                            pm_chl_sms_conn: ["4.2", "Channel Module - SMS connection established", "Channel Module - SMS connectivity is down", "Warning Message", "Unknown Message"],
                            pm_chl_http_conn: ["4.3", "Channel Module - HTTP connection established", "Channel Module - HTTP connectivity is down", "Warning Message", "Unknown Message"],
                            pm_db_mysql_conn: ["4.4", "Database Module - MySQL connection established", "Database Module - MySQL connectivity is down", "Warning Message", "Unknown Message"],
                            pm_db_oracle_conn: ["4.5", "Database Module - Oracle connection established", "Database Module - Oracle connectivity is down", "Warning Message", "Unknown Message"],
                            pm_db_mariadb_conn: ["4.6", "Database Module - mariadb connection established", "Database Module - mariadb connectivity is down", "Warning Message", "Unknown Message"],
                            pm_ucip_conn: ["4.7", "UCIP Module connection established", "UCIP Module connectivity is down", "Warning Message", "Unknown Message"],
                            pm_acip_conn: ["4.8", "ACIP Module connection established", "ACIP Module connectivity is down", "Warning Message", "Unknown Message"],
                            pm_vsip_conn: ["4.9", "VSIP Module connection established", "VSIP Module connectivity is down", "Warning Message", "Unknown Message"],
                            pm_soap_conn: ["4.10", "SOAP Module connection established", "SOAP Module connectivity is down", "Warning Message", "Unknown Message"],
                            authserver: ["5.1", "AUTH-SERV is up and running", "AUTH-SERV is down", "Warning Message", "Unknown Message"],
                            app_store: ["6.1", "App Store is up and running", "App Store is down", "Warning Message", "Unknown Message"],
                            mockserver: ["7.1", "MOCK-SERV is up and running", "MOCK-SERV is down", "Warning Message", "Unknown Message"],
                            login_session_store: ["8.1", "Login Session Store is up and running", "Login Session Store is down", "Warning Message", "Unknown Message"],
                            user_session_store: ["9.1", "User Session Store is up and running", "User Session Store is down", "Warning Message", "Unknown Message"],
                            cdr_queue: ["10.1", "CDR Queue is up and running", "CDR Queue is down", "Warning Message", "Unknown Message"],
                            whiteboard: ["11.1", "Event Queue is up and running", "Events Queue is down", "Warning Message", "Unknown Message"],
                            tps_agent: ["12.1", "tpsAgent is up and running", "tpsAgent is down", "Warning Message", "Unknown Message"],
                            cdrSweeper: ["13.1", "cdrsweeper is up and running", "cdrsweeper is down", "Warning Message", "Unknown Message"],
                            duService: ["14.1", "duService is up and running", "duService is down", "Warning Message", "Unknown Message"],
                            gui: ["15.1", "LEAP Studio is up and running", "LEAP Studio is down", "Warning Message", "Unknown Message"],
                            session_cleaner: ["16.1", "SESSION-CLEANER is up and running", "SESSION-CLEANER is down", "Warning Message", "Unknown Message"],
                            session_cleaner_configserver_conn: ["16.2", "SESSION-CLEANER-Config Server connection established", "SESSION-CLEANER-Config Server connectivity is down", "Warning Message", "Unknown Message"],
                            session_cleaner_sessionstore_conn: ["16.3", "SESSION-CLEANER-Session store connection established", "SESSION-CLEANER-Session store connectivity is down", "Warning Message", "Unknown Message"],
                            session_cleaner_cdrqueue_conn: ["16.4", "SESSION-CLEANER-logQueue connection established", "SESSION-CLEANER-logQueue connectivity is down", "Warning Message", "Unknown Message"],
                            elasticsearch_conn: ["17.1", "elasticsearch connection established", "elasticsearch connectivity is down", "Warning Message", "Unknown Message"],
                            diskspace_full: ["17.2", "LEAP CDR Disk space is OK", "LOW LEAP CDR Disk space, please clear some space", "Warning Message", "Unknown Message"]
                        }
                    }
                },
                databases: {
                    app_store: {
                        username: "leap",
                        password: "leap123",
                        database: "leapDB",
                        host: "************",
                        port: 3306,
                        dialect: "mariadb",
                        pool: {
                            minConnections: 1,
                            maxIdleTime: 9
                        },
                        logging: false,
                        dialectOptions: {
                            useUTC: false,
                            dateStrings: true,
                            typeCast: true
                        },
                        timezone: 'Etc/GMT+01:00',
                        generateUUID: false
                    },
                    authDB: {
                        username: "auth",
                        password: "auth123",
                        database: "authDB",
                        host: "************",
                        port: 3306,
                        dialect: "mariadb",
                        pool: {
                            minConnections: 1,
                            maxIdleTime: 9
                        },
                        logging: false,
                        dialectOptions: {
                            useUTC: false,
                            dateStrings: true,
                            typeCast: true
                        },
                        timezone: 'Etc/GMT+01:00'
                    },
                    reportServer: {
                        hosts: [{
                            host: "************:9007",
                            port: 9007
                        }],
                        requestTimeout: 10000, // Milliseconds before an HTTP request will be aborted and retried. This can also be set per request. Default=30000
                        deadTimeout: 60000, // Milliseconds that a dead connection will wait before attempting to revive itself. Default=60000
                        pingTimeout: 3000, // Milliseconds that a ping request can take before timing out. Default=3000
                        maxRetries: 3, // How many times should the client try to connect to other nodes before returning a ConnectionFault error. Default=3
                        maxSockets: 100, // Maximum number of sockets to allow per host. Default=Infinity
                        keepAlive: true, /* Should the connections to the node be kept open forever?
                     This behavior is recommended when you are connecting directly to Elasticsearch.
                      Default=true*/
                        keepAliveInterval: 1000, /*How often, in milliseconds, should TCP KeepAlive packets be sent over sockets being kept alive.
                    Only relevant if keepAlive is set to true. Default=1000*/
                        marketingIndex: "leap",
                        auditIndex: "au",
                        tempDir: "/data/leap/tmp",
                        archiveDir: "/data/leap/archive"
                    },
                    influxDB: {
                        host: "************",
                        port: 9009,
                        database: "leap_kpi",
                        username: "leap",
                        password: "leap123",
                        thresholdValue: 1600
                    }
                },
                whiteboard: {
                    host: "************",
                    port: 6383,
                    db: 0,
                    password: "leap123"
                },
                security: {
                    enabled: false,
                    allowedIPs: [],
                    allowedRestMethods: ["GET", "POST", "OPTIONS"],
                    cors: []
                }
            },
            components: {
                api_gw: {
                    host: "************",
                    port: 9001,
                    contextPath: "/leap_gw",
                    protocol: "https",
                    captureAppSnapshot: true,
                    isCollaborativeAppDevelopmentEnabled: false,
                    sessionCache: {
                        host: "************",
                        port: 6382,
                        password: "leap123",
                        sessionKey: "sessionJWT",
                        cleanupDuration: "1d"
                    },
                    appsApi: {
                        defaultSortField: "name",
                        defaultPageSize: 50,
                        defaultSortOrder: "asc",
                        expressionAppName: "^[a-zA-Z0-9]{1,}[a-zA-Z0-9_ ]*$",
                        expressionAppStates: "^[0-9]{1,2}(,[0-9]{1,2})+$",
                        maxLengthAppName: 32,
                        maxLengthAppDesc: 128,
                        allowedSortFields: {
                            id: "id",
                            name: "name",
                            status: "status",
                            createdAt: "createdAt",
                            updatedAt: "updatedAt"
                        },
                        allowedSortOrder: {
                            asc: "asc",
                            desc: "desc"
                        },
                        configApis: {
                            "/leap_gw/configapi/passwordstrength": "Get the regular expression to validate the Strength of Password",
                            "/leap_gw/configapi/appstates": "Get App Status Code information",
                            "/leap_gw/configapi/macros": "Get list of MACROS supported in platform",
                            "/leap_gw/configapi/operators": "Get list of Operators supported in platform",
                            "/leap_gw/configapi/languages": "Get list of Languges supported in platform",
                            "/leap_gw/configapi/simulationinfo": "Get the App simulation settings.",
                            "/leap_gw/configapi/userstatuses": "Gets User Status information",
                            "/leap_gw/configapi/userActivationLink": "User activation callback URL"
                        },
                        passwordstrength: {
                            expression: "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})",
                            description: "The password string must contain at least 1 lowercase alphabetical character, must contain at least 1 uppercase alphabetical character, must contain at least 1 numeric character, must contain at least one special character, but we are escaping reserved RegEx characters to avoid conflict, The string must be 8 characters or longer"
                        },
                        simulationinfo: {
                            "app_url": "https://*************:9004/samvaadak/apps/",
                            "websocket_url": "wss://*************:9004"
                        },
                        userActivationLink: "https://************:9000/leap/verify-otp"
                    },
                    log: {
                        logdir: "/data/leap/logs",
                        cdr: "apigw_access",
                        cdrFormat: "[:date[iso]] :transactionId :process :remote-addr :remote-user :username :method :url HTTP/:http-version :status :res[content-length] :response-time :referrer :event",
                        log4js: {
                            appenders: {
                                console: {
                                    type: "console"
                                },
                                apigw: {
                                    type: "dateFile",
                                    filename: "api_gw.log",
                                    pattern: ".yyyy-MM-dd_hh",
                                    layout: {
                                        type: "pattern",
                                        pattern: "[%d{ISO8601}] [%p] %m"
                                    }
                                }
                            },
                            categories: {
                                default: { appender: "apigw", level: "TRACE" },
                                apigw: { appender: "apigw", level: "ERROR" }
                            }
                        }
                    },
                    myDownloads: {
                        downloadDirectory: "/data/leap/downloads"
                    }
                },
                app_engine: {
                    defaultLanguage: "en",
                    userPreferenceStoreCheck: false,
                    menuNavigationEnable: true,
                    menuHelpEnable: false,
                    sessionCleanKey: "clean-session",
                    app_cache: {
                        filter: {
                            development: "0,1,2,5,6,8",
                            staging: "2,5,6",
                            production: "6,8"
                        }
                    },
                    sessionstore: {
                        host: "************",
                        port: 6381,
                        db: 0,
                        password: "leap123",
                        timeout: 60,
                        delta: 5,
                        cachePolicy: "redis",
                        compression_required: false
                    },
                    cdrqueue: {
                        host: "************",
                        port: 6384,
                        db: 0,
                        password: "leap123",
                        log_key: "LEAP_CDRQ",
                        limit: 100000,
                        fallback_cdr_path: "/data/leap/logs"
                    },
                    plugin_exec: {
                        client: {
                            maxSockets: 50,
                            keepAlive: true,
                            maxFreeSockets: 20
                        },
                        thresholdValue: 2000
                    }
                },
                app_engine_development: {
                    host: "************",
                    port: 9002,
                    contextPath: "/app_engine/development",
                    protocol: "https",
                    clusterCPUs: 1,
                    interfaces: {
                        httpXML: {
                            enabled: false,
                            contextPathSuffix: "xml",
                            options: {

                            }
                        },
                        httpJSON: {
                            enabled: false,
                            options: {

                            }
                        },
                        smpp: {
                            enabled: false,
                            port: 2776,
                            delay_between_submitsm: 5000,
                            accounts: {
                                leap: {
                                    password: "leap123",
                                    max_connections: 10,
                                    status: true,
                                    enquire_link: false,
                                    enquire_link_interval: 60000
                                }
                            }
                        }
                    },
                    log: {
                        logdir: "/data/leap/logs",
                        cdr: "app_engine_access",
                        cdrFormat: ":remote-addr :remote-user [:date[iso]] :username :method :url HTTP/:http-version :status :res[content-length] :response-time",
                        log4js: {
                            appenders: {
                                console: {
                                    type: "console",
                                    pattern: '%d{ISO8601}|%p|%m'
                                },
                                app_engine: {
                                    type: "dateFile",
                                    filename: "app_engine_development.log",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '%d{ISO8601}|%p|%m'
                                    }
                                },
                                pluginCDR: {
                                    type: "dateFile",
                                    filename: "plugin_dev_exec.cdr",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '%d{ISO8601}|%m'
                                    }
                                },
                                smpp_events: {
                                    type: "dateFile",
                                    filename: "smpp_events_development.log",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '%d{ISO8601}|%m'
                                    }
                                }
                            },
                            categories: {
                                default: { appender: "app_engine", level: "TRACE" },
                                app_engine: { appender: "app_engine", level: "ERROR" },
                                smpp_events: { appender: "smpp_events", level: "ERROR" },
                                pluginCDR: { appender: "pluginCDR", level: "ERROR" }
                            }
                        }
                    }
                },
                app_engine_staging: {
                    host: "************",
                    port: 9003,
                    contextPath: "/app_engine/staging",
                    protocol: "http",
                    clusterCPUs: 1,
                    interfaces: {
                        httpXML: {
                            enabled: false,
                            contextPathSuffix: "xml",
                            options: {

                            }
                        },
                        httpJSON: {
                            enabled: false,
                            options: {

                            }
                        },
                        smpp: {
                            enabled: false,
                            port: 2777,
                            delay_between_submitsm: 5000,
                            accounts: {
                                leap: {
                                    password: "leap123",
                                    max_connections: 10,
                                    status: true,
                                    enquire_link: false,
                                    enquire_link_interval: 60000
                                }
                            }
                        }
                    },
                    log: {
                        logdir: "/data/leap/logs",
                        cdr: "app_engine_access",
                        cdrFormat: ":remote-addr :remote-user [:date[iso]] :username :method :url HTTP/:http-version :status :res[content-length] :response-time",
                        log4js: {
                            appenders: {
                                console: {
                                    type: "console",
                                    pattern: '%d{ISO8601}|%p|%m'
                                },
                                app_engine: {
                                    type: "dateFile",
                                    filename: "app_engine_staging.log",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '%d{ISO8601}|%p|%m'
                                    }
                                },
                                pluginCDR: {
                                    type: "dateFile",
                                    filename: "plugin_staging_exec.cdr",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '%d{ISO8601}|%m'
                                    }
                                },
                                smpp_events: {
                                    type: "dateFile",
                                    filename: "smpp_events_staging.log",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '%d{ISO8601}|%m'
                                    }
                                }
                            },
                            categories: {
                                default: { appender: "app_engine", level: "ERROR" },
                                app_engine: { appender: "app_engine", level: "ERROR" },
                                smpp_events: { appender: "smpp_events", level: "ERROR" },
                                pluginCDR: { appender: "pluginCDR", level: "ERROR" }
                            }
                        }
                    }
                },
                app_engine_production: {
                    host: "***********",
                    port: 9004,
                    contextPath: "/app_engine/production",
                    protocol: "http",
                    clusterCPUs: 8,
                    interfaces: {
                        httpXML: {
                            enabled: false,
                            contextPathSuffix: "xml",
                            options: {

                            }
                        },
                        httpJSON: {
                            enabled: false,
                            options: {

                            }
                        },
                        smpp: {
                            enabled: false,
                            port: 2778,
                            delay_between_submitsm: 5000,
                            accounts: {
                                leap: {
                                    password: "leap123",
                                    max_connections: 10,
                                    status: true,
                                    enquire_link: false,
                                    enquire_link_interval: 60000
                                }
                            }
                        }
                    },
                    log: {
                        logdir: "/data/leap/logs",
                        cdr: "app_engine_access",
                        cdrFormat: ":remote-addr :remote-user [:date[iso]] :username :method :url HTTP/:http-version :status :res[content-length] :response-time",
                        log4js: {
                            appenders: {
                                console: {
                                    type: "console",
                                    pattern: '%d{ISO8601}|%p|%m'
                                },
                                app_engine: {
                                    type: "dateFile",
                                    filename: "app_engine.log",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '%d{ISO8601}|%p|%m'
                                    }
                                },
                                pluginCDR: {
                                    type: "dateFile",
                                    filename: "plugin_exec.cdr",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '%d{ISO8601}|%m'
                                    }
                                },
                                smpp_events: {
                                    type: "dateFile",
                                    filename: "smpp_events.log",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '%d{ISO8601}|%m'
                                    }
                                }
                            },
                            categories: {
                                default: { appender: "app_engine", level: "ERROR" },
                                app_engine: { appender: "app_engine", level: "ERROR" },
                                smpp_events: { appender: "smpp_events", level: "ERROR" },
                                pluginCDR: { appender: "pluginCDR", level: "TRACE" }
                            }
                        }
                    }
                },
                authServer: {
                    host: "************",
                    port: 9005,
                    db: 0,
                    ctxPath: "/authenticator",
                    protocol: "http",
                    log: {
                        logdir: "/data/leap/logs",
                        cdr: "authServer_access",
                        cdrFormat: ":remote-addr :remote-user [:date[iso]] :username :method :url HTTP/:http-version :status :res[content-length] :response-time",
                        log4js: {
                            appenders: {
                                console: {
                                    type: "console"
                                },
                                authServer: {
                                    type: "dateFile",
                                    filename: "authServer.log",
                                    pattern: ".yyyy-MM-dd_hh",
                                    layout: {
                                        type: "pattern",
                                        pattern: "[%d{ISO8601}] [%p] %m"
                                    }
                                }
                            },
                            categories: {
                                default: { appender: "authServer", level: "TRACE" },
                                authServer: { appender: "authServer", level: "ERROR" }
                            }
                        }
                    },
                    otpValidityPeriod: "15m",
                    resetLink: "https://************:9000/leap/create-password",
                    activationLinkValidityPeriod: "3d",
                    jwtExpiryTime: "3h",
                    refreshExpiryTime: "1h",
                    maxPasswordsToBeStored: 5,
                    allowedEmailDuration: '5m',
                    allowedEmailCount: 3,
                    secretkey: "mysecretpassword",
                    features: {
                        /*otp:{
                      enabled: false,
                      options: {
                        channels: ["sms", "email"],
                        maxOtp: 180,
                        otpResetTimeInSec: 60,
                        otpTimeoutInSec: 900
                      }
                        },
                        stats: { enabled: false },
                        fb_authenticator: {enabled:false},
                        twitter_authenticator: {enabled:false},
                        cluster: {enabled:false },*/
                        lockOnInvalidLogins: {
                            enabled: true,
                            options: {
                                maxInvalidLoginAttempts: 3,  // no. of attempts after which locking happens
                                userLockTime: 10    // Time duration (in mins) until which account remains locked.
                            }
                        },
                        cache: {
                            enabled: true,
                            options: {
                                cachePolicy: "redis",
                                redis: { host: "************", port: 6382, db: 0, password: "leap123" }
                            }
                        },
                        ldap: {
                            enabled: false,
                            options: {
                                url: "ldap://***********:389",
                                bindDn: "COMVIVA\\lakshmi.k",
                                bindCredentials: "sra@0218",
                                searchBase: "DC=comviva,DC=com",
                                filtername: "uid"
                            }
                        },
                        defaultRootUser: {
                            enabled: true,
                            options: {
                                username: "root",
                                password: "root123",
                                firstName: "root",
                                email: "<EMAIL>",
                                roleName: "root",
                                claims: "{ \"1.1\": { \"perms\": 6, \"scope\": 1 },\"*\": { \"perms\": 7, \"scope\": 2 } }"
                            }
                        },
                        email: {
                            enabled: true,
                            options: {
                                host: "************", // host name
                                port: 25, // port for secure SMTP
                                secure: false, // TLS requires secureConnection to be false
                                authRequired: true,
                                auth: {
                                    user: "<EMAIL>",
                                    pass: "Ch@#Me2019"
                                }
                            }
                        },
                        oam: {
                            enabled: true,
                            options: {
                                oamServerIp: "127.0.0.1",
                                oamServerPort: 4545,
                                appId: ".1",
                                oidMap: {
                                    authServerStatus: [".*******.1.19338.126.5.1",
                                        "AuthServer started successfully",
                                        "AuthServer stopped"],
                                    cacheConnectionStatus: [".*******.1.19338.126.5.2",
                                        "Redis connection established",
                                        "Redis connectivity down"],
                                    smtpConnectionStatus: [".*******.1.19338.126.5.3",
                                        "smtp connection established",
                                        "smtp connectivity down"],
                                    ldapConnectionStatus: [".*******.1.19338.126.5.4",
                                        "ldap connection established",
                                        "ldap connectivity down"],
                                    rdbmsConnectionStatus: [".*******.1.19338.126.5.5",
                                        "rdbms connection established",
                                        "rdbms connectivity down"]
                                }
                            }
                        },
                        kpi: {
                            enabled: false,
                            options: {
                                frequency: 1,
                                redis: { host: "************", port: 6384, db: 0, password: "leap123" }
                            }
                        }
                    }
                },
                pm_fsync: {
                    host: "************",
                    port: 9006,
                    contextPath: "/pm_fsync",
                    protocol: "http",
                    clusterCPUs: 1,
                    allowedFileCount: 10,
                    publishPluginSyncEvents: true,
                    log: {
                        logdir: "/data/leap/logs",
                        cdr: "pm_fsync_access",
                        cdrFormat: ":remote-addr :remote-user [:date[iso]] :username :method :url HTTP/:http-version :event :status :res[content-length] :response-time",
                        log4js: {
                            appenders: {
                                console: {
                                    type: "console"
                                },
                                pm_fsync: {
                                    type: "dateFile",
                                    filename: "pm_fsync.log",
                                    pattern: ".yyyy-MM-dd_hh",
                                    layout: {
                                        type: "pattern",
                                        pattern: "[%d{ISO8601}] [%p] %m"
                                    }
                                }
                            },
                            categories: {
                                default: { appender: "pm_fsync", level: "TRACE" },
                                pm_fsync: { appender: "pm_fsync", level: "ERROR" }
                            }
                        }
                    }
                },
                cdrSweeper: {
                    clusterMode: true,
                    clusterSize: 2,     // number of worker processes to spawn.  Could be defaulted to num cpus in the system.
                    transferMode: 1,    // 0:direct to ES, 1:To File and then to ES
                    batchSize4ES: 2000,  // if direct mode, set this value to about 1000, if indirect, then to a higher value say 1lac
                    batchSize4FS: 4000,  // if direct mode, set this value to about 1000, if indirect, then to a higher value say 1lac
                    esRetryInterval: 10000, // retry interval to connect to elasticsearch
                    outputPath: "/data/leap/cdrStore/cdrs", // a path relative or absolute, where to create output entries, for transferMode 1.
                    cdrPollingTimeout: 5, // how long to wait (seconds) to get an element from cdrStore. Default=1sec
                    diskChkInterval: 10,  // Diskspace checking periodicity in seconds. Used if transferMode is 1.
                    minFreeDiskSpace: 1, // Minimum free disk space avaiable in order to start writing CDR files. Used for transferMode 1.
                    recordMaxSize: 10000, // Maximum number of characters allowed in LEAP CDR
                    filePrefix: "LEAP",
                    log: {
                        logdir: "/data/leap/logs",
                        log4js: {
                            appenders: {
                                console: {
                                    type: "console",
                                    pattern: '[%d{ISO8601}] [%p] %m'
                                },
                                cdrSweeper: {
                                    type: "dateFile",
                                    filename: "cdrSweeper.log",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '[%d{ISO8601}] [%p] %m'
                                    }
                                }
                            },
                            categories: {
                                default: { appender: "cdrSweeper", level: "ERROR" },
                                cdrSweeper: { appender: "cdrSweeper", level: "ERROR" }
                            }
                        }
                    }
                },
                nodeLogger: {
                    path: "/data/leap/cdrStore/cdrs",
                    proc: "/data/leap/cdrStore/proc",
                    backup: "/data/leap/cdrStore/backup",
                    corrupted: "/data/leap/cdrStore/corrupted",
                    intervalSize: 5000,
                    compressionInterval: 120000,
                    batch: 1000,
                    fileBatchSize: 1000,
                    retryTimeOut: 30000,
                    log: {
                        logdir: "/data/leap/logs",
                        log4js: {
                            appenders: {
                                console: {
                                    type: "console",
                                    pattern: '[%d{ISO8601}] [%p] %m'
                                },
                                nodeLogger: {
                                    type: "dateFile",
                                    filename: "nodeLogger.log",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '[%d{ISO8601}] [%p] %m'
                                    }
                                }
                            },
                            categories: {
                                default: { appender: "nodeLogger", level: "ERROR" },
                                nodeLogger: { appender: "nodeLogger", level: "ERROR" }
                            }
                        }
                    }
                },
                tps_agent: {
                    log: {
                        logdir: "/data/leap/cdrs",
                        log4js: {
                            appenders: {
                                console: {
                                    type: "console",
                                    pattern: "%d{ISO8601}|%m"
                                },
                                tps_agent: {
                                    type: "dateFile",
                                    filename: "tps_agent.cdr",
                                    pattern: ".yyyy-MM-dd_hh",
                                    layout: {
                                        type: "pattern",
                                        pattern: "%d{ISO8601}|%m"
                                    }
                                }
                            },
                            categories: {
                                default: { appender: "tps_agent", level: "INFO" },
                                tps_agent: { appender: "tps_agent", level: "ERROR" }
                            }
                        }
                    }
                },
                acl_manager: {
                    host: "localhost",
                    port: 9012,
                    contextPath: "/ussd",
                    protocol: "http",
                    clusterCPUs: 1,
                    file_upload: {
                        minFreeDiskSpace: 1,
                        maxSessions: 5,
                        limits: {
                            fileSize: 2,
                            files: 10
                        },
                        supportedExtentions: "csv,txt",
                        source: "/data/leap/files",
                        processed: "/data/leap/files/processed",
                        error: "/data/leap/files/error"
                    },
                    queue: {
                        host: "************",
                        port: 6384,
                        db: 0,
                        password: "leap123",
                        log_key: "LEAP_CDRQ",
                        limit: 100000
                    },
                    log: {
                        logdir: "/data/leap/logs",
                        cdr: "acl_manager_access",
                        cdrFormat: ":remote-addr :remote-user [:date[iso]] :username :method :url HTTP/:http-version :event :status :res[content-length] :response-time",
                        log4js: {
                            appenders: {
                                console: {
                                    type: "console",
                                    pattern: '[%d{ISO8601}] [%p] %m'
                                },
                                acl_manager: {
                                    type: "dateFile",
                                    filename: "acl_manager.log",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '[%d{ISO8601}] [%p] %m'
                                    }
                                }
                            },
                            categories: {
                                default: { appender: "acl_manager", level: "ERROR" },
                                acl_manager: { appender: "acl_manager", level: "ERROR" }
                            }
                        }
                    }
                },
                file_processor: {
                    file_paths: {
                        archieve: true,
                        source: "/data/leap/files",
                        processed: "/data/leap/files/processed/",
                        error: "/data/leap/files/error/"
                    },
                    batchvalue: {
                        datavalue: 10,
                        msisdnvalue: 5
                    },
                    minFreeDiskSpace: 1,
                    retry_policy: {
                        enabled: true,
                        maxRetry: 5,
                        retryInterval: 10000
                    },
                    retention_policy: {
                        enabled: true,
                        period: 30
                    },
                    queue: {
                        host: "************",
                        port: 6384,
                        db: 0,
                        password: "leap123",
                        log_key: "LEAP_CDRQ",
                        limit: 100000
                    },
                    log: {
                        logdir: "/data/leap/logs/",
                        cdr: "FILE_PROCESSOR",
                        log4js: {
                            appenders: {
                                console: {
                                    type: "console",
                                    pattern: '[%d{ISO8601}] [%p] %m'
                                },
                                file_processor: {
                                    type: "dateFile",
                                    filename: "file_processor.log",
                                    pattern: '.yyyy-MM-dd_hh',
                                    layout: {
                                        type: 'pattern',
                                        pattern: '[%d{ISO8601}] [%p] %m'
                                    }
                                }
                            },
                            categories: {
                                default: { appender: "file_processor", level: "TRACE" },
                                file_processor: { appender: "file_processor", level: "TRACE" }
                            }
                        }
                    }
                },
                session_cleaner: {
                    log: {
                        logdir: "/data/leap/logs/",
                        log4js: {
                            appenders: {
                                console: {
                                    type: "console",
                                    pattern: "%d{ISO8601}|%m"
                                },
                                session_cleaner: {
                                    type: "dateFile",
                                    filename: "session_cleaner.log",
                                    pattern: ".yyyy-MM-dd_hh",
                                    layout: {
                                        type: "pattern",
                                        pattern: "%d{ISO8601}|%m"
                                    }
                                }
                            },
                            categories: {
                                default: { appender: "session_cleaner", level: "ERROR" },
                                session_cleaner: { appender: "session_cleaner", level: "ERROR" }
                            }
                        }
                    }
                }
            }
        }
    }  //EOF: Global section
};
