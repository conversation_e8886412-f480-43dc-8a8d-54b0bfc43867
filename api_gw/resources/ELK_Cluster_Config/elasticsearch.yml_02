# ======================== Elasticsearch Configuration =========================
#
# NOTE: Elasticsearch comes with reasonable defaults for most settings.
#       Before you set out to tweak and tune the configuration, make sure you
#       understand what are you trying to accomplish and the consequences.
#
# The primary way of configuring a node is via this file. This template lists
# the most important settings you may want to configure for a production cluster.
#
# Please see the documentation for further information on configuration options:
# <https://www.elastic.co/guide/en/elasticsearch/reference/5.0/settings.html>
#
# ---------------------------------- Cluster -----------------------------------
#
# Use a descriptive name for your cluster:
#
cluster.name: "LEAP-Cluster"
#
# ------------------------------------ Node ------------------------------------
#
# Use a descriptive name for the node:
#
node.name:  Node2
node.master: true
node.data: true
#
# Add custom attributes to the node:
#
#node.attr.rack: r1
#
# ----------------------------------- Paths ------------------------------------
#
# Path to directory where to store the data (separate multiple locations by comma):
#
path.data: /data/leap/elasticsearch/data
#
# Path to log files:
#
path.logs: /data/leap/elasticsearch/log
#
# ----------------------------------- Memory -----------------------------------
#
# Lock the memory on startup:
#
#bootstrap.memory_lock: true
#
# Make sure that the heap size is set to about half the memory available
# on the system and that the owner of the process is allowed to use this
# limit.
#
# Elasticsearch performs poorly when the system is swapping the memory.
#
# ---------------------------------- Network -----------------------------------
#
# Set the bind address to a specific IP (IPv4 or IPv6):
#
network.host: 0.0.0.0
#
# Set a custom port for HTTP:
#
http.port: 9007
#
# For more information, see the documentation at:
# <https://www.elastic.co/guide/en/elasticsearch/reference/5.0/modules-network.html>
#
# --------------------------------- Discovery ----------------------------------
#
# Pass an initial list of hosts to perform discovery when new node is started:
# The default list of hosts is ["127.0.0.1", "[::1]"]
#

network.publish_host: 0.0.0.0


#discovery.zen.ping.multicast.enabled: false
discovery.zen.ping.unicast.hosts: ['*************','*************', '************']
discovery.zen.minimum_master_nodes: 2

#
# Prevent the "split brain" by configuring the majority of nodes (total number of nodes / 2 + 1):
#

#discovery.zen.minimum_master_nodes: 1

#
# For more information, see the documentation at:
# <https://www.elastic.co/guide/en/elasticsearch/reference/5.0/modules-discovery-zen.html>
#
# ---------------------------------- Gateway -----------------------------------
#
# Block initial recovery after a full cluster restart until N nodes are started:
#
#gateway.recover_after_nodes: 3
#
# For more information, see the documentation at:
# <https://www.elastic.co/guide/en/elasticsearch/reference/5.0/modules-gateway.html>
#
# ---------------------------------- Various -----------------------------------
#
# Require explicit names when deleting indices:
#
#action.destructive_requires_name: true
http.enabled: true
http.cors.allow-credentials: true
http.cors.enabled: true
http.cors.allow-origin: /(.*)?/
http.cors.allow-methods: OPTIONS,HEAD,GET,POST,PUT,DELETE
#http.jsonp.enable: true
indices.fielddata.cache.size:  40%

# Search pool
# thread_pool.search.type: fixed
# thread_pool.search.size: 20
thread_pool.search.queue_size: 500

# Bulk pool
# thread_pool.bulk.type: fixed
# thread_pool.bulk.size: 100
thread_pool.bulk.queue_size: 500

# Index pool
# thread_pool.index.type: fixed
# thread_pool.index.size: 32
thread_pool.index.queue_size: 500
