
<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>LEAP GW API Reference</title>
    <link rel="shortcut icon" type="image/x-icon" href="images/fav.ico" sizes="16x16" />
    <style>
      .highlight table td { padding: 5px; }
.highlight table pre { margin: 0; }
.highlight .gh {
  color: #999999;
}
.highlight .sr {
  color: #f6aa11;
}
.highlight .go {
  color: #888888;
}
.highlight .gp {
  color: #555555;
}
.highlight .gs {
}
.highlight .gu {
  color: #aaaaaa;
}
.highlight .nb {
  color: #f6aa11;
}
.highlight .cm {
  color: #75715e;
}
.highlight .cp {
  color: #75715e;
}
.highlight .c1 {
  color: #75715e;
}
.highlight .cs {
  color: #75715e;
}
.highlight .c, .highlight .cd {
  color: #75715e;
}
.highlight .err {
  color: #960050;
}
.highlight .gr {
  color: #960050;
}
.highlight .gt {
  color: #960050;
}
.highlight .gd {
  color: #49483e;
}
.highlight .gi {
  color: #49483e;
}
.highlight .ge {
  color: #49483e;
}
.highlight .kc {
  color: #66d9ef;
}
.highlight .kd {
  color: #66d9ef;
}
.highlight .kr {
  color: #66d9ef;
}
.highlight .no {
  color: #66d9ef;
}
.highlight .kt {
  color: #66d9ef;
}
.highlight .mf {
  color: #ae81ff;
}
.highlight .mh {
  color: #ae81ff;
}
.highlight .il {
  color: #ae81ff;
}
.highlight .mi {
  color: #ae81ff;
}
.highlight .mo {
  color: #ae81ff;
}
.highlight .m, .highlight .mb, .highlight .mx {
  color: #ae81ff;
}
.highlight .sc {
  color: #ae81ff;
}
.highlight .se {
  color: #ae81ff;
}
.highlight .ss {
  color: #ae81ff;
}
.highlight .sd {
  color: #e6db74;
}
.highlight .s2 {
  color: #e6db74;
}
.highlight .sb {
  color: #e6db74;
}
.highlight .sh {
  color: #e6db74;
}
.highlight .si {
  color: #e6db74;
}
.highlight .sx {
  color: #e6db74;
}
.highlight .s1 {
  color: #e6db74;
}
.highlight .s {
  color: #e6db74;
}
.highlight .na {
  color: #a6e22e;
}
.highlight .nc {
  color: #a6e22e;
}
.highlight .nd {
  color: #a6e22e;
}
.highlight .ne {
  color: #a6e22e;
}
.highlight .nf {
  color: #a6e22e;
}
.highlight .vc {
  color: #ffffff;
}
.highlight .nn {
  color: #ffffff;
}
.highlight .nl {
  color: #ffffff;
}
.highlight .ni {
  color: #ffffff;
}
.highlight .bp {
  color: #ffffff;
}
.highlight .vg {
  color: #ffffff;
}
.highlight .vi {
  color: #ffffff;
}
.highlight .nv {
  color: #ffffff;
}
.highlight .w {
  color: #ffffff;
}
.highlight {
  color: #ffffff;
}
.highlight .n, .highlight .py, .highlight .nx {
  color: #ffffff;
}
.highlight .ow {
  color: #f92672;
}
.highlight .nt {
  color: #f92672;
}
.highlight .k, .highlight .kv {
  color: #f92672;
}
.highlight .kn {
  color: #f92672;
}
.highlight .kp {
  color: #f92672;
}
.highlight .o {
  color: #f92672;
}
    </style>
    <link href="stylesheets/screen.css" rel="stylesheet" media="screen" />
    <link href="stylesheets/print.css" rel="stylesheet" media="print" />
      <script src="javascripts/all.js"></script>
  </head>

  <body class="index" data-languages="[]">
    <a href="#" id="nav-button">
      <span>
        NAV
        <img src="images/navbar.png" alt="Navbar" />
      </span>
    </a>
    <div class="toc-wrapper">
        <div class="search">
          <input type="text" class="search" id="input-search" placeholder="Search">
        </div>
        <ul class="search-results"></ul>
      <ul id="toc" class="toc-list-h1">
          <li>
            <a href="#introduction" class="toc-h1 toc-link" data-title="Introduction">Introduction</a>
          </li>
          <li>
            <a href="#document-history" class="toc-h1 toc-link" data-title="Document History">Document History</a>
          </li>
          <li>
            <a href="#user-management" class="toc-h1 toc-link" data-title="User Management">User Management</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#login" class="toc-h2 toc-link" data-title="Login">Login</a>
                  </li>
                  <li>
                    <a href="#logout" class="toc-h2 toc-link" data-title="Logout">Logout</a>
                  </li>
                  <li>
                    <a href="#refreshtoken" class="toc-h2 toc-link" data-title="Refreshtoken">Refreshtoken</a>
                  </li>
                  <li>
                    <a href="#generate-otp" class="toc-h2 toc-link" data-title="Generate OTP">Generate OTP</a>
                  </li>
                  <li>
                    <a href="#verify-otp" class="toc-h2 toc-link" data-title="Verify OTP">Verify OTP</a>
                  </li>
                  <li>
                    <a href="#reset-password" class="toc-h2 toc-link" data-title="Reset Password">Reset Password</a>
                  </li>
                  <li>
                    <a href="#change-password" class="toc-h2 toc-link" data-title="Change Password">Change Password</a>
                  </li>
                  <li>
                    <a href="#create-user-without-password" class="toc-h2 toc-link" data-title="Create User Without Password">Create User Without Password</a>
                  </li>
                  <li>
                    <a href="#update-user-settings" class="toc-h2 toc-link" data-title="Update User Settings">Update User Settings</a>
                  </li>
                  <li>
                    <a href="#list-users" class="toc-h2 toc-link" data-title="List Users">List Users</a>
                  </li>
                  <li>
                    <a href="#user-profile" class="toc-h2 toc-link" data-title="User Profile">User Profile</a>
                  </li>
                  <li>
                    <a href="#delete-user" class="toc-h2 toc-link" data-title="Delete User">Delete User</a>
                  </li>
                  <li>
                    <a href="#create-role" class="toc-h2 toc-link" data-title="Create Role">Create Role</a>
                  </li>
                  <li>
                    <a href="#list-roles" class="toc-h2 toc-link" data-title="List Roles">List Roles</a>
                  </li>
                  <li>
                    <a href="#list-userid-by-roles" class="toc-h2 toc-link" data-title="List UserID by Roles">List UserID by Roles</a>
                  </li>
                  <li>
                    <a href="#activate-user" class="toc-h2 toc-link" data-title="Activate User">Activate User</a>
                  </li>
                  <li>
                    <a href="#deactivate-user" class="toc-h2 toc-link" data-title="Deactivate User">Deactivate User</a>
                  </li>
                  <li>
                    <a href="#user-creation-template" class="toc-h2 toc-link" data-title="User Creation Template">User Creation Template</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#application-management" class="toc-h1 toc-link" data-title="Application Management">Application Management</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#list-apps" class="toc-h2 toc-link" data-title="List Apps">List Apps</a>
                  </li>
                  <li>
                    <a href="#list-application-templates" class="toc-h2 toc-link" data-title="List Application Templates">List Application Templates</a>
                  </li>
                  <li>
                    <a href="#create-application" class="toc-h2 toc-link" data-title="Create Application">Create Application</a>
                  </li>
                  <li>
                    <a href="#clone-app-from-template" class="toc-h2 toc-link" data-title="Clone App from template">Clone App from template</a>
                  </li>
                  <li>
                    <a href="#clone-app-from-existing-app" class="toc-h2 toc-link" data-title="Clone App from existing App">Clone App from existing App</a>
                  </li>
                  <li>
                    <a href="#update-application" class="toc-h2 toc-link" data-title="Update Application">Update Application</a>
                  </li>
                  <li>
                    <a href="#delete-application" class="toc-h2 toc-link" data-title="Delete Application">Delete Application</a>
                  </li>
                  <li>
                    <a href="#retrieve-application" class="toc-h2 toc-link" data-title="Retrieve Application">Retrieve Application</a>
                  </li>
                  <li>
                    <a href="#assign-application" class="toc-h2 toc-link" data-title="Assign Application">Assign Application</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#workflow-management" class="toc-h1 toc-link" data-title="Workflow Management">Workflow Management</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#route-application" class="toc-h2 toc-link" data-title="Route Application">Route Application</a>
                  </li>
                  <li>
                    <a href="#retrieve-application-history" class="toc-h2 toc-link" data-title="Retrieve Application History">Retrieve Application History</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#notification-management" class="toc-h1 toc-link" data-title="Notification Management">Notification Management</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#get-notification-count" class="toc-h2 toc-link" data-title="Get Notification Count">Get Notification Count</a>
                  </li>
                  <li>
                    <a href="#list-notifications" class="toc-h2 toc-link" data-title="List Notifications">List Notifications</a>
                  </li>
                  <li>
                    <a href="#mark-notifications-read" class="toc-h2 toc-link" data-title="Mark Notifications Read">Mark Notifications Read</a>
                  </li>
                  <li>
                    <a href="#delete-notifications" class="toc-h2 toc-link" data-title="Delete Notifications">Delete Notifications</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#plugin-management" class="toc-h1 toc-link" data-title="Plugin Management">Plugin Management</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#list-palettes" class="toc-h2 toc-link" data-title="List Palettes">List Palettes</a>
                  </li>
                  <li>
                    <a href="#get-plugin-metainfo" class="toc-h2 toc-link" data-title="Get Plugin Metainfo">Get Plugin Metainfo</a>
                  </li>
                  <li>
                    <a href="#upload-soap-plugin-files" class="toc-h2 toc-link" data-title="Upload SOAP Plugin files">Upload SOAP Plugin files</a>
                  </li>
                  <li>
                    <a href="#deploy-soap-plugin" class="toc-h2 toc-link" data-title="Deploy SOAP Plugin">Deploy SOAP Plugin</a>
                  </li>
                  <li>
                    <a href="#confirm-plugin-deploy" class="toc-h2 toc-link" data-title="Confirm Plugin Deploy">Confirm Plugin Deploy</a>
                  </li>
                  <li>
                    <a href="#delete-soap-plugin-file" class="toc-h2 toc-link" data-title="Delete SOAP Plugin File">Delete SOAP Plugin File</a>
                  </li>
                  <li>
                    <a href="#list-plugin-stats" class="toc-h2 toc-link" data-title="List Plugin Stats">List Plugin Stats</a>
                  </li>
                  <li>
                    <a href="#get-plugin-settings" class="toc-h2 toc-link" data-title="Get Plugin Settings">Get Plugin Settings</a>
                  </li>
                  <li>
                    <a href="#update-plugin-settings" class="toc-h2 toc-link" data-title="Update Plugin Settings">Update Plugin Settings</a>
                  </li>
                  <li>
                    <a href="#delete-custom-plugin" class="toc-h2 toc-link" data-title="Delete Custom Plugin">Delete Custom Plugin</a>
                  </li>
                  <li>
                    <a href="#activate-deactivate-plugin" class="toc-h2 toc-link" data-title="Activate/Deactivate Plugin">Activate/Deactivate Plugin</a>
                  </li>
                  <li>
                    <a href="#list-plugin-categories" class="toc-h2 toc-link" data-title="List Plugin Categories">List Plugin Categories</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#ussd-services-shortcodes" class="toc-h1 toc-link" data-title="USSD Services - Shortcodes">USSD Services - Shortcodes</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#list-services" class="toc-h2 toc-link" data-title="List Services">List Services</a>
                  </li>
                  <li>
                    <a href="#create-service" class="toc-h2 toc-link" data-title="Create Service">Create Service</a>
                  </li>
                  <li>
                    <a href="#update-service" class="toc-h2 toc-link" data-title="Update Service">Update Service</a>
                  </li>
                  <li>
                    <a href="#delete-service" class="toc-h2 toc-link" data-title="Delete Service">Delete Service</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#ussd-services-gateway-linkings" class="toc-h1 toc-link" data-title="USSD Services - Gateway Linkings">USSD Services - Gateway Linkings</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#list-gateway-configurations" class="toc-h2 toc-link" data-title="List Gateway Configurations">List Gateway Configurations</a>
                  </li>
                  <li>
                    <a href="#create-gateway-configuration" class="toc-h2 toc-link" data-title="Create Gateway Configuration">Create Gateway Configuration</a>
                  </li>
                  <li>
                    <a href="#update-gateway-configuration" class="toc-h2 toc-link" data-title="Update Gateway Configuration">Update Gateway Configuration</a>
                  </li>
                  <li>
                    <a href="#delete-gateway-configuration" class="toc-h2 toc-link" data-title="Delete Gateway Configuration">Delete Gateway Configuration</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#ussd-services-access-control-list" class="toc-h1 toc-link" data-title="USSD Services - Access Control List">USSD Services - Access Control List</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#list-acl" class="toc-h2 toc-link" data-title="List ACL">List ACL</a>
                  </li>
                  <li>
                    <a href="#create-acl" class="toc-h2 toc-link" data-title="Create ACL">Create ACL</a>
                  </li>
                  <li>
                    <a href="#update-acl" class="toc-h2 toc-link" data-title="Update ACL">Update ACL</a>
                  </li>
                  <li>
                    <a href="#delete-acl" class="toc-h2 toc-link" data-title="Delete ACL">Delete ACL</a>
                  </li>
                  <li>
                    <a href="#upload-acl-file" class="toc-h2 toc-link" data-title="Upload ACL file">Upload ACL file</a>
                  </li>
                  <li>
                    <a href="#cancel-list-creation" class="toc-h2 toc-link" data-title="Cancel list creation">Cancel list creation</a>
                  </li>
                  <li>
                    <a href="#delete-specific-file" class="toc-h2 toc-link" data-title="Delete specific file">Delete specific file</a>
                  </li>
                  <li>
                    <a href="#download-master-error-file" class="toc-h2 toc-link" data-title="Download Master/Error file">Download Master/Error file</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#my-downloads" class="toc-h1 toc-link" data-title="My Downloads">My Downloads</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#list-downloadable-files" class="toc-h2 toc-link" data-title="List Downloadable files">List Downloadable files</a>
                  </li>
                  <li>
                    <a href="#download-the-file" class="toc-h2 toc-link" data-title="Download the file">Download the file</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#reports-management" class="toc-h1 toc-link" data-title="Reports Management">Reports Management</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#mktadmin-overview" class="toc-h2 toc-link" data-title="MktAdmin Overview">MktAdmin Overview</a>
                  </li>
                  <li>
                    <a href="#top-20-apps" class="toc-h2 toc-link" data-title="TOP 20 Apps">TOP 20 Apps</a>
                  </li>
                  <li>
                    <a href="#application-wise-stats" class="toc-h2 toc-link" data-title="Application Wise Stats">Application Wise Stats</a>
                  </li>
                  <li>
                    <a href="#interface-wise-stats" class="toc-h2 toc-link" data-title="Interface Wise Stats">Interface Wise Stats</a>
                  </li>
                  <li>
                    <a href="#audit-trails" class="toc-h2 toc-link" data-title="Audit Trails">Audit Trails</a>
                  </li>
                  <li>
                    <a href="#itadmin-overview" class="toc-h2 toc-link" data-title="ITAdmin Overview">ITAdmin Overview</a>
                  </li>
                  <li>
                    <a href="#dashboard-apps-listing" class="toc-h2 toc-link" data-title="Dashboard Apps Listing">Dashboard Apps Listing</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#others" class="toc-h1 toc-link" data-title="Others">Others</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#list-config-apis" class="toc-h2 toc-link" data-title="List Config APIs">List Config APIs</a>
                  </li>
                  <li>
                    <a href="#password-strength" class="toc-h2 toc-link" data-title="Password Strength">Password Strength</a>
                  </li>
                  <li>
                    <a href="#application-states" class="toc-h2 toc-link" data-title="Application States">Application States</a>
                  </li>
                  <li>
                    <a href="#list-macros" class="toc-h2 toc-link" data-title="List Macros">List Macros</a>
                  </li>
                  <li>
                    <a href="#list-operators" class="toc-h2 toc-link" data-title="List Operators">List Operators</a>
                  </li>
                  <li>
                    <a href="#list-languages" class="toc-h2 toc-link" data-title="List Languages">List Languages</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#errors" class="toc-h1 toc-link" data-title="Errors">Errors</a>
          </li>
      </ul>
        <ul class="toc-footer">
            <li><a href='http://localhost:9000'>Login to Studio</a></li>
        </ul>
    </div>
    <div class="page-wrapper">
      <div class="dark-box"></div>
      <div class="content">
        <h1 id='introduction'>Introduction</h1><pre class="highlight javascript tab-javascript"><code>
<span class="kd">let</span> <span class="nx">request</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="s1">'request'</span><span class="p">);</span>
<span class="nx">request</span><span class="p">(</span><span class="s1">'http://example.com/leap_gw/configapi'</span><span class="p">,</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">error</span><span class="p">,</span> <span class="nx">response</span><span class="p">,</span> <span class="nx">body</span><span class="p">)</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">'error:'</span><span class="p">,</span> <span class="nx">error</span><span class="p">);</span> <span class="c1">// Print the error if one occurred</span>
  <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">'statusCode:'</span><span class="p">,</span> <span class="nx">response</span> <span class="o">&amp;&amp;</span> <span class="nx">response</span><span class="p">.</span><span class="nx">statusCode</span><span class="p">);</span> <span class="c1">// Print the response status code if a response was received</span>
  <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">'body:'</span><span class="p">,</span> <span class="nx">body</span><span class="p">);</span> <span class="c1">// Print the JSON for the LEAP gateway configapi.</span>
<span class="p">});</span>

</code></pre>
<p><b>Welcome to the LEAP Gateway API!</b></p>

<p>This part of the document deals with defining a set of HTTP REST api that are meant for consumption by LEAP GUI. LEAP UI consists of features such as user login, user management, apps listing, apps creation etc. All these GUI functions need data coming from the platform by way of AJAX requests. These API are classified and defined here..</p>

<aside class="notice">
<b>Internationalization support:</b>
<p>The caller may pass the <b>Accept-Language</b> header, to indicate the language preference for receiving response message string. Wherever applicable, LEAP platform will try to provide error messages (if any) in that preferred language. Note that this is a preference only, and NOT binding upon the platform to deliver responses in that specific language. The platform will do its best effort to provide in the specified language and if and only if such a language support is available.
</p>
</aside>

<p><b>RESTful API</b>
<p>
A RESTful API is an application program interface (API) that uses HTTP requests to GET, PUT, POST and DELETE data.</p>

<p>A RESTful API -- also referred to as a RESTful web service -- is based on representational state transfer (REST) technology, an architectural style and approach to communications often used in web services development.</p>

<p>REST technology is generally preferred to the more robust Simple Object Access Protocol (SOAP) technology because REST leverages less bandwidth, making it more suitable for internet usage. An API for a website is code that allows two software programs to communicate with each another . The API spells out the proper way for a developer to write a program requesting services from an operating system or other application.</p>

<p>The REST used by browsers can be thought of as the language of the internet. With cloud use on the rise, APIs are emerging to expose web services. REST is a logical choice for building APIs that allow users to connect and interact with cloud services. RESTful APIs are used by such sites as Amazon, Google, LinkedIn and Twitter.
<p></p>
<h1 id='document-history'>Document History</h1>
<table><thead>
<tr>
<th>Version</th>
<th>Date</th>
<th>Description</th>
<th>Author</th>
</tr>
</thead><tbody>
<tr>
<td>1</td>
<td>15-Nov-2017</td>
<td><ul><li>Initial draft</li></ul></td>
<td>Ravindranath</td>
</tr>
<tr>
<td>2</td>
<td>14-Dec-2017</td>
<td><ul><li>Added API for forgot/reset password</li><li>Added API for refresh token</li><li>Revised the response payloads for all API. Now the response payload carries a business specific error code, where applicable</li></ul></td>
<td>swathi.s, Yuvaraj K, Umesh Joge</td>
</tr>
<tr>
<td>3</td>
<td>30-Dec-2017</td>
<td><ul><li>Updated the API for App listing, introducing filtering based on status.<p>Query param: status</p><p>Range: 0-9</p></li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>4</td>
<td>04-Jan-2018</td>
<td><ul><li>Added API for Create new App.</li><li>Added API for Update existing App</li><li>Added API for App deletion.</li></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>5</td>
<td>05-Jan-2018</td>
<td><ul><b>Added Config API</b><li>passwordstrength</li><li>appstates</li><li>operators</li><li>languages</li></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>6</td>
<td>08-Jan-2018</td>
<td><ul><li>Added API for Retrieve list of App Templates</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>7</td>
<td>09-Jan-2018</td>
<td><ul><li>Added the Response Headers for Pagination for list of Apps</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>8</td>
<td>11-Jan-2018</td>
<td><ul><li>Updated the Response Payload for Pagination for list of Apps</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>9</td>
<td>12-Jan-2018</td>
<td><ul><li>Added API for Purge an application</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>10</td>
<td>15-Jan-2018</td>
<td><ul><li>Added API for Create an App using Template</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>11</td>
<td>16-Jan-2018</td>
<td><ul><li>Added response payloads for Change Password</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>12</td>
<td>18-Jan-2018</td>
<td><ul><li>Updated Refresh Token API</li></ul></td>
<td>swathi.s</td>
</tr>
<tr>
<td>13</td>
<td>18-Jan-2018</td>
<td><ul><li>Added Config API for macros</li></ul></td>
<td>Zeba Karishma</td>
</tr>
<tr>
<td>14</td>
<td>23-Jan-2018</td>
<td><ul><li>Updated SendPasswordOTP, verifyOTP APIs.</li></ul></td>
<td>swathi.s</td>
</tr>
<tr>
<td>15</td>
<td>24-Jan-2018</td>
<td><ul><li>Updated the reponse payload for all APIs with respect to JWT expiry status</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>16</td>
<td>26-Jan-2018</td>
<td><ul><li>APIs prefixed with /leap_gw</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>17</td>
<td>05-Feb-2018</td>
<td><ul><li>Create and Save App content type and JSON body error response payload</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>18</td>
<td>06-Feb-2018</td>
<td><ul><li>Added App Simulation Config API</li><li>Added App Simulation API</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>19</td>
<td>14-Feb-2018</td>
<td><ul><li>Added secure OTP for reset password.</li><li>Added purge user API</li></ul></td>
<td>swathi.s</td>
</tr>
<tr>
<td>20</td>
<td>01-Mar-2018</td>
<td><ul><li>Database plugin API</li></ul></td>
<td>Umesh Joge</td>
</tr>
<tr>
<td>21</td>
<td>05-Mar-2018</td>
<td><ul><li>Updated response payload of App module validation API</li></ul></td>
<td>Umesh Joge</td>
</tr>
<tr>
<td>22</td>
<td>05-Mar-2018</td>
<td><ul><li>Updated MySQL request and response payload in Database plugin API</li></ul></td>
<td>Umesh Joge</td>
</tr>
<tr>
<td>23</td>
<td>21-May-2018</td>
<td><ul><li>Updated SOAP upload/deploy API</li></ul></td>
<td>Aditya Prateek</td>
</tr>
<tr>
<td>24</td>
<td>21-May-2018</td>
<td><ul><li>Added work flow API&#39;s</li></ul></td>
<td>Umesh Joge</td>
</tr>
<tr>
<td>25</td>
<td>28-May-2018</td>
<td><ul><li>Updated SOAP API and included testConnection API</li></ul></td>
<td>Aditya Prateek</td>
</tr>
<tr>
<td>26</td>
<td>08-June-2018</td>
<td><ul><li>Added appsOverview API for IT admin overview page</li></ul></td>
<td>Zeba Karishma</td>
</tr>
<tr>
<td>27</td>
<td>08-June-2018</td>
<td><ul><b>Added role APIs to leapGW</b><li>createRole</li><li>list Roles</li><li>list role by roleName</li><li>getUsersByRole</li><li>getUserIdsByRole</li></ul></td>
<td>Zeba Karishma</td>
</tr>
<tr>
<td>28</td>
<td>26-June-2018</td>
<td><ul><b>Added Marketing Admin report API&#39;s</b><li>Overview-Summary</li><li>Overview-Top Used Apps</li><li>Overview-Top Success Apps</li><li>Overview-Top Fail Apps</li><li>Top20Apps - Highest Active Users</li><li>Top20Apps - Highest success</li><li>Top20Apps - Highest Transactions</li><li>AppStats - Module wise transactions</li><li>AppStats - Total Active users</li><li>AppStats - Total transactions</li><li>AppStats - Average response time</li><li>InterfaceStats - Total Transactions</li><li>InterfaceStats - Total applications</li></ul></td>
<td>Umesh Joge</td>
</tr>
<tr>
<td>29</td>
<td>14-Jul-2018</td>
<td><ul><li>Plugin Category listing</li><li>Plugin stats listing</li><li>Retrieve plugin settings schema with data</li><li>Delete custom plugin</li><li>Activate/Deactivate Plugin</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>30</td>
<td>20-Jul-2018</td>
<td><ul><li>Added Audit Trail API</li></ul></td>
<td>Zeba Karishma</td>
</tr>
<tr>
<td>31</td>
<td>12-Sep-2018</td>
<td><ul><li>Added App Clone API</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>32</td>
<td>19-Mar-2019</td>
<td><ul><li>Added USSD Service APIs</li><li>Added USSD Gateway configuration APIs</li></ul></td>
<td>Yuvaraj K</td>
</tr>
<tr>
<td>33</td>
<td>03-May-2019</td>
<td><ul><b>Added ACL APIs</b><li>List ACL</li><li>Create ACL</li><li>Update ACL</li><li>Delete ACL</li><li>Upload ACL file</li><li>Append ACL file</li><li>Delete files on cancel</li><li>Delete a specific file</li><li>Link List to App</li><li>Unlink List from App</li></ul></td>
<td>Yuvaraj K, Rohit Jaiswal, Vrinda Agarwal</td>
</tr>
<tr>
<td>34</td>
<td>03-May-2019</td>
<td><ul><b>My Download APIs</b><li>List Downloadable files</li><li>Download file</li></ul></td>
<td>Yuvaraj K, Rohit Jaiswal, Vrinda Agarwal</td>
</tr>
</tbody></table>
<h1 id='user-management'>User Management</h1><h2 id='login'>Login</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"username"</span><span class="p">:</span><span class="w"> </span><span class="s2">"leap"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"password"</span><span class="p">:</span><span class="w"> </span><span class="s2">"leap@123"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.wkR6bu27zqZiG-6xB-1F6niyCAdkhaiT9QeJJ8uRKRQ",</span>
<span class="s">    "code": 200,</span>
<span class="s">    "msg": "Success"</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad Request</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{ </span>
<span class="s">  "code": 602,</span>
<span class="s">  "msg" : "Invalid username or password"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to authenticate a user with a login credential.</p>
<h3 id='http-request'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/users/authenticate</code></p>
<h3 id='http-response-error-codes'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Login is successful</td>
<td>JWT (JSON Web Token) will be obtainined in response body. { &quot;accessToken&quot;: <JWT>}</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>response payload:{&quot;code&quot;:400,&quot;msg&quot;:[&quot;data should have required property &#39;username&#39;&quot;,&quot;data should have required property &#39;password&#39;&quot;]}</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>Invalid username or password</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>Any internal or unhandled error</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Login API!
</aside>
<h2 id='logout'>Logout</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 0,</span>
<span class="s">  "msg": "success"</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">403</span> <span class="ne">Forbidden</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 623,</span>
<span class="s">  "msg": "JWT blacklisted"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to logout a user.</p>
<h3 id='http-request-2'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/users/logout</code></p>
<h3 id='http-request-header'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-2'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Logout is successful</td>
<td>response payload:{ &quot;code&quot;: 0, &quot;msg&quot;: &quot;success&quot;}</td>
</tr>
<tr>
<td>400</td>
<td>Forbidden</td>
<td>If the user has already logged out but same access token is tried to be accessed.</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>Any internal or unhandled error</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Logout API!
</aside>
<h2 id='refreshtoken'>Refreshtoken</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.wkR6bu27zqZiG-6xB-1F6niyCAdkhaiT9QeJJ8uRKRQ"</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">403</span> <span class="ne">Forbidden</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 623,</span>
<span class="s">  "msg": "JWT blacklisted"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to refresh an Access Token using the Refresh Token you got during authorization.</p>
<h3 id='http-request-3'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/refreshToken</code></p>
<h3 id='http-request-header-2'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-3'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td>response payload:{ &quot;accessToken&quot;: &quot;<token>&quot;}</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Conflict</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Refresh token API!
</aside>
<h2 id='generate-otp'>Generate OTP</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"email"</span><span class="p">:</span><span class="s2">"<EMAIL>"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">403</span> <span class="ne">Forbidden</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 623,</span>
<span class="s">  "msg": "JWT blacklisted"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to generate the OTP for a user, when user forgets the password. OTP will be sent to users e-mail address.</p>
<h3 id='http-request-4'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/sendPasswordOTP</code></p>
<h3 id='http-response-error-codes-4'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>601</td>
<td>Missing &#39;username&#39; property in request payload</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>622</td>
<td>User is not found in system.</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>623</td>
<td>Request is sent too soon.</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>624</td>
<td>Maximum attempt Limit reached.</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Generate OTP API!
</aside>
<h2 id='verify-otp'>Verify OTP</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"otp"</span><span class="p">:</span><span class="w"> </span><span class="s2">"715734"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"newToken"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w"> </span><span class="err">//Default</span><span class="p">:</span><span class="kc">false</span><span class="err">.</span><span class="w"> </span><span class="err">Allows</span><span class="w"> </span><span class="err">to</span><span class="w"> </span><span class="err">send</span><span class="w"> </span><span class="err">new</span><span class="w"> </span><span class="err">token</span><span class="w"> </span><span class="err">after</span><span class="w"> </span><span class="err">OTP</span><span class="w"> </span><span class="err">verification.</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">403</span> <span class="ne">Forbidden</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 623,</span>
<span class="s">  "msg": "JWT blacklisted"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to verify the OTP.</p>
<h3 id='http-request-5'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/verifyOTP</code></p>
<h3 id='http-response-error-codes-5'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>603</td>
<td>OTP Verification failed</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Verify OTP API!
</aside>
<h2 id='reset-password'>Reset Password</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"password"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Leap@1234"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"otp"</span><span class="p">:</span><span class="w"> </span><span class="s2">"715734"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad request</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 601,</span>
<span class="s">  "msg": "Missing 'username' property in request payload."</span>
<span class="s">}</span>
</code></pre><pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad request</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 602,</span>
<span class="s">  "msg": "Invalid 'username' property in request payload."</span>
<span class="s">}</span>
</code></pre><pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad request</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 604,</span>
<span class="s">  "msg": "Password criteria unmet."</span>
<span class="s">}</span>
</code></pre><pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">409</span> <span class="ne">Conflict</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 605,</span>
<span class="s">  "msg": "Unrecognized username"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to reset the password for a user.</p>
<h3 id='http-request-6'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/resetPassword</code></p>
<h3 id='http-response-error-codes-6'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad request</td>
<td>601</td>
<td>Missing &#39;username&#39; property in request payload.</td>
</tr>
<tr>
<td>400</td>
<td>Bad request</td>
<td>602</td>
<td>Invalid &#39;username&#39; property in request payload.</td>
</tr>
<tr>
<td>400</td>
<td>Bad request</td>
<td>604</td>
<td>Password criteria unmet.</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>605</td>
<td>Unrecognized username</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Reset Password API!
</aside>
<h2 id='change-password'>Change Password</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"oldPassword"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="p">,</span><span class="w">
    </span><span class="s2">"password"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad request</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 601,</span>
<span class="s">  "msg": "Missing 'username' property in request payload."</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to change the password for a user.</p>
<h3 id='http-request-7'>HTTP Request</h3>
<p><code>PUT http://example.com/leap_gw/users/changePassword</code></p>
<h3 id='http-request-header-3'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-7'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad request</td>
<td>604</td>
<td>Password criteria unmet</td>
</tr>
<tr>
<td>400</td>
<td>Bad request</td>
<td>608</td>
<td>Old Password is incorrect</td>
</tr>
<tr>
<td>400</td>
<td>Bad request</td>
<td>617</td>
<td>Missing &#39;oldPassword&#39; property in request payload.</td>
</tr>
<tr>
<td>400</td>
<td>Bad request</td>
<td>618</td>
<td>Missing new &#39;password&#39; property in request payload.</td>
</tr>
<tr>
<td>400</td>
<td>Bad request</td>
<td>619</td>
<td>Old and New password cannot be same.</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>620</td>
<td>Password matches with one of the last 5 passwords</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Change Password API!
</aside>
<h2 id='create-user-without-password'>Create User Without Password</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"loginId"</span><span class="p">:</span><span class="w"> </span><span class="s2">"leap"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"firstName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"leap"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"lastName"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"Surname"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"contactNo"</span><span class="p">:</span><span class="w"> </span><span class="s2">"919090909090"</span><span class="p">,</span><span class="w">    </span><span class="err">//Optional</span><span class="w">
    </span><span class="s2">"email"</span><span class="p">:</span><span class="w"> </span><span class="s2">"<EMAIL>"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"activationLink"</span><span class="p">:</span><span class="s2">"http://159.89.175.116/create-password?"</span><span class="p">,</span><span class="w"> </span><span class="err">//mandatory</span><span class="w">
    </span><span class="s2">"role"</span><span class="p">:</span><span class="s2">"app-developer"</span><span class="p">,</span><span class="w"> </span><span class="err">//</span><span class="w"> </span><span class="err">mkt-admin</span><span class="w"> </span><span class="err">or</span><span class="w"> </span><span class="err">it-admin</span><span class="w"> </span><span class="err">or</span><span class="w"> </span><span class="err">app-developer</span><span class="w">
    </span><span class="s2">"tags"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="s2">"lang"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"en"</span><span class="w"> </span><span class="p">}</span><span class="w">      </span><span class="err">//Optional</span><span class="p">,</span><span class="w"> </span><span class="err">lang</span><span class="p">:</span><span class="err">Default</span><span class="w"> </span><span class="err">English</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad request</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 400,</span>
<span class="s">  "msg": [</span>
<span class="s">    "data.firstName should match pattern \"^[A-Za-z]{2,}$\"",</span>
<span class="s">    "data.lastName should match pattern \"^[A-Za-z]{2,}$\"",</span>
<span class="s">    "data.email should match pattern \"^[a-zA-Z0-9]+[a-zA-Z0-9._-]*[a-zA-Z0-9]+@[a-zA-Z0-9]+[a-zA-Z0-9._-]*[a-zA-Z0-9]+.[a-zA-Z]{2,5}$\"",</span>
<span class="s">    "data.contactNo should match pattern \"^[0-9]{6,}$\"",</span>
<span class="s">    "data should have required property 'role'"</span>
<span class="s">  ]</span>
<span class="s">}</span>
</code></pre><pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad request</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 400,</span>
<span class="s">    "msg": [</span>
<span class="s">      "data should have required property 'password'",</span>
<span class="s">      "data should have required property 'firstName'",</span>
<span class="s">      "data should have required property 'email'",</span>
<span class="s">      "data should have required property 'role'",</span>
<span class="s">      "data should have required property 'loginId'",</span>
<span class="s">      "data should have required property 'username'",</span>
<span class="s">      "data should match exactly one schema in oneOf"</span>
<span class="s">    ]</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to create user without the password.</p>
<h3 id='http-request-8'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/users/createUserWithoutPassword</code></p>
<h3 id='http-request-header-4'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-8'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Create User Without Password API!
</aside>
<h2 id='update-user-settings'>Update User Settings</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
  </span><span class="s2">"firstName"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="p">,</span><span class="w">
  </span><span class="s2">"lastName"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="p">,</span><span class="w">
  </span><span class="s2">"contactNo"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="p">,</span><span class="w">
  </span><span class="s2">"tags"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="s2">"lang"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"en"</span><span class="w"> </span><span class="p">}</span><span class="w">    </span><span class="err">//Optional</span><span class="p">,</span><span class="w"> </span><span class="err">lang</span><span class="p">:</span><span class="err">Default</span><span class="w"> </span><span class="err">English</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad request</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 613,</span>
<span class="s">  "msg": "Length of the firstname exceeded"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to update the user settings.</p>
<h3 id='http-request-9'>HTTP Request</h3>
<p><code>PUT http://example.com/leap_gw/users/updateUser/:userId</code></p>

<aside class="notice"><b>userId: </b>is user id</aside>
<h3 id='http-request-header-5'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-9'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>603</td>
<td>Length of the firstname exceeded</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Update User Settings API!
</aside>
<h2 id='list-users'>List Users</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": "success",</span>
<span class="s">    "data": {</span>
<span class="s">        "pageSize": 10,</span>
<span class="s">        "currentPage": 1,</span>
<span class="s">        "totalUsers": 3,</span>
<span class="s">        "totalPages": 1,</span>
<span class="s">        "userList": [ ]</span>
<span class="s">    },</span>
<span class="s">    "limit": 10,</span>
<span class="s">    "offset": 0</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list the users in the system.</p>
<h3 id='http-request-10'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/users</code></p>
<h3 id='http-request-header-6'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>token</td>
<td>null</td>
<td>A Search string.</td>
</tr>
<tr>
<td>page</td>
<td>1</td>
<td>Page number.</td>
</tr>
<tr>
<td>size</td>
<td>10</td>
<td>Number of records per page.</td>
</tr>
<tr>
<td>sortf</td>
<td>null</td>
<td>Name of the field on which to sorting to be applied. Possible values: username or email or createdAt or updatedAt</td>
</tr>
<tr>
<td>order</td>
<td>asc</td>
<td>Sorting order applied for above filed. Possible values: asc or desc</td>
</tr>
<tr>
<td>status</td>
<td>null</td>
<td>Filter the users by user activation status. Choose from active or inactive or both(comma separated).</td>
</tr>
<tr>
<td>startTime</td>
<td>null</td>
<td>Time (in miliseconds) from which user is to be listed.</td>
</tr>
<tr>
<td>endTime</td>
<td>null</td>
<td>Time (in milliseconds) to which user is to be listed.</td>
</tr>
<tr>
<td>app_count</td>
<td>false</td>
<td>Computes the total apps under each of the users, if set to true. Possible values: true or false</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-10'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>603</td>
<td>Length of the firstname exceeded</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Users API!
</aside>
<h2 id='user-profile'>User Profile</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "id": 1,</span>
<span class="s">    "loginId": "",</span>
<span class="s">    "firstName": "",</span>
<span class="s">    "lastName": "",</span>
<span class="s">    "status": "1",</span>
<span class="s">    "email": "",</span>
<span class="s">    "contactNo": "919090909092",</span>
<span class="s">    "activationDt": "2018-12-20T07:00:23.000Z",</span>
<span class="s">    "role": "app-developer",</span>
<span class="s">    "createdBy": 1,</span>
<span class="s">    "modifiedBy": 1,</span>
<span class="s">    "createdAt": "2018-12-20T19:00:23.000Z",</span>
<span class="s">    "updatedAt": "2018-12-20T19:00:23.000Z"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to retrieve the user profile information from the system.</p>
<h3 id='http-request-11'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/users/:userId</code></p>

<aside class="notice"><b>userId: </b>is user id</aside>
<h3 id='http-request-header-7'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-11'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>603</td>
<td>Length of the firstname exceeded</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — User Profile API!
</aside>
<h2 id='delete-user'>Delete User</h2>
<p>Use this endpoint to retrieve the user profile information from the system.</p>
<h3 id='http-request-12'>HTTP Request</h3>
<p><code>DELETE http://example.com/leap_gw/users/deleteUser/:userId</code></p>

<p>or</p>

<p><code>DELETE http://example.com/leap_gw/users/deleteUser/:userId/purge</code></p>

<aside class="notice"><b>userId: </b>is user id</aside>
<h3 id='http-request-header-8'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-12'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>603</td>
<td>Length of the firstname exceeded</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Delete User API!
</aside>
<h2 id='create-role'>Create Role</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"roleName"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="p">,</span><span class="w">
    </span><span class="s2">"perms"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre><pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">409</span> <span class="ne">Bad request</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">  "code": 633,</span>
<span class="s">  "msg": "Role already exists"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to create a new role in the system.</p>
<h3 id='http-request-13'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/roles/createRole</code></p>

<aside class="notice"><b>userId: </b>is user id</aside>
<h3 id='http-request-header-9'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-13'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>603</td>
<td>Length of the firstname exceeded</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Create Role API!
</aside>
<h2 id='list-roles'>List Roles</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">[</span>
<span class="s">    {</span>
<span class="s">        "roleName": "app-developer",</span>
<span class="s">        "description": null</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "roleName": "it-admin",</span>
<span class="s">        "description": null</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "roleName": "mkt-admin",</span>
<span class="s">        "description": null</span>
<span class="s">    }</span>
<span class="s">]</span>
</code></pre>
<p>Use this endpoint to list the roles in the system.</p>
<h3 id='http-request-14'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/roles</code></p>
<h3 id='http-request-header-10'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-14'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>603</td>
<td>Length of the firstname exceeded</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Roles API!
</aside>
<h2 id='list-userid-by-roles'>List UserID by Roles</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"roles"</span><span class="p">:[</span><span class="s2">"root"</span><span class="p">,</span><span class="s2">"app-developer"</span><span class="p">,</span><span class="s2">"it-admin"</span><span class="p">,</span><span class="s2">"mkt-admin"</span><span class="p">]</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "root": [</span>
<span class="s">        1,</span>
<span class="s">        2</span>
<span class="s">    ],</span>
<span class="s">    "app-developer": [</span>
<span class="s">        5,</span>
<span class="s">        6</span>
<span class="s">    ],</span>
<span class="s">    "it-admin": [</span>
<span class="s">        3</span>
<span class="s">    ],</span>
<span class="s">    "mkt-admin": [</span>
<span class="s">        4</span>
<span class="s">    ]</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list the roles in the system.</p>
<h3 id='http-request-15'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/roles/getUserIdsByRole</code></p>
<h3 id='http-request-header-11'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-15'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>603</td>
<td>Length of the firstname exceeded</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List UserID by Roles API!
</aside>
<h2 id='activate-user'>Activate User</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"status"</span><span class="p">:</span><span class="mi">1</span><span class="w"> </span><span class="err">//status</span><span class="w"> </span><span class="err">=</span><span class="mi">1</span><span class="w"> </span><span class="err">corresponds</span><span class="w"> </span><span class="err">to</span><span class="w"> </span><span class="err">activating</span><span class="w"> </span><span class="err">the</span><span class="w"> </span><span class="err">user</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 610,</span>
<span class="s">    "msg": "User Activated Successfully"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to Activate the user.</p>
<h3 id='http-request-16'>HTTP Request</h3>
<p><code>PUT http://example.com/leap_gw/users/activate/:userId</code></p>

<aside class="notice"><b>userId: </b>is user id</aside>
<h3 id='http-request-header-12'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-16'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>641</td>
<td>User Already Activated</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>754</td>
<td>Incorrect status field</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Activate User API!
</aside>
<h2 id='deactivate-user'>Deactivate User</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"status"</span><span class="p">:</span><span class="mi">2</span><span class="w"> </span><span class="err">//status</span><span class="w"> </span><span class="err">=</span><span class="mi">1</span><span class="w"> </span><span class="err">corresponds</span><span class="w"> </span><span class="err">to</span><span class="w"> </span><span class="err">activating</span><span class="w"> </span><span class="err">the</span><span class="w"> </span><span class="err">user</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 611,</span>
<span class="s">    "msg": "User Deactivated Successfully"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to Activate the user.</p>
<h3 id='http-request-17'>HTTP Request</h3>
<p><code>PUT http://example.com/leap_gw/users/deactivate/:userId</code></p>

<aside class="notice"><b>userId: </b>is user id</aside>
<h3 id='http-request-header-13'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-17'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>641</td>
<td>User Already Activated</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>754</td>
<td>Incorrect status field</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Deactivate User API!
</aside>
<h2 id='user-creation-template'>User Creation Template</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "required": [</span>
<span class="s">        "activationLink",</span>
<span class="s">        "email",</span>
<span class="s">        "firstName",</span>
<span class="s">        "role"</span>
<span class="s">    ],</span>
<span class="s">    "oneOf": [</span>
<span class="s">        {</span>
<span class="s">            "required": [</span>
<span class="s">                "loginId"</span>
<span class="s">            ]</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "required": [</span>
<span class="s">                "username"</span>
<span class="s">            ]</span>
<span class="s">        }</span>
<span class="s">    ],</span>
<span class="s">    "properties": {</span>
<span class="s">        "loginId": {</span>
<span class="s">            "type": "string",</span>
<span class="s">            "description": "This field allows alphanumeric characters, hyphen, dot and underscore.MinLength = 2",</span>
<span class="s">            "maxLength": 32,</span>
<span class="s">            "pattern": "[A-Za-z0-9_.-]{2,}$"</span>
<span class="s">        },</span>
<span class="s">        "username": {</span>
<span class="s">            "type": "string",</span>
<span class="s">            "description": "This field allows alphanumeric characters, hyphen, dot and underscore.MinLength = 2",</span>
<span class="s">            "maxLength": 32,</span>
<span class="s">            "pattern": "[A-Za-z0-9_.-]{2,}$"</span>
<span class="s">        },</span>
<span class="s">        "activationLink": {</span>
<span class="s">            "type": "string",</span>
<span class="s">            "description": "Base url to which unique activation endpoint can be attached."</span>
<span class="s">        },</span>
<span class="s">        "firstName": {</span>
<span class="s">            "type": "string",</span>
<span class="s">            "description": "This field allows alphabets only.MinLength = 2",</span>
<span class="s">            "maxLength": 32,</span>
<span class="s">            "pattern": "[A-Za-z]{2,}$"</span>
<span class="s">        },</span>
<span class="s">        "middleName": {</span>
<span class="s">            "type": "string",</span>
<span class="s">            "description": "This field allows alphabets only.MinLength = 2",</span>
<span class="s">            "maxLength": 32,</span>
<span class="s">            "pattern": "[A-Za-z]{2,}$"</span>
<span class="s">        },</span>
<span class="s">        "lastName": {</span>
<span class="s">            "type": "string",</span>
<span class="s">            "description": "This field allows alphabets only.MinLength = 2",</span>
<span class="s">            "maxLength": 32,</span>
<span class="s">            "pattern": "[A-Za-z]{2,}$"</span>
<span class="s">        },</span>
<span class="s">        "email": {</span>
<span class="s">            "type": "string",</span>
<span class="s">            "description": "Valid email id.",</span>
<span class="s">            "maxLength": 128,</span>
<span class="s">            "format": "email",</span>
<span class="s">            "pattern": "^[a-zA-Z0-9._-]{2,}@[a-zA-Z0-9._-]{2,}.[a-zA-Z]{2,5}$"</span>
<span class="s">        },</span>
<span class="s">        "contactNo": {</span>
<span class="s">            "type": "string",</span>
<span class="s">            "description": "This field allows digits only.",</span>
<span class="s">            "maxLength": 24,</span>
<span class="s">            "pattern": "[0-9]{6,}$"</span>
<span class="s">        },</span>
<span class="s">        "role": {</span>
<span class="s">            "type": "string",</span>
<span class="s">            "description": "RoleName of the role to be assigned to the user."</span>
<span class="s">        }</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to get JSON template for user creation form.</p>
<h3 id='http-request-18'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/users/template/createUser</code></p>
<h3 id='http-request-header-14'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-18'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — User Creation Template API!
</aside>
<h1 id='application-management'>Application Management</h1><h2 id='list-apps'>List Apps</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 200,</span>
<span class="s">    "msg": "Apps listed successfully",</span>
<span class="s">    "data": {</span>
<span class="s">        "pageSize": 1,</span>
<span class="s">        "totalApps": 1,</span>
<span class="s">        "totalPages": 1,</span>
<span class="s">        "currentPage": 1,</span>
<span class="s">        "appList": [</span>
<span class="s">            {</span>
<span class="s">                "id": "1548840798135",</span>
<span class="s">                "name": "135 NPP Value Menu App",</span>
<span class="s">                "desc": "135 NPP Value Menu App",</span>
<span class="s">                "status": "6",</span>
<span class="s">                "owner": 5,</span>
<span class="s">                "createdBy": 5,</span>
<span class="s">                "modifiedBy": 5,</span>
<span class="s">                "createdAt": "2019-01-30 15:03:18",</span>
<span class="s">                "updatedAt": "2019-04-13 22:57:25"</span>
<span class="s">            }</span>
<span class="s">        ]</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list the apps in the system.</p>
<h3 id='http-request-19'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/apps</code></p>
<h3 id='http-request-header-15'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-2'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>token</td>
<td>null</td>
<td>A Search string.</td>
</tr>
<tr>
<td>page</td>
<td>1</td>
<td>Page number.</td>
</tr>
<tr>
<td>size</td>
<td>10</td>
<td>Number of records per page.</td>
</tr>
<tr>
<td>sortf</td>
<td>null</td>
<td>Name of the field on which to sorting to be applied. Possible values: name or status or createdAt or updatedAt</td>
</tr>
<tr>
<td>order</td>
<td>asc</td>
<td>Sorting order applied for above filed. Possible values: asc or desc</td>
</tr>
<tr>
<td>status</td>
<td>null</td>
<td>Filter the apps by status codes.  Status codes are from 0 to 9 can be used or comma separated codes.</td>
</tr>
<tr>
<td>user</td>
<td>null</td>
<td>Filter the apps by user id(Owner&#39;s of app)</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-19'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>703</td>
<td>Bad input for size field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>704</td>
<td>Bad input for sortf field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>705</td>
<td>Bad input for order field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>740</td>
<td>Bad input for page field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>743</td>
<td>Page does not exists</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>
<h3 id='http-response-headers'>HTTP Response Headers</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>totalApps</td>
<td>Total Apps availble for the filter.</td>
</tr>
<tr>
<td>firstPage</td>
<td>First Page number of the results for the filter.</td>
</tr>
<tr>
<td>prevPage</td>
<td>Previous Page number from current page.</td>
</tr>
<tr>
<td>currPage</td>
<td>Current page number, Input provided by App developer.</td>
</tr>
<tr>
<td>nextPage</td>
<td>Next Page number to current page.</td>
</tr>
<tr>
<td>lastPage</td>
<td>Last Page number of the results for the filter.</td>
</tr>
<tr>
<td>pageSize</td>
<td>Number of Apps Per page.</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Apps API!
</aside>
<h2 id='list-application-templates'>List Application Templates</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 200,</span>
<span class="s">    "msg": "Apps listed successfully",</span>
<span class="s">    "data": [</span>
<span class="s">        {</span>
<span class="s">            "id": "1543681814395",</span>
<span class="s">            "name": "Balance Enquiry",</span>
<span class="s">            "desc": "App template to demontrate the usage of UCIP module in the application"</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "id": "1543681814396",</span>
<span class="s">            "name": "Call Me Back",</span>
<span class="s">            "desc": "App template to demontrate the Call me back application"</span>
<span class="s">        }</span>
<span class="s">    ]</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list the application templates in the system.</p>
<h3 id='http-request-20'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/api/app/templates</code></p>
<h3 id='http-request-header-16'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-20'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Application Templates API!
</aside>
<h2 id='create-application'>Create Application</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
   </span><span class="s2">"name"</span><span class="p">:</span><span class="s2">"Test App"</span><span class="p">,</span><span class="w">
   </span><span class="s2">"desc"</span><span class="p">:</span><span class="s2">"Test Automation"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<p>Use this endpoint to create application in the system.</p>
<h3 id='http-request-21'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/apps</code></p>
<h3 id='http-request-header-17'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-21'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>201</td>
<td>Created</td>
<td>201</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>711</td>
<td>Payload missing</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>715</td>
<td>App name field is missing</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>729</td>
<td>Invalid App name</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>753</td>
<td>App name already exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Create Application API!
</aside>
<h2 id='clone-app-from-template'>Clone App from template</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
   </span><span class="s2">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"New App"</span><span class="p">,</span><span class="w">
   </span><span class="s2">"desc"</span><span class="p">:</span><span class="w"> </span><span class="s2">"New App using template"</span><span class="p">,</span><span class="w">
   </span><span class="s2">"appTemplateId"</span><span class="p">:</span><span class="w"> </span><span class="mi">1514430879843</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<p>Use this endpoint to create application in the system.</p>
<h3 id='http-request-22'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/api/templates/clone</code></p>
<h3 id='http-request-header-18'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-22'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>201</td>
<td>Created</td>
<td>201</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>711</td>
<td>Payload missing</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>715</td>
<td>App name field is missing</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>729</td>
<td>Invalid App name</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>745</td>
<td>App template id is missing</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>739</td>
<td>Resource conflicts, App already exists</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>746</td>
<td>Resource conflicts, App Template no longer exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Clone App from template API!
</aside>
<h2 id='clone-app-from-existing-app'>Clone App from existing App</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
   </span><span class="s2">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"New App"</span><span class="p">,</span><span class="w">
   </span><span class="s2">"desc"</span><span class="p">:</span><span class="w"> </span><span class="s2">"New App using existing application"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<p>Use this endpoint to create application by cloning from existing application.</p>
<h3 id='http-request-23'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/apps/clone/:appId</code></p>

<aside class="notice"><b>appId: </b>is application id</aside>
<h3 id='http-request-header-19'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-23'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>201</td>
<td>Created</td>
<td>201</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>711</td>
<td>Payload missing</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>715</td>
<td>App name field is missing</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>729</td>
<td>Invalid App name</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>748</td>
<td>App name should have Minimum 5 characters</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>749</td>
<td>App name should not exceed more than 32 characters</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>750</td>
<td>App description field MAX limit exceeds</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>739</td>
<td>Resource conflicts, App already exists</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>746</td>
<td>Resource conflicts, App Template no longer exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Clone App from existing App API!
</aside>
<h2 id='update-application'>Update Application</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
  </span><span class="s2">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Test App Name"</span><span class="p">,</span><span class="w">
  </span><span class="s2">"desc"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="p">,</span><span class="w">
  </span><span class="s2">"appData"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<p>Use this endpoint to update application in the system.</p>
<h3 id='http-request-24'>HTTP Request</h3>
<p><code>PUT http://example.com/leap_gw/apps/:appId</code></p>

<aside class="notice"><b>appId: </b>is application id</aside>
<h3 id='http-request-header-20'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-24'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>711</td>
<td>Payload missing</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>715</td>
<td>App name field is missing</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>729</td>
<td>Invalid App name</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>745</td>
<td>App template id is missing</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>739</td>
<td>Resource conflicts, App no longer exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Update Application API!
</aside>
<h2 id='delete-application'>Delete Application</h2>
<p>Use this endpoint to delete application in the system.</p>
<h3 id='http-request-25'>HTTP Request</h3>
<p><code>DELETE http://example.com/leap_gw/apps/:appId</code></p>

<aside class="notice"><b>appId: </b>is application id</aside>
<h3 id='http-request-header-21'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-25'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>739</td>
<td>Resource conflicts, App no longer exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Delete Application API!
</aside>
<h2 id='retrieve-application'>Retrieve Application</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "id": "1555470891917",</span>
<span class="s">    "name": "Hello World",</span>
<span class="s">    "desc": "",</span>
<span class="s">    "status": "0",</span>
<span class="s">    "owner": 5,</span>
<span class="s">    "createdBy": 5,</span>
<span class="s">    "modifiedBy": 5,</span>
<span class="s">    "createdAt": "2019-04-17 08:44:51",</span>
<span class="s">    "updatedAt": "2019-04-17 08:45:31",</span>
<span class="s">    "appData": {</span>
<span class="s">        "id": "0b004c8d-1ced-4454-928c-db0d7e9812f3",</span>
<span class="s">        "offsetX": 0,</span>
<span class="s">        "offsetY": 0,</span>
<span class="s">        "zoom": 100,</span>
<span class="s">        "gridSize": 0,</span>
<span class="s">        "links": [</span>
<span class="s">            {</span>
<span class="s">                "id": "d7fcbc602785",</span>
<span class="s">                "type": "default",</span>
<span class="s">                "selected": false,</span>
<span class="s">                "source": "9bfa38fe-81d7-4bde-9e13-cf115132306c",</span>
<span class="s">                "sourcePort": "87ef79b986e1",</span>
<span class="s">                "target": "d35c73d5-e454-4721-bf21-1f18b8b732fe",</span>
<span class="s">                "targetPort": "19863763216b",</span>
<span class="s">                "points": [</span>
<span class="s">                    {</span>
<span class="s">                        "id": "8ea677e1-8e29-4758-ab62-0acc5c157f11",</span>
<span class="s">                        "selected": false,</span>
<span class="s">                        "x": 0,</span>
<span class="s">                        "y": 0</span>
<span class="s">                    },</span>
<span class="s">                    {</span>
<span class="s">                        "id": "b8bc48f1-9c36-4102-a158-3eb9345bc903",</span>
<span class="s">                        "selected": false,</span>
<span class="s">                        "x": 0,</span>
<span class="s">                        "y": 0</span>
<span class="s">                    }</span>
<span class="s">                ],</span>
<span class="s">                "color": "link__conditions",</span>
<span class="s">                "extras": {}</span>
<span class="s">            }</span>
<span class="s">        ],</span>
<span class="s">        "startId": "2745140",</span>
<span class="s">        "endId": "eec4e35",</span>
<span class="s">        "modules": {</span>
<span class="s">            "2745140": {</span>
<span class="s">                "settings": {</span>
<span class="s">                    "auth_required": false,</span>
<span class="s">                    "username": "leap",</span>
<span class="s">                    "password": "leap123",</span>
<span class="s">                    "immediateResponseFlag": false,</span>
<span class="s">                    "method": "GET",</span>
<span class="s">                    "samplePayload": "{\"MSISDN\": \"919876543210\",\n\"subscriberInput\": \"919876543210\",\"query\": {\"subscriberType\": 1,\"contentType\": \"text\"}",</span>
<span class="s">                    "contentType": "application/json",</span>
<span class="s">                    "aparty": "MSISDN",</span>
<span class="s">                    "params": [</span>
<span class="s">                        "MSISDN"</span>
<span class="s">                    ],</span>
<span class="s">                    "freeflow": "Freeflow",</span>
<span class="s">                    "title": "App Start"</span>
<span class="s">                },</span>
<span class="s">                "input": {},</span>
<span class="s">                "process": {},</span>
<span class="s">                "output": {</span>
<span class="s">                    "conditions": {</span>
<span class="s">                        "eec4e35_200": {</span>
<span class="s">                            "statement": [</span>
<span class="s">                                {</span>
<span class="s">                                    "expr": [</span>
<span class="s">                                        "ok",</span>
<span class="s">                                        "eq",</span>
<span class="s">                                        "ok"</span>
<span class="s">                                    ]</span>
<span class="s">                                }</span>
<span class="s">                            ],</span>
<span class="s">                            "fallbackcode": "200",</span>
<span class="s">                            "isActive": true</span>
<span class="s">                        }</span>
<span class="s">                    },</span>
<span class="s">                    "codeActive": false,</span>
<span class="s">                    "customCode": "// Custom code panel\n// main function is the default method executed after processing current module\nfunction main(){\n  return \"\";// return end moduleId\n}"</span>
<span class="s">                },</span>
<span class="s">                "conditionsMap": [</span>
<span class="s">                    "eec4e35_200"</span>
<span class="s">                ],</span>
<span class="s">                "menuNavigationOptions": {},</span>
<span class="s">                "coordinates": {</span>
<span class="s">                    "id": "d35c73d5-e454-4721-bf21-1f18b8b732fe",</span>
<span class="s">                    "type": "pallete",</span>
<span class="s">                    "selected": false,</span>
<span class="s">                    "x": 373.84375,</span>
<span class="s">                    "y": 80,</span>
<span class="s">                    "extras": {},</span>
<span class="s">                    "ports": [</span>
<span class="s">                        {</span>
<span class="s">                            "id": "19863763216b",</span>
<span class="s">                            "type": "pallete",</span>
<span class="s">                            "selected": false,</span>
<span class="s">                            "name": "default_node-in",</span>
<span class="s">                            "parentNode": "d35c73d5-e454-4721-bf21-1f18b8b732fe",</span>
<span class="s">                            "links": [</span>
<span class="s">                                "d7fcbc602785"</span>
<span class="s">                            ],</span>
<span class="s">                            "in": true,</span>
<span class="s">                            "color": "link__conditions"</span>
<span class="s">                        }</span>
<span class="s">                    ],</span>
<span class="s">                    "nodeData": {</span>
<span class="s">                        "title": "App Start",</span>
<span class="s">                        "name": "App Start",</span>
<span class="s">                        "id": "2745140",</span>
<span class="s">                        "isEditable": true,</span>
<span class="s">                        "canDelete": false,</span>
<span class="s">                        "status": "",</span>
<span class="s">                        "moduleType": "appStart"</span>
<span class="s">                    }</span>
<span class="s">                },</span>
<span class="s">                "type": "appStart",</span>
<span class="s">                "typeId": "0.1",</span>
<span class="s">                "name": "appStart"</span>
<span class="s">            },</span>
<span class="s">            "eec4e35": {</span>
<span class="s">                "settings": {</span>
<span class="s">                    "title": "App End"</span>
<span class="s">                },</span>
<span class="s">                "input": {},</span>
<span class="s">                "process": {</span>
<span class="s">                    "success": {</span>
<span class="s">                        "code": [</span>
<span class="s">                            "0",</span>
<span class="s">                            "200-226",</span>
<span class="s">                            "S9000"</span>
<span class="s">                        ],</span>
<span class="s">                        "message": "Dear Customer, Your request has been successfully processed"</span>
<span class="s">                    },</span>
<span class="s">                    "customErrors": [</span>
<span class="s">                        {</span>
<span class="s">                            "code": [],</span>
<span class="s">                            "message": ""</span>
<span class="s">                        }</span>
<span class="s">                    ],</span>
<span class="s">                    "defaultError": {</span>
<span class="s">                        "code": "E9000",</span>
<span class="s">                        "message": "Dear Customer, Your request cannot be processed now. Please try again later."</span>
<span class="s">                    },</span>
<span class="s">                    "staticMessage": {</span>
<span class="s">                        "code": false,</span>
<span class="s">                        "message": "Thanks for using LEAP-USSD Services"</span>
<span class="s">                    }</span>
<span class="s">                },</span>
<span class="s">                "output": {</span>
<span class="s">                    "conditions": {}</span>
<span class="s">                },</span>
<span class="s">                "conditionsMap": [],</span>
<span class="s">                "menuNavigationOptions": {},</span>
<span class="s">                "type": "appEnd",</span>
<span class="s">                "typeId": "0.2",</span>
<span class="s">                "coordinates": {</span>
<span class="s">                    "id": "9bfa38fe-81d7-4bde-9e13-cf115132306c",</span>
<span class="s">                    "type": "pallete",</span>
<span class="s">                    "selected": false,</span>
<span class="s">                    "x": 380.84375,</span>
<span class="s">                    "y": 309,</span>
<span class="s">                    "extras": {},</span>
<span class="s">                    "ports": [</span>
<span class="s">                        {</span>
<span class="s">                            "id": "87ef79b986e1",</span>
<span class="s">                            "type": "pallete",</span>
<span class="s">                            "selected": false,</span>
<span class="s">                            "name": "default_node-out",</span>
<span class="s">                            "parentNode": "9bfa38fe-81d7-4bde-9e13-cf115132306c",</span>
<span class="s">                            "links": [</span>
<span class="s">                                "d7fcbc602785"</span>
<span class="s">                            ],</span>
<span class="s">                            "in": false,</span>
<span class="s">                            "color": "link__conditions"</span>
<span class="s">                        }</span>
<span class="s">                    ],</span>
<span class="s">                    "nodeData": {</span>
<span class="s">                        "title": "App End",</span>
<span class="s">                        "name": "App End",</span>
<span class="s">                        "id": "eec4e35",</span>
<span class="s">                        "isEditable": true,</span>
<span class="s">                        "canDelete": false,</span>
<span class="s">                        "status": "",</span>
<span class="s">                        "moduleType": "appEnd"</span>
<span class="s">                    }</span>
<span class="s">                },</span>
<span class="s">                "name": "appEnd"</span>
<span class="s">            }</span>
<span class="s">        },</span>
<span class="s">        "errors": {</span>
<span class="s">            "2745140": [],</span>
<span class="s">            "eec4e35": []</span>
<span class="s">        }</span>
<span class="s">    },</span>
<span class="s">    "code": 200,</span>
<span class="s">    "supportedLanguages": "English"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to register application for simulation.</p>
<h3 id='http-request-26'>HTTP Request</h3>
<p><code>GET http://example.com/samvaadak/apps/:appId</code></p>

<aside class="notice"><b>appId: </b>is application id</aside>
<h3 id='http-request-header-22'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-26'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>202</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>739</td>
<td>Resource conflicts, App no longer exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Delete Application API!
</aside>
<h2 id='assign-application'>Assign Application</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"assignTo"</span><span class="p">:</span><span class="mi">2</span><span class="err">//userId</span><span class="w"> </span><span class="err">of</span><span class="w"> </span><span class="err">the</span><span class="w"> </span><span class="err">developer</span><span class="w"> </span><span class="err">to</span><span class="w"> </span><span class="err">which</span><span class="w"> </span><span class="err">app</span><span class="w"> </span><span class="err">is</span><span class="w"> </span><span class="err">to</span><span class="w"> </span><span class="err">be</span><span class="w"> </span><span class="err">assigned.</span><span class="w">
    </span><span class="err">//</span><span class="w"> </span><span class="err">Use</span><span class="w"> </span><span class="err">value</span><span class="w"> </span><span class="err">as</span><span class="w"> </span><span class="mi">-1</span><span class="w"> </span><span class="err">to</span><span class="w"> </span><span class="err">unassign</span><span class="w"> </span><span class="err">an</span><span class="w"> </span><span class="err">application</span><span class="w"> </span><span class="err">from</span><span class="w"> </span><span class="err">a</span><span class="w"> </span><span class="err">developer.</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<p>Use this endpoint to assign application to app-developer.</p>
<h3 id='http-request-27'>HTTP Request</h3>
<p><code>PATCH http://example.com/leap_gw/api/:appId/assign</code></p>

<aside class="notice"><b>appId: </b>is application id</aside>
<h3 id='http-request-header-23'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-27'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>739</td>
<td>Resource conflicts, App no longer exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Assign Application API!
</aside>
<h1 id='workflow-management'>Workflow Management</h1>
<p><b>Workflow Management Design.</b>
<p align="center"><img src="images/workflowdesign.png" width=700 alt="Workflow Management Design"></p></p>
<h2 id='route-application'>Route Application</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"comment"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Routing for approval"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<p>Use this endpoint to route the application to next level user for approval.</p>
<h3 id='http-request-28'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/workflow/:appId/:action</code></p>

<aside class="notice"><b>appId: </b>is application id</aside>
<h3 id='http-request-header-24'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='workflow-actions'>Workflow actions</h3>
<table><thead>
<tr>
<th>No.</th>
<th>Description</th>
<th>Owner</th>
<th>Action</th>
<th>Action Description</th>
<th>Start State</th>
<th>End State</th>
<th>Notify</th>
</tr>
</thead><tbody>
<tr>
<td>1</td>
<td>Submit to Marketing Admin for Get Approval</td>
<td>app-developer</td>
<td>SUBMIT</td>
<td>Submits for Get Approval</td>
<td>Draft</td>
<td>ApprovalPending</td>
<td>mkt-admin</td>
</tr>
<tr>
<td>2</td>
<td>Simulate and validate the application</td>
<td>mkt-admin</td>
<td>VIEW</td>
<td>View application</td>
<td>ApprovalPending</td>
<td>ApprovalPending</td>
<td>NA</td>
</tr>
<tr>
<td>3</td>
<td>Withdraw application</td>
<td>app-developer</td>
<td>WITHDRAW</td>
<td>Withdraw for Rework</td>
<td>ApprovalPending</td>
<td>Draft</td>
<td>mkt-admin</td>
</tr>
<tr>
<td>4</td>
<td>If Any changes/issues, Marketing Admin Rejects application</td>
<td>mkt-admin</td>
<td>REJECT</td>
<td>Rejects for rework</td>
<td>ApprovalPending</td>
<td>Draft</td>
<td>app-developer</td>
</tr>
<tr>
<td>5</td>
<td>If No Changes/issues, Marketing Admin route to IT Admin as approved for staging</td>
<td>mkt-admin</td>
<td>STAGING</td>
<td>Approved for staging</td>
<td>ApprovalPending</td>
<td>Staging</td>
<td>app-developer,it-admin</td>
</tr>
<tr>
<td>6</td>
<td>If Any issues, IT Admin Reject application</td>
<td>it-admin</td>
<td>REJECT</td>
<td>Reject for rework</td>
<td>Staging</td>
<td>Draft</td>
<td>app-developer,mkt-admin</td>
</tr>
<tr>
<td>7</td>
<td>If Any issues, IT Admin Reject application</td>
<td>it-admin</td>
<td>STAGE</td>
<td>Routes for staging</td>
<td>Staging</td>
<td>Staged</td>
<td>app-developer,mkt-admin</td>
</tr>
<tr>
<td>8</td>
<td>If Any issues, Marketing Admin will Reject application</td>
<td>mkt-admin</td>
<td>REJECT</td>
<td>Rejects for rework</td>
<td>Staged</td>
<td>Draft</td>
<td>app-developer,it-admin</td>
</tr>
<tr>
<td>9</td>
<td>If everything is fine, Marketing Admin will Launch the application to Production</td>
<td>mkt-admin</td>
<td>LAUNCH</td>
<td>Launch for production</td>
<td>Staged</td>
<td>Launched</td>
<td>app-developer,it-admin</td>
</tr>
<tr>
<td>10</td>
<td>Withdraw application from production by making Retire</td>
<td>mkt-admin</td>
<td>RETIRE</td>
<td>Withdraw from production</td>
<td>Launched</td>
<td>Retired</td>
<td>app-developer,it-admin</td>
</tr>
<tr>
<td>11</td>
<td>Launch for production</td>
<td>mkt-admin</td>
<td>LAUNCH</td>
<td>Launch from production</td>
<td>ApprovalPending</td>
<td>Launched</td>
<td>app-developer,it-admin</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-28'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>760</td>
<td>Application is not in the state to perform the action</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Route Application API!
</aside>
<h2 id='retrieve-application-history'>Retrieve Application History</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": 1</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to the retrieve the History of an Application.</p>
<h3 id='http-request-29'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/apps/apphistory/:appId</code></p>

<aside class="notice"><b>appId: </b>is application id</aside>
<h3 id='http-request-header-25'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-29'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>760</td>
<td>Application is not in the state to perform the action</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Retrieve Application History API!
</aside>
<h1 id='notification-management'>Notification Management</h1><h2 id='get-notification-count'>Get Notification Count</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": 1</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to the count of un-read notifications.</p>
<h3 id='http-request-30'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/notification/count</code></p>
<h3 id='http-request-header-26'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-30'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>760</td>
<td>Application is not in the state to perform the action</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Get Notification Count API!
</aside>
<h2 id='list-notifications'>List Notifications</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": "Success",</span>
<span class="s">    "data": {</span>
<span class="s">        "pageSize": 1,</span>
<span class="s">        "totalEvents": 1,</span>
<span class="s">        "totalPages": 1,</span>
<span class="s">        "currentPage": 1,</span>
<span class="s">        "eventList": [</span>
<span class="s">            {</span>
<span class="s">                "msg": "DefaultMktAdmin launched the 135 NPP Value Menu App app",</span>
<span class="s">                "data": {</span>
<span class="s">                    "id": 2,</span>
<span class="s">                    "appId": "1548840798135",</span>
<span class="s">                    "appName": "135 NPP Value Menu App",</span>
<span class="s">                    "action": "8",</span>
<span class="s">                    "comment": null,</span>
<span class="s">                    "is_read": "0",</span>
<span class="s">                    "performer": "4",</span>
<span class="s">                    "createdAt": "2019-04-13 22:57:25",</span>
<span class="s">                    "updatedAt": "2019-04-13 22:57:25"</span>
<span class="s">                }</span>
<span class="s">            }</span>
<span class="s">        ]</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list the notifications.</p>
<h3 id='http-request-31'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/notification</code></p>
<h3 id='http-request-header-27'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-31'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>760</td>
<td>Application is not in the state to perform the action</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Notifications API!
</aside>
<h2 id='mark-notifications-read'>Mark Notifications Read</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"events"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="mi">1</span><span class="p">,</span><span class="mi">2</span><span class="p">,</span><span class="mi">5</span><span class="p">,</span><span class="mi">7</span><span class="p">,</span><span class="mi">10</span><span class="p">]</span><span class="w"> </span><span class="err">//Array</span><span class="w"> </span><span class="err">of</span><span class="w"> </span><span class="err">event</span><span class="w"> </span><span class="err">ID's</span><span class="w"> </span><span class="err">to</span><span class="w"> </span><span class="err">update</span><span class="w"> </span><span class="err">status</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<p>Use this endpoint to update the notification status.</p>
<h3 id='http-request-32'>HTTP Request</h3>
<p><code>PUT http://example.com/leap_gw/notification</code></p>
<h3 id='http-request-header-28'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-32'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>760</td>
<td>Application is not in the state to perform the action</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Mark Notifications Read API!
</aside>
<h2 id='delete-notifications'>Delete Notifications</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"events"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="mi">1</span><span class="p">,</span><span class="mi">2</span><span class="p">,</span><span class="mi">5</span><span class="p">,</span><span class="mi">7</span><span class="p">,</span><span class="mi">10</span><span class="p">]</span><span class="w"> </span><span class="err">//Array</span><span class="w"> </span><span class="err">of</span><span class="w"> </span><span class="err">event</span><span class="w"> </span><span class="err">ID's</span><span class="w"> </span><span class="err">to</span><span class="w"> </span><span class="err">update</span><span class="w"> </span><span class="err">status</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<p>Use this endpoint to delete the notifications.</p>
<h3 id='http-request-33'>HTTP Request</h3>
<p><code>DELETE http://example.com/leap_gw/notification</code></p>
<h3 id='http-request-header-29'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-33'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>760</td>
<td>Application is not in the state to perform the action</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Delete Notifications API!
</aside>
<h1 id='plugin-management'>Plugin Management</h1><h2 id='list-palettes'>List Palettes</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">[</span>
<span class="s">    {</span>
<span class="s">        "category": {</span>
<span class="s">            "type": "appModules",</span>
<span class="s">            "name": "App Modules",</span>
<span class="s">            "id": "0",</span>
<span class="s">            "expand": true</span>
<span class="s">        },</span>
<span class="s">        "modules": [</span>
<span class="s">            {</span>
<span class="s">                "category": "appModules",</span>
<span class="s">                "id": "0.1",</span>
<span class="s">                "type": "appStart",</span>
<span class="s">                "name": "App Start"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "appModules",</span>
<span class="s">                "id": "0.2",</span>
<span class="s">                "type": "appEnd",</span>
<span class="s">                "name": "App End"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "appModules",</span>
<span class="s">                "id": "0.3",</span>
<span class="s">                "type": "appMenu",</span>
<span class="s">                "name": "Menu",</span>
<span class="s">                "description": "Menu design module"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "appModules",</span>
<span class="s">                "id": "0.4",</span>
<span class="s">                "type": "appConfig",</span>
<span class="s">                "name": "App Config"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "appModules",</span>
<span class="s">                "id": "0.5",</span>
<span class="s">                "type": "codeModule",</span>
<span class="s">                "name": "Code Module"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "appModules",</span>
<span class="s">                "id": "0.6",</span>
<span class="s">                "type": "appRedirect",</span>
<span class="s">                "name": "App Redirect"</span>
<span class="s">            }</span>
<span class="s">        ],</span>
<span class="s">        "id": "0"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "category": {</span>
<span class="s">            "type": "channelModules",</span>
<span class="s">            "name": "Channel Modules",</span>
<span class="s">            "id": "1",</span>
<span class="s">            "expand": true</span>
<span class="s">        },</span>
<span class="s">        "modules": [</span>
<span class="s">            {</span>
<span class="s">                "category": "channelModules",</span>
<span class="s">                "id": "1.1",</span>
<span class="s">                "type": "sms",</span>
<span class="s">                "name": "SMS Push",</span>
<span class="s">                "description": "This module is used to push the message using smsc"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "channelModules",</span>
<span class="s">                "id": "1.2",</span>
<span class="s">                "type": "http",</span>
<span class="s">                "name": "HTTP Push",</span>
<span class="s">                "description": "HTTP Channel functions as a request– response protocol in the client– server computing mode"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "channelModules",</span>
<span class="s">                "id": "1.3",</span>
<span class="s">                "type": "email",</span>
<span class="s">                "name": "e-mails Push",</span>
<span class="s">                "description": "HTTP Channel functions as a request– response protocol in the client– server computing mode"</span>
<span class="s">            }</span>
<span class="s">        ],</span>
<span class="s">        "id": "1"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "category": {</span>
<span class="s">            "type": "databases",</span>
<span class="s">            "name": "Database Modules",</span>
<span class="s">            "id": "2",</span>
<span class="s">            "expand": true</span>
<span class="s">        },</span>
<span class="s">        "modules": [</span>
<span class="s">            {</span>
<span class="s">                "category": "databases",</span>
<span class="s">                "id": "2.1",</span>
<span class="s">                "type": "mysql",</span>
<span class="s">                "name": "MySQL"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "databases",</span>
<span class="s">                "id": "2.2",</span>
<span class="s">                "type": "oracle",</span>
<span class="s">                "name": "Oracle DB"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "databases",</span>
<span class="s">                "id": "2.3",</span>
<span class="s">                "type": "mariadb",</span>
<span class="s">                "name": "MariaDB"</span>
<span class="s">            }</span>
<span class="s">        ],</span>
<span class="s">        "id": "2"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "category": {</span>
<span class="s">            "type": "ucip",</span>
<span class="s">            "name": "UCIP v5 Modules",</span>
<span class="s">            "id": "3",</span>
<span class="s">            "description": "UCIP(User Communication Interface Protocol) is intended for user self services such as Adjustments, Account Refill, and Account Enquiries and to extract account details. UCIP is an IP-based protocol used for integration towards the AIR server from the external application. UCIP is an XML over HTTP based protocol, which makes it easy to integrate with a central integration point within a network. The protocol supports both session as well as event based clients. A UCIP request is sent to one of the AIR servers within the network and for redundancy purposes it is required to have N+1 AIR system in the network.",</span>
<span class="s">            "expand": false</span>
<span class="s">        },</span>
<span class="s">        "modules": [</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.1",</span>
<span class="s">                "type": "GeneralUpdate",</span>
<span class="s">                "name": "General Update",</span>
<span class="s">                "description": "The message GeneralUpdate is used by external system to adjust offers, account balances, accumulators, service class and more in a single transaction"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.2",</span>
<span class="s">                "type": "GetAccountDetails",</span>
<span class="s">                "name": "Get Account Details",</span>
<span class="s">                "description": "The GetAccountDetails message is used to obtain account information in order to validate and tailor the user communication"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.3",</span>
<span class="s">                "type": "GetAccountManagementCounters",</span>
<span class="s">                "name": "Get Account Management Counters",</span>
<span class="s">                "description": "The message GetAccountManagementCounters will return account management counters"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.4",</span>
<span class="s">                "type": "GetAccountServiceFeeData",</span>
<span class="s">                "name": "Get Account Service Fee Data",</span>
<span class="s">                "description": "The GetAccountServiceFeeData message is used to fetch service fee data tied to an account"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.5",</span>
<span class="s">                "type": "GetAccumulators",</span>
<span class="s">                "name": "Get Accumulators",</span>
<span class="s">                "description": "The message GetAccumulators is used to obtain accumulator values and (optional) start and end dates related to those accumulators"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.6",</span>
<span class="s">                "type": "GetAllowedServiceClasses",</span>
<span class="s">                "name": "Get AllowedService Classes",</span>
<span class="s">                "description": "The GetAllowedServiceClasses message is used to fetch a list of service classes the subscriber is allowed to change to"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.7",</span>
<span class="s">                "type": "GetBalanceAndDate",</span>
<span class="s">                "name": "Get Balance and Date",</span>
<span class="s">                "description": "The message GetBalanceAndDate is used to perform a balance enquiry on the account associated with a specific subscriber identity"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.8",</span>
<span class="s">                "type": "GetFaFList",</span>
<span class="s">                "name": "Get FaF List",</span>
<span class="s">                "description": "The GetFaFList message is used to fetch the list of Family and Friends numbers with attached FaF indicators"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.9",</span>
<span class="s">                "type": "GetOffers",</span>
<span class="s">                "name": "Get Offers",</span>
<span class="s">                "description": "The message GetOffers will return a list of offers currently assigned to an account"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.10",</span>
<span class="s">                "type": "GetRefillOptions",</span>
<span class="s">                "name": "Get Refill Options",</span>
<span class="s">                "description": "This message GetRefillOptions is used to fetch the refill options"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.11",</span>
<span class="s">                "type": "GetUsageThresholdsAndCounters",</span>
<span class="s">                "name": "Get Usage Thresholds And Counters",</span>
<span class="s">                "description": "The message GetUsageThresholdsAndCounters is used to fetch the active usage counters and thresholds for a subscriber"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.12",</span>
<span class="s">                "type": "Refill",</span>
<span class="s">                "name": "Refill",</span>
<span class="s">                "description": "The message Refill is used to apply a refill from an administrative system to a prepaid account associated with a specific subscriber identity"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.13",</span>
<span class="s">                "type": "UpdateAccountDetails",</span>
<span class="s">                "name": "Update Account Details",</span>
<span class="s">                "description": "The message UpdateAccountDetails is used to update the account information"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.14",</span>
<span class="s">                "type": "UpdateBalanceAndDate",</span>
<span class="s">                "name": "Update Balance And Date",</span>
<span class="s">                "description": "The message UpdateBalanceAndDate is used by external system to adjust balances, start dates and expiry dates on the main account and the dedicated accounts"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.15",</span>
<span class="s">                "type": "UpdateCommunityList",</span>
<span class="s">                "name": "Update Community List",</span>
<span class="s">                "description": "The message UpdateCommunityList set or updates the list of communities which the account belong to"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.16",</span>
<span class="s">                "type": "UpdateFaFList",</span>
<span class="s">                "name": "Update FaF List",</span>
<span class="s">                "description": "The message UpdateFaFList is used to update the Family and Friends list for either the account or subscriber"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.17",</span>
<span class="s">                "type": "UpdateOffer",</span>
<span class="s">                "name": "Update Offer",</span>
<span class="s">                "description": "The UpdateOffer message will assign a new offer or update an existing offer to an account"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.18",</span>
<span class="s">                "type": "UpdateServiceClass",</span>
<span class="s">                "name": "Update Service Class",</span>
<span class="s">                "description": "This message UpdateServiceClass is used to update the service class (SC) for the subscriber"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.19",</span>
<span class="s">                "type": "UpdateSubscriberSegmentation",</span>
<span class="s">                "name": "Update Subscriber Segmentation",</span>
<span class="s">                "description": "The message UpdateSubscriberSegmentation is used in order set or update the accountGroupID and serviceOffering parameters which are used for subscriber segmentation"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "ucip",</span>
<span class="s">                "id": "3.20",</span>
<span class="s">                "type": "UpdateUsageThresholdsAndCounters",</span>
<span class="s">                "name": "Update Usage Thresholds And Counters",</span>
<span class="s">                "description": "The message UpdateUsageThresholdsAndCounters is used to personalize a usage threshold for a subscriber by setting a value other than the default value, either an individual value for a subscriber or an individual value for a provider shared by all consumers"</span>
<span class="s">            }</span>
<span class="s">        ],</span>
<span class="s">        "id": "3"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "category": {</span>
<span class="s">            "type": "acip",</span>
<span class="s">            "name": "ACIP v5 Modules",</span>
<span class="s">            "id": "4",</span>
<span class="s">            "description": "ACIP(Administrative Communication Interface Protocol) is an IP-based protocol used for integration towards the AIR server from the external administrative application. ACIP is an XML over HTTP based protocol, which makes it easy to integrate with a central integration point within a network. The protocol supports both session as well as event based clients. An ACIP request is sent to one of the AIR servers within the network and for redundancy purposes it is required to have N+1 AIR system in the network. ",</span>
<span class="s">            "expand": false</span>
<span class="s">        },</span>
<span class="s">        "modules": [</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.1",</span>
<span class="s">                "type": "AddPeriodicAccountManagementData",</span>
<span class="s">                "name": "Add Periodic Account Management Data",</span>
<span class="s">                "description": "The message AddPeriodicAccountManagementData adds periodic account management data to a subscriber"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.2",</span>
<span class="s">                "type": "DeleteAccumulators",</span>
<span class="s">                "name": "Delete Accumulators",</span>
<span class="s">                "description": "This message is intended to remove one or more accumulators identified by their accumulatorID"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.3",</span>
<span class="s">                "type": "DeleteDedicatedAccounts",</span>
<span class="s">                "name": "Delete Dedicated Accounts",</span>
<span class="s">                "description": "This message is intended to remove one or more dedicated accounts identified by their dedicatedAccountID"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.4",</span>
<span class="s">                "type": "DeleteOffer",</span>
<span class="s">                "name": "Delete Offer",</span>
<span class="s">                "description": "The message DeleteOffer is used to disconnect an offer assigned to an account"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.5",</span>
<span class="s">                "type": "DeletePeriodicAccountManagementData",</span>
<span class="s">                "name": "Delete Periodic Account Management Data",</span>
<span class="s">                "description": "The message DeletePeriodicAccountManagementData deletes periodic account management evaluation data for a subscriber"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.6",</span>
<span class="s">                "type": "DeleteSubscriber",</span>
<span class="s">                "name": "Delete Subscriber",</span>
<span class="s">                "description": "The message DeleteSubscriber performs a deletion of subscriber and account "</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.7",</span>
<span class="s">                "type": "DeleteTimeRestriction",</span>
<span class="s">                "name": "Delete Time Restriction",</span>
<span class="s">                "description": "This message removes any number of time restrictions. If no identifier is given all existing restrictions will be deleted"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.8",</span>
<span class="s">                "type": "DeleteUsageThreshold",</span>
<span class="s">                "name": "Delete Usage Threshold",</span>
<span class="s">                "description": "The message DeleteUsageThresholds removes a personal or common usage threshold from a subscriber"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.9",</span>
<span class="s">                "type": "GetCapabilities",</span>
<span class="s">                "name": "Get Capabilities",</span>
<span class="s">                "description": "The message GetCapabilities is used to fetch available capabilities"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.10",</span>
<span class="s">                "type": "GetPromotionCounters",</span>
<span class="s">                "name": "Get Promotion Counters",</span>
<span class="s">                "description": "The message GetPromotionCounters will return the current accumulated values used as base for the calculation of when to give a promotion and when to progress a promotion plan"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.11",</span>
<span class="s">                "type": "GetPromotionPlans",</span>
<span class="s">                "name": "Get Promotion Plans",</span>
<span class="s">                "description": "The message GetPromotionPlans will return the promotion plan allocated to the subscribers account"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.12",</span>
<span class="s">                "type": "GetTimeRestriction",</span>
<span class="s">                "name": "Get Time Restriction",</span>
<span class="s">                "description": "This message retrieves time restrictions. Any number of time restriction IDs can be specified for retrieval. If no IDs are requested all the time restriction will be returned"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.13",</span>
<span class="s">                "type": "InstallSubscriber",</span>
<span class="s">                "name": "InstallSubscriber",</span>
<span class="s">                "description": "The message InstallSubscriber performs an installation of a subscriber with relevant account and subscriber data"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.14",</span>
<span class="s">                "type": "LinkSubordinateSubscriber",</span>
<span class="s">                "name": "Link Subordinate Subscriber",</span>
<span class="s">                "description": "The message LinkSubordinateSubscriber will link a previously installed subscriber to another subscriber's account"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.15",</span>
<span class="s">                "type": "ManageSubscriber",</span>
<span class="s">                "name": "Manage Subscriber",</span>
<span class="s">                "description": "The message ManageSubscriber will be used to manage single subscribers"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.16",</span>
<span class="s">                "type": "RunPeriodicAccountManagement",</span>
<span class="s">                "name": "Run Periodic Account Management",</span>
<span class="s">                "description": "The message RunPeriodicAccountManagement executes an on demand periodic account management evaluation"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.17",</span>
<span class="s">                "type": "UpdateAccountManagementCounters",</span>
<span class="s">                "name": "Update Account Management Counters",</span>
<span class="s">                "description": "The message UpdateAccountManagementCounters will modify account management counters"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.18",</span>
<span class="s">                "type": "UpdateAccumulators",</span>
<span class="s">                "name": "Update Accumulators",</span>
<span class="s">                "description": "The message UpdateAccumulators performs an adjustment to the counter values of the chosen accumulators"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.19",</span>
<span class="s">                "type": "UpdateCommunicationID",</span>
<span class="s">                "name": "Update CommunicationID",</span>
<span class="s">                "description": "The communication ID change operation changes the Communication ID"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.20",</span>
<span class="s">                "type": "UpdateMidCycleRerating",</span>
<span class="s">                "name": "Update Mid Cycle Rerating",</span>
<span class="s">                "description": "The UpdateMidCycleRerating operation changes the rerating status of the account"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.21",</span>
<span class="s">                "type": "UpdatePeriodicAccountManagementData",</span>
<span class="s">                "name": "Update Periodic Account Management Data",</span>
<span class="s">                "description": "The message UpdatePeriodicAccountManagementData changes periodic account management data for a subscriber"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.22",</span>
<span class="s">                "type": "UpdatePromotionCounters",</span>
<span class="s">                "name": "Update Promotion Counters",</span>
<span class="s">                "description": "The message UpdatePromotionCounters give access to modify the counters used in the calculation when to give a promotion or promotion plan progression"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.23",</span>
<span class="s">                "type": "UpdatePromotionPlan",</span>
<span class="s">                "name": "Update Promotion Plan",</span>
<span class="s">                "description": "The message UpdatePromotionPlan can Add, Set or Delete a promotion plan allocation to an account"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.24",</span>
<span class="s">                "type": "UpdateRefillBarring",</span>
<span class="s">                "name": "Update Refill Barring",</span>
<span class="s">                "description": "The message UpdateRefillBarring either bar or clear the subscriber when attempting refills"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.25",</span>
<span class="s">                "type": "UpdateSubDedicatedAccounts",</span>
<span class="s">                "name": "Update Sub-Dedicated Accounts",</span>
<span class="s">                "description": "The message UpdateSubDedicatedAccounts is used by external system to adjust balances, start dates and expiry dates on the sub dedicated accounts"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.26",</span>
<span class="s">                "type": "UpdateTemporaryBlocked",</span>
<span class="s">                "name": "Update Temporary Blocked",</span>
<span class="s">                "description": "The message UpdateTemporaryBlocked set or clear the temporary blocked status on a subscriber"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "acip",</span>
<span class="s">                "id": "4.27",</span>
<span class="s">                "type": "UpdateTimeRestriction",</span>
<span class="s">                "name": "Update Time Restriction",</span>
<span class="s">                "description": "This message handles both creation and updates to time restrictions. If a restriction id is given that does not exist the restriction will be created"</span>
<span class="s">            }</span>
<span class="s">        ],</span>
<span class="s">        "id": "4"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "category": {</span>
<span class="s">            "type": "miscellaneous",</span>
<span class="s">            "name": "Miscellaneous Modules",</span>
<span class="s">            "id": "5",</span>
<span class="s">            "expand": true</span>
<span class="s">        },</span>
<span class="s">        "modules": [</span>
<span class="s">            {</span>
<span class="s">                "category": "miscellaneous",</span>
<span class="s">                "id": "5.1",</span>
<span class="s">                "type": "OnNetOffNet",</span>
<span class="s">                "name": "OnNetOffNet Check"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "miscellaneous",</span>
<span class="s">                "id": "5.2",</span>
<span class="s">                "type": "CheckUtilization",</span>
<span class="s">                "name": "Utilization Check"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "miscellaneous",</span>
<span class="s">                "id": "5.3",</span>
<span class="s">                "type": "CSVOperations",</span>
<span class="s">                "name": "CSV Operations"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "miscellaneous",</span>
<span class="s">                "id": "5.4",</span>
<span class="s">                "type": "xmlPullInterface",</span>
<span class="s">                "name": "XML PULL Interface",</span>
<span class="s">                "description": "HTTP Channel functions as a request– response protocol in the client– server computing mode"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "miscellaneous",</span>
<span class="s">                "id": "5.5",</span>
<span class="s">                "type": "dbill",</span>
<span class="s">                "name": "dbill",</span>
<span class="s">                "description": "Dbill module"</span>
<span class="s">            }</span>
<span class="s">        ],</span>
<span class="s">        "id": "5"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "category": {</span>
<span class="s">            "type": "vsip",</span>
<span class="s">            "name": "VSIP_v5 Modules",</span>
<span class="s">            "id": "6",</span>
<span class="s">            "description": "VSIP(Voucher Communication Interface Protocol) is intended to be used for integration with voucher administrative systems other than Charging System. VSIP is an RPC style protocol wherein each request-response consists of XML messages sent over HTTP. This makes it easy to integrate with a central integration point within a network. The protocol supports a wide variety of administrative and refill related services. A VSIP request is sent to one of the redundant Voucher Servers.",</span>
<span class="s">            "expand": false</span>
<span class="s">        },</span>
<span class="s">        "modules": [</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.1",</span>
<span class="s">                "type": "GetVoucherDetails",</span>
<span class="s">                "name": "Get Voucher Details",</span>
<span class="s">                "description": "The message GetVoucherDetails is used in order to obtain detailed information on an individual voucher"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.2",</span>
<span class="s">                "type": "GetVoucherHistory",</span>
<span class="s">                "name": "Get Voucher History",</span>
<span class="s">                "description": "The message GetVoucherHistory is used to get historical information for a voucher including information about voucher state changes performed for a specific voucher"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.3",</span>
<span class="s">                "type": "UpdateVoucherState",</span>
<span class="s">                "name": "Update Voucher State"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.4",</span>
<span class="s">                "type": "LoadVoucherCheck",</span>
<span class="s">                "name": "Load Voucher Check",</span>
<span class="s">                "description": "The message LoadVoucherCheck is used to check if the vouchers in a serial number range are loaded into the database"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.5",</span>
<span class="s">                "type": "GenerateVoucher",</span>
<span class="s">                "name": "Generate Voucher",</span>
<span class="s">                "description": "The message GenerateVoucher is used to schedule a generate voucher task"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.6",</span>
<span class="s">                "type": "GetGenerateVoucherTaskInfo",</span>
<span class="s">                "name": "Get Generate Voucher TaskInfo",</span>
<span class="s">                "description": "The message GetGenerateVoucherTaskInfo is used to retrieve information about a GenerateVoucher task"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.7",</span>
<span class="s">                "type": "LoadVoucherBatchFile",</span>
<span class="s">                "name": "Load Voucher Batch File",</span>
<span class="s">                "description": "The message LoadVoucherBatchFile is used to schedule the loading of a batch file"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.8",</span>
<span class="s">                "type": "GetLoadVoucherBatchFileTaskInfo",</span>
<span class="s">                "name": "Get Load Voucher Batch File TaskInfo",</span>
<span class="s">                "description": "The message GetLoadVoucherBatchFileTaskInfo is used to retrieve information about a LoadVoucherBatchFile"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.9",</span>
<span class="s">                "type": "GetVoucherBatchFilesList",</span>
<span class="s">                "name": "Get Voucher Batch Files List",</span>
<span class="s">                "description": "The message GetVoucherBatchFilesList is used to get a list of all generated batch files"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.10",</span>
<span class="s">                "type": "ChangeVoucherState",</span>
<span class="s">                "name": "Change Voucher State",</span>
<span class="s">                "description": "The message ChangeVoucherState message is used to schedule a task to change the state of vouchers"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.11",</span>
<span class="s">                "type": "GetChangeVoucherStateTaskInfo",</span>
<span class="s">                "name": "Get Change Voucher State TaskInfo",</span>
<span class="s">                "description": "The message GetChangeVoucherStateTaskInfo message is used to retrieve information about a ChangeVoucherState task"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.12",</span>
<span class="s">                "type": "PurgeVouchers",</span>
<span class="s">                "name": "Purge Vouchers",</span>
<span class="s">                "description": "The message PurgeVouchers is used to schedule a purge voucher task"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.13",</span>
<span class="s">                "type": "GetPurgeVouchersTaskInfo",</span>
<span class="s">                "name": "Get Purge Vouchers TaskInfo",</span>
<span class="s">                "description": "The message GetPurgeVouchersTaskInfo message is used to return information about a PurgeVoucherTask"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.14",</span>
<span class="s">                "type": "DeleteGenerateVoucherTask",</span>
<span class="s">                "name": "Delete Generate Voucher Task",</span>
<span class="s">                "description": "The DeleteGenerateVoucherTask message is used to delete a task"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.14",</span>
<span class="s">                "type": "ReserveVoucher",</span>
<span class="s">                "name": "Reserve Voucher",</span>
<span class="s">                "description": "This message is used to reserve a voucher. The message represents the start of a refill transaction."</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.15",</span>
<span class="s">                "type": "DeleteLoadVoucherBatchTask",</span>
<span class="s">                "name": "Delete Load Voucher Batch Task",</span>
<span class="s">                "description": "The DeleteLoadVoucherBatchTask message is used to delete a task"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.15",</span>
<span class="s">                "type": "EndReservation",</span>
<span class="s">                "name": "End Reservation",</span>
<span class="s">                "description": "This message is used to reserve a voucher. The message represents the start of a refill transaction."</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.16",</span>
<span class="s">                "type": "DeleteChangeVoucherStateTask",</span>
<span class="s">                "name": "Delete Change Voucher State Task",</span>
<span class="s">                "description": "The DeleteChangeVoucherStateTask message is used to delete a task"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.17",</span>
<span class="s">                "type": "GenerateVoucherDetailsReport",</span>
<span class="s">                "name": "Generate Voucher Details Report",</span>
<span class="s">                "description": "The GenerateVoucherDetailsReport message is used to schedule a report file of all vouchers in a specified batch"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.18",</span>
<span class="s">                "type": "GetGenerateVoucherDetailsReportTaskInfo",</span>
<span class="s">                "name": "Get Generate Voucher Details Report TaskInfo",</span>
<span class="s">                "description": "The GetGenerateVoucherDetailsReportTaskInfo message is used to return information about a GenerateVoucherDetailsReport Task"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.19",</span>
<span class="s">                "type": "GenerateVoucherDistributionReport",</span>
<span class="s">                "name": "Generate Voucher Distribution Report",</span>
<span class="s">                "description": "The GenerateVoucherDistributionReport message is used to create a voucher distribution report file either for a batch or for all vouchers in the database"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.20",</span>
<span class="s">                "type": "GetGenerateVoucherDistributionReportTaskInfo",</span>
<span class="s">                "name": "Get Generate Voucher Distribution Report TaskInfo",</span>
<span class="s">                "description": "The GetGenerateVoucherDistributionReportTaskInfo message is used to return information about a GenerateVoucherDistributionReport task"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.21",</span>
<span class="s">                "type": "GenerateVoucherUsageReport",</span>
<span class="s">                "name": "Generate Voucher Usage Report",</span>
<span class="s">                "description": "The GenerateVoucherUsageReport message is used to schedule a report file of all vouchers that was used within a specified time frame"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.22",</span>
<span class="s">                "type": "GetGenerateVoucherUsageReportTaskInfo",</span>
<span class="s">                "name": "Get Generate Voucher Usage Report TaskInfo",</span>
<span class="s">                "description": "The GetGenerateVoucherUsageReportTaskInfo message is used to return information about a specific or all GenerateVoucherUsageReport tasks"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.23",</span>
<span class="s">                "type": "ChangeVoucherAttribute",</span>
<span class="s">                "name": "Change Voucher Attribute",</span>
<span class="s">                "description": "The message ChangeVoucherAttribute message is used to change (update) user-defined attributes for vouchers that are already loaded into the database"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.24",</span>
<span class="s">                "type": "ChangeEncryptionKey",</span>
<span class="s">                "name": "Change Encryption Key",</span>
<span class="s">                "description": "The message ChangeEncryptionKey is used to change the encryption key for the default cipher profile in the Voucher Server"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "vsip",</span>
<span class="s">                "id": "6.24",</span>
<span class="s">                "type": "GetChangeVoucherAttributeTaskInfo",</span>
<span class="s">                "name": "Get Change Voucher Attribute TaskInfo",</span>
<span class="s">                "description": "The message GetChangeVoucherAttributeTaskInfo message is used to retrieve information about a ChangeVoucherAttribute task"</span>
<span class="s">            }</span>
<span class="s">        ],</span>
<span class="s">        "id": "6"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "category": {</span>
<span class="s">            "type": "Mobiquity",</span>
<span class="s">            "name": "Mobiquity Modules",</span>
<span class="s">            "id": "7",</span>
<span class="s">            "expand": true</span>
<span class="s">        },</span>
<span class="s">        "modules": [</span>
<span class="s">            {</span>
<span class="s">                "category": "Mobiquity",</span>
<span class="s">                "id": "7.0",</span>
<span class="s">                "type": "TransactionEnquiry",</span>
<span class="s">                "name": "Transaction Enquiry",</span>
<span class="s">                "description": "The API Mobiquity shall reverse the transaction amount and charges"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "Mobiquity",</span>
<span class="s">                "id": "7.1",</span>
<span class="s">                "type": "MerchantPaymentWithPin",</span>
<span class="s">                "name": "Merchant Payment With Pin",</span>
<span class="s">                "description": "The API can be used by 3rd party to initiate merchant payment where the AM Customer is entering the AM PIN on 3rd party portal"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "category": "Mobiquity",</span>
<span class="s">                "id": "7.2",</span>
<span class="s">                "type": "TransactionReversal",</span>
<span class="s">                "name": "Transaction Reversal",</span>
<span class="s">                "description": "The API Mobiquity shall reverse the transaction amount and charges"</span>
<span class="s">            }</span>
<span class="s">        ],</span>
<span class="s">        "id": "7"</span>
<span class="s">    }</span>
<span class="s">]</span>
</code></pre>
<p>Use this endpoint to list the palettes.</p>
<h3 id='http-request-34'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/palette</code></p>
<h3 id='http-request-header-30'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-3'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>category</td>
<td>all</td>
<td>category is the name of the palette category. If specified, the api will only return sub items under the matching category. If not specified, returns all.</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-34'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>801</td>
<td>Category is not found</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>763</td>
<td>User dont have the permission</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>800</td>
<td>Internal error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Palettes API!
</aside>
<h2 id='get-plugin-metainfo'>Get Plugin Metainfo</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "typeId": "0.1",</span>
<span class="s">    "name": "appStart",</span>
<span class="s">    "title": "App Start",</span>
<span class="s">    "type": "object",</span>
<span class="s">    "required": [</span>
<span class="s">        "name",</span>
<span class="s">        "coordinates",</span>
<span class="s">        "settings",</span>
<span class="s">        "process",</span>
<span class="s">        "output"</span>
<span class="s">    ],</span>
<span class="s">    "properties": {</span>
<span class="s">        "name": {</span>
<span class="s">            "description": "Name Of the Module",</span>
<span class="s">            "title": "Module name",</span>
<span class="s">            "type": "string",</span>
<span class="s">            "minLength": 1</span>
<span class="s">        },</span>
<span class="s">        "coordinates": {</span>
<span class="s">            "description": "Coordinates Of the Module",</span>
<span class="s">            "title": "Coordinates",</span>
<span class="s">            "type": "object",</span>
<span class="s">            "properties": {</span>
<span class="s">                "id": {</span>
<span class="s">                    "description": "Coordinate ID",</span>
<span class="s">                    "title": "Coordinate ID",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "type": {</span>
<span class="s">                    "description": "Coordinate type",</span>
<span class="s">                    "title": "Coordinate Type",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "ports": {</span>
<span class="s">                    "description": "Coordinate ports",</span>
<span class="s">                    "title": "Coordinate ports",</span>
<span class="s">                    "type": "array",</span>
<span class="s">                    "minItems": 1</span>
<span class="s">                },</span>
<span class="s">                "nodedata": {</span>
<span class="s">                    "description": "Node data",</span>
<span class="s">                    "title": "Node data",</span>
<span class="s">                    "type": "object",</span>
<span class="s">                    "properties": {</span>
<span class="s">                        "title": {</span>
<span class="s">                            "description": "The title of node",</span>
<span class="s">                            "title": "Module title",</span>
<span class="s">                            "type": "string"</span>
<span class="s">                        },</span>
<span class="s">                        "name": {</span>
<span class="s">                            "description": "The name of node",</span>
<span class="s">                            "title": "Module Name",</span>
<span class="s">                            "type": "string"</span>
<span class="s">                        },</span>
<span class="s">                        "id": {</span>
<span class="s">                            "description": "The Id of node",</span>
<span class="s">                            "title": "Module ID",</span>
<span class="s">                            "type": "string"</span>
<span class="s">                        }</span>
<span class="s">                    }</span>
<span class="s">                }</span>
<span class="s">            }</span>
<span class="s">        },</span>
<span class="s">        "settings": {</span>
<span class="s">            "description": "Setting properties Of the Module",</span>
<span class="s">            "title": "Settings",</span>
<span class="s">            "type": "object",</span>
<span class="s">            "required": [</span>
<span class="s">                "method",</span>
<span class="s">                "aparty"</span>
<span class="s">            ],</span>
<span class="s">            "oneOf": [</span>
<span class="s">                {</span>
<span class="s">                    "properties": {</span>
<span class="s">                        "method": {</span>
<span class="s">                            "enum": [</span>
<span class="s">                                "POST"</span>
<span class="s">                            ]</span>
<span class="s">                        }</span>
<span class="s">                    },</span>
<span class="s">                    "required": [</span>
<span class="s">                        "contentType",</span>
<span class="s">                        "samplePayload"</span>
<span class="s">                    ]</span>
<span class="s">                },</span>
<span class="s">                {</span>
<span class="s">                    "properties": {</span>
<span class="s">                        "method": {</span>
<span class="s">                            "enum": [</span>
<span class="s">                                "GET"</span>
<span class="s">                            ]</span>
<span class="s">                        }</span>
<span class="s">                    },</span>
<span class="s">                    "required": [</span>
<span class="s">                        "contentType"</span>
<span class="s">                    ]</span>
<span class="s">                }</span>
<span class="s">            ],</span>
<span class="s">            "properties": {</span>
<span class="s">                "auth_required": {</span>
<span class="s">                    "description": "Basic Authentication",</span>
<span class="s">                    "title": "Basic Authentication",</span>
<span class="s">                    "type": "boolean",</span>
<span class="s">                    "hint": "checkBox",</span>
<span class="s">                    "default": true</span>
<span class="s">                },</span>
<span class="s">                "username": {</span>
<span class="s">                    "description": "Username",</span>
<span class="s">                    "title": "Username",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "default": "leap"</span>
<span class="s">                },</span>
<span class="s">                "password": {</span>
<span class="s">                    "description": "Password",</span>
<span class="s">                    "title": "Password",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "hint": "password",</span>
<span class="s">                    "default": "leap123"</span>
<span class="s">                },</span>
<span class="s">                "immediateResponseFlag": {</span>
<span class="s">                    "description": "Immdediate Response Required",</span>
<span class="s">                    "title": "Immdediate Response Required",</span>
<span class="s">                    "type": "boolean",</span>
<span class="s">                    "hint": "checkBox",</span>
<span class="s">                    "default": false</span>
<span class="s">                },</span>
<span class="s">                "method": {</span>
<span class="s">                    "description": "HTTP Request Method",</span>
<span class="s">                    "title": "HTTP Request Method",</span>
<span class="s">                    "default": "GET",</span>
<span class="s">                    "hint": "radio",</span>
<span class="s">                    "enum": [</span>
<span class="s">                        "GET",</span>
<span class="s">                        "POST"</span>
<span class="s">                    ]</span>
<span class="s">                },</span>
<span class="s">                "samplePayload": {</span>
<span class="s">                    "description": "Request payload",</span>
<span class="s">                    "title": "Sample payload",</span>
<span class="s">                    "hint": "textArea",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "default": "{\"MSISDN\": \"919876543210\",\n\"subscriberInput\": \"919876543210\",\"query\": {\"subscriberType\": 1,\"contentType\": \"text\"}"</span>
<span class="s">                },</span>
<span class="s">                "contentType": {</span>
<span class="s">                    "description": "Content Type to send in request header Of the App Module",</span>
<span class="s">                    "title": "Content-Type",</span>
<span class="s">                    "default": "application/json",</span>
<span class="s">                    "enum": [</span>
<span class="s">                        "application/json"</span>
<span class="s">                    ]</span>
<span class="s">                },</span>
<span class="s">                "aparty": {</span>
<span class="s">                    "description": "A Party Query String Parameter",</span>
<span class="s">                    "title": "A Party Query String Param",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "default": "MSISDN"</span>
<span class="s">                },</span>
<span class="s">                "params": {</span>
<span class="s">                    "description": "Array of output params",</span>
<span class="s">                    "title": "App Request params",</span>
<span class="s">                    "type": "array",</span>
<span class="s">                    "default": [</span>
<span class="s">                        "MSISDN"</span>
<span class="s">                    ]</span>
<span class="s">                },</span>
<span class="s">                "freeflow": {</span>
<span class="s">                    "description": "App Response header",</span>
<span class="s">                    "title": "Response Header-Freeflow",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "default": "Freeflow"</span>
<span class="s">                }</span>
<span class="s">            }</span>
<span class="s">        },</span>
<span class="s">        "input": {</span>
<span class="s">            "description": "Input parameters",</span>
<span class="s">            "title": "Input",</span>
<span class="s">            "type": "object"</span>
<span class="s">        },</span>
<span class="s">        "process": {</span>
<span class="s">            "description": "Setting properties Of the Module",</span>
<span class="s">            "title": "Process",</span>
<span class="s">            "type": "object"</span>
<span class="s">        },</span>
<span class="s">        "output": {</span>
<span class="s">            "description": "The output params",</span>
<span class="s">            "type": "object",</span>
<span class="s">            "customCode": "// Custom code panel\n// main function is the default method executed after processing current module\nfunction main(){\n  return \"\";// return end moduleId\n}",</span>
<span class="s">            "codeActive": true</span>
<span class="s">        }</span>
<span class="s">    },</span>
<span class="s">    "category": "appmodules"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to get the plugin metainfo.</p>
<h3 id='http-request-35'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/plugins/:pluginId</code></p>
<h3 id='http-request-header-31'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='pluginid'>PluginId</h3>
<table><thead>
<tr>
<th>PluginId</th>
<th>Category</th>
</tr>
</thead><tbody>
<tr>
<td>appStart</td>
<td>appModules</td>
</tr>
<tr>
<td>appEnd</td>
<td>appModules</td>
</tr>
<tr>
<td>appMenu</td>
<td>appModules</td>
</tr>
<tr>
<td>appConfig</td>
<td>appModules</td>
</tr>
<tr>
<td>codeModule</td>
<td>appModules</td>
</tr>
<tr>
<td>appRedirect</td>
<td>appModules</td>
</tr>
<tr>
<td>sms</td>
<td>channelModules</td>
</tr>
<tr>
<td>http</td>
<td>channelModules</td>
</tr>
<tr>
<td>email</td>
<td>channelModules</td>
</tr>
<tr>
<td>mysql</td>
<td>databases</td>
</tr>
<tr>
<td>oracle</td>
<td>databases</td>
</tr>
<tr>
<td>mariadb</td>
<td>databases</td>
</tr>
<tr>
<td>OnNetOffNet</td>
<td>miscellaneous</td>
</tr>
<tr>
<td>CheckUtilization</td>
<td>miscellaneous</td>
</tr>
<tr>
<td>CSVOperations</td>
<td>miscellaneous</td>
</tr>
<tr>
<td>xmlPullInterface</td>
<td>miscellaneous</td>
</tr>
<tr>
<td>dbill</td>
<td>miscellaneous</td>
</tr>
<tr>
<td>TransactionEnquiry</td>
<td>Mobiquity</td>
</tr>
<tr>
<td>MerchantPaymentWithPin</td>
<td>Mobiquity</td>
</tr>
<tr>
<td>TransactionReversal</td>
<td>Mobiquity</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-35'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>801</td>
<td>Category is not found</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>763</td>
<td>User dont have the permission</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>800</td>
<td>Internal error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Get Plugin Metainfo API!
</aside>
<h2 id='upload-soap-plugin-files'>Upload SOAP Plugin files</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="err">Body &gt; “form-data”

Key: “file”

Type: file

Value: File to be uploaded
</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{ </span>
<span class="s">    "code": 0, </span>
<span class="s">    "msg": "File upload successful"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to upload the SOAP files(xsd/wsdl files).</p>
<h3 id='http-request-36'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/plugins/soap</code></p>
<h3 id='http-request-header-32'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-36'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>851</td>
<td>WSDL/XSD root upload directory not found</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>406</td>
<td>Not Acceptable</td>
<td>853</td>
<td>File extension not supported</td>
</tr>
<tr>
<td>409</td>
<td>Conflicts</td>
<td>892</td>
<td>Plugin already exists. Check for user&#39;s confirmation</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>854</td>
<td>Internal error while uploading the file</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>870</td>
<td>Error while reading WSDL/XSD directory</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>871</td>
<td>Empty directory. No WSDL/XSD to process</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>872</td>
<td>Error while parsing the WSDL/XSD</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>873</td>
<td>SOAP plugin check error</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>874</td>
<td>WSDL directory deletion unsuccessful</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>875</td>
<td>Create plugin error</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>876</td>
<td>Error in deploy method</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Upload SOAP Plugin files API!
</aside>
<h2 id='deploy-soap-plugin'>Deploy SOAP Plugin</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{ </span>
<span class="s">    "code": 0, </span>
<span class="s">    "msg": "Plugin deployed successfully"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to deploy the SOAP plugon for uploaded SOAP files(xsd/wsdl files).</p>
<h3 id='http-request-37'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/plugins/soap/deploy</code></p>
<h3 id='http-request-header-33'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-37'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>851</td>
<td>WSDL/XSD root upload directory not found</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>406</td>
<td>Not Acceptable</td>
<td>853</td>
<td>File extension not supported</td>
</tr>
<tr>
<td>409</td>
<td>Conflicts</td>
<td>892</td>
<td>Plugin already exists. Check for user&#39;s confirmation</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>854</td>
<td>Internal error while uploading the file</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>870</td>
<td>Error while reading WSDL/XSD directory</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>871</td>
<td>Empty directory. No WSDL/XSD to process</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>872</td>
<td>Error while parsing the WSDL/XSD</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>873</td>
<td>SOAP plugin check error</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>874</td>
<td>WSDL directory deletion unsuccessful</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>875</td>
<td>Create plugin error</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>876</td>
<td>Error in deploy method</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Deploy SOAP Plugin API!
</aside>
<h2 id='confirm-plugin-deploy'>Confirm Plugin Deploy</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{ </span>
<span class="s">    "code": 0, </span>
<span class="s">    "msg": "Plugin overwriting successful"</span>
<span class="s">}</span>
</code></pre><pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{ </span>
<span class="s">    "code": 0, </span>
<span class="s">    "msg": "Plugin overwriting aborted"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to overwrite the existing plugin with user&#39;s confirmation.</p>
<h3 id='http-request-38'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/plugins/soap/confirm</code></p>
<h3 id='http-request-header-34'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-4'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>ans</td>
<td>null</td>
<td>Confirmation yes or no</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-38'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Bad Request</td>
<td>862</td>
<td>Request should have either &#39;Yes&#39; or &#39;No&#39; in query</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>851</td>
<td>WSDL/XSD root upload directory not found</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>406</td>
<td>Not Acceptable</td>
<td>853</td>
<td>File extension not supported</td>
</tr>
<tr>
<td>409</td>
<td>Conflicts</td>
<td>892</td>
<td>Plugin already exists. Check for user&#39;s confirmation</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>860</td>
<td>Error while performing plugin confirmation step</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>861</td>
<td>Plugin Overwriting Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Confirm Plugin Deploy API!
</aside>
<h2 id='delete-soap-plugin-file'>Delete SOAP Plugin File</h2>
<p>Use this endpoint to delete the SOAP plugin file uploaded.</p>
<h3 id='http-request-39'>HTTP Request</h3>
<p><code>DELETE http://example.com/leap_gw/plugins/soap/:filename</code></p>

<aside class="notice"><b>filename: </b>is filename of file uploaded.</aside>
<h3 id='http-request-header-35'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-39'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>859</td>
<td>File deletion unsuccesful</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>900</td>
<td>Plugin - Internal error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Delete SOAP Plugin File API!
</aside>
<h2 id='list-plugin-stats'>List Plugin Stats</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "data": {</span>
<span class="s">        "pageSize": 10,</span>
<span class="s">        "totalPlugins": 22,</span>
<span class="s">        "totalPages": 3,</span>
<span class="s">        "currentPage": 1,</span>
<span class="s">        "pluginList": [</span>
<span class="s">            {</span>
<span class="s">                "id": "0.1",</span>
<span class="s">                "category": "appModules",</span>
<span class="s">                "name": "appStart",</span>
<span class="s">                "description": "",</span>
<span class="s">                "type": 0,</span>
<span class="s">                "status": 1,</span>
<span class="s">                "typeDesc": "Built-In",</span>
<span class="s">                "statusDesc": "Active"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "0.2",</span>
<span class="s">                "category": "appModules",</span>
<span class="s">                "name": "appEnd",</span>
<span class="s">                "description": "",</span>
<span class="s">                "type": 0,</span>
<span class="s">                "status": 1,</span>
<span class="s">                "typeDesc": "Built-In",</span>
<span class="s">                "statusDesc": "Active"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "0.3",</span>
<span class="s">                "category": "appModules",</span>
<span class="s">                "name": "appMenu",</span>
<span class="s">                "description": "Menu design module",</span>
<span class="s">                "type": 0,</span>
<span class="s">                "status": 1,</span>
<span class="s">                "typeDesc": "Built-In",</span>
<span class="s">                "statusDesc": "Active"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "0.4",</span>
<span class="s">                "category": "appModules",</span>
<span class="s">                "name": "appConfig",</span>
<span class="s">                "description": "",</span>
<span class="s">                "type": 0,</span>
<span class="s">                "status": 1,</span>
<span class="s">                "typeDesc": "Built-In",</span>
<span class="s">                "statusDesc": "Active"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "0.5",</span>
<span class="s">                "category": "appModules",</span>
<span class="s">                "name": "codeModule",</span>
<span class="s">                "description": "",</span>
<span class="s">                "type": 0,</span>
<span class="s">                "status": 1,</span>
<span class="s">                "typeDesc": "Built-In",</span>
<span class="s">                "statusDesc": "Active"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "1.1",</span>
<span class="s">                "category": "channelModules",</span>
<span class="s">                "name": "sms",</span>
<span class="s">                "description": "This module is used to push the message using smsc",</span>
<span class="s">                "type": 0,</span>
<span class="s">                "status": 1,</span>
<span class="s">                "typeDesc": "Built-In",</span>
<span class="s">                "statusDesc": "Active"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "1.2",</span>
<span class="s">                "category": "channelModules",</span>
<span class="s">                "name": "http",</span>
<span class="s">                "description": "HTTP Channel functions as a request– response protocol in the client– server computing mode",</span>
<span class="s">                "type": 0,</span>
<span class="s">                "status": 1,</span>
<span class="s">                "typeDesc": "Built-In",</span>
<span class="s">                "statusDesc": "Active"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "2.1",</span>
<span class="s">                "category": "databases",</span>
<span class="s">                "name": "mysql",</span>
<span class="s">                "description": "",</span>
<span class="s">                "type": 0,</span>
<span class="s">                "status": 1,</span>
<span class="s">                "typeDesc": "Built-In",</span>
<span class="s">                "statusDesc": "Active"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "2.2",</span>
<span class="s">                "category": "databases",</span>
<span class="s">                "name": "oracle",</span>
<span class="s">                "description": "",</span>
<span class="s">                "type": 0,</span>
<span class="s">                "status": 1,</span>
<span class="s">                "typeDesc": "Built-In",</span>
<span class="s">                "statusDesc": "Active"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "2.3",</span>
<span class="s">                "category": "databases",</span>
<span class="s">                "name": "mariadb",</span>
<span class="s">                "description": "",</span>
<span class="s">                "type": 0,</span>
<span class="s">                "status": 1,</span>
<span class="s">                "typeDesc": "Built-In",</span>
<span class="s">                "statusDesc": "Active"</span>
<span class="s">            }</span>
<span class="s">        ]</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list the plugin settings.</p>
<h3 id='http-request-40'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/pm</code></p>
<h3 id='http-request-header-36'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-5'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>token</td>
<td>null</td>
<td>A Search string.</td>
</tr>
<tr>
<td>page</td>
<td>1</td>
<td>Page number.</td>
</tr>
<tr>
<td>size</td>
<td>10</td>
<td>Number of records per page.</td>
</tr>
<tr>
<td>type</td>
<td>all</td>
<td>Type of Plugins that is Custom or built-in. Possible values: 0 or 1.</td>
</tr>
<tr>
<td>sortf</td>
<td>null</td>
<td>Name of the field on which to sorting to be applied. Possible values: id or name or status or category or description</td>
</tr>
<tr>
<td>order</td>
<td>asc</td>
<td>Sorting order applied for above filed. Possible values: asc or desc</td>
</tr>
<tr>
<td>status</td>
<td>null</td>
<td>Plugin status value 0(InActive) or 1(Active), if param is negative or if it is undefined. API retrieve all records.</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-40'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>908</td>
<td>Page does not exists</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>900</td>
<td>Plugin - Internal error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>
<h3 id='http-response-headers-2'>HTTP Response Headers</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>totalPlugins</td>
<td>Total plugins availble for the filter.</td>
</tr>
<tr>
<td>firstPage</td>
<td>First Page number of the results for the filter.</td>
</tr>
<tr>
<td>prevPage</td>
<td>Previous Page number from current page.</td>
</tr>
<tr>
<td>currPage</td>
<td>Current page number, Input provided by IT Admin.</td>
</tr>
<tr>
<td>nextPage</td>
<td>Next Page number to current page.</td>
</tr>
<tr>
<td>lastPage</td>
<td>Last Page number of the results for the filter.</td>
</tr>
<tr>
<td>pageSize</td>
<td>Number of plugins Per page.</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Plugin Stats API!
</aside>
<h2 id='get-plugin-settings'>Get Plugin Settings</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "typeId": "6",</span>
<span class="s">    "pluginName": "All",</span>
<span class="s">    "category": "vsip",</span>
<span class="s">    "testRequired": false,</span>
<span class="s">    "url": null,</span>
<span class="s">    "required": [</span>
<span class="s">        "dev",</span>
<span class="s">        "prod"</span>
<span class="s">    ],</span>
<span class="s">    "properties": {</span>
<span class="s">        "dev": {</span>
<span class="s">            "description": "Setting properties Of the Module",</span>
<span class="s">            "title": "Settings",</span>
<span class="s">            "type": "object",</span>
<span class="s">            "required": [</span>
<span class="s">                "host",</span>
<span class="s">                "port",</span>
<span class="s">                "path"</span>
<span class="s">            ],</span>
<span class="s">            "properties": {</span>
<span class="s">                "host": {</span>
<span class="s">                    "description": "The hostname of the VSIP",</span>
<span class="s">                    "title": "Host",</span>
<span class="s">                    "default": "127.0.0.1",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "format": "ipv4"</span>
<span class="s">                },</span>
<span class="s">                "port": {</span>
<span class="s">                    "description": "The port number of the VSIP",</span>
<span class="s">                    "title": "Port",</span>
<span class="s">                    "default": 3306,</span>
<span class="s">                    "type": "integer",</span>
<span class="s">                    "minimum": 1024,</span>
<span class="s">                    "maximum": 65535</span>
<span class="s">                },</span>
<span class="s">                "timeout": {</span>
<span class="s">                    "description": "The time interval defines the Request timeout in millis",</span>
<span class="s">                    "title": "Request Timeout in ms",</span>
<span class="s">                    "default": 10000,</span>
<span class="s">                    "type": "integer"</span>
<span class="s">                },</span>
<span class="s">                "path": {</span>
<span class="s">                    "description": "Path of the VSIP client",</span>
<span class="s">                    "title": "Path",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "username": {</span>
<span class="s">                    "description": "User Name to connect VSIP client",</span>
<span class="s">                    "title": "User name",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "password": {</span>
<span class="s">                    "description": "Password to connect VSIP client",</span>
<span class="s">                    "title": "Password",</span>
<span class="s">                    "hint": "password",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "Content-Type": {</span>
<span class="s">                    "description": "Content Type to send in request header",</span>
<span class="s">                    "title": "Content type",</span>
<span class="s">                    "default": "text/xml",</span>
<span class="s">                    "enum": [</span>
<span class="s">                        "text/xml",</span>
<span class="s">                        "application/json",</span>
<span class="s">                        "text/json"</span>
<span class="s">                    ],</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "Accept-Charset": {</span>
<span class="s">                    "description": "Content Type to send in request header",</span>
<span class="s">                    "title": "Accept charset",</span>
<span class="s">                    "default": "UTF-8",</span>
<span class="s">                    "enum": [</span>
<span class="s">                        "US-ASCII",</span>
<span class="s">                        "UTF-8",</span>
<span class="s">                        "UTF-32",</span>
<span class="s">                        "UTF-64"</span>
<span class="s">                    ],</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "User-Agent": {</span>
<span class="s">                    "description": "The extrenal user agent to send in the HTTP request header",</span>
<span class="s">                    "title": "User agent",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "maxRetry": {</span>
<span class="s">                    "description": "The Max number of retries to connect to the IN",</span>
<span class="s">                    "title": "maxRetry",</span>
<span class="s">                    "default": 1,</span>
<span class="s">                    "type": "integer"</span>
<span class="s">                },</span>
<span class="s">                "retryInterval": {</span>
<span class="s">                    "description": "The time interval betwwen retries to connect to the IN in millis",</span>
<span class="s">                    "title": "retryInterval",</span>
<span class="s">                    "default": 3000,</span>
<span class="s">                    "type": "integer"</span>
<span class="s">                }</span>
<span class="s">            }</span>
<span class="s">        },</span>
<span class="s">        "prod": {</span>
<span class="s">            "description": "Setting properties Of the Module",</span>
<span class="s">            "title": "Settings",</span>
<span class="s">            "type": "object",</span>
<span class="s">            "required": [</span>
<span class="s">                "host",</span>
<span class="s">                "port",</span>
<span class="s">                "path"</span>
<span class="s">            ],</span>
<span class="s">            "properties": {</span>
<span class="s">                "host": {</span>
<span class="s">                    "description": "The hostname of the VSIP",</span>
<span class="s">                    "title": "Host",</span>
<span class="s">                    "default": "127.0.0.1",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "format": "ipv4"</span>
<span class="s">                },</span>
<span class="s">                "port": {</span>
<span class="s">                    "description": "The port number of the VSIP",</span>
<span class="s">                    "title": "Port",</span>
<span class="s">                    "default": 3306,</span>
<span class="s">                    "type": "integer",</span>
<span class="s">                    "minimum": 1024,</span>
<span class="s">                    "maximum": 65535</span>
<span class="s">                },</span>
<span class="s">                "timeout": {</span>
<span class="s">                    "description": "The time interval defines the Request timeout in millis",</span>
<span class="s">                    "title": "Request Timeout in ms",</span>
<span class="s">                    "default": 10000,</span>
<span class="s">                    "type": "integer"</span>
<span class="s">                },</span>
<span class="s">                "path": {</span>
<span class="s">                    "description": "Path of the VSIP client",</span>
<span class="s">                    "title": "Path",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "username": {</span>
<span class="s">                    "description": "User Name to connect VSIP client",</span>
<span class="s">                    "title": "User name",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "password": {</span>
<span class="s">                    "description": "Password to connect VSIP client",</span>
<span class="s">                    "title": "Password",</span>
<span class="s">                    "hint": "password",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "Content-Type": {</span>
<span class="s">                    "description": "Content Type to send in request header",</span>
<span class="s">                    "title": "Content type",</span>
<span class="s">                    "default": "text/xml",</span>
<span class="s">                    "enum": [</span>
<span class="s">                        "text/xml",</span>
<span class="s">                        "application/json",</span>
<span class="s">                        "text/json"</span>
<span class="s">                    ],</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "Accept-Charset": {</span>
<span class="s">                    "description": "Content Type to send in request header",</span>
<span class="s">                    "title": "Accept charset",</span>
<span class="s">                    "default": "UTF-8",</span>
<span class="s">                    "enum": [</span>
<span class="s">                        "US-ASCII",</span>
<span class="s">                        "UTF-8",</span>
<span class="s">                        "UTF-32",</span>
<span class="s">                        "UTF-64"</span>
<span class="s">                    ],</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "User-Agent": {</span>
<span class="s">                    "description": "The extrenal user agent to send in the HTTP request header",</span>
<span class="s">                    "title": "User agent",</span>
<span class="s">                    "type": "string",</span>
<span class="s">                    "minLength": 1</span>
<span class="s">                },</span>
<span class="s">                "maxRetry": {</span>
<span class="s">                    "description": "The Max number of retries to connect to the IN",</span>
<span class="s">                    "title": "maxRetry",</span>
<span class="s">                    "default": 1,</span>
<span class="s">                    "type": "integer"</span>
<span class="s">                },</span>
<span class="s">                "retryInterval": {</span>
<span class="s">                    "description": "The time interval betwwen retries to connect to the IN in millis",</span>
<span class="s">                    "title": "retryInterval",</span>
<span class="s">                    "default": 3000,</span>
<span class="s">                    "type": "integer"</span>
<span class="s">                }</span>
<span class="s">            }</span>
<span class="s">        }</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to get the plugin settings.</p>
<h3 id='http-request-41'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/pm/settings/:id</code></p>

<aside class="notice"><b>id: </b>is plugin id or category id.</aside>
<h3 id='http-request-header-37'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-41'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>908</td>
<td>Page does not exists</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>900</td>
<td>Plugin - Internal error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachabl</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Get Plugin Settings API!
</aside>
<h2 id='update-plugin-settings'>Update Plugin Settings</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
 </span><span class="s2">"dev"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="s2">"host"</span><span class="p">:</span><span class="w"> </span><span class="s2">"*************"</span><span class="p">,</span><span class="w">
  </span><span class="s2">"port"</span><span class="p">:</span><span class="w"> </span><span class="mi">8080</span><span class="p">,</span><span class="w">
  </span><span class="s2">"path"</span><span class="p">:</span><span class="w"> </span><span class="s2">"/mock/vsip"</span><span class="p">,</span><span class="w">
  </span><span class="s2">"username"</span><span class="p">:</span><span class="w"> </span><span class="s2">"test"</span><span class="p">,</span><span class="w">
  </span><span class="s2">"password"</span><span class="p">:</span><span class="w"> </span><span class="s2">"test"</span><span class="p">,</span><span class="w">
  </span><span class="s2">"User-Agent"</span><span class="p">:</span><span class="w"> </span><span class="s2">"test"</span><span class="w">
 </span><span class="p">},</span><span class="w">
 </span><span class="s2">"prod"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="s2">"host"</span><span class="p">:</span><span class="w"> </span><span class="s2">"localhost"</span><span class="p">,</span><span class="w">
  </span><span class="s2">"port"</span><span class="p">:</span><span class="w"> </span><span class="mi">2144</span><span class="p">,</span><span class="w">
  </span><span class="s2">"path"</span><span class="p">:</span><span class="w"> </span><span class="s2">"/vsip/ferfer"</span><span class="p">,</span><span class="w">
  </span><span class="s2">"username"</span><span class="p">:</span><span class="w"> </span><span class="s2">"test"</span><span class="p">,</span><span class="w">
  </span><span class="s2">"password"</span><span class="p">:</span><span class="w"> </span><span class="s2">"test"</span><span class="p">,</span><span class="w">
  </span><span class="s2">"User-Agent"</span><span class="p">:</span><span class="w"> </span><span class="s2">"test"</span><span class="w">
 </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": "Plugin settings updated successfully"</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Failure Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">400</span> <span class="ne">Bad Request</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 907,</span>
<span class="s">    "msg": [</span>
<span class="s">        {</span>
<span class="s">            "parameter": "host",</span>
<span class="s">            "path": "dev",</span>
<span class="s">            "severity": "error",</span>
<span class="s">            "msg": "should have required property 'host'"</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "parameter": "host",</span>
<span class="s">            "path": "prod",</span>
<span class="s">            "severity": "error",</span>
<span class="s">            "msg": "should have required property 'host'"</span>
<span class="s">        }</span>
<span class="s">    ]</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to update the plugin settings.</p>
<h3 id='http-request-42'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/pm/settings/:id</code></p>

<aside class="notice"><b>id: </b>is plugin id or category id.</aside>
<h3 id='http-request-header-38'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-42'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>905</td>
<td>Payload is required to Update the Plugin settings</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>907</td>
<td>JSON Array of errors</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>909</td>
<td>Plugin not found</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>900</td>
<td>Plugin - Internal error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Update Plugin Settings API!
</aside>
<h2 id='delete-custom-plugin'>Delete Custom Plugin</h2>
<p>Use this endpoint to delete the plugin/category.</p>
<h3 id='http-request-43'>HTTP Request</h3>
<p><code>DELETE http://example.com/leap_gw/pm/:id</code></p>

<aside class="notice"><b>id: </b>is plugin id or category id.</aside>
<h3 id='http-request-header-39'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-43'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>901</td>
<td>Invalid plugin id</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>902</td>
<td>built-in plugin cannot be deleted</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>904</td>
<td>Plugin delete failed</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>909</td>
<td>Plugin not found</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>900</td>
<td>Plugin - Internal error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Delete Custom Plugin API!
</aside>
<h2 id='activate-deactivate-plugin'>Activate/Deactivate Plugin</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"status"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="err">//</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="err">-</span><span class="w"> </span><span class="err">active</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="err">-</span><span class="w"> </span><span class="err">Deactive</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": "Plugin Activation is Successful"</span>
<span class="s">}</span>
</code></pre><pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": "Plugin Deactivation is Successful"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to update the plugin/category status.</p>
<h3 id='http-request-44'>HTTP Request</h3>
<p><code>PATCH http://example.com/leap_gw/pm/:id</code></p>

<aside class="notice"><b>id: </b>is plugin id or category id.</aside>
<h3 id='http-request-header-40'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-44'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>910</td>
<td>Payload is required</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>911</td>
<td>Invalid status Field</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>914</td>
<td>Plugin Activation is Failed</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>915</td>
<td>Plugin Deactivation is Failed</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>900</td>
<td>Plugin - Internal error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Activate/Deactivate Plugin API!
</aside>
<h2 id='list-plugin-categories'>List Plugin Categories</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">[</span>
<span class="s">    "appModules",</span>
<span class="s">    "channelModules",</span>
<span class="s">    "databases",</span>
<span class="s">    "ucip",</span>
<span class="s">    "acip",</span>
<span class="s">    "miscellaneous",</span>
<span class="s">    "vsip",</span>
<span class="s">    "Mobiquity",</span>
<span class="s">    "SOAP_soaptopupsuite_soaptopupsuiteSoapBinding"</span>
<span class="s">]</span>
</code></pre>
<p>Use this endpoint to list the categories.</p>
<h3 id='http-request-45'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/pm/categories</code></p>
<h3 id='http-request-header-41'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-45'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>900</td>
<td>Plugin - Internal error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Plugin Categories API!
</aside>
<h1 id='ussd-services-shortcodes'>USSD Services - Shortcodes</h1><h2 id='list-services'>List Services</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 200,</span>
<span class="s">    "msg": "USSD Shortcode Listed Successfully",</span>
<span class="s">    "data": {</span>
<span class="s">        "pageSize": 0,</span>
<span class="s">        "totalShortcodes": 0,</span>
<span class="s">        "totalPages": null,</span>
<span class="s">        "currentPage": 1,</span>
<span class="s">        "ussdServiceList": []</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list the USSD Services in the system.</p>
<h3 id='http-request-46'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/ussd/sc</code></p>
<h3 id='http-request-header-42'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-6'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>token</td>
<td>null</td>
<td>A Search string.</td>
</tr>
<tr>
<td>page</td>
<td>1</td>
<td>Page number.</td>
</tr>
<tr>
<td>size</td>
<td>10</td>
<td>Number of records per page.</td>
</tr>
<tr>
<td>sortf</td>
<td>null</td>
<td>Name of the field on which to sorting to be applied. Possible values: name or status or createdAt or updatedAt</td>
</tr>
<tr>
<td>order</td>
<td>asc</td>
<td>Sorting order applied for above filed. Possible values: asc or desc</td>
</tr>
<tr>
<td>status</td>
<td>null</td>
<td>Filter the servies by status codes.  Status codes are 0 or 1, also it can be comma separated codes like 0,1</td>
</tr>
<tr>
<td>user</td>
<td>null</td>
<td>Filter the servies by user id(Owner&#39;s of USSD Service)</td>
</tr>
<tr>
<td>startTime</td>
<td>null</td>
<td>Time (in miliseconds) from which user is to be listed.</td>
</tr>
<tr>
<td>endTime</td>
<td>null</td>
<td>Time (in milliseconds) to which user is to be listed.</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-46'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>703</td>
<td>Bad input for size field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>704</td>
<td>Bad input for sortf field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>705</td>
<td>Bad input for order field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>740</td>
<td>Bad input for page field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>743</td>
<td>Page does not exists</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>
<h3 id='http-response-headers-3'>HTTP Response Headers</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>totalShortcodes</td>
<td>Total Services availble for the filter.</td>
</tr>
<tr>
<td>firstPage</td>
<td>First Page number of the results for the filter.</td>
</tr>
<tr>
<td>prevPage</td>
<td>Previous Page number from current page.</td>
</tr>
<tr>
<td>currPage</td>
<td>Current page number, Input provided by IT Admin.</td>
</tr>
<tr>
<td>nextPage</td>
<td>Next Page number to current page.</td>
</tr>
<tr>
<td>lastPage</td>
<td>Last Page number of the results for the filter.</td>
</tr>
<tr>
<td>pageSize</td>
<td>Number of Services Per page.</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Services API!
</aside>
<h2 id='create-service'>Create Service</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"shortcode"</span><span class="p">:</span><span class="w"> </span><span class="s2">"153"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"name"</span><span class="p">:</span><span class="s2">"USSD Service3"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"desc"</span><span class="p">:</span><span class="w"> </span><span class="s2">"refrerr"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"status"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w">
    </span><span class="s2">"options"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="s2">"KEY1"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VALUE"</span><span class="p">,</span><span class="w">
        </span><span class="s2">"KEY2"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VALUE"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="s2">"appId"</span><span class="p">:</span><span class="w"> </span><span class="mi">1550396075330</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": "USSD Service created successfully",</span>
<span class="s">    "id": 1555650655614</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to create the USSD Service.</p>
<h3 id='http-request-47'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/ussd/sc</code></p>
<h3 id='http-request-header-43'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-47'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>1006</td>
<td>Service name already exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Create Service API!
</aside>
<h2 id='update-service'>Update Service</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"name"</span><span class="p">:</span><span class="s2">"USSD Service3"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"desc"</span><span class="p">:</span><span class="w"> </span><span class="s2">"refrerr"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"options"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="s2">"KEY1"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VALUE"</span><span class="p">,</span><span class="w">
        </span><span class="s2">"KEY2"</span><span class="p">:</span><span class="w"> </span><span class="s2">"VALUE"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="s2">"appId"</span><span class="p">:</span><span class="w"> </span><span class="mi">1550396075330</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": "USSD Shortcode Updated Successfully"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to update the USSD Service.</p>
<h3 id='http-request-48'>HTTP Request</h3>
<p><code>PATCH http://example.com/leap_gw/ussd/sc/:shortcode</code></p>
<h3 id='http-request-header-44'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-48'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>1006</td>
<td>Service name already exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Update Service API!
</aside>
<h2 id='delete-service'>Delete Service</h2>
<p>Use this endpoint to update the USSD Service.</p>
<h3 id='http-request-49'>HTTP Request</h3>
<p><code>DELETE http://example.com/leap_gw/ussd/sc/:shortcode</code></p>
<h3 id='http-request-header-45'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-49'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>1008</td>
<td>USSD Shortcode does not exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Delete Service API!
</aside>
<h1 id='ussd-services-gateway-linkings'>USSD Services - Gateway Linkings</h1><h2 id='list-gateway-configurations'>List Gateway Configurations</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 200,</span>
<span class="s">    "msg": "USSD Gateway Listed Successfully",</span>
<span class="s">    "data": {</span>
<span class="s">        "pageSize": 0,</span>
<span class="s">        "totalGateways": 0,</span>
<span class="s">        "totalPages": null,</span>
<span class="s">        "currentPage": 1,</span>
<span class="s">        "ussdGWList": []</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list the USSD Gateway Configurations in the system.</p>
<h3 id='http-request-50'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/ussd/gw</code></p>
<h3 id='http-request-header-46'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-7'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>token</td>
<td>null</td>
<td>A Search string.</td>
</tr>
<tr>
<td>page</td>
<td>1</td>
<td>Page number.</td>
</tr>
<tr>
<td>size</td>
<td>10</td>
<td>Number of records per page.</td>
</tr>
<tr>
<td>sortf</td>
<td>null</td>
<td>Name of the field on which to sorting to be applied. Possible values: name or status or createdAt or updatedAt</td>
</tr>
<tr>
<td>order</td>
<td>asc</td>
<td>Sorting order applied for above filed. Possible values: asc or desc</td>
</tr>
<tr>
<td>status</td>
<td>null</td>
<td>Filter the servies by status codes.  Status codes are 0 or 1, also it can be comma separated codes like 0,1</td>
</tr>
<tr>
<td>user</td>
<td>null</td>
<td>Filter the servies by user id(Owner&#39;s of USSD Gateway config)</td>
</tr>
<tr>
<td>startTime</td>
<td>null</td>
<td>Time (in miliseconds) from which user is to be listed.</td>
</tr>
<tr>
<td>endTime</td>
<td>null</td>
<td>Time (in milliseconds) to which user is to be listed.</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-50'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>703</td>
<td>Bad input for size field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>704</td>
<td>Bad input for sortf field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>705</td>
<td>Bad input for order field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>740</td>
<td>Bad input for page field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>743</td>
<td>Page does not exists</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>
<h3 id='http-response-headers-4'>HTTP Response Headers</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>totalgateways</td>
<td>Total USSD Gateways configs availble for the filter.</td>
</tr>
<tr>
<td>firstPage</td>
<td>First Page number of the results for the filter.</td>
</tr>
<tr>
<td>prevPage</td>
<td>Previous Page number from current page.</td>
</tr>
<tr>
<td>currPage</td>
<td>Current page number, Input provided by IT Admin.</td>
</tr>
<tr>
<td>nextPage</td>
<td>Next Page number to current page.</td>
</tr>
<tr>
<td>lastPage</td>
<td>Last Page number of the results for the filter.</td>
</tr>
<tr>
<td>pageSize</td>
<td>Number of USSD Gateways configs Per page.</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Gateway Configurations API!
</aside>
<h2 id='create-gateway-configuration'>Create Gateway Configuration</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"auto_gw1"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"type"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
    </span><span class="s2">"ip"</span><span class="p">:</span><span class="w"> </span><span class="s2">"***********"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"port"</span><span class="p">:</span><span class="w"> </span><span class="mi">4545</span><span class="p">,</span><span class="w">
    </span><span class="s2">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"LC135"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"pass"</span><span class="p">:</span><span class="w"> </span><span class="s2">"LC135"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"enquiry_link_interval"</span><span class="p">:</span><span class="w"> </span><span class="mi">10000</span><span class="p">,</span><span class="w">
    </span><span class="s2">"no_of_channels"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w">
    </span><span class="s2">"system_type"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
    </span><span class="s2">"interface_type"</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w">
    </span><span class="s2">"version"</span><span class="p">:</span><span class="w"> </span><span class="mi">4</span><span class="p">,</span><span class="w">
    </span><span class="s2">"buffer_size"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
    </span><span class="s2">"parameters_order"</span><span class="p">:</span><span class="w"> </span><span class="s2">"SERVICE_STRING||IMSI||MSISDN||MSC||MCC||MNC||LAC||CI"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"url"</span><span class="p">:</span><span class="w"> </span><span class="s2">"smpp://localhost:2775"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"maxUssdMessageCharacters"</span><span class="p">:</span><span class="w"> </span><span class="mi">182</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": "USSD Gateway Configuration created successfully",</span>
<span class="s">    "id": 1555650655614</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to create the USSD Gateway Configuration.</p>
<h3 id='http-request-51'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/ussd/gw</code></p>
<h3 id='http-request-header-47'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-51'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>1006</td>
<td>Gateway Config name already exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Create Gateway Configuration API!
</aside>
<h2 id='update-gateway-configuration'>Update Gateway Configuration</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"auto_gw1"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"type"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
    </span><span class="s2">"ip"</span><span class="p">:</span><span class="w"> </span><span class="s2">"***********"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"port"</span><span class="p">:</span><span class="w"> </span><span class="mi">4545</span><span class="p">,</span><span class="w">
    </span><span class="s2">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"LC135"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"pass"</span><span class="p">:</span><span class="w"> </span><span class="s2">"LC135"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"enquiry_link_interval"</span><span class="p">:</span><span class="w"> </span><span class="mi">10000</span><span class="p">,</span><span class="w">
    </span><span class="s2">"no_of_channels"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w">
    </span><span class="s2">"system_type"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
    </span><span class="s2">"interface_type"</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w">
    </span><span class="s2">"version"</span><span class="p">:</span><span class="w"> </span><span class="mi">4</span><span class="p">,</span><span class="w">
    </span><span class="s2">"buffer_size"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
    </span><span class="s2">"parameters_order"</span><span class="p">:</span><span class="w"> </span><span class="s2">"SERVICE_STRING||IMSI||MSISDN||MSC||MCC||MNC||LAC||CI"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"url"</span><span class="p">:</span><span class="w"> </span><span class="s2">"smpp://localhost:2775"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"maxUssdMessageCharacters"</span><span class="p">:</span><span class="w"> </span><span class="mi">182</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": "USSD Gateway Configuration Updated Successfully"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to update the USSD Gateway Configuration.</p>
<h3 id='http-request-52'>HTTP Request</h3>
<p><code>PATCH http://example.com/leap_gw/ussd/gw/:gatewayId</code></p>

<aside class="notice"><b>gatewayId: </b>is USSD Gateway configuration id.</aside>
<h3 id='http-request-header-48'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-52'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>1006</td>
<td>Gateway Configuration name already exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Update Gateway Configuration API!
</aside>
<h2 id='delete-gateway-configuration'>Delete Gateway Configuration</h2>
<p>Use this endpoint to delete the USSD Gateway Configuration.</p>
<h3 id='http-request-53'>HTTP Request</h3>
<p><code>DELETE http://example.com/leap_gw/ussd/gw/:gatewayId</code></p>

<aside class="notice"><b>gatewayId: </b>is USSD Gateway configuration id.</aside>
<h3 id='http-request-header-49'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-53'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>706</td>
<td>Gateway Configuration found in AppStore</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>1008</td>
<td>Gateway Configuration does not exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Delete Gateway Configuration API!
</aside>
<h1 id='ussd-services-access-control-list'>USSD Services - Access Control List</h1><h2 id='list-acl'>List ACL</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 200,</span>
<span class="s">    "msg": "ACL listed successfully",</span>
<span class="s">    "data": {</span>
<span class="s">        "pageSize": 1,</span>
<span class="s">        "totalACLs": 1,</span>
<span class="s">        "totalPages": 1,</span>
<span class="s">        "currentPage": 1,</span>
<span class="s">        "list": [</span>
<span class="s">            {</span>
<span class="s">                "id": "1548840798135",</span>
<span class="s">                "name": "My LIST 1",</span>
<span class="s">                "type": "MSISDN",</span>
<span class="s">                "status": "Completed", // Possible values: inprogress/completed</span>
<span class="s">                "totalRecords": 1000,</span>
<span class="s">                "createdBy": 5,</span>
<span class="s">                "modifiedBy": 5,</span>
<span class="s">                "createdAt": "2019-01-30 15:03:18",</span>
<span class="s">                "updatedAt": "2019-04-13 22:57:25"</span>
<span class="s">            }</span>
<span class="s">        ]</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list the Access Control lists in the system.</p>
<h3 id='http-request-54'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/ussd/acl</code></p>
<h3 id='http-request-header-50'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-8'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>token</td>
<td>null</td>
<td>A Search string.</td>
</tr>
<tr>
<td>page</td>
<td>1</td>
<td>Page number.</td>
</tr>
<tr>
<td>size</td>
<td>10</td>
<td>Number of records per page.</td>
</tr>
<tr>
<td>sortf</td>
<td>null</td>
<td>Name of the field on which to sorting to be applied. Possible values: name or type or createdAt or updatedAt</td>
</tr>
<tr>
<td>order</td>
<td>asc</td>
<td>Sorting order applied for above filed. Possible values: asc or desc</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-54'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>930</td>
<td>Bad input for page field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>931</td>
<td>Bad input for size field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>932</td>
<td>Bad input for sortf field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>933</td>
<td>Bad input for order field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>934</td>
<td>Page does not exists</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>950</td>
<td>Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>
<h3 id='http-response-headers-5'>HTTP Response Headers</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>totalACL</td>
<td>Total ACL availble for the filter.</td>
</tr>
<tr>
<td>firstPage</td>
<td>First Page number of the results for the filter.</td>
</tr>
<tr>
<td>prevPage</td>
<td>Previous Page number from current page.</td>
</tr>
<tr>
<td>currPage</td>
<td>Current page number, Input provided by IT Admin.</td>
</tr>
<tr>
<td>nextPage</td>
<td>Next Page number to current page.</td>
</tr>
<tr>
<td>lastPage</td>
<td>Last Page number of the results for the filter.</td>
</tr>
<tr>
<td>pageSize</td>
<td>Number of ACL Per page.</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List ACLs API!
</aside>
<h2 id='create-acl'>Create ACL</h2>
<blockquote>
<p>HTTP Request Payload</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"name"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"My List 1"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"type"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"MSISDN"</span><span class="p">,</span><span class="w"> </span><span class="err">//</span><span class="w"> </span><span class="err">Possible</span><span class="w"> </span><span class="err">values</span><span class="w"> </span><span class="err">example</span><span class="p">:</span><span class="w"> </span><span class="err">MSISDN</span><span class="p">,</span><span class="w"> </span><span class="err">IMSI</span><span class="p">,</span><span class="w"> </span><span class="err">IMEI</span><span class="p">,</span><span class="w"> </span><span class="err">etc.</span><span class="w">
    </span><span class="s2">"searchKey"</span><span class="p">:</span><span class="w"> </span><span class="s2">"MSISDN"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"uuid"</span><span class="p">:</span><span class="w"> </span><span class="mi">2143567854321</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">Success</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code" : 0,</span>
<span class="s">    "msg" : "List created successfully",</span>
<span class="s">    "id": 2345678909876543</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to create Access Control List using predfined List type.</p>
<h3 id='http-request-55'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/ussd/acl</code></p>
<h3 id='http-request-header-51'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='error-codes'>Error Codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>0</td>
<td>List created successfully</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>935</td>
<td>Invalid AC List name</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>936</td>
<td>Minimum characters for list name atleast 5.</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>937</td>
<td>Max length exceeds for list name</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>938</td>
<td>Missing type</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>939</td>
<td>Invalid List Type</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>940</td>
<td>Missing Search KEY</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>941</td>
<td>Invalid Search KEY</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>942</td>
<td>List name already exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>950</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Create ACL API!
</aside>
<h2 id='update-acl'>Update ACL</h2>
<blockquote>
<p>HTTP Request Payload</p>
</blockquote>
<pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="s2">"name"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"My List 12"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"searchKey"</span><span class="p">:</span><span class="w"> </span><span class="s2">"msisdn"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"uuid"</span><span class="p">:</span><span class="w"> </span><span class="mi">************</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">Success</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code" : 0,</span>
<span class="s">    "msg" : "List updated successfully"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to update the Access Control List properties like name and searchKey.</p>
<h3 id='http-request-56'>HTTP Request</h3>
<p><code>PATCH http://example.com/leap_gw/ussd/acl/:listId</code></p>
<h3 id='http-request-header-52'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='error-codes-2'>Error Codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>0</td>
<td>List updated successfully</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>935</td>
<td>Invalid AC List name</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>936</td>
<td>Minimum characters for list name atleast 5.</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>937</td>
<td>Max length exceeds for list name</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>940</td>
<td>Missing Search KEY</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>941</td>
<td>Invalid Search KEY</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>942</td>
<td>List name already exists</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Update ACL API!
</aside>
<h2 id='delete-acl'>Delete ACL</h2>
<blockquote>
<p>Success Response Payload</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">Success</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code" : 0,</span>
<span class="s">    "msg" : "List deleted successfully"</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Failure Response Payload</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">409</span> <span class="ne">Conflict</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code" : 944,</span>
<span class="s">    "msg" : "List used by applications",</span>
<span class="s">    "mappings": {</span>
<span class="s">        "12345678": ["bwvee23", "qwer324"], // Application ID and array of module ID</span>
<span class="s">        "1234563456": ["bwvewere", "rthyjy324"]</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to delete the Access Control List from system.</p>
<h3 id='http-request-57'>HTTP Request</h3>
<p><code>DELETE http://example.com/leap_gw/ussd/acl/:listId</code></p>
<h3 id='http-request-header-53'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='error-codes-3'>Error Codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>0</td>
<td>List deleted successfully</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>943</td>
<td>List not found</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>944</td>
<td>List used by applications</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>950</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Delete ACL API!
</aside>
<h2 id='upload-acl-file'>Upload ACL file</h2>
<blockquote>
<p>HTTP Request Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="err">Body &gt; “form-data”
Key: “file”
Type: file
Value: File to be uploaded
</span></code></pre>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{ </span>
<span class="s">    "code": 0, </span>
<span class="s">    "msg": "File uploaded successful"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to upload the files(.csv files) for Access control list.</p>
<h3 id='http-request-58'>HTTP Request</h3>
<p><code>POST http://example.com/leap_gw/ussd/acl/upload/:uuid</code></p>
<h3 id='http-request-header-54'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-55'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>0</td>
<td>File uploaded successfully</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>945</td>
<td>Malicious file uploaded by user.</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>946</td>
<td>temp directory not found/permission.</td>
</tr>
<tr>
<td>406</td>
<td>Not Acceptable</td>
<td>947</td>
<td>File extension not supported.</td>
</tr>
<tr>
<td>413</td>
<td>Payload Too Large</td>
<td>948</td>
<td>Maximum file size allowed in X mb.</td>
</tr>
<tr>
<td>429</td>
<td>Too Many Requests</td>
<td>949</td>
<td>Too many files uploaded in a given amount of time.</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>950</td>
<td>Internal error while uploading the file</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Upload ACL file API!
</aside>
<h2 id='cancel-list-creation'>Cancel list creation</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">Success</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{ </span>
<span class="s">    "code": 0, </span>
<span class="s">    "msg": "Successfully deleted the files for :uuid"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to delete the uploaded files for :uuid generated.</p>
<h3 id='http-request-59'>HTTP Request</h3>
<p><code>DELETE http://example.com/leap_gw/ussd/acl/cancel/:uuid</code></p>
<h3 id='http-request-header-55'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-56'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>0</td>
<td>Successfully deleted the files for :uuid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>953</td>
<td>:uuid files cannot be deleted at this moment.</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>950</td>
<td>Unable to delete the files/folder due to permission.</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Cancel list creation API!
</aside>
<h2 id='delete-specific-file'>Delete specific file</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">Success</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{ </span>
<span class="s">    "code": 0, </span>
<span class="s">    "msg": "Successfully deleted the file :filename"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to delete the speific file which is uploaded for :uuid.</p>
<h3 id='http-request-60'>HTTP Request</h3>
<p><code>DELETE http://example.com/leap_gw/ussd/acl/file/:uuid/:filename</code></p>
<h3 id='http-request-header-56'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-57'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>0</td>
<td>Successfully deleted the file :filename</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>963</td>
<td>File not found</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>953</td>
<td>:uuid files cannot be deleted at this moment.</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>950</td>
<td>Unable to delete the files/folder due to permission.</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Delete specific file API!
</aside>
<h2 id='download-master-error-file'>Download Master/Error file</h2>
<p>Use this endpoint to download the Master/Error file of :listId.</p>
<h3 id='http-request-61'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/ussd/acl/download/:type/:listId</code></p>
<h3 id='http-request-header-57'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-58'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>0</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>943</td>
<td>List not found.</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>954</td>
<td>List is empty</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>956</td>
<td>You cannot download file at this moment.</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>957</td>
<td>download link expired.</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>950</td>
<td>Unable to delete the files/folder due to permission.</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Download Master/Error file API!
</aside>
<h1 id='my-downloads'>My Downloads</h1><h2 id='list-downloadable-files'>List Downloadable files</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 200,</span>
<span class="s">    "msg": "files listed successfully",</span>
<span class="s">    "data": {</span>
<span class="s">        "pageSize": 1,</span>
<span class="s">        "totalFiles": 1,</span>
<span class="s">        "totalPages": 1,</span>
<span class="s">        "currentPage": 1,</span>
<span class="s">        "list": [</span>
<span class="s">            {</span>
<span class="s">                "id": "1548840798135",</span>
<span class="s">                "name": "ERROR_213456bwgf.csv",</span>
<span class="s">                "createdAt": "2019-01-30 15:03:18",</span>
<span class="s">                "expiryTime": "2019-04-13 22:57:25"</span>
<span class="s">            }</span>
<span class="s">        ]</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list the downloadable files in the system.</p>
<h3 id='http-request-62'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/md</code></p>
<h3 id='http-request-header-58'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-9'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>token</td>
<td>null</td>
<td>A Search string.</td>
</tr>
<tr>
<td>page</td>
<td>1</td>
<td>Page number.</td>
</tr>
<tr>
<td>size</td>
<td>10</td>
<td>Number of records per page.</td>
</tr>
<tr>
<td>sortf</td>
<td>null</td>
<td>Name of the field on which to sorting to be applied. Possible values: name or createdAt or expiryTime</td>
</tr>
<tr>
<td>order</td>
<td>asc</td>
<td>Sorting order applied for above filed. Possible values: asc or desc</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-59'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>930</td>
<td>Bad input for page field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>931</td>
<td>Bad input for size field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>932</td>
<td>Bad input for sortf field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>933</td>
<td>Bad input for order field</td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>934</td>
<td>Page does not exists</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>950</td>
<td>Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>
<h3 id='http-response-headers-6'>HTTP Response Headers</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>totalFiles</td>
<td>Total files availble for the filter.</td>
</tr>
<tr>
<td>firstPage</td>
<td>First Page number of the results for the filter.</td>
</tr>
<tr>
<td>prevPage</td>
<td>Previous Page number from current page.</td>
</tr>
<tr>
<td>currPage</td>
<td>Current page number, Input provided by IT Admin.</td>
</tr>
<tr>
<td>nextPage</td>
<td>Next Page number to current page.</td>
</tr>
<tr>
<td>lastPage</td>
<td>Last Page number of the results for the filter.</td>
</tr>
<tr>
<td>pageSize</td>
<td>Number of Files Per page.</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List ACLs API!
</aside>
<h2 id='download-the-file'>Download the file</h2>
<p>Use this endpoint to download th file from LEAP system.</p>
<h3 id='http-request-63'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/md/:id</code></p>
<h3 id='http-request-header-59'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-60'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>0</td>
<td></td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>958</td>
<td>File not found.</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>957</td>
<td>download link expired.</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>950</td>
<td>Unable to delete the files/folder due to permission.</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Download the file API!
</aside>
<h1 id='reports-management'>Reports Management</h1><h2 id='mktadmin-overview'>MktAdmin Overview</h2>
<blockquote>
<p>Success Response Payload: Summary Report</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": {</span>
<span class="s">        "totalUsers": 9,</span>
<span class="s">        "totalApps": 4</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Success Response Payload: Top Used Apps Report</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": [</span>
<span class="s">        {</span>
<span class="s">            "appId": "app1",</span>
<span class="s">            "appName": "Balance Transfer",</span>
<span class="s">            "count": 4</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app2",</span>
<span class="s">            "appName": "FAF",</span>
<span class="s">            "count": 3</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app3",</span>
<span class="s">            "appName": "SMS",</span>
<span class="s">            "count": 1</span>
<span class="s">        }</span>
<span class="s">    ]</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Success Response Payload: Top Success Apps Report</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": [</span>
<span class="s">        {</span>
<span class="s">            "appId": "app1",</span>
<span class="s">            "appName": "Balance Transfer",</span>
<span class="s">            "count": 3</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app2",</span>
<span class="s">            "appName": "FAF",</span>
<span class="s">            "count": 1</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app3",</span>
<span class="s">            "appName": "SMS",</span>
<span class="s">            "count": 1</span>
<span class="s">        }</span>
<span class="s">    ]</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Success Response Payload: Top Failed Apps Report</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": [</span>
<span class="s">        {</span>
<span class="s">            "appId": "app2",</span>
<span class="s">            "appName": "FAF",</span>
<span class="s">            "count": 2</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app1",</span>
<span class="s">            "appName": "Balance Transfer",</span>
<span class="s">            "count": 1</span>
<span class="s">        }</span>
<span class="s">    ]</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Success Response Payload: Top 20 Apps Report</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": [</span>
<span class="s">        {</span>
<span class="s">            "appId": "app1",</span>
<span class="s">            "appName": "Balance Transfer",</span>
<span class="s">            "count": 4</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app2",</span>
<span class="s">            "appName": "FAF",</span>
<span class="s">            "count": 3</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app3",</span>
<span class="s">            "appName": "SMS",</span>
<span class="s">            "count": 1</span>
<span class="s">        }</span>
<span class="s">    ]</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to Overview page details for Marketing Admin user dashboard.</p>
<h3 id='http-request-64'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/reports/overview/:reportType</code></p>
<h3 id='http-request-header-60'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-10'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>reportType</td>
<td>null</td>
<td>Please refer bellow table for values</td>
</tr>
<tr>
<td>startTime</td>
<td>null</td>
<td>This parameter indicates start time to fetch overview report</td>
</tr>
<tr>
<td>endTime</td>
<td>null</td>
<td>This parameter indicates end time to fetch overview report</td>
</tr>
</tbody></table>

<table><thead>
<tr>
<th>reportType</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>summary</td>
<td>This report type gives the overview summary report</td>
</tr>
<tr>
<td>topusedapps</td>
<td>This report type gives the overview summary report</td>
</tr>
<tr>
<td>topsuccapps</td>
<td>This report type gives the overview top success apps report</td>
</tr>
<tr>
<td>topfailapps</td>
<td>This report type gives the overview top failed apps report</td>
</tr>
<tr>
<td>top20apps</td>
<td>This report type gives the overview top 20 apps report</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-61'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>783</td>
<td>invalid report type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>780</td>
<td>Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>781</td>
<td>Elastic search error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>782</td>
<td>Elastic search empty data</td>
</tr>
</tbody></table>

<aside class="success">
Remember — MktAdmin Overview API!
</aside>
<h2 id='top-20-apps'>TOP 20 Apps</h2>
<blockquote>
<p>Success Response Payload: Top 20 Apps With Highest Users</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": [</span>
<span class="s">        {</span>
<span class="s">            "appId": "app3",</span>
<span class="s">            "appName": "SMS",</span>
<span class="s">            "count": 1</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app1",</span>
<span class="s">            "appName": "Balance Transfer",</span>
<span class="s">            "count": 4</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app2",</span>
<span class="s">            "appName": "FAF",</span>
<span class="s">            "count": 3</span>
<span class="s">        }</span>
<span class="s">    ]</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Success Response Payload: Top 20 Apps With Highest Success</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": [</span>
<span class="s">        {</span>
<span class="s">            "appId": "app3",</span>
<span class="s">            "appName": "SMS",</span>
<span class="s">            "count": 1</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app1",</span>
<span class="s">            "appName": "Balance Transfer",</span>
<span class="s">            "count": 3</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app2",</span>
<span class="s">            "appName": "FAF",</span>
<span class="s">            "count": 1</span>
<span class="s">        }</span>
<span class="s">    ]</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Success Response Payload: Top 20 Apps With Highest Transactions</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": [</span>
<span class="s">        {</span>
<span class="s">            "appId": "app3",</span>
<span class="s">            "appName": "SMS",</span>
<span class="s">            "count": 1</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app1",</span>
<span class="s">            "appName": "Balance Transfer",</span>
<span class="s">            "count": 4</span>
<span class="s">        },</span>
<span class="s">        {</span>
<span class="s">            "appId": "app2",</span>
<span class="s">            "appName": "FAF",</span>
<span class="s">            "count": 3</span>
<span class="s">        }</span>
<span class="s">    ]</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list the Top 20 apps.</p>
<h3 id='http-request-65'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/reports/top20apps/:reportType</code></p>
<h3 id='http-request-header-61'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-11'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>reportType</td>
<td>null</td>
<td>Please refer bellow table for values</td>
</tr>
<tr>
<td>startTime</td>
<td>24 back hours time</td>
<td>This parameter indicates start time to fetch overview report</td>
</tr>
<tr>
<td>endTime</td>
<td>current system time</td>
<td>This parameter indicates end time to fetch overview report</td>
</tr>
</tbody></table>

<table><thead>
<tr>
<th>reportType</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>users</td>
<td>This report type gives the Top 20 Applications with highest number of users(Sorted by latest launched)</td>
</tr>
<tr>
<td>success</td>
<td>This report type gives the Top 20 Applications with highest number of success(Sorted by latest launched)</td>
</tr>
<tr>
<td>total</td>
<td>This report type gives the Top 20 Applications with highest number of transactions(Sorted by latest launched)</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-62'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>783</td>
<td>invalid report type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>780</td>
<td>Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>781</td>
<td>Elastic search error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>782</td>
<td>Elastic search empty data</td>
</tr>
</tbody></table>

<aside class="success">
Remember — TOP 20 Apps API!
</aside>
<h2 id='application-wise-stats'>Application Wise Stats</h2>
<blockquote>
<p>Success Response Payload: AppStats Module Wise Transactions</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": {</span>
<span class="s">        "1528405200000": {</span>
<span class="s">            "0.1": 2,</span>
<span class="s">            "12.11": 2,</span>
<span class="s">            "3.1": 1</span>
<span class="s">        },</span>
<span class="s">        "1528653600000": {</span>
<span class="s">            "0.1": 1,</span>
<span class="s">            "3.7": 1,</span>
<span class="s">            "6.11": 1</span>
<span class="s">        },</span>
<span class="s">        "1528696800000": {</span>
<span class="s">            "0.1": 1,</span>
<span class="s">            "3.1": 1,</span>
<span class="s">            "5.1": 1</span>
<span class="s">        }</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Success Response Payload: AppStats Active Users</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": {</span>
<span class="s">        "1528405200000": 2,</span>
<span class="s">        "1528653600000": 1,</span>
<span class="s">        "1528696800000": 1</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Success Response Payload: AppStats Total Transactions</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>

<span class="p">{</span><span class="w">
    </span><span class="s2">"code"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
    </span><span class="s2">"msg"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="s2">"1528405200000"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="s2">"total"</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w">
            </span><span class="s2">"success"</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w">
            </span><span class="s2">"failure"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="s2">"1528653600000"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="s2">"total"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w">
            </span><span class="s2">"success"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w">
            </span><span class="s2">"failure"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="s2">"1528696800000"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="s2">"total"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w">
            </span><span class="s2">"success"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
            </span><span class="s2">"failure"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="w">
        </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre>
<blockquote>
<p>Success Response Payload: AppStats Average Response Time</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": {</span>
<span class="s">        "1528405200000": "3821017517500.00",</span>
<span class="s">        "1528653600000": "4585966221000.00",</span>
<span class="s">        "1528696800000": "4586095821000.00"</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to get the reports for selected application.</p>
<h3 id='http-request-66'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/reports/appstats/:reportType</code></p>
<h3 id='http-request-header-62'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-12'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>reportType</td>
<td>null</td>
<td>Please refer bellow table for values</td>
</tr>
<tr>
<td>startTime</td>
<td>24 back hours time</td>
<td>This parameter indicates start time to fetch overview report</td>
</tr>
<tr>
<td>endTime</td>
<td>current system time</td>
<td>This parameter indicates end time to fetch overview report</td>
</tr>
<tr>
<td>appId</td>
<td>This parameter indicates the selected Application id and this parameter is mandatory</td>
<td></td>
</tr>
<tr>
<td>interval</td>
<td>This parameter indicates the interval to show the data</td>
<td></td>
</tr>
</tbody></table>

<table><thead>
<tr>
<th>reportType</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>modulewisetrans</td>
<td>This report type gives the Total transactions for the selected app grouped by Interfaces.</td>
</tr>
<tr>
<td>activeusers</td>
<td>This report type gives the total active users foe the selected App</td>
</tr>
<tr>
<td>apptotaltrans</td>
<td>This report type gives the total transaction for the selected App</td>
</tr>
<tr>
<td>appsavgres</td>
<td>This report type gives the average response time for the selected App</td>
</tr>
<tr>
<td>apperrorcodes</td>
<td>This report type gives top 10 status codes count for the selected App</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-63'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>783</td>
<td>invalid report type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>780</td>
<td>Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>781</td>
<td>Elastic search error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>782</td>
<td>Elastic search empty data</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Application Wise Stats API!
</aside>
<h2 id='interface-wise-stats'>Interface Wise Stats</h2>
<blockquote>
<p>Success Response Payload: Interface Total Transactions</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": {</span>
<span class="s">        "1528398000000": {</span>
<span class="s">            "total":1,</span>
<span class="s">            "success": 1,</span>
<span class="s">            "fail": 0</span>
<span class="s">        },</span>
<span class="s">        "1528405200000": {</span>
<span class="s">            "total":3,</span>
<span class="s">            "success": 2,</span>
<span class="s">            "fail": 1</span>
<span class="s">        },</span>
<span class="s">        "1528416000000": {</span>
<span class="s">            "total":1,</span>
<span class="s">            "success": 1,</span>
<span class="s">            "fail": 0</span>
<span class="s">        },</span>
<span class="s">        "1528653600000": {</span>
<span class="s">            "total":1,</span>
<span class="s">            "success": 1,</span>
<span class="s">            "fail": 0</span>
<span class="s">        }</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<blockquote>
<p>Success Response Payload: Interface Total Apps</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 0,</span>
<span class="s">    "msg": {</span>
<span class="s">        "1528399800000": 1,</span>
<span class="s">        "1528407000000": 2,</span>
<span class="s">        "1528414200000": 1,</span>
<span class="s">        "1528655400000": 1</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to get the reports for selected interface.</p>
<h3 id='http-request-67'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/reports/interfacestats/:reportType</code></p>
<h3 id='http-request-header-63'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-13'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>reportType</td>
<td>null</td>
<td>Please refer bellow table for values</td>
</tr>
<tr>
<td>startTime</td>
<td>24 back hours time</td>
<td>This parameter indicates start time to fetch report</td>
</tr>
<tr>
<td>endTime</td>
<td>current system time</td>
<td>This parameter indicates end time to fetch report</td>
</tr>
<tr>
<td>moduleName</td>
<td>This parameter indicates the selected modulename and this parameter is mandatory</td>
<td></td>
</tr>
<tr>
<td>interval</td>
<td>This parameter indicates the interval to show the data</td>
<td></td>
</tr>
</tbody></table>

<table><thead>
<tr>
<th>reportType</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>interfaceavgtrans</td>
<td>This report type gives the average transactions per second</td>
</tr>
<tr>
<td>interfaceavgres</td>
<td>This report type gives the average response time</td>
</tr>
<tr>
<td>interfacetotaltrans</td>
<td>This report type gives the total transaction for the selected Interface</td>
</tr>
<tr>
<td>interfacetotalapps</td>
<td>This report type gives the total applications the selected Interface</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-64'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>783</td>
<td>invalid report type</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>780</td>
<td>Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>781</td>
<td>Elastic search error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>782</td>
<td>Elastic search empty data</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Interface Wise Stats API!
</aside>
<h2 id='audit-trails'>Audit Trails</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">[</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T19:55:36.000Z",</span>
<span class="s">        "usr": "TeamLeap",</span>
<span class="s">        "mtd": "PATCH",</span>
<span class="s">        "aid": "1538395620058",</span>
<span class="s">        "an": "FriendsAndFamily",</span>
<span class="s">        "url": "/leap_gw/api/1538395620058/assign",</span>
<span class="s">        "action": "assigned",</span>
<span class="s">        "desc": "TeamLeap assigned FriendsAndFamily"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T18:55:36.000Z",</span>
<span class="s">        "usr": "TeamLeap",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "1538395620058",</span>
<span class="s">        "an": "FriendsAndFamily",</span>
<span class="s">        "url": "/leap_gw/workflow/1538395620058/ApproveForStaging",</span>
<span class="s">        "action": "approved for staging",</span>
<span class="s">        "desc": "TeamLeap approved for staging FriendsAndFamily"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T18:15:36.000Z",</span>
<span class="s">        "usr": "TeamLeap",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "1538395620058",</span>
<span class="s">        "an": "FriendsAndFamily",</span>
<span class="s">        "url": "/leap_gw/workflow/1538395620058/LAUNCH",</span>
<span class="s">        "action": "launched",</span>
<span class="s">        "desc": "TeamLeap launched FriendsAndFamily"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T18:05:36.000Z",</span>
<span class="s">        "usr": "TeamLeap",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "1538395620058",</span>
<span class="s">        "an": "FriendsAndFamily",</span>
<span class="s">        "url": "/leap_gw/workflow/1538395620058/RETIRE",</span>
<span class="s">        "action": "retired",</span>
<span class="s">        "desc": "TeamLeap retired FriendsAndFamily"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T17:55:36.000Z",</span>
<span class="s">        "usr": "TeamLeap",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "1538395620058",</span>
<span class="s">        "an": "FriendsAndFamily",</span>
<span class="s">        "url": "/leap_gw/workflow/1538395620058/ApproveForStaging",</span>
<span class="s">        "action": "approved for staging",</span>
<span class="s">        "desc": "TeamLeap approved for staging FriendsAndFamily"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T17:45:36.000Z",</span>
<span class="s">        "usr": "TeamLeap",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "1538395620058",</span>
<span class="s">        "an": "FriendsAndFamily",</span>
<span class="s">        "url": "/leap_gw/workflow/1538395620058/STAGE",</span>
<span class="s">        "action": "staged",</span>
<span class="s">        "desc": "TeamLeap staged FriendsAndFamily"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T16:55:36.000Z",</span>
<span class="s">        "usr": "TeamLeap",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "1538395620058",</span>
<span class="s">        "an": "FriendsAndFamily",</span>
<span class="s">        "url": "/leap_gw/workflow/1538395620058/WITHDRAW",</span>
<span class="s">        "action": "withdrew",</span>
<span class="s">        "desc": "TeamLeap withdrew FriendsAndFamily"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T16:45:36.000Z",</span>
<span class="s">        "usr": "TeamLeap",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "1538395620058",</span>
<span class="s">        "an": "FriendsAndFamily",</span>
<span class="s">        "url": "/leap_gw/workflow/1538395620058/REJECT",</span>
<span class="s">        "action": "rejected",</span>
<span class="s">        "desc": "TeamLeap rejected FriendsAndFamily"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T14:58:36.000Z",</span>
<span class="s">        "usr": "TeamLeap",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "1538395620058",</span>
<span class="s">        "an": "FriendsAndFamily",</span>
<span class="s">        "url": "/leap_gw/workflow/1538395620058/SUBMIT",</span>
<span class="s">        "action": "submitted",</span>
<span class="s">        "desc": "TeamLeap submitted FriendsAndFamily"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T14:55:36.000Z",</span>
<span class="s">        "usr": "root",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "10.0",</span>
<span class="s">        "an": "PluginName",</span>
<span class="s">        "url": "/leap_gw/plugins/soap/10.0/deactivate",</span>
<span class="s">        "action": "deactivated",</span>
<span class="s">        "desc": "root deactivated PluginName"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T14:55:36.000Z",</span>
<span class="s">        "usr": "root",</span>
<span class="s">        "mtd": "DELETE",</span>
<span class="s">        "aid": "7.0",</span>
<span class="s">        "an": "LeapDemo2",</span>
<span class="s">        "url": "/leap_gw/plugins/soap/:filename",</span>
<span class="s">        "action": "deleted",</span>
<span class="s">        "desc": "root deleted LeapDemo2"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T14:50:36.000Z",</span>
<span class="s">        "usr": "root",</span>
<span class="s">        "mtd": "PUT",</span>
<span class="s">        "aid": "7",</span>
<span class="s">        "an": "LeapDemo2",</span>
<span class="s">        "url": "/leap_gw/users/updateUser/7",</span>
<span class="s">        "action": "updated",</span>
<span class="s">        "desc": "root updated LeapDemo2"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T13:55:36.000Z",</span>
<span class="s">        "usr": "1",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "2",</span>
<span class="s">        "an": "LeapDemo2",</span>
<span class="s">        "url": "/leap_gw/users/createUserWithoutPassword",</span>
<span class="s">        "action": "created",</span>
<span class="s">        "desc": "1 created LeapDemo2"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T13:55:36.000Z",</span>
<span class="s">        "usr": "root",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "2",</span>
<span class="s">        "an": "LeapDemo2",</span>
<span class="s">        "url": "/leap_gw/users/deactivate/2",</span>
<span class="s">        "action": "deactivated",</span>
<span class="s">        "desc": "root deactivated LeapDemo2"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T13:55:36.000Z",</span>
<span class="s">        "usr": "LeapDemo2",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "1528395620058",</span>
<span class="s">        "an": "JustForGags",</span>
<span class="s">        "url": "/leap_gw/apps",</span>
<span class="s">        "action": "created",</span>
<span class="s">        "desc": "LeapDemo2 created JustForGags"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T13:55:36.000Z",</span>
<span class="s">        "usr": "root",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "10.0",</span>
<span class="s">        "an": "PluginName",</span>
<span class="s">        "url": "/leap_gw/plugins/soap",</span>
<span class="s">        "action": "uploaded",</span>
<span class="s">        "desc": "root uploaded PluginName"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T13:55:36.000Z",</span>
<span class="s">        "usr": "root",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "3",</span>
<span class="s">        "an": "InvalidUser",</span>
<span class="s">        "url": "/leap_gw/users/deleteUser/3/purge",</span>
<span class="s">        "action": "deleted",</span>
<span class="s">        "desc": "root deleted InvalidUser"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T13:55:36.000Z",</span>
<span class="s">        "usr": "root",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "2",</span>
<span class="s">        "an": "LeapDemo2",</span>
<span class="s">        "url": "/leap_gw/users/createUser",</span>
<span class="s">        "action": "created",</span>
<span class="s">        "desc": "root created LeapDemo2"</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T13:55:36.000Z",</span>
<span class="s">        "usr": "root",</span>
<span class="s">        "mtd": "POST",</span>
<span class="s">        "aid": "",</span>
<span class="s">        "an": "",</span>
<span class="s">        "url": "/leap_gw/users/authenticate",</span>
<span class="s">        "action": "logged in.",</span>
<span class="s">        "desc": "root logged in. "</span>
<span class="s">    },</span>
<span class="s">    {</span>
<span class="s">        "t": "2018-07-05T13:55:36.000Z",</span>
<span class="s">        "usr": "root",</span>
<span class="s">        "mtd": "DELETE",</span>
<span class="s">        "aid": "7.0",</span>
<span class="s">        "an": "PluginName",</span>
<span class="s">        "url": "/leap_gw/plugins/soap/:filename",</span>
<span class="s">        "action": "deleted",</span>
<span class="s">        "desc": "root deleted PluginName"</span>
<span class="s">    }</span>
<span class="s">]</span>
</code></pre>
<p>Use this endpoint to list the audit trails in the system.</p>
<h3 id='http-request-68'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/reports/auditTrail</code></p>
<h3 id='http-request-header-64'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-14'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>token</td>
<td>null</td>
<td>A Search string.</td>
</tr>
<tr>
<td>page</td>
<td>1</td>
<td>Page number.</td>
</tr>
<tr>
<td>size</td>
<td>10</td>
<td>Number of records per page.</td>
</tr>
<tr>
<td>sortf</td>
<td>null</td>
<td>Name of the field on which to sorting to be applied. Possible values: username or email or createdAt or updatedAt</td>
</tr>
<tr>
<td>order</td>
<td>asc</td>
<td>Sorting order applied for above filed. Possible values: asc or desc</td>
</tr>
<tr>
<td>status</td>
<td>null</td>
<td>Filter the users by user activation status. Choose from active or inactive or both(comma separated).</td>
</tr>
<tr>
<td>startTime</td>
<td>null</td>
<td>Time (in miliseconds) from which user is to be listed.</td>
</tr>
<tr>
<td>endTime</td>
<td>null</td>
<td>Time (in milliseconds) to which user is to be listed.</td>
</tr>
<tr>
<td>user_filter</td>
<td>null</td>
<td>the username to get the audit trail for a particular user.</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-65'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>603</td>
<td>Length of the firstname exceeded</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Audit Trails API!
</aside>
<h2 id='itadmin-overview'>ITAdmin Overview</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "totalUsers": 4,</span>
<span class="s">    "totalApps": 7,</span>
<span class="s">    "appsStatus": {</span>
<span class="s">        "Draft": 7,</span>
<span class="s">        "ApprovalPending": 0,</span>
<span class="s">        "ScheduledforLaunch": 0,</span>
<span class="s">        "Staged": 0,</span>
<span class="s">        "Launched": 0,</span>
<span class="s">        "Retired": 0,</span>
<span class="s">        "Deleted": 0</span>
<span class="s">    },</span>
<span class="s">    "unassignedApps": 5,</span>
<span class="s">    "assignedApps": 2</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to get the details for IT admin dashboard.</p>
<h3 id='http-request-69'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/dashboard/stats</code></p>
<h3 id='http-request-header-65'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-66'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>603</td>
<td>Length of the firstname exceeded</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — ITAdmin Overview API!
</aside>
<h2 id='dashboard-apps-listing'>Dashboard Apps Listing</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "code": 200,</span>
<span class="s">    "msg": "Apps listed successfully",</span>
<span class="s">    "data": {</span>
<span class="s">        "pageSize": 7,</span>
<span class="s">        "totalApps": 7,</span>
<span class="s">        "totalPages": 1,</span>
<span class="s">        "currentPage": 1,</span>
<span class="s">        "appList": [</span>
<span class="s">            {</span>
<span class="s">                "id": "1528395620058",</span>
<span class="s">                "name": "JustForGags222",</span>
<span class="s">                "desc": "fkernferkfnerkf",</span>
<span class="s">                "status": "0",</span>
<span class="s">                "owner": "Unknown User",</span>
<span class="s">                "createdBy": "root ",</span>
<span class="s">                "modifiedBy": 1,</span>
<span class="s">                "createdAt": "2018-06-07T06:20:20.000Z",</span>
<span class="s">                "updatedAt": "2018-06-07T18:20:20.000Z"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "1528395884601",</span>
<span class="s">                "name": "JustForGags22256",</span>
<span class="s">                "desc": "fkernferkfnerkf",</span>
<span class="s">                "status": "0",</span>
<span class="s">                "owner": "Unknown User",</span>
<span class="s">                "createdBy": "root ",</span>
<span class="s">                "modifiedBy": 1,</span>
<span class="s">                "createdAt": "2018-06-07T06:24:44.000Z",</span>
<span class="s">                "updatedAt": "2018-06-07T18:24:44.000Z"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "1528395899691",</span>
<span class="s">                "name": "callMeback",</span>
<span class="s">                "desc": "fkernferkfnerkf",</span>
<span class="s">                "status": "4",</span>
<span class="s">                "owner": "Unassigned",</span>
<span class="s">                "createdBy": "root ",</span>
<span class="s">                "modifiedBy": 1,</span>
<span class="s">                "createdAt": "2018-06-07T06:24:59.000Z",</span>
<span class="s">                "updatedAt": "2018-06-11T14:37:33.000Z"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "1528395907967",</span>
<span class="s">                "name": "callMeback67788",</span>
<span class="s">                "desc": "fkernferkfnerkf",</span>
<span class="s">                "status": "2",</span>
<span class="s">                "owner": "DefaultAppDev User",</span>
<span class="s">                "createdBy": "root ",</span>
<span class="s">                "modifiedBy": 1,</span>
<span class="s">                "createdAt": "2018-06-07T06:25:07.000Z",</span>
<span class="s">                "updatedAt": "2018-06-07T18:25:07.000Z"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "1528395925838",</span>
<span class="s">                "name": "friendsAndFamily",</span>
<span class="s">                "desc": "fkernferkfnerkf",</span>
<span class="s">                "status": "0",</span>
<span class="s">                "owner": "Unknown User",</span>
<span class="s">                "createdBy": "root ",</span>
<span class="s">                "modifiedBy": 1,</span>
<span class="s">                "createdAt": "2018-06-07T06:25:25.000Z",</span>
<span class="s">                "updatedAt": "2018-06-07T18:25:25.000Z"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "1528395986628",</span>
<span class="s">                "name": "BalanceEnquiry",</span>
<span class="s">                "desc": "fkernferkfnerkf",</span>
<span class="s">                "status": "0",</span>
<span class="s">                "owner": "Unknown User",</span>
<span class="s">                "createdBy": "root ",</span>
<span class="s">                "modifiedBy": 1,</span>
<span class="s">                "createdAt": "2018-06-07T06:26:26.000Z",</span>
<span class="s">                "updatedAt": "2018-06-07T18:26:26.000Z"</span>
<span class="s">            },</span>
<span class="s">            {</span>
<span class="s">                "id": "1528396026063",</span>
<span class="s">                "name": "LanguageChange",</span>
<span class="s">                "desc": "fkernferkfnerkf",</span>
<span class="s">                "status": "4",</span>
<span class="s">                "owner": "Unknown User",</span>
<span class="s">                "createdBy": "root ",</span>
<span class="s">                "modifiedBy": 1,</span>
<span class="s">                "createdAt": "2018-06-07T06:27:06.000Z",</span>
<span class="s">                "updatedAt": "2018-06-07T18:27:06.000Z"</span>
<span class="s">            }</span>
<span class="s">        ]</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list all the apps in the platform with required details for IT admin.</p>
<h3 id='http-request-70'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/dashboard/</code></p>
<h3 id='http-request-header-66'>HTTP Request Header</h3>
<table><thead>
<tr>
<th>Key</th>
<th>Value</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>Authorization</td>
<td>bearer {JWT Token}</td>
<td>Authorization token</td>
</tr>
</tbody></table>
<h3 id='http-request-query-parameters-15'>HTTP Request Query Parameters</h3>
<table><thead>
<tr>
<th>Parameter</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>token</td>
<td>null</td>
<td>A Search string.</td>
</tr>
<tr>
<td>page</td>
<td>1</td>
<td>Page number.</td>
</tr>
<tr>
<td>size</td>
<td>10</td>
<td>Number of records per page.</td>
</tr>
<tr>
<td>sortf</td>
<td>null</td>
<td>Name of the field on which to sorting to be applied. Possible values: username or email or createdAt or updatedAt</td>
</tr>
<tr>
<td>order</td>
<td>asc</td>
<td>Sorting order applied for above filed. Possible values: asc or desc</td>
</tr>
<tr>
<td>startTime</td>
<td>null</td>
<td>Time (in miliseconds) from which app is to be listed(filtered by created date).</td>
</tr>
<tr>
<td>endTime</td>
<td>null</td>
<td>Time (in milliseconds) to which useappr is to be listed(filtered by created date).</td>
</tr>
</tbody></table>
<h3 id='http-response-error-codes-67'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>400</td>
<td>Bag Request</td>
<td>603</td>
<td>Length of the firstname exceeded</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>605</td>
<td>Authorization header is missing</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>-</td>
<td>Any internal or unhandled error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Dashboard Apps Listing API!
</aside>
<h1 id='others'>Others</h1><h2 id='list-config-apis'>List Config APIs</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "/leap_gw/configapi/passwordstrength": "Get the regular expression to validate the Strength of Password",</span>
<span class="s">    "/leap_gw/configapi/appstates": "Get App Status Code information",</span>
<span class="s">    "/leap_gw/configapi/macros": "Get list of MACROS supported in platform",</span>
<span class="s">    "/leap_gw/configapi/operators": "Get list of Operators supported in platform",</span>
<span class="s">    "/leap_gw/configapi/languages": "Get list of Languges supported in platform"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list the Config APIS in the system.</p>
<h3 id='http-request-71'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/configapi</code></p>
<h3 id='http-response-error-codes-68'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Config APIs API!
</aside>
<h2 id='password-strength'>Password Strength</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "description": "The password string must contain at least 1 lowercase alphabetical character, must contain at least 1 uppercase alphabetical character, must contain at least 1 numeric character, must contain at least one special character, but we are escaping reserved RegEx characters to avoid conflict, The string must be 8 characters or longer",</span>
<span class="s">    "expression": "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&amp;*])(?=.{8,})"</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to get the regular expression to validate the Strength of Password.</p>
<h3 id='http-request-72'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/configapi/passwordstrength</code></p>
<h3 id='http-response-error-codes-69'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Password Strength API!
</aside>
<h2 id='application-states'>Application States</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "Draft": {</span>
<span class="s">        "code": 0,</span>
<span class="s">        "description": "Draft"</span>
<span class="s">    },</span>
<span class="s">    "ApprovalPending": {</span>
<span class="s">        "code": 1,</span>
<span class="s">        "description": "Approval Pending"</span>
<span class="s">    },</span>
<span class="s">    "ApprovedForStaging": {</span>
<span class="s">        "code": 2,</span>
<span class="s">        "description": "Approved for Staging"</span>
<span class="s">    },</span>
<span class="s">    "ScheduledforStaging": {</span>
<span class="s">        "code": 3,</span>
<span class="s">        "description": "Scheduled for Staging"</span>
<span class="s">    },</span>
<span class="s">    "ScheduledforLaunch": {</span>
<span class="s">        "code": 4,</span>
<span class="s">        "description": "Scheduled for Launch"</span>
<span class="s">    },</span>
<span class="s">    "Staged": {</span>
<span class="s">        "code": 5,</span>
<span class="s">        "description": "Staged"</span>
<span class="s">    },</span>
<span class="s">    "Launched": {</span>
<span class="s">        "code": 6,</span>
<span class="s">        "description": "Launched"</span>
<span class="s">    },</span>
<span class="s">    "Retired": {</span>
<span class="s">        "code": 7,</span>
<span class="s">        "description": "Retired"</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to get Application Status Code information.</p>
<h3 id='http-request-73'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/configapi/appstates</code></p>
<h3 id='http-response-error-codes-70'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — Application States API!
</aside>
<h2 id='list-macros'>List Macros</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "_toLowerCase": {</span>
<span class="s">        "description": "Converts uppercase string into lowercase",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string"</span>
<span class="s">        },</span>
<span class="s">        "functor": "toLowerCase",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_toUpperCase": {</span>
<span class="s">        "description": "Converts lowercase string into uppercase",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string"</span>
<span class="s">        },</span>
<span class="s">        "functor": "toUpperCase",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_substring": {</span>
<span class="s">        "description": "Extracts the characters from a string between two specified indices and returns a new substring",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "Input string",</span>
<span class="s">            "start_index": "The starting index of the substring.First character is at index 0.",</span>
<span class="s">            "end_index": "The position (up to, but not including) where to end the extraction."</span>
<span class="s">        },</span>
<span class="s">        "functor": "substring",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_trim": {</span>
<span class="s">        "description": "Removes whitespaces/set of characters from both the sides of the string",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string",</span>
<span class="s">            "left_substring": "Set of characters to be deleted from the left",</span>
<span class="s">            "right_substring": "Set of characters to be deleted from the right"</span>
<span class="s">        },</span>
<span class="s">        "functor": "trim",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_trimLeft": {</span>
<span class="s">        "description": "Removes whitespaces from the left side of the string",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string",</span>
<span class="s">            "left_substring": "Set of characters to be deleted from the left"</span>
<span class="s">        },</span>
<span class="s">        "functor": "trimLeft",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_trimRight": {</span>
<span class="s">        "description": "Removes whitespaces from the right side of the string",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string",</span>
<span class="s">            "right_substring": "Set of characters to be deleted from the right"</span>
<span class="s">        },</span>
<span class="s">        "functor": "trimRight",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_stringify": {</span>
<span class="s">        "description": "Converts an input/object to string",</span>
<span class="s">        "args": {</span>
<span class="s">            "input": "The input to be converted into string"</span>
<span class="s">        },</span>
<span class="s">        "functor": "toString",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_toNumber": {</span>
<span class="s">        "description": "Converts an input/object to a number",</span>
<span class="s">        "args": {</span>
<span class="s">            "input": "The input string/object to be converted into number"</span>
<span class="s">        },</span>
<span class="s">        "functor": "Number",</span>
<span class="s">        "this": false</span>
<span class="s">    },</span>
<span class="s">    "_length": {</span>
<span class="s">        "description": "Returns the length of the given string",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string"</span>
<span class="s">        },</span>
<span class="s">        "functor": "length",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_isEmpty": {</span>
<span class="s">        "description": "Checks if the string is empty. Returns true if the length of the string is zero,otherwise false.",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string"</span>
<span class="s">        },</span>
<span class="s">        "functor": "isEmpty",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_isAlpha": {</span>
<span class="s">        "description": "Checks whether a string contains only alphabets or not",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string"</span>
<span class="s">        },</span>
<span class="s">        "functor": "isAlpha",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_isNumeric": {</span>
<span class="s">        "description": "Checks whether a string contains only number or not",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string"</span>
<span class="s">        },</span>
<span class="s">        "functor": "isNumber",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_isAlphanumeric": {</span>
<span class="s">        "description": "Checks whether a string contains only alphabet or number",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string"</span>
<span class="s">        },</span>
<span class="s">        "functor": "isAlphanumeric",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_isDigit": {</span>
<span class="s">        "description": "Checks whether a character is a digit",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input character"</span>
<span class="s">        },</span>
<span class="s">        "functor": "isDigit",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_isLower": {</span>
<span class="s">        "description": "Checks whether a passed string is in lowercase letters or not",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string"</span>
<span class="s">        },</span>
<span class="s">        "functor": "isLower",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_isUpper": {</span>
<span class="s">        "description": "Checks whether a passed string is in uppercase letters or not",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string"</span>
<span class="s">        },</span>
<span class="s">        "functor": "isUpper",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_startsWith": {</span>
<span class="s">        "description": "Checks whether a string starts with a given substring",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string",</span>
<span class="s">            "start_substring": "The substring with which string should start"</span>
<span class="s">        },</span>
<span class="s">        "functor": "startsWith",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_endsWith": {</span>
<span class="s">        "description": "Checks whether a string ends with a given substring",</span>
<span class="s">        "args": {</span>
<span class="s">            "input_string": "The input string",</span>
<span class="s">            "end_substring": "The substring with which string should end"</span>
<span class="s">        },</span>
<span class="s">        "functor": "endsWith",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_now": {</span>
<span class="s">        "description": "Returns the number of milliseconds since 1970/01/01",</span>
<span class="s">        "functor": "now",</span>
<span class="s">        "this": "Date"</span>
<span class="s">    },</span>
<span class="s">    "_uuid": {</span>
<span class="s">        "description": "Returns the transactionId",</span>
<span class="s">        "functor": "getUuid",</span>
<span class="s">        "this": false</span>
<span class="s">    },</span>
<span class="s">    "_getDate": {</span>
<span class="s">        "description": "Returns the day of the month(from 0-31)",</span>
<span class="s">        "args": {</span>
<span class="s">            "dateString": "The timestamp from which date needs to be extracted",</span>
<span class="s">            "milliseconds": "Time in milliseconds for which date needs to be extracted"</span>
<span class="s">        },</span>
<span class="s">        "functor": "getDate",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_getDay": {</span>
<span class="s">        "description": "Returns the day of the week(from 0-6)",</span>
<span class="s">        "args": {</span>
<span class="s">            "dateString": "The timestamp from which date needs to be extracted",</span>
<span class="s">            "milliseconds": "Time in milliseconds for which date needs to be extracted"</span>
<span class="s">        },</span>
<span class="s">        "functor": "getDay",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_getFullYear": {</span>
<span class="s">        "description": "Returns the year,according to the local time or given timestamp",</span>
<span class="s">        "args": {</span>
<span class="s">            "dateString": "The timestamp from which date needs to be extracted",</span>
<span class="s">            "milliseconds": "Time in milliseconds for which date needs to be extracted"</span>
<span class="s">        },</span>
<span class="s">        "functor": "getFullYear",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_getHours": {</span>
<span class="s">        "description": "Returns the hour(from 0-23), according to the local time or given timestamp",</span>
<span class="s">        "args": {</span>
<span class="s">            "dateString": "The timestamp from which date needs to be extracted",</span>
<span class="s">            "milliseconds": "Time in milliseconds for which date needs to be extracted"</span>
<span class="s">        },</span>
<span class="s">        "functor": "getHours",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_getMinutes": {</span>
<span class="s">        "description": "Returns the minute(from 0-59), according to the local time or given timestamp",</span>
<span class="s">        "args": {</span>
<span class="s">            "dateString": "The timestamp from which date needs to be extracted",</span>
<span class="s">            "milliseconds": "Time in milliseconds for which date needs to be extracted"</span>
<span class="s">        },</span>
<span class="s">        "functor": "getMinutes",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_getMonth": {</span>
<span class="s">        "description": "Returns the month(from 0-11), according to the local time or given timestamp ",</span>
<span class="s">        "args": {</span>
<span class="s">            "dateString": "The timestamp from which date needs to be extracted",</span>
<span class="s">            "milliseconds": "Time in milliseconds for which date needs to be extracted"</span>
<span class="s">        },</span>
<span class="s">        "functor": "getMonth",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_getSeconds": {</span>
<span class="s">        "description": "Returns the second(from 0-59), according to the local time or given timestamp",</span>
<span class="s">        "args": {</span>
<span class="s">            "dateString": "The timestamp from which date needs to be extracted",</span>
<span class="s">            "milliseconds": "Time in milliseconds for which date needs to be extracted"</span>
<span class="s">        },</span>
<span class="s">        "functor": "getSeconds",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_getMilliseconds": {</span>
<span class="s">        "description": "Returns the milliseconds(from 0-999), according to the local time or given timestamp",</span>
<span class="s">        "args": {</span>
<span class="s">            "dateString": "The timestamp from which date needs to be extracted",</span>
<span class="s">            "milliseconds": "Time in milliseconds for which date needs to be extracted"</span>
<span class="s">        },</span>
<span class="s">        "functor": "getMilliseconds",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_getTime": {</span>
<span class="s">        "description": "Returns the number of milliseconds since 1970/01/01",</span>
<span class="s">        "args": {</span>
<span class="s">            "dateString": "The timestamp from which date needs to be extracted",</span>
<span class="s">            "milliseconds": "Time in milliseconds for which date needs to be extracted"</span>
<span class="s">        },</span>
<span class="s">        "functor": "getTime",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_add": {</span>
<span class="s">        "description": "Adds the two given numbers",</span>
<span class="s">        "args": {</span>
<span class="s">            "operand1": "First operand to add",</span>
<span class="s">            "operand2": "Second operand to add"</span>
<span class="s">        },</span>
<span class="s">        "functor": "add",</span>
<span class="s">        "this": false</span>
<span class="s">    },</span>
<span class="s">    "_substract": {</span>
<span class="s">        "description": "Subtracts the two given numbers",</span>
<span class="s">        "args": {</span>
<span class="s">            "operand1": "First operand to subtract",</span>
<span class="s">            "operand2": "Second operand to subtract"</span>
<span class="s">        },</span>
<span class="s">        "functor": "subtract",</span>
<span class="s">        "this": false</span>
<span class="s">    },</span>
<span class="s">    "_multiply": {</span>
<span class="s">        "description": "Multiplies the two given numbers",</span>
<span class="s">        "args": {</span>
<span class="s">            "operand1": "First operand to multiply",</span>
<span class="s">            "operand2": "Second operand to multiply"</span>
<span class="s">        },</span>
<span class="s">        "functor": "multiply",</span>
<span class="s">        "this": false</span>
<span class="s">    },</span>
<span class="s">    "_divide": {</span>
<span class="s">        "description": "Divides the two given numbers",</span>
<span class="s">        "args": {</span>
<span class="s">            "operand1": "First operand to divide",</span>
<span class="s">            "operand2": "Second operand to divide"</span>
<span class="s">        },</span>
<span class="s">        "functor": "divide",</span>
<span class="s">        "this": false</span>
<span class="s">    },</span>
<span class="s">    "_isInRange": {</span>
<span class="s">        "description": "Checks whether the given number is in the given range or not.",</span>
<span class="s">        "args": {</span>
<span class="s">            "Input": "Number to check the range for.",</span>
<span class="s">            "min": "Minimum value in the range",</span>
<span class="s">            "max": "Maximun value in the range"</span>
<span class="s">        },</span>
<span class="s">        "functor": "checkRange",</span>
<span class="s">        "this": false</span>
<span class="s">    },</span>
<span class="s">    "_getTimestamp": {</span>
<span class="s">        "description": "Returns the current timestamp",</span>
<span class="s">        "functor": "getTimestamp",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_msisdnValidator": {</span>
<span class="s">        "description": "Validates whether an MSISDN is correct or not",</span>
<span class="s">        "args": {</span>
<span class="s">            "MSISDN": "String consisting of the MSISDN number"</span>
<span class="s">        },</span>
<span class="s">        "functor": "msisdnValidator",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_parseDedicatedAccount": {</span>
<span class="s">        "description": "Parse the Dedicated account information Array",</span>
<span class="s">        "args": {</span>
<span class="s">            "dedicatedAccountInformation": "Array of dedicatedAccountInformation",</span>
<span class="s">            "dedicatedAccountID": "dedicatedAccountID for filter"</span>
<span class="s">        },</span>
<span class="s">        "functor": "parseDedicatedAccount",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_parseDedicatedOfferID": {</span>
<span class="s">        "description": "Parse the Dedicated account information for offerID",</span>
<span class="s">        "args": {</span>
<span class="s">            "dedicatedAccountInformation": "Array of dedicatedAccountInformation",</span>
<span class="s">            "dedicatedOfferID": "dedicatedOfferID for filter"</span>
<span class="s">        },</span>
<span class="s">        "functor": "parseDedicatedOfferID",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_parseOfferInformationList": {</span>
<span class="s">        "description": "Parse the Offer information Array",</span>
<span class="s">        "args": {</span>
<span class="s">            "offerInformationList": "Array of offerInformation",</span>
<span class="s">            "offerID": "offerID for filter"</span>
<span class="s">        },</span>
<span class="s">        "functor": "parseOfferInformationList",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_checkOfferIDExists": {</span>
<span class="s">        "description": "Parse the OfferList and Validate OfferID exists in list",</span>
<span class="s">        "args": {</span>
<span class="s">            "offerInformationList": "Array of offerInformation",</span>
<span class="s">            "offerID": "offerID for filter"</span>
<span class="s">        },</span>
<span class="s">        "functor": "checkOfferIDExists",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_contains": {</span>
<span class="s">        "description": "To check wether the given given element exists in the given lists",</span>
<span class="s">        "args": {</span>
<span class="s">            "element": "element to check",</span>
<span class="s">            "elementsList": "elements list"</span>
<span class="s">        },</span>
<span class="s">        "functor": "contains",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_date2customformat": {</span>
<span class="s">        "description": "Date custom formatter",</span>
<span class="s">        "args": {</span>
<span class="s">            "date": "Date string",</span>
<span class="s">            "format": "Date format"</span>
<span class="s">        },</span>
<span class="s">        "functor": "date2customformat",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_regexValidator": {</span>
<span class="s">        "description": "Regex Validator",</span>
<span class="s">        "args": {</span>
<span class="s">            "regex": "Regex string",</span>
<span class="s">            "str": "String for validation against regex"</span>
<span class="s">        },</span>
<span class="s">        "functor": "regexValidator",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_xml2json": {</span>
<span class="s">        "description": "XML to JSON convertor",</span>
<span class="s">        "args": {</span>
<span class="s">            "str": "xml string buffer"</span>
<span class="s">        },</span>
<span class="s">        "functor": "xml2json",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_accumlateDAValue": {</span>
<span class="s">        "description": "Looping the dedicatedAccountValue for offerID",</span>
<span class="s">        "args": {</span>
<span class="s">            "dedicatedAccountInformation": "Array of dedicatedAccountInformation",</span>
<span class="s">            "OfferID": "OfferID for filter"</span>
<span class="s">        },</span>
<span class="s">        "functor": "_accumlateDAValue",</span>
<span class="s">        "this": true</span>
<span class="s">    },</span>
<span class="s">    "_getNoOfDaysBetween": {</span>
<span class="s">        "description": "Returns the day of days from the current system time",</span>
<span class="s">        "args": {</span>
<span class="s">            "dateString": "The timestamp from which date needs to be extracted"</span>
<span class="s">        },</span>
<span class="s">        "functor": "_getNoOfDaysBetween",</span>
<span class="s">        "this": true</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list of MACROS supported in platform.</p>
<h3 id='http-request-74'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/configapi/macros</code></p>
<h3 id='http-response-error-codes-71'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Macros API!
</aside>
<h2 id='list-operators'>List Operators</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "eq": {</span>
<span class="s">        "op": "eq",</span>
<span class="s">        "name": "Equals",</span>
<span class="s">        "description": "First operand Equal to Second operand"</span>
<span class="s">    },</span>
<span class="s">    "ne": {</span>
<span class="s">        "op": "ne",</span>
<span class="s">        "name": "Not Equals",</span>
<span class="s">        "description": "First operand Not Equal to Second operand"</span>
<span class="s">    },</span>
<span class="s">    "gte": {</span>
<span class="s">        "op": "gte",</span>
<span class="s">        "name": "Greater Than or Equals",</span>
<span class="s">        "description": "First operand is greater than or equal to Second operand"</span>
<span class="s">    },</span>
<span class="s">    "gt": {</span>
<span class="s">        "op": "gt",</span>
<span class="s">        "name": "Greater Than",</span>
<span class="s">        "description": "First operand is greater than to Second operand"</span>
<span class="s">    },</span>
<span class="s">    "lte": {</span>
<span class="s">        "op": "lte",</span>
<span class="s">        "name": "Less Than or Equals",</span>
<span class="s">        "description": "First operand is lesser than or equal to Second operand"</span>
<span class="s">    },</span>
<span class="s">    "lt": {</span>
<span class="s">        "op": "lt",</span>
<span class="s">        "name": "Less Than",</span>
<span class="s">        "description": "First operand is lesser than to Second operand"</span>
<span class="s">    },</span>
<span class="s">    "contains": {</span>
<span class="s">        "op": "contains",</span>
<span class="s">        "name": "Contains",</span>
<span class="s">        "description": "Left operand contains in Right operand"</span>
<span class="s">    },</span>
<span class="s">    "ncontains": {</span>
<span class="s">        "op": "ncontains",</span>
<span class="s">        "name": "Does Not Contains",</span>
<span class="s">        "description": "Left operand does not contains in Right operand"</span>
<span class="s">    },</span>
<span class="s">    "and": {</span>
<span class="s">        "op": "and",</span>
<span class="s">        "name": "And",</span>
<span class="s">        "description": "Left operand and Right operand"</span>
<span class="s">    },</span>
<span class="s">    "or": {</span>
<span class="s">        "op": "or",</span>
<span class="s">        "name": "Or",</span>
<span class="s">        "description": "Left operand or Right operand"</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list of Operators supported in platform.</p>
<h3 id='http-request-75'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/configapi/operators</code></p>
<h3 id='http-response-error-codes-72'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Operators API!
</aside>
<h2 id='list-languages'>List Languages</h2>
<blockquote>
<p>Success Response Payload:</p>
</blockquote>
<pre class="highlight http tab-http"><code><span class="k">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">Content-Type</span><span class="p">:</span> <span class="s">application/json</span>
<span class="s">{</span>
<span class="s">    "te": {</span>
<span class="s">        "locale": "te",</span>
<span class="s">        "description": "Telugu"</span>
<span class="s">    },</span>
<span class="s">    "ta": {</span>
<span class="s">        "locale": "ta",</span>
<span class="s">        "description": "Tamil"</span>
<span class="s">    },</span>
<span class="s">    "hi": {</span>
<span class="s">        "locale": "hi",</span>
<span class="s">        "description": "Hindi"</span>
<span class="s">    },</span>
<span class="s">    "kn": {</span>
<span class="s">        "locale": "kn",</span>
<span class="s">        "description": "Kannada"</span>
<span class="s">    },</span>
<span class="s">    "en_US": {</span>
<span class="s">        "locale": "en_US",</span>
<span class="s">        "description": "English - United States"</span>
<span class="s">    },</span>
<span class="s">    "en": {</span>
<span class="s">        "locale": "en",</span>
<span class="s">        "description": "English"</span>
<span class="s">    },</span>
<span class="s">    "en_IN": {</span>
<span class="s">        "locale": "en_IN",</span>
<span class="s">        "description": "English - India"</span>
<span class="s">    }</span>
<span class="s">}</span>
</code></pre>
<p>Use this endpoint to list of Languages supported in platform.</p>
<h3 id='http-request-76'>HTTP Request</h3>
<p><code>GET http://example.com/leap_gw/configapi/languages</code></p>
<h3 id='http-response-error-codes-73'>HTTP Response Error codes</h3>
<table><thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Internal Code</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
<td>-</td>
<td></td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>702</td>
<td>App Store not reachable</td>
</tr>
</tbody></table>

<aside class="success">
Remember — List Languages API!
</aside>
<h1 id='errors'>Errors</h1>
<p>The LEAP API uses the following error codes:</p>

<table><thead>
<tr>
<th>Error Code</th>
<th>Meaning</th>
</tr>
</thead><tbody>
<tr>
<td>200</td>
<td>Success</td>
</tr>
<tr>
<td>201</td>
<td>Created</td>
</tr>
<tr>
<td>202</td>
<td>Accepted</td>
</tr>
<tr>
<td>204</td>
<td>No Content</td>
</tr>
<tr>
<td>400</td>
<td>Bad Request</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
</tr>
<tr>
<td>413</td>
<td>Request Entity Too Large</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
</tr>
<tr>
<td>501</td>
<td>Not Implemented</td>
</tr>
<tr>
<td>502</td>
<td>Bad Gateway</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
</tr>
<tr>
<td>504</td>
<td>Gateway Timeout</td>
</tr>
</tbody></table>

<table><thead>
<tr>
<th>Internal Error Code</th>
<th>Meaning</th>
</tr>
</thead><tbody>
<tr>
<td>600</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>601</td>
<td>Missing username</td>
</tr>
<tr>
<td>602</td>
<td>Invalid Username or Password</td>
</tr>
<tr>
<td>603</td>
<td>OTP verification failed.</td>
</tr>
<tr>
<td>605</td>
<td>Authorization missing</td>
</tr>
<tr>
<td>606</td>
<td>JWT invalid</td>
</tr>
<tr>
<td>607</td>
<td>JWT expired</td>
</tr>
<tr>
<td>608</td>
<td>Old Password is incorrect</td>
</tr>
<tr>
<td>609</td>
<td>Auth Server is down</td>
</tr>
<tr>
<td>610</td>
<td>User Activated Successfully</td>
</tr>
<tr>
<td>611</td>
<td>User Deactivated Successfully</td>
</tr>
<tr>
<td>612</td>
<td>Length of the LoginID exceeded</td>
</tr>
<tr>
<td>613</td>
<td>Length of the firstname exceeded</td>
</tr>
<tr>
<td>614</td>
<td>Length of the email Id exceeded</td>
</tr>
<tr>
<td>615</td>
<td>Missing OTP</td>
</tr>
<tr>
<td>616</td>
<td>Missing password</td>
</tr>
<tr>
<td>617</td>
<td>Missing Old password</td>
</tr>
<tr>
<td>618</td>
<td>Missing New password</td>
</tr>
<tr>
<td>619</td>
<td>Old and New password cannot be same</td>
</tr>
<tr>
<td>620</td>
<td>Password matches with one of the last 5 passwords</td>
</tr>
<tr>
<td>621</td>
<td>User logged out. Cannot issue JWT</td>
</tr>
<tr>
<td>622</td>
<td>User not found</td>
</tr>
<tr>
<td>623</td>
<td>JWT blacklisted</td>
</tr>
<tr>
<td>624</td>
<td>User Suspended</td>
</tr>
<tr>
<td>625</td>
<td>Resource Permission Denied</td>
</tr>
<tr>
<td>641</td>
<td>User Already Activated</td>
</tr>
<tr>
<td>642</td>
<td>User Already Deactivated</td>
</tr>
<tr>
<td>699</td>
<td>Something went wrong please try later</td>
</tr>
<tr>
<td>700</td>
<td>Service not available</td>
</tr>
<tr>
<td>701</td>
<td>Application Management Internal Error</td>
</tr>
<tr>
<td>702</td>
<td>App Store not reachable</td>
</tr>
<tr>
<td>703</td>
<td>Bad input for size field</td>
</tr>
<tr>
<td>704</td>
<td>Bad input for sortf field</td>
</tr>
<tr>
<td>705</td>
<td>Bad input for order field</td>
</tr>
<tr>
<td>706</td>
<td>App Not found in AppStore</td>
</tr>
<tr>
<td>707</td>
<td>App created successfully</td>
</tr>
<tr>
<td>708</td>
<td>App updated successfully</td>
</tr>
<tr>
<td>709</td>
<td>App deleted successfully</td>
</tr>
<tr>
<td>710</td>
<td>Apps listed successfully</td>
</tr>
<tr>
<td>711</td>
<td>App is not defined</td>
</tr>
<tr>
<td>712</td>
<td>Invalid App Schema</td>
</tr>
<tr>
<td>713</td>
<td>Invalid JSON body</td>
</tr>
<tr>
<td>714</td>
<td>App id field is missing</td>
</tr>
<tr>
<td>715</td>
<td>App name field is missing</td>
</tr>
<tr>
<td>716</td>
<td>appData field is missing in appData</td>
</tr>
<tr>
<td>717</td>
<td>startId field is missing in appData</td>
</tr>
<tr>
<td>718</td>
<td>modules field is missing in appData</td>
</tr>
<tr>
<td>719</td>
<td>type field is missing in module</td>
</tr>
<tr>
<td>720</td>
<td>coordinates field is missing in module</td>
</tr>
<tr>
<td>721</td>
<td>settings field is missing in module</td>
</tr>
<tr>
<td>722</td>
<td>input field is missing in module</td>
</tr>
<tr>
<td>723</td>
<td>process field is missing in module</td>
</tr>
<tr>
<td>724</td>
<td>output field is missing in module</td>
</tr>
<tr>
<td>725</td>
<td>End module id is not found</td>
</tr>
<tr>
<td>726</td>
<td>Start module is not defined</td>
</tr>
<tr>
<td>727</td>
<td>End module is not defined</td>
</tr>
<tr>
<td>728</td>
<td>Invalid App:id</td>
</tr>
<tr>
<td>729</td>
<td>Invalid App:name</td>
</tr>
<tr>
<td>730</td>
<td>Invalid App:appData</td>
</tr>
<tr>
<td>731</td>
<td>Invalid App:startId</td>
</tr>
<tr>
<td>732</td>
<td>Invalid App:modules</td>
</tr>
<tr>
<td>733</td>
<td>Invalid AppModules:type</td>
</tr>
<tr>
<td>734</td>
<td>Invalid AppModules:coordinates</td>
</tr>
<tr>
<td>735</td>
<td>Invalid AppModules:settings</td>
</tr>
<tr>
<td>736</td>
<td>Invalid AppModules:input</td>
</tr>
<tr>
<td>737</td>
<td>Invalid AppModules:process</td>
</tr>
<tr>
<td>738</td>
<td>Invalid AppModules:output</td>
</tr>
<tr>
<td>739</td>
<td>Resource conflicts, App already exists</td>
</tr>
<tr>
<td>740</td>
<td>Page does not exists</td>
</tr>
<tr>
<td>741</td>
<td>Bad input for status field</td>
</tr>
<tr>
<td>742</td>
<td>Resource conflicts, App no longer exists</td>
</tr>
<tr>
<td>743</td>
<td>Page does not exist</td>
</tr>
<tr>
<td>744</td>
<td>Access forbidden for App</td>
</tr>
<tr>
<td>745</td>
<td>appTemplateId field is missing in request Payload</td>
</tr>
<tr>
<td>746</td>
<td>Resource conflicts, App Template no longer exists</td>
</tr>
<tr>
<td>747</td>
<td>Module undefined</td>
</tr>
<tr>
<td>748</td>
<td>App name should have Minimum 5 characters</td>
</tr>
<tr>
<td>749</td>
<td>App name should not exceed more than 32 characters</td>
</tr>
<tr>
<td>750</td>
<td>App description field MAX limit exceeds</td>
</tr>
<tr>
<td>751</td>
<td>Invalid Content Type</td>
</tr>
<tr>
<td>752</td>
<td>App owner updated successfully</td>
</tr>
<tr>
<td>753</td>
<td>App name already exists</td>
</tr>
<tr>
<td>754</td>
<td>Incorrect status field</td>
</tr>
<tr>
<td>760</td>
<td>Currently application is not in the state to perform this action</td>
</tr>
<tr>
<td>761</td>
<td>Invalid events</td>
</tr>
<tr>
<td>762</td>
<td>Invalid input</td>
</tr>
<tr>
<td>763</td>
<td>User dont have the permission</td>
</tr>
<tr>
<td>781</td>
<td>Elastic search Error</td>
</tr>
<tr>
<td>782</td>
<td>ESEmptyData</td>
</tr>
<tr>
<td>783</td>
<td>reportTypeError</td>
</tr>
<tr>
<td>784</td>
<td>InfluxDB search error</td>
</tr>
<tr>
<td>785</td>
<td>App Archive request accepted, You receive notification shortly</td>
</tr>
<tr>
<td>800</td>
<td>Plugins internal error</td>
</tr>
<tr>
<td>801</td>
<td>Category is not found</td>
</tr>
<tr>
<td>802</td>
<td>Requested plugin is not available</td>
</tr>
<tr>
<td>803</td>
<td>Not able to connnect to external interface</td>
</tr>
<tr>
<td>804</td>
<td>Settings not found</td>
</tr>
<tr>
<td>805</td>
<td>Query object is not available</td>
</tr>
<tr>
<td>806</td>
<td>Permission is not allowed</td>
</tr>
<tr>
<td>807</td>
<td>Socket time out</td>
</tr>
<tr>
<td>808</td>
<td>Resource not found/Connection refused</td>
</tr>
<tr>
<td>811</td>
<td>Module name is missing</td>
</tr>
<tr>
<td>812</td>
<td>Host is missing</td>
</tr>
<tr>
<td>813</td>
<td>port is missing</td>
</tr>
<tr>
<td>814</td>
<td>path is missing</td>
</tr>
<tr>
<td>815</td>
<td>origin time stamp invalid format</td>
</tr>
<tr>
<td>816</td>
<td>subscriber NAI is not allowed</td>
</tr>
<tr>
<td>817</td>
<td>subscriber number type not correct</td>
</tr>
<tr>
<td>818</td>
<td>Message capability flag type is not correct</td>
</tr>
<tr>
<td>851</td>
<td>WSDL/XSD root upload directory not found</td>
</tr>
<tr>
<td>852</td>
<td>WSDL/XSD upload failed</td>
</tr>
<tr>
<td>853</td>
<td>File extension not supported</td>
</tr>
<tr>
<td>854</td>
<td>Internal error while uploading the file</td>
</tr>
<tr>
<td>855</td>
<td>Filename should be <appName>_<locale>.json format</td>
</tr>
<tr>
<td>856</td>
<td>Unsupported file format is being uploaded</td>
</tr>
<tr>
<td>859</td>
<td>File deletion unsuccesful</td>
</tr>
<tr>
<td>860</td>
<td>Error while performing plugin confirmation step</td>
</tr>
<tr>
<td>861</td>
<td>Plugin Overwriting Error</td>
</tr>
<tr>
<td>862</td>
<td>Request should have either &#39;Yes&#39; or &#39;No&#39; in query</td>
</tr>
<tr>
<td>863</td>
<td>Uploaded files exceed the max size</td>
</tr>
<tr>
<td>870</td>
<td>Error while reading WSDL/XSD directory</td>
</tr>
<tr>
<td>871</td>
<td>Empty directory. No WSDL/XSD to process</td>
</tr>
<tr>
<td>872</td>
<td>Error while parsing the WSDL/XSD</td>
</tr>
<tr>
<td>873</td>
<td>SOAP plugin check error</td>
</tr>
<tr>
<td>874</td>
<td>WSDL directory deletion unsuccessful</td>
</tr>
<tr>
<td>875</td>
<td>Create plugin error</td>
</tr>
<tr>
<td>876</td>
<td>Error in deploy method</td>
</tr>
<tr>
<td>880</td>
<td>SOAP execution failed</td>
</tr>
<tr>
<td>881</td>
<td>SOAP testing failed</td>
</tr>
<tr>
<td>882</td>
<td>SOAP Execution Fault Error</td>
</tr>
<tr>
<td>890</td>
<td>File upload successful</td>
</tr>
<tr>
<td>891</td>
<td>File delete successful</td>
</tr>
<tr>
<td>892</td>
<td>Plugin already exists. Check for user&#39;s confirmation</td>
</tr>
<tr>
<td>893</td>
<td>Plugin deployed successfully</td>
</tr>
<tr>
<td>894</td>
<td>Plugin overwriting successful</td>
</tr>
<tr>
<td>895</td>
<td>Plugin overwriting aborted</td>
</tr>
<tr>
<td>896</td>
<td>Page does not exist</td>
</tr>
<tr>
<td>899</td>
<td>Plugin request execution timeout</td>
</tr>
<tr>
<td>900</td>
<td>Plugin Store Internal error</td>
</tr>
<tr>
<td>901</td>
<td>Invalid plugin ID</td>
</tr>
<tr>
<td>902</td>
<td>built-in plugin cannot be deleted</td>
</tr>
<tr>
<td>903</td>
<td>Plugin deleted successfully</td>
</tr>
<tr>
<td>904</td>
<td>Failed to delete Plugin</td>
</tr>
<tr>
<td>905</td>
<td>Payload is required to Update the Plugin settings</td>
</tr>
<tr>
<td>906</td>
<td>Plugin settings updated successfully</td>
</tr>
<tr>
<td>907</td>
<td>Failed to update Plugin settings</td>
</tr>
<tr>
<td>908</td>
<td>Plugin page does not exists</td>
</tr>
<tr>
<td>909</td>
<td>Plugin not found</td>
</tr>
<tr>
<td>910</td>
<td>Payload is required</td>
</tr>
<tr>
<td>911</td>
<td>Invalid status Field</td>
</tr>
<tr>
<td>912</td>
<td>Plugin Activation is Successful</td>
</tr>
<tr>
<td>913</td>
<td>Plugin Deactivation is Successful</td>
</tr>
<tr>
<td>914</td>
<td>Plugin Activation is Failed</td>
</tr>
<tr>
<td>915</td>
<td>Plugin Deactivation is Failed</td>
</tr>
<tr>
<td>1000</td>
<td>USSD Service created successfully</td>
</tr>
<tr>
<td>1001</td>
<td>USSD Service name field is missing</td>
</tr>
<tr>
<td>1002</td>
<td>Service name should have Minimum 5 characters</td>
</tr>
<tr>
<td>1003</td>
<td>Invalid USSD Service name</td>
</tr>
<tr>
<td>1004</td>
<td>Service name should not exceed more than 32 characters</td>
</tr>
<tr>
<td>1005</td>
<td>Service description field MAX limit exceeds</td>
</tr>
<tr>
<td>1006</td>
<td>Service name already exists</td>
</tr>
<tr>
<td>1007</td>
<td>USSD Shortcode already exists</td>
</tr>
<tr>
<td>1008</td>
<td>USSD Shortcode does not exists</td>
</tr>
<tr>
<td>1009</td>
<td>USSD Shortcode Purged Successfully</td>
</tr>
<tr>
<td>1010</td>
<td>USSD Shortcode Updated Successfully</td>
</tr>
</tbody></table>

      </div>
      <div class="dark-box">
      </div>
    </div>
  </body>
</html>
