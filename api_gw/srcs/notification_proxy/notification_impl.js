/**
 * Implements the api for plugins management.
 **/
const logger = global.logger;
const common = require("common");
const httpErrorCodes = common.http_codes;
const workFlowMap = require('../workflow_apis/workflow_map');
const AppStore = require("app_store");
const message = require("message");
const error_codes = message.error_codes;
const DEFAULT_PAGESIZE = 10;

module.exports = {

  getUnReadNotificationCount: async function (req, res) {
    try {
      logger.trace("Get Un-Read notification Count is executing...");
      let count = await AppStore.getCountOfUnReadEvents(req.invoker && req.invoker.id);
      logger.trace("The Un-Read notification count are:" + count);
      return res.status(httpErrorCodes.ok.code).json({
        code: error_codes.success,
        msg: count
      });
    } catch (error) {
      logger.error(error);
      return res.status(httpErrorCodes.badGateway.code).json(message.getResponseJson(req.locale, httpErrorCodes.appStoreError));
    }
  },

  listNotifications: async function (req, res) {
    try {
      logger.trace("List notifications is executing...");
      let opts = prepareListQuery(req);
      const headers = await AppStore.getNotificationsCount(Object.assign({}, opts));
      if (opts.page > headers.lastPage) {
        return res.status(httpErrorCodes.badRequest.code).json(message.getResponseJson(req.locale, error_codes.pageDoesNotExists));
      }
      const response = preparePaginationHeaders(req, res, opts, headers);
      let eventsList = await AppStore.listEvents(opts);
      response.data.eventList = await prepareEventsResponse(req, eventsList);
      return res.status(httpErrorCodes.ok.code).json(response);
    } catch (error) {
      logger.error(error);
      return res.status(httpErrorCodes.badGateway.code).json(message.getResponseJson(req.locale, httpErrorCodes.appStoreError));
    }
  },

  markNotificationsRead: async function (req, res) {
    try {
      let userId = req.invoker && req.invoker.id;
      logger.trace("Mark notifications as Read is executing...:" + req.body.events + "::For User:" + userId);

      /*
      Commenting this code for Release purpose to make Smoke test pass
      if (req.headers['content-type'] != 'application/json') {
          logger.error("Invalid content type");
          return res.status(httpErrorCodes.badRequest.code).json(message.getResponseJson(req.locale, error_codes.invalidContentType));
      }
      */
      if (req.body.events == null || !(req.body.events instanceof Array)) {
        logger.error("Event is null or invalid datatype");
        return res.status(httpErrorCodes.badRequest.code).json(message.getResponseJson(req.locale, error_codes.invalidInput));
      }


      let result = await AppStore.updateEvents(req.body.events, userId);
      //logger.trace("result is:" + JSON.stringify(result));
      if (result == true) {
        return res.status(httpErrorCodes.ok.code).json(message.getResponseJson(req.locale, error_codes.success));
      } else {
        return res.status(httpErrorCodes.resourceConflict.code).json(message.getResponseJson(req.locale, error_codes.invalidEvent));
      }
    } catch (error) {
      logger.error(error);
      return res.status(httpErrorCodes.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
    }
  },

  deleteNotifications: async function (req, res) {
    try {
      let userId = req.invoker && req.invoker.id;
      logger.trace("Delete notifications is executing...:" + req.body.events + ":: For user:" + userId);
      /*
      Commenting this code for Release purpose to make Smoke test pass
      if (req.headers['content-type'] != 'application/json') {
          logger.error("Invalid content type");
          return res.status(httpErrorCodes.badRequest.code).json(message.getResponseJson(req.locale, error_codes.invalidContentType));
      }
      */

      logger.trace("Delete notifications:", req.body.events, req.body.events instanceof Array);
      if (req.body.events == null || !(req.body.events instanceof Array)) {
        logger.error("Event is null or invalid datatype");
        return res.status(httpErrorCodes.badRequest.code).json(message.getResponseJson(req.locale, error_codes.invalidInput));
      }

      let result = await AppStore.deleteEvents(req.body.events, userId);
      if (result == true) {
        return res.status(httpErrorCodes.ok.code).json(message.getResponseJson(req.locale, error_codes.success));
      } else {
        return res.status(httpErrorCodes.resourceConflict.code).json(message.getResponseJson(req.locale, error_codes.invalidEvent));
      }
    } catch (error) {
      logger.error(error);
      return res.status(httpErrorCodes.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
    }
  }
};


function prepareEventsResponse(req, eventsList) {
  return new Promise(resolve => {
    let FinalEventList = [];
    if (eventsList == null || eventsList.length == 0) return resolve([]);
    eventsList.forEach(async event => {
      let tmpEvent = {}, eventDesc = workFlowMap.eventsMessage[event.action];
      eventDesc = eventDesc.replace("$appName", event.appName);
      tmpEvent.msg = eventDesc;
      tmpEvent.data = event;
      FinalEventList.push(tmpEvent);
      if (FinalEventList.length == eventsList.length) resolve(FinalEventList);
    });
    logger.trace("Final events response list:" + JSON.stringify(FinalEventList));
  });
}

function prepareListQuery(req) {
  let opts = {};
  opts.token = req.query.token || "";
  opts.page = req.query.page || 1;
  opts.size = req.query.size || global.config.api_gw.appsApi.defaultPageSize || DEFAULT_PAGESIZE;
  if (req.query.sortf != null) {
    opts.sortf = req.query.sortf;
    opts.order = req.query.order;
  } else {
    opts.sortf = "createdAt";
    opts.order = "desc";
  }
  opts.user = req.invoker && req.invoker.id;
  return opts;
}

function preparePaginationHeaders(req, res, opts, headers) {
  res.set("totalEvents", headers.totalEvents);
  res.set("pageSize", opts.size);
  if (headers.totalEvents > 0) {
    let header = "/apps?token=" + opts.token + "&size=" + headers.pageSize;
    if (req.query.status) {
      header += "&status=" + req.query.status
    }
    header += "&page=";
    res.set("firstPage", header + 1);
    res.set("prevPage", header + headers.prevPage);
    res.set("currPage", header + opts.page);
    res.set("nextPage", header + headers.nextPage);
    res.set("lastPage", header + headers.lastPage);
  }
  let response = message.getResponseJson(req.locale, error_codes.success);
  response.data = {};
  response.data.pageSize = headers.pageSize;
  response.data.totalEvents = headers.totalEvents;
  response.data.totalPages = headers.lastPage;
  response.data.currentPage = parseInt(opts.page, 10);
  return response;
}
