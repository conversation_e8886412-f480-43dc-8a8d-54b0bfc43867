"use strict";
/**
 * List of routes for notification management.
 */

const notificationAPI = require("./notification_impl");

const Router = require("express").Router();
const JWTBlackListCheck = require("../session.js").check4Blacklist;

Router.get("/count", JWTBlackListCheck, notificationAPI.getUnReadNotificationCount);
Router.get("/", JWTBlackListCheck, notificationAPI.listNotifications);
Router.post("/update", JWTBlackListCheck, notificationAPI.markNotificationsRead);

module.exports = Router;
