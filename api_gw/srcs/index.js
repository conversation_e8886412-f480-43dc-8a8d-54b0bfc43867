"use strict";
/**
 *  A middle-ware / middle-tier app or an API proxy (or whatever)
 *  for accessing various REST API's of LEAP platform.
 *
 *  As of this writing (6th Dec 2017), it provides the following
 *  category of functions:
 *  1.  User & Role Management API
 *  2.  Application Management API
 *
 **/
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
const fs = require("fs");
const path = require("path");
const args = require("commander");
const express = require("express");
const os = require("os");
const cluster = require("cluster");
const utility = require("utility");
const ConfigTree = require("config-tree");
const bodyParser = require("body-parser");
const message = require("message");
const common = require("common");
const KPI = require("app_kpi");
const OAM = require("oam");
const http = require("http");
const https = require("https")
const HTTPCODES = common.http_codes;
const error_codes = message.error_codes;
const tls = require("tls");
const net = require('net');

const app = express();
const redis_man = common.redis_man;
const whiteboard = common.whiteboard;
const ThrottleLimiter = require("express-rate-limit");

let logger;
////////////////////////////////////////////////////////////////////////
// Internal helper routines...
////////////////////////////////////////////////////////////////////////

function printBanner() {
  let data = fs.readFileSync(__dirname + "/banner.txt");
  console.log(data.toString());
}

async function initBasic() {
  args.version(common.version)
    .option("-H, --host [host]", "Config server host -- defaults to", process.env.CONFIG_HOST || "127.0.0.1")
    .option("-p, --port [port]", "Config server port -- defaults to", process.env.CONFIG_PORT || 6380)
    .option("-n, --db [db]", "Config server db index -- defaults to", process.env.CONFIG_DB || 0)
    .option("-a, --password [password]", "config server password", process.env.CONFIG_AUTH || undefined)
    .option("-s, --pluginScan [pluginScan]", "Plugin Scannaer enable/disbale. If value supplied is 0 initial plugin scanner is disabled. Defaults to 1.", 1)
    .parse(process.argv);

  printBanner();
  global.args = args.opts();
  let opts = {
    args: global.args,
    configTasks: [
      "getModuleInfoEx",
      "getDBInfo",
      "getGlobalInfo"
    ],
    keys2read: {
      getModuleInfoEx: ["authServer", "api_gw", "app_engine_production", "app_engine_staging", "pm_fsync", "acl_manager", "app_engine_development"],
      getDBInfo: ["app_store", "reportServer", "influxDB"],
      getGlobalInfo: ["whiteboard", "country", "timezone", "authSecret", "kpi", "oam", "certificates", "security"]
    }
  };
  process.stdout.write("Reading configuration from config server at:" + global.args.host + ":" + global.args.port + "...");
  const ConfigProxy = new ConfigTree(opts);
  return ConfigProxy.readConfig()
    .then(async () => {
      global.componentName = "apigw";
      ConfigProxy.on("reload_config", async (pattern, channel, key) => {
        try {
          delete require.cache[require.resolve("./logger")];
          logger = require("./logger");
        } catch (error) {
          logger.error(error);
        }
      });
    })
    .catch(e => {
      console.error("Failed to start LEAP APIGW, Connection to:", global.args.host, global.args.port, "Failed", e);
      OAM.raiseCriticalAlert("apigw")
        .then(() => {
          process.exit(1);
        }).catch(err => {
          console.error("Failed to Critical alert for API gateway process", err);
          process.exit(1);
        });
    });
}

initBasic()
  .then(function () {
    return checkTelnetConnection(global.config.app_store.host, global.config.app_store.port);
  })
  .then(function () {
    return checkTelnetConnection(global.config.pm_fsync.host, global.config.pm_fsync.port);
  })
  .then(async function () {
    logger = require("./logger.js");
    await OAM.init(global.config.oam);
    OAM.emit("clearAlert", "apigw_configserver_conn");
    await KPI.init(Object.assign({ api_gw_kpi_enabled: true }, global.config.kpi, { policy: "influx" }));
    whiteboard.init(global.config.whiteboard);

    if (global.args.pluginScan === 1) {
      await require("pluginmanager").init();
    }
    redis_man.init({
      key: "login_sessionstore",
      config: global.config.api_gw.sessionCache,
      oid: "apigw_login_sessionstore_conn"
    });
    await require("app_store").init();
    await require("./apps_proxy/loadDefaultAppTemplates").init();
    restify();
    global.config.plugin = global.config.mockserver;
    OAM.emit("clearAlert", "apigw");
  })
  .catch(error => {
    OAM.emit("warningAlert", "apigw");
    console.error("Error while starting LEAP gateway. ", error);
    process.exit(1);
  });

function createServer(app) {
  global.logger.info("Preparing APIGW server...");
  return http.createServer(app);
}

function createSecureServer(app) {
  global.logger.info("Preparing secure APIGW server...");
  return https.createServer({
    key: fs.readFileSync(path.resolve(global.config.certificates.key), "utf8"),
    cert: fs.readFileSync(path.resolve(global.config.certificates.cert), "utf8"),
    secureOptions: require('constants').SSL_OP_NO_SSLv2 | require('constants').SSL_OP_NO_SSLv3 | require('constants').SSL_OP_NO_TLSv1 | require('constants').SSL_OP_NO_TLSv1_1 | require('constants').SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION,
    ciphers: "ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256",
    honorCipherOrder: true
  }, app);
}

function restify() {
  const leapGWContextPath = global.config.api_gw.contextPath || "/leap_gw";
  const appsRoutes = require("./apps_proxy/apps_routes");
  const utilsRoutes = require("./utils_proxy/utils_routes");
  const appApiRoutes = require("./apps_proxy/apps_api_routes");
  const dashboardRoutes = require("./apps_proxy/dashboard_routes");
  const pmRoutes = require("./plugins_proxy/pm_routes");
  const pluginsRoutes = require("./plugins_proxy/plugins_routes");
  const palettesRoutes = require("./plugins_proxy/palette_routes");
  const configApiRoutes = require("./config_apis/config_routes");
  const workFlowRoutes = require("./workflow_apis/workflow_routes");
  const notificationRoutes = require("./notification_proxy/notification_routes");
  const reportRoutes = require("./reports_proxy/reports_routes");
  const engineRoutes = require("./reports_proxy/engine_routes");
  //const help_routes = require("./helpPage/help_routes")

  const limiter = ThrottleLimiter({
    windowMs: 1000,
    max: global.config.api_gw.ratelimit || 100,
    skip: (req, res) => {
      if (req.method != 'POST')
        return true;
    }
  });

  // app.use(limiter);
  app.use(utility.setTxnId);
  app.use(utility.filterHttpMethods);
  app.use(utility.isIPWhiteListed);
  app.use(utility.set_cors_headers);
  app.enable("trust proxy");
  app.use(logger.morgan);
  app.set("etag", false); // turning off etag
  app.use(bodyParser.json({ limit: "10mb" }));
  app.use(bodyParserError());;
  app.use(leapGWContextPath + "/apps", appsRoutes);
  app.use(leapGWContextPath + "/utils", utilsRoutes);
  app.use(leapGWContextPath + "/api", appApiRoutes);
  app.use(leapGWContextPath + "/dashboard", dashboardRoutes);
  app.use(leapGWContextPath + "/palette", palettesRoutes);
  app.use(leapGWContextPath + "/pm", pmRoutes);
  app.use(leapGWContextPath + "/plugins", pluginsRoutes);
  app.use(leapGWContextPath + "/configapi", configApiRoutes);
  app.use(leapGWContextPath + "/workflow", workFlowRoutes);
  app.use(leapGWContextPath + "/notification", notificationRoutes);
  app.use(leapGWContextPath + "/reports", reportRoutes);
  app.use(leapGWContextPath + "/engine", engineRoutes);
  app.use(leapGWContextPath + "/docs", express.static(path.join(__dirname, '../resources/apidoc')));
  //app.use(leapGWContextPath+"/helpPage",help_routes);

  app.use((req, res) => {
    res.status(HTTPCODES.resourceNotFound.code).json(HTTPCODES.resourceNotFound);
  });
  app.use((err, req, res) => {
    res.status(err.statusCode || HTTPCODES.internalServerError.code);
    res.render("error", {
      message: err.message,
      error: (app.get("env") === "development") ? err : {}
    });
  });

  app.use((err, req, res) => {
    global.logger.error(err);
    return res.status(err.status || HTTPCODES.internalServerError.code)
      .json("error", HTTPCODES.internalServerError);
  });

  message.init({
    autoReload: true,
    defaultLocale: "en",
    defaultKey: "E9000"
  });
  let port = global.config.api_gw.port || 9001;
  let protocol = global.config.api_gw.protocol;
  let server = (protocol == null || protocol != "https") ? createServer(app) : createSecureServer(app);

  server.listen(port, async () => {
    global.clusterId = (cluster.worker) ? cluster.worker.id : 1;
    let identifier = "api" + (process.env.NODE_NAME || os.hostname() || "dev");
    global.nodeid = identifier + "_" + port + "_" + global.clusterId;
    global.mode = "api_gw";
    console.warn("Node identifier: " + global.nodeid);
    console.warn("APIGW Service running at port: " + port);
    await require("./apps_proxy/websocket").init(server);
  });
}

process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);

process.on("unhandledRejection", error => {
  // Will print "unhandledRejection err is not defined"
  logger.error(error);
});

process.on("uncaughtException", (err) => {
  logger.error(err);
});

process.on("unhandledRejection", (reason, p) => {
  logger.error(reason, p);
});

function shutdown() {
  console.log("Received kill signal. Initiating shutdown...");
  OAM.raiseCriticalAlert("apigw")
    .then(() => {
      process.exit(1);
    }).catch(err => {
      console.error("Failed to Critical alert for API gateway process", err);
      process.exit(1);
    });
}

function bodyParserError() {
  return function (error, req, res, next) {
    if (error) {
      if (error instanceof SyntaxError) {
        res.status(HTTPCODES.badRequest.code).json(message.getResponseJson(req.query.locale, error_codes.invalidJSONBody));
        return;
      } else {
        res.status(HTTPCODES.badRequest.code).json(message.getResponseJson(req.query.locale, error_codes.apigwGenericError));
        return;
      }
    }
    next();
  };
}

function checkTelnetConnection(host, port) {
  return new Promise((resolve, reject) => {
    const socket = new net.Socket();
    socket.setTimeout(5000); // Set timeout to 5 seconds

    socket.once('connect', () => {
      console.log(`Connection successful to ${host}:${port}`);
      socket.destroy();
      resolve();
    });

    socket.once('error', (err) => {
      console.error(`Connection failed to ${host}:${port} - ${err.message}`);
      socket.destroy();
      reject(err);
    });

    socket.once('timeout', () => {
      console.error(`Connection timeout to ${host}:${port}`);
      socket.destroy();
      reject(new Error('Connection timeout'));
    });

    socket.connect(port, host);
  });
}
