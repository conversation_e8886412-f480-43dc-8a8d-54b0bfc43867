const WebSocket = require("ws");

module.exports = {
  init: (server) => {
    try {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Initializing Websocket");
      }

      // Declare wss locally or use global.wss
      const wss = new WebSocket.Server({ server });
      global.wss = wss; // Keep it in global scope if needed elsewhere

      wss.on("connection", wsClient => {
        if (global.logger.isTraceEnabled()) { global.logger.trace("Obtained Websocket client " + wsClient); }

        wsClient.on("open", (incomingData) => {
          if (global.logger.isTraceEnabled()) { global.logger.trace("WS-EVENT-OPEN: " + incomingData); }
        });

        wsClient.on("close", incomingData => {
          if (global.logger.isTraceEnabled()) { global.logger.trace("WS-EVENT-CLOSE: " + incomingData); }
        });

        wsClient.on("error", (error) => {
          if (global.logger.isTraceEnabled()) { global.logger.trace("WS-EVENT-ERROR: " + error); }
        });

        wsClient.on("message", incomingData => {
          if (global.logger.isTraceEnabled()) { global.logger.trace("WS-EVENT-INPUT: " + JSON.stringify(incomingData)); }
          requestProcessor(wsClient, incomingData);
        });
      });
    } catch (e) {
      global.logger.error("Failed to init WS", e);
    }
    return Promise.resolve();
  }
};

function requestProcessor(wsClient, request) {
  try {
    request = JSON.parse(request);

    switch (request.method) {
      case "lock": {
        // Handle lock request
      } break;
      case "unlock": {
        // Handle unlock request
      } break;
      case "status": {
        // Handle status request
      } break;
    }
  } catch (error) {
    global.logger.error(error);
  }
}
