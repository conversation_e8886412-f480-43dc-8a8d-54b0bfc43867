
//const ESClient = require("./reports_proxy/elastiClient");

let axios = require('axios');
const https = require('https');

function loggingMiddleware(req, res, next) {

  const startTime = Date.now();

  const originalEnd = res.end;
  res.end = function (chunk, encoding) {
    res.end = originalEnd;
    res.end(chunk, encoding);

    const endTime = Date.now();

    let operationJson;

    try {
      operationJson = getOperation(req);
    }
    catch (e) {
      operationJson = {
        "action": "null",
        "operation": "null"
      }
    }

    const logData = {
      "timestamp": Date.now(),
      "userName": req.invoker.given_name,
      "ipAddress": req.ip,
      "action": operationJson.action,
      "operation": operationJson.operation,
      "module": "Studio",
      "role": req.invoker.realm_access.roles[0],
      "accountId": req.invoker.groupId[0],
      "userId": req.invoker.sub,
      "enterpriseName": "sample",
      "amount": "null"
    }

    let token = req.headers["authorization"];

    sendLogData(logData, token).catch(error => {
      console.error('Error sending log data:', error);
    });
  };

  next();
}

function getOperation(req) {
  let mapping = {
    "GET": {
      "/": ['Apps retrieved', 'read'],
      "/:appId": ['App retrieved ' + req.params.appId, 'read']
    },
    "POST": {
      "/": ['App created ' + req.body.name, 'create'],
      "/:appId/update": ['App updated ' + req.params.appId, 'update'],
      "/:appId/delete": ['App deleted ' + req.params.appId, 'delete']
    }
  }
  return {
    "action": mapping[req.method][req.route.path][0],
    "operation": mapping[req.method][req.route.path][1]
  }
}

async function sendLogData(logData, token) {
  try {
    const response = await axios.post(global.config.pm_fsync.pluginData.ngageBaseUrl + '/api/v1/logs/audit', logData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });
  } catch (error) {
    console.error('There was an issue sending the log data:', error);
    throw error;
  }
}

module.exports = loggingMiddleware;