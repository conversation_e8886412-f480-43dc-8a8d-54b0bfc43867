[{"id": "adbd4040-a271-11ef-b4ae-7f557da0bb13", "name": "Appointment Reminder", "desc": null, "status": "0", "type": "generic", "image": "generic", "owner": 0, "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-15 02:47:40", "updatedAt": "2024-11-14 22:35:08", "appData": {"links": [], "startId": "b789727", "modules": {"b789727": {"settings": {"aparty": "*********", "nodeName": "Start"}, "process": {"cronjob": "5 * * * *", "params": ["MSISDN"], "trigger": "HTTP Trigger"}, "output": {"conditions": {"82a27eabb4": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"987f6ac\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["987f6ac"]}}, "input": {}, "type": "appStart", "typeId": "0.1", "coordinates": {"x": 440, "y": 30, "nodeData": {"title": "Start", "name": "appStart", "id": "b789727", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appStart"}}, "isChoiceLinked": true}, "63f2adf": {"settings": {}, "process": {"success": {"code": [], "message": "", "nodeName": "End"}, "customErrors": [{"code": [], "message": ""}], "defaultError": {"code": "E9000", "message": ""}}, "output": {"conditions": {}}, "input": {}, "type": "appEnd", "typeId": "0.2", "coordinates": {"x": 410, "y": 470, "nodeData": {"title": "End", "name": "appEnd", "id": "63f2adf", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appEnd"}}}, "987f6ac": {"settings": {"nodeName": "Choice"}, "input": {}, "process": {"match_conditions": [{"key": "date.count", "condition": "Less than", "value": "7", "moduleId": "ea53d18", "id": "50ab6d3", "coordinates": {"x": 180, "y": 230}}], "no_match_module_id": "63f2adf"}, "output": {}, "isChoiceLinked": false, "coordinates": {"x": 440, "y": 140, "nodeData": {"title": "Choice", "name": "choice", "id": "987f6ac", "isEditable": true, "canDelete": false, "status": "", "moduleType": "choice"}}, "type": "choice", "typeId": "0.9"}, "ea53d18": {"settings": {}, "input": {}, "process": {"body": "", "senderID": "", "subject": "", "receiverAddress": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "coordinates": {"x": 190, "y": 370, "nodeData": {"title": "Email", "name": "email", "id": "ea53d18", "isEditable": true, "canDelete": false, "status": "", "moduleType": "email"}}, "type": "email", "typeId": "1.2"}}, "errors": {"b789727": [{"parameter": "method", "path": "settings", "severity": "error", "msg": "should have required property 'method'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "samplePayload", "path": "settings", "severity": "error", "msg": "should have required property 'samplePayload'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "settings", "path": "", "severity": "warn", "msg": "should match exactly one schema in oneOf"}], "63f2adf": [{"parameter": "code", "path": "process.success", "severity": "warn", "msg": "should NOT have fewer than 1 items"}, {"parameter": "message", "path": "process.success", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}, {"parameter": "message", "path": "process.defaultError", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}], "987f6ac": {"code": 802, "msg": "Requested plugin is not available"}}, "version": "0.5.9", "id": "adbd4040-a271-11ef-b4ae-7f557da0bb13"}, "ngage_id": "9a7f854e-fbea-4aca-b7c2-243ffe1f255d", "alignment": "vertical", "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" joint-selector=\"svg\" id=\"v-170\" style=\"overflow: hidden;\"><defs joint-selector=\"defs\"><marker id=\"v-170-1198995455\" orient=\"auto\" overflow=\"visible\" markerUnits=\"userSpaceOnUse\"><path id=\"v-210\" stroke=\"#7547DB\" fill=\"#7547DB\" transform=\"rotate(180)\" d=\"M 10 -5 0 0 10 5 z\"/></marker></defs><g joint-selector=\"layers\" class=\"joint-layers\" transform=\"matrix(1,0,0,1,562.5,234)\"><g class=\"joint-back-layer\"/><g class=\"joint-cells-layer joint-viewport\"><g model-id=\"c5e2abc\" data-type=\"standard.Link\" id=\"j_48\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-371\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 465 84 L 465 136\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-370\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 465 84 L 465 136\" marker-end=\"url(#v-170-1198995455)\"/></g><g model-id=\"955bf97\" data-type=\"standard.Link\" id=\"j_49\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-373\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 452 194 L 452 202 C 452 207.33333333333331 448.66666666666663 210 442 210 L 215 210 C 208.33333333333331 210 205 212.66666666666666 205 218 L 205 226\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-372\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 452 194 L 452 202 C 452 207.33333333333331 448.66666666666663 210 442 210 L 215 210 C 208.33333333333331 210 205 212.66666666666666 205 218 L 205 226\" marker-end=\"url(#v-170-1198995455)\"/></g><g model-id=\"219f1a7\" data-type=\"standard.Link\" id=\"j_50\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-375\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 220.5 294 L 221 340 C 220.66666666666666 346.66666666666663 219.66666666666666 350 218 350 L 218 350 C 215.*********99997 350 214.*********99997 352.66666666666663 215 358 L 215 366\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-374\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 220.5 294 L 221 340 C 220.66666666666666 346.66666666666663 219.66666666666666 350 218 350 L 218 350 C 215.*********99997 350 214.*********99997 352.66666666666663 215 358 L 215 366\" marker-end=\"url(#v-170-1198995455)\"/></g><g model-id=\"a19ff36\" data-type=\"standard.Link\" id=\"j_51\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-377\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 480 194 L 480 340 C 480 346.66666666666663 483.3333333333333 350 490 350 L 497 350 C 503.66666666666663 350 507 353.3333333333333 507 360 L 507 440 C 507 446.66666666666663 503.66666666666663 450 497 450 L 445 450 C 438.3333333333333 450 435 452.66666666666663 435 458 L 435 466\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-376\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 480 194 L 480 340 C 480 346.66666666666663 483.3333333333333 350 490 350 L 497 350 C 503.66666666666663 350 507 353.3333333333333 507 360 L 507 440 C 507 446.66666666666663 503.66666666666663 450 497 450 L 445 450 C 438.3333333333333 450 435 452.66666666666663 435 458 L 435 466\" marker-end=\"url(#v-170-1198995455)\"/></g><g model-id=\"59d4e59\" data-type=\"standard.Link\" id=\"j_52\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-379\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 215 424 L 215 440 C 214.*********99997 446.66666666666663 218.33333333333331 450 225 450 L 425 450 C 431.66666666666663 450 435 452.66666666666663 435 458 L 435 466\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-378\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 215 424 L 215 440 C 214.*********99997 446.66666666666663 218.33333333333331 450 225 450 L 425 450 C 431.66666666666663 450 435 452.66666666666663 435 458 L 435 466\" marker-end=\"url(#v-170-1198995455)\"/></g><!--z-index:1--><g model-id=\"b789727\" data-type=\"appStart\" id=\"j_53\" class=\"joint-cell joint-type-appstart joint-element joint-theme-default\" transform=\"translate(440,30)\"><rect joint-selector=\"rect\" id=\"v-342\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-343\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXBsYXkiPjxwb2x5Z29uIHBvaW50cz0iNSAzIDE5IDEyIDUgMjEgNSAzIj48L3BvbHlnb24+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-344\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Start</tspan></text><g id=\"v-345\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"34bda1b\" port-group=\"out\" class=\"joint-port-body\" id=\"v-350\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-349\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-346\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"648284f\" port-group=\"in\" class=\"joint-port-body\" id=\"v-348\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-347\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><g model-id=\"50ab6d3\" data-type=\"choiceOption\" id=\"j_54\" class=\"joint-cell joint-type-choiceoption joint-element joint-theme-default\" transform=\"translate(180,230)\"><rect joint-selector=\"rect\" id=\"v-352\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" width=\"81\" height=\"60\"/><text joint-selector=\"text\" id=\"v-351\" font-size=\"10\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" y=\"15\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Less than</tspan></text><rect joint-selector=\"rect1\" id=\"v-353\" width=\"63\" height=\"20\" fill=\"#F2EEEE\" stroke=\"#F2EEEE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" y=\"30\" x=\"5.670000000000001\"/><text joint-selector=\"text1\" id=\"v-354\" font-size=\"9\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" font-weight=\"bold\" y=\"40\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">7</tspan></text><g id=\"v-355\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40.5,60)\"><circle joint-selector=\"portBody\" port=\"0d1557f\" port-group=\"out\" class=\"joint-port-body\" id=\"v-360\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-359\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-356\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"dd9a0ea\" port-group=\"in\" class=\"joint-port-body\" id=\"v-358\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-357\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:2--><g model-id=\"63f2adf\" data-type=\"appEnd\" id=\"j_55\" class=\"joint-cell joint-type-append joint-element joint-theme-default\" transform=\"translate(410,470)\"><rect joint-selector=\"rect\" id=\"v-336\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-337\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICA8cGF0aCBpZD0iUmVjdGFuZ2xlXzE3NzVfLV9PdXRsaW5lIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSAxNzc1IC0gT3V0bGluZSIgZD0iTTQsMkEyLDIsMCwwLDAsMiw0VjE0YTIsMiwwLDAsMCwyLDJIMTRhMiwyLDAsMCwwLDItMlY0YTIsMiwwLDAsMC0yLTJINE00LDBIMTRhNCw0LDAsMCwxLDQsNFYxNGE0LDQsMCwwLDEtNCw0SDRhNCw0LDAsMCwxLTQtNFY0QTQsNCwwLDAsMSw0LDBaIiBmaWxsPSIjNzU0N2RiIi8+Cjwvc3ZnPgo=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-338\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">End</tspan></text><g id=\"v-339\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"bad2219\" port-group=\"in\" class=\"joint-port-body\" id=\"v-341\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-340\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:3--><g model-id=\"987f6ac\" data-type=\"choice\" id=\"j_56\" class=\"joint-cell joint-type-choice joint-element joint-theme-default\" transform=\"translate(440,140)\"><rect joint-selector=\"rect\" id=\"v-320\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-321\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMC45OSIgaGVpZ2h0PSIxNy43NjEiIHZpZXdCb3g9IjAgMCAyMC45OSAxNy43NjEiPgogIDxnIGlkPSJJY29uX2lvbmljLWlvcy1vcHRpb25zIiBkYXRhLW5hbWU9Ikljb24gaW9uaWMtaW9zLW9wdGlvbnMiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0zLjM3NSAtNS42MjUpIj4KICAgIDxwYXRoIGlkPSJQYXRoXzEzNTkiIGRhdGEtbmFtZT0iUGF0aCAxMzU5IiBkPSJNMTYuMDU1LDI1Ljk2MWEyLjAyMSwyLjAyMSwwLDAsMSwzLjcsMGgzLjhhLjgxLjgxLDAsMCwxLC44MDcuODA3aDBhLjgxLjgxLDAsMCwxLS44MDcuODA3aC0zLjhhMi4wMjEsMi4wMjEsMCwwLDEtMy43LDBINC4xODJhLjgxLjgxLDAsMCwxLS44MDctLjgwN2gwYS44MS44MSwwLDAsMSwuODA3LS44MDdaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwIC01LjQwMSkiIGZpbGw9IiM3NTQ3ZGIiLz4KICAgIDxwYXRoIGlkPSJQYXRoXzEzNjAiIGRhdGEtbmFtZT0iUGF0aCAxMzYwIiBkPSJNNy45ODIsMTYuNGEyLjAyMSwyLjAyMSwwLDAsMSwzLjcsMEgyMy41NThhLjgxLjgxLDAsMCwxLC44MDcuODA3aDBhLjgxLjgxLDAsMCwxLS44MDcuODA3SDExLjY4NWEyLjAyMSwyLjAyMSwwLDAsMS0zLjcsMGgtMy44YS44MS44MSwwLDAsMS0uODA3LS44MDdoMGEuODEuODEsMCwwLDEsLjgwNy0uODA3WiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCAtMi43KSIgZmlsbD0iIzc1NDdkYiIvPgogICAgPHBhdGggaWQ9IlBhdGhfMTM2MSIgZGF0YS1uYW1lPSJQYXRoIDEzNjEiIGQ9Ik0xNi4wNTUsNi44MzZhMi4wMjEsMi4wMjEsMCwwLDEsMy43LDBoMy44YS44MS44MSwwLDAsMSwuODA3LjgwN2gwYS44MS44MSwwLDAsMS0uODA3LjgwN2gtMy44YTIuMDIxLDIuMDIxLDAsMCwxLTMuNywwSDQuMTgyYS44MS44MSwwLDAsMS0uODA3LS44MDdoMGEuODEuODEsMCwwLDEsLjgwNy0uODA3WiIgZmlsbD0iIzc1NDdkYiIvPgogIDwvZz4KPC9zdmc+Cg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-322\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Choice</tspan></text><rect joint-selector=\"rect4\" id=\"v-324\" fill=\"#FFE4EC\" stroke=\"#FFE4EC\" stroke-width=\"1\" x=\"50\" y=\"50\" width=\"80\" height=\"20\" rx=\"6\" ry=\"6\"/><rect joint-selector=\"rect5\" id=\"v-325\" fill=\"#CBFFE7\" stroke=\"#CBFFE7\" stroke-width=\"1\" x=\"-55\" y=\"50\" width=\"60\" height=\"20\" rx=\"6\" ry=\"6\"/><text joint-selector=\"label4\" id=\"v-323\" font-size=\"10\" xml:space=\"preserve\" fill=\"#ED1C24\" text-anchor=\"middle\" pointer-events=\"none\" x=\"90\" y=\"60\"><tspan dy=\"0.3em\" class=\"v-line\">No Match</tspan></text><text joint-selector=\"label5\" id=\"v-326\" font-size=\"10\" xml:space=\"preserve\" fill=\"#075E36\" text-anchor=\"middle\" pointer-events=\"none\" x=\"-25\" y=\"60\"><tspan dy=\"0.3em\" class=\"v-line\">Match</tspan></text><g id=\"v-327\" class=\"joint-port\" transform=\"matrix(1,0,0,1,12,50)\"><circle joint-selector=\"portBody\" port=\"7f20e76\" port-group=\"out\" class=\"joint-port-body\" id=\"v-333\" magnet=\"true\" r=\"4\" fill=\"#31D88A\" stroke=\"#31D88A\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-332\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-328\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40,50)\"><circle joint-selector=\"portBody\" port=\"1c68934\" port-group=\"out\" class=\"joint-port-body\" id=\"v-335\" magnet=\"true\" r=\"4\" fill=\"red\" stroke=\"red\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-334\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-329\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"54967e8\" port-group=\"in\" class=\"joint-port-body\" id=\"v-331\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-330\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:4--><g model-id=\"ea53d18\" data-type=\"email\" id=\"j_57\" class=\"joint-cell joint-type-email joint-element joint-theme-default\" transform=\"translate(190,370)\"><rect joint-selector=\"rect\" id=\"v-361\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-362\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLW1haWwiPjxwYXRoIGQ9Ik00IDRoMTZjMS4xIDAgMiAuOSAyIDJ2MTJjMCAxLjEtLjkgMi0yIDJINGMtMS4xIDAtMi0uOS0yLTJWNmMwLTEuMS45LTIgMi0yeiI+PC9wYXRoPjxwb2x5bGluZSBwb2ludHM9IjIyLDYgMTIsMTMgMiw2Ij48L3BvbHlsaW5lPjwvc3ZnPg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-363\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Email</tspan></text><g id=\"v-364\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"77bdebf\" port-group=\"out\" class=\"joint-port-body\" id=\"v-369\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-368\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-365\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"4269056\" port-group=\"in\" class=\"joint-port-body\" id=\"v-367\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-366\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:5--></g><g class=\"joint-labels-layer joint-viewport\"/><g class=\"joint-front-layer\"/><g class=\"joint-tools-layer\"><g class=\"joint-tools joint-theme-default\"><g class=\"joint-tool joint-theme-default\" data-tool-name=\"button\" model-id=\"63f2adf\" transform=\"matrix(1,0,0,1,455,474)\"><circle r=\"7\" fill=\"#FF1D00\" cursor=\"pointer\" joint-selector=\"button\"/><path d=\"M -3 -3 3 3 M -3 3 3 -3\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" pointer-events=\"none\" joint-selector=\"icon\"/></g></g><g class=\"joint-tools joint-theme-default\"><g class=\"joint-tool joint-theme-default\" data-tool-name=\"button\" model-id=\"59d4e59\" transform=\"matrix(1,0,0,1,252.76937866210938,450)\"><circle r=\"7\" fill=\"#FF1D00\" cursor=\"pointer\" joint-selector=\"button\"/><path d=\"M -3 -3 3 3 M -3 3 3 -3\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" pointer-events=\"none\" joint-selector=\"icon\"/></g></g><!--z-index:1--></g></g></svg>", "OTC": "10", "MRC": "100", "channels": 1, "nodes": 4, "freeNodeExec": 10, "nodeExecCharge": "100", "localeData": [{"id": 1698, "appId": "adbd4040-a271-11ef-b4ae-7f557da0bb13", "locale": "en", "translation": "eJx1jjEOwyAMRe/yZwbWZmftBaIqQkAjpIArDF0i7l7DEmXoZL3n/2Wf4OZcYMZywpEPWNaXQhJjdwFAIYt+2jTIZI+u4BpXSqYUKlJc/zS7oA9v2446o9cJmIfWGvf0oNy2GusRxjsiOPGNZyDbb9xtjZQ3+owxl/0HYThEog==", "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-14 15:47:40", "updatedAt": "2024-11-14 17:05:08", "deletedAt": null}]}, {"id": "43565430-a271-11ef-b4ae-7f557da0bb13", "name": "Event Registration", "desc": null, "type": "generic", "image": "generic", "status": "0", "owner": 0, "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-15 02:44:41", "updatedAt": "2024-11-14 21:17:05", "appData": {"links": [], "startId": "b789727", "modules": {"b789727": {"settings": {"aparty": "*********", "nodeName": "Start"}, "process": {"cronjob": "5 * * * *", "params": ["MSISDN"], "trigger": "HTTP Trigger"}, "output": {"conditions": {"82a27eabb4": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"4c71020\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["4c71020"]}}, "input": {}, "type": "appStart", "typeId": "0.1", "coordinates": {"x": 750, "y": 80, "nodeData": {"title": "Start", "name": "appStart", "id": "b789727", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appStart"}}}, "63f2adf": {"settings": {}, "process": {"success": {"code": [], "message": "", "nodeName": "End"}, "customErrors": [{"code": [], "message": ""}], "defaultError": {"code": "E9000", "message": ""}}, "output": {"conditions": {}}, "input": {}, "type": "appEnd", "typeId": "0.2", "coordinates": {"x": 400, "y": 843.7551020408164, "nodeData": {"title": "End", "name": "appEnd", "id": "63f2adf", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appEnd"}}}, "4c71020": {"settings": {"timeout": 10000, "title": "HTTP", "nodeName": "Get Customer Details ", "image": ""}, "input": {}, "process": {"URL": "", "requestType": "", "headers": [{"headerKey": "", "headerValue": ""}], "requestBody": "", "responseCache": "", "callReference": "", "responseType": "", "Value": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"870dd14\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["870dd14"]}}, "coordinates": {"x": 750, "y": 230.9387755102041, "nodeData": {"title": "HTTP", "name": "http", "id": "4c71020", "isEditable": true, "canDelete": false, "status": "", "moduleType": "http"}}, "type": "http", "typeId": "1.2", "isChoiceLinked": true}, "870dd14": {"settings": {"nodeName": "Check Channel"}, "input": {}, "process": {"match_conditions": [{"key": "channel", "condition": "Equals To", "value": "SMS", "moduleId": "b70af6f", "id": "1e763b9", "coordinates": {"x": 509.5, "y": 532.*************}}, {"key": "channel", "condition": "Equals To", "value": "Email", "moduleId": "237d662", "id": "2bbaf08", "coordinates": {"x": 859.5, "y": 532.*************}}], "no_match_module_id": "63f2adf"}, "output": {}, "isChoiceLinked": false, "coordinates": {"x": 750, "y": 381.*************, "nodeData": {"title": "Choice", "name": "choice", "id": "870dd14", "isEditable": true, "canDelete": false, "status": "", "moduleType": "choice"}}, "type": "choice", "typeId": "0.9"}, "b70af6f": {"settings": {"account": "", "nodeName": "Send SMS"}, "input": {}, "process": {"receiverAddress": "", "text_message": "", "senderAddress": "", "moduleName": "SMS"}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "coordinates": {"x": 400, "y": 692.*************, "nodeData": {"title": "SMS", "name": "sms", "id": "b70af6f", "isEditable": true, "canDelete": false, "status": "", "moduleType": "sms"}}, "type": "sms", "typeId": "1.1"}, "237d662": {"settings": {"nodeName": "Send Email"}, "input": {}, "process": {"body": "", "senderID": "", "subject": "", "receiverAddress": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "coordinates": {"x": 750, "y": 692.*************, "nodeData": {"title": "Email", "name": "email", "id": "237d662", "isEditable": true, "canDelete": false, "status": "", "moduleType": "email"}}, "type": "email", "typeId": "1.2"}}, "errors": {"b789727": [{"parameter": "method", "path": "settings", "severity": "error", "msg": "should have required property 'method'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "samplePayload", "path": "settings", "severity": "error", "msg": "should have required property 'samplePayload'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "settings", "path": "", "severity": "warn", "msg": "should match exactly one schema in oneOf"}], "63f2adf": [{"parameter": "code", "path": "process.success", "severity": "warn", "msg": "should NOT have fewer than 1 items"}, {"parameter": "message", "path": "process.success", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}, {"parameter": "message", "path": "process.defaultError", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}]}, "version": "0.10.35", "id": "43565430-a271-11ef-b4ae-7f557da0bb13"}, "ngage_id": "9a7f854e-fbea-4aca-b7c2-243ffe1f255d", "alignment": "vertical", "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" joint-selector=\"svg\" id=\"v-1030\" style=\"overflow: hidden;\"><defs joint-selector=\"defs\"><marker id=\"v-1030-1198995455\" orient=\"auto\" overflow=\"visible\" markerUnits=\"userSpaceOnUse\"><path id=\"v-1082\" stroke=\"#7547DB\" fill=\"#7547DB\" transform=\"rotate(180)\" d=\"M 10 -5 0 0 10 5 z\"/></marker></defs><g joint-selector=\"layers\" class=\"joint-layers\" transform=\"matrix(1,0,0,1,562.5,234)\"><g class=\"joint-back-layer\"/><g class=\"joint-cells-layer joint-viewport\"><g model-id=\"af5fd1b\" data-type=\"standard.Link\" id=\"j_235\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1487\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 775 134 L 775 226.94\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1486\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 775 134 L 775 226.94\" marker-end=\"url(#v-1030-1198995455)\"/></g><g model-id=\"18c5ae1\" data-type=\"standard.Link\" id=\"j_236\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1489\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 775 284.94 L 775 377.88\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1488\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 775 284.94 L 775 377.88\" marker-end=\"url(#v-1030-1198995455)\"/></g><g model-id=\"379d503\" data-type=\"standard.Link\" id=\"j_237\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1491\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 762 435.88 L 762 503 C 762 509.4 758.6666666666666 512.7333333333333 752 513 L 545 513 C 538 512.7333333333333 534.6666666666666 515.4 535 521 L 534.5 528.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1490\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 762 435.88 L 762 503 C 762 509.4 758.6666666666666 512.7333333333333 752 513 L 545 513 C 538 512.7333333333333 534.6666666666666 515.4 535 521 L 534.5 528.82\" marker-end=\"url(#v-1030-1198995455)\"/></g><g model-id=\"2e2169f\" data-type=\"standard.Link\" id=\"j_238\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1493\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 550 596.82 L 550 663 C 550 669.5333333333333 546.6666666666666 672.8666666666666 540 673 L 435 673 C 428.3333333333333 672.8666666666666 425 675.5333333333333 425 681 L 425 688.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1492\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 550 596.82 L 550 663 C 550 669.5333333333333 546.6666666666666 672.8666666666666 540 673 L 435 673 C 428.3333333333333 672.8666666666666 425 675.5333333333333 425 681 L 425 688.82\" marker-end=\"url(#v-1030-1198995455)\"/></g><g model-id=\"4e5bdf1\" data-type=\"standard.Link\" id=\"j_239\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1495\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 762 435.88 L 762 503 C 762 509.4 765.3333333333333 512.7333333333333 772 513 L 875 513 C 881.3333333333333 512.7333333333333 884.6666666666666 515.4 885 521 L 884.5 528.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1494\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 762 435.88 L 762 503 C 762 509.4 765.3333333333333 512.7333333333333 772 513 L 875 513 C 881.3333333333333 512.7333333333333 884.6666666666666 515.4 885 521 L 884.5 528.82\" marker-end=\"url(#v-1030-1198995455)\"/></g><g model-id=\"f419eb0\" data-type=\"standard.Link\" id=\"j_240\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1497\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 900 596.82 L 900 663 C 900 669.5333333333333 896.6666666666666 672.8666666666666 890 673 L 785 673 C 778.3333333333333 672.8666666666666 775 675.5333333333333 775 681 L 775 688.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1496\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 900 596.82 L 900 663 C 900 669.5333333333333 896.6666666666666 672.8666666666666 890 673 L 785 673 C 778.3333333333333 672.8666666666666 775 675.5333333333333 775 681 L 775 688.82\" marker-end=\"url(#v-1030-1198995455)\"/></g><g model-id=\"9a677c9\" data-type=\"standard.Link\" id=\"j_241\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1499\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 790 435.88 L 790 452 C 790 458.5850340136054 793.3333333333333 461.9183673469388 800 462 L 890 462 C 896.6666666666666 461.9183673469388 900 458.5850340136054 900 452 L 900 439 C 900 432.0043868014495 903.3333333333333 428.6710534681162 910 429 L 940 429 C 946.6666666666665 428.6710534681162 949.*********9999 432.0043868014495 950 439 L 950 634 C 949.*********9999 640.5034013605442 949.*********9999 647.1700680272108 950 654 L 950 784 C 949.*********9999 790.5034013605441 946.6666666666665 793.8367346938775 940 794 L 475 794 C 468.3333333333333 793.8367346938775 461.66666666666663 793.8367346938775 455 794 L 435 794 C 428.3333333333333 793.8367346938775 425 797.1700680272108 425 804 L 425 839.76\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1498\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 790 435.88 L 790 452 C 790 458.5850340136054 793.3333333333333 461.9183673469388 800 462 L 890 462 C 896.6666666666666 461.9183673469388 900 458.5850340136054 900 452 L 900 439 C 900 432.0043868014495 903.3333333333333 428.6710534681162 910 429 L 940 429 C 946.6666666666665 428.6710534681162 949.*********9999 432.0043868014495 950 439 L 950 634 C 949.*********9999 640.5034013605442 949.*********9999 647.1700680272108 950 654 L 950 784 C 949.*********9999 790.5034013605441 946.6666666666665 793.8367346938775 940 794 L 475 794 C 468.3333333333333 793.8367346938775 461.66666666666663 793.8367346938775 455 794 L 435 794 C 428.3333333333333 793.8367346938775 425 797.1700680272108 425 804 L 425 839.76\" marker-end=\"url(#v-1030-1198995455)\"/></g><g model-id=\"ce1b04e5-d922-487d-a5e8-bd8c29c1de3f\" data-type=\"standard.Link\" id=\"j_250\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1501\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 425 746.82 L 425 839.76\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1500\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 425 746.82 L 425 839.76\" marker-end=\"url(#v-1030-1198995455)\"/></g><g model-id=\"27197f50-79c1-4392-8a6c-8b04cdfdbd8e\" data-type=\"standard.Link\" id=\"j_251\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1503\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 775 746.82 L 775 814 C 775 820.3*********999 771.6666666666666 823.7333333333332 765 824 L 435 824 C 428.3333333333333 823.7333333333332 425 826.3*********999 425 832 L 425 839.76\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1502\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 775 746.82 L 775 814 C 775 820.3*********999 771.6666666666666 823.7333333333332 765 824 L 435 824 C 428.3333333333333 823.7333333333332 425 826.3*********999 425 832 L 425 839.76\" marker-end=\"url(#v-1030-1198995455)\"/></g><!--z-index:1--><g model-id=\"b789727\" data-type=\"appStart\" id=\"j_242\" class=\"joint-cell joint-type-appstart joint-element joint-theme-default\" transform=\"translate(750,80)\"><rect joint-selector=\"rect\" id=\"v-1430\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1431\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXBsYXkiPjxwb2x5Z29uIHBvaW50cz0iNSAzIDE5IDEyIDUgMjEgNSAzIj48L3BvbHlnb24+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1432\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Start</tspan></text><g id=\"v-1433\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"1e157fa\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1438\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1437\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1434\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"36a288f\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1436\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1435\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><g model-id=\"1e763b9\" data-type=\"choiceOption\" id=\"j_243\" class=\"joint-cell joint-type-choiceoption joint-element joint-theme-default\" transform=\"translate(509.5,532.*************)\"><rect joint-selector=\"rect\" id=\"v-1440\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" width=\"81\" height=\"60\"/><text joint-selector=\"text\" id=\"v-1439\" font-size=\"10\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" y=\"15\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Equals To</tspan></text><rect joint-selector=\"rect1\" id=\"v-1441\" width=\"63\" height=\"20\" fill=\"#F2EEEE\" stroke=\"#F2EEEE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" y=\"30\" x=\"5.670000000000001\"/><text joint-selector=\"text1\" id=\"v-1442\" font-size=\"9\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" font-weight=\"bold\" y=\"40\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">SMS</tspan></text><g id=\"v-1443\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40.5,60)\"><circle joint-selector=\"portBody\" port=\"c94c58e\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1448\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1447\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1444\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"cc6d281\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1446\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1445\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:2--><g model-id=\"63f2adf\" data-type=\"appEnd\" id=\"j_244\" class=\"joint-cell joint-type-append joint-element joint-theme-default\" transform=\"translate(400,843.7551020408164)\"><rect joint-selector=\"rect\" id=\"v-1424\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1425\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICA8cGF0aCBpZD0iUmVjdGFuZ2xlXzE3NzVfLV9PdXRsaW5lIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSAxNzc1IC0gT3V0bGluZSIgZD0iTTQsMkEyLDIsMCwwLDAsMiw0VjE0YTIsMiwwLDAsMCwyLDJIMTRhMiwyLDAsMCwwLDItMlY0YTIsMiwwLDAsMC0yLTJINE00LDBIMTRhNCw0LDAsMCwxLDQsNFYxNGE0LDQsMCwwLDEtNCw0SDRhNCw0LDAsMCwxLTQtNFY0QTQsNCwwLDAsMSw0LDBaIiBmaWxsPSIjNzU0N2RiIi8+Cjwvc3ZnPgo=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1426\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">End</tspan></text><g id=\"v-1427\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"db58d24\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1429\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1428\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><g model-id=\"2bbaf08\" data-type=\"choiceOption\" id=\"j_245\" class=\"joint-cell joint-type-choiceoption joint-element joint-theme-default\" transform=\"translate(859.5,532.*************)\"><rect joint-selector=\"rect\" id=\"v-1450\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" width=\"81\" height=\"60\"/><text joint-selector=\"text\" id=\"v-1449\" font-size=\"10\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" y=\"15\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Equals To</tspan></text><rect joint-selector=\"rect1\" id=\"v-1451\" width=\"63\" height=\"20\" fill=\"#F2EEEE\" stroke=\"#F2EEEE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" y=\"30\" x=\"5.670000000000001\"/><text joint-selector=\"text1\" id=\"v-1452\" font-size=\"9\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" font-weight=\"bold\" y=\"40\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Email</tspan></text><g id=\"v-1453\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40.5,60)\"><circle joint-selector=\"portBody\" port=\"1ed5077\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1458\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1457\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1454\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"1d6d964\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1456\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1455\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:3--><g model-id=\"4c71020\" data-type=\"http\" id=\"j_246\" class=\"joint-cell joint-type-http joint-element joint-theme-default\" transform=\"translate(750,230.9387755102041)\"><rect joint-selector=\"rect\" id=\"v-1459\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1460\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWdsb2JlIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjIiIHkxPSIxMiIgeDI9IjIyIiB5Mj0iMTIiPjwvbGluZT48cGF0aCBkPSJNMTIgMmExNS4zIDE1LjMgMCAwIDEgNCAxMCAxNS4zIDE1LjMgMCAwIDEtNCAxMCAxNS4zIDE1LjMgMCAwIDEtNC0xMCAxNS4zIDE1LjMgMCAwIDEgNC0xMHoiPjwvcGF0aD48L3N2Zz4=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1461\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Get Customer Details </tspan></text><g id=\"v-1462\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"784195d\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1467\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1466\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1463\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"f9dd294\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1465\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1464\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:4--><g model-id=\"870dd14\" data-type=\"choice\" id=\"j_247\" class=\"joint-cell joint-type-choice joint-element joint-theme-default\" transform=\"translate(750,381.*************)\"><rect joint-selector=\"rect\" id=\"v-1408\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1409\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMC45OSIgaGVpZ2h0PSIxNy43NjEiIHZpZXdCb3g9IjAgMCAyMC45OSAxNy43NjEiPgogIDxnIGlkPSJJY29uX2lvbmljLWlvcy1vcHRpb25zIiBkYXRhLW5hbWU9Ikljb24gaW9uaWMtaW9zLW9wdGlvbnMiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0zLjM3NSAtNS42MjUpIj4KICAgIDxwYXRoIGlkPSJQYXRoXzEzNTkiIGRhdGEtbmFtZT0iUGF0aCAxMzU5IiBkPSJNMTYuMDU1LDI1Ljk2MWEyLjAyMSwyLjAyMSwwLDAsMSwzLjcsMGgzLjhhLjgxLjgxLDAsMCwxLC44MDcuODA3aDBhLjgxLjgxLDAsMCwxLS44MDcuODA3aC0zLjhhMi4wMjEsMi4wMjEsMCwwLDEtMy43LDBINC4xODJhLjgxLjgxLDAsMCwxLS44MDctLjgwN2gwYS44MS44MSwwLDAsMSwuODA3LS44MDdaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwIC01LjQwMSkiIGZpbGw9IiM3NTQ3ZGIiLz4KICAgIDxwYXRoIGlkPSJQYXRoXzEzNjAiIGRhdGEtbmFtZT0iUGF0aCAxMzYwIiBkPSJNNy45ODIsMTYuNGEyLjAyMSwyLjAyMSwwLDAsMSwzLjcsMEgyMy41NThhLjgxLjgxLDAsMCwxLC44MDcuODA3aDBhLjgxLjgxLDAsMCwxLS44MDcuODA3SDExLjY4NWEyLjAyMSwyLjAyMSwwLDAsMS0zLjcsMGgtMy44YS44MS44MSwwLDAsMS0uODA3LS44MDdoMGEuODEuODEsMCwwLDEsLjgwNy0uODA3WiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCAtMi43KSIgZmlsbD0iIzc1NDdkYiIvPgogICAgPHBhdGggaWQ9IlBhdGhfMTM2MSIgZGF0YS1uYW1lPSJQYXRoIDEzNjEiIGQ9Ik0xNi4wNTUsNi44MzZhMi4wMjEsMi4wMjEsMCwwLDEsMy43LDBoMy44YS44MS44MSwwLDAsMSwuODA3LjgwN2gwYS44MS44MSwwLDAsMS0uODA3LjgwN2gtMy44YTIuMDIxLDIuMDIxLDAsMCwxLTMuNywwSDQuMTgyYS44MS44MSwwLDAsMS0uODA3LS44MDdoMGEuODEuODEsMCwwLDEsLjgwNy0uODA3WiIgZmlsbD0iIzc1NDdkYiIvPgogIDwvZz4KPC9zdmc+Cg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1410\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Check Channel</tspan></text><rect joint-selector=\"rect4\" id=\"v-1412\" fill=\"#FFE4EC\" stroke=\"#FFE4EC\" stroke-width=\"1\" x=\"50\" y=\"50\" width=\"80\" height=\"20\" rx=\"6\" ry=\"6\"/><rect joint-selector=\"rect5\" id=\"v-1413\" fill=\"#CBFFE7\" stroke=\"#CBFFE7\" stroke-width=\"1\" x=\"-55\" y=\"50\" width=\"60\" height=\"20\" rx=\"6\" ry=\"6\"/><text joint-selector=\"label4\" id=\"v-1411\" font-size=\"10\" xml:space=\"preserve\" fill=\"#ED1C24\" text-anchor=\"middle\" pointer-events=\"none\" x=\"90\" y=\"60\"><tspan dy=\"0.3em\" class=\"v-line\">No Match</tspan></text><text joint-selector=\"label5\" id=\"v-1414\" font-size=\"10\" xml:space=\"preserve\" fill=\"#075E36\" text-anchor=\"middle\" pointer-events=\"none\" x=\"-25\" y=\"60\"><tspan dy=\"0.3em\" class=\"v-line\">Match</tspan></text><g id=\"v-1415\" class=\"joint-port\" transform=\"matrix(1,0,0,1,12,50)\"><circle joint-selector=\"portBody\" port=\"d1d16dc\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1421\" magnet=\"true\" r=\"4\" fill=\"#31D88A\" stroke=\"#31D88A\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1420\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1416\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40,50)\"><circle joint-selector=\"portBody\" port=\"500fbed\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1423\" magnet=\"true\" r=\"4\" fill=\"red\" stroke=\"red\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1422\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1417\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"f2c3f1b\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1419\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1418\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:5--><g model-id=\"b70af6f\" data-type=\"sms\" id=\"j_248\" class=\"joint-cell joint-type-sms joint-element joint-theme-default\" transform=\"translate(400,692.*************)\"><rect joint-selector=\"rect\" id=\"v-1468\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1469\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLW1lc3NhZ2Utc3F1YXJlIj48cGF0aCBkPSJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6Ij48L3BhdGg+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1470\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Send SMS</tspan></text><g id=\"v-1471\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"96ce938\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1476\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1475\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1472\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"08ad107\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1474\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1473\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:6--><g model-id=\"237d662\" data-type=\"email\" id=\"j_249\" class=\"joint-cell joint-type-email joint-element joint-theme-default\" transform=\"translate(750,692.*************)\"><rect joint-selector=\"rect\" id=\"v-1477\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1478\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLW1haWwiPjxwYXRoIGQ9Ik00IDRoMTZjMS4xIDAgMiAuOSAyIDJ2MTJjMCAxLjEtLjkgMi0yIDJINGMtMS4xIDAtMi0uOS0yLTJWNmMwLTEuMS45LTIgMi0yeiI+PC9wYXRoPjxwb2x5bGluZSBwb2ludHM9IjIyLDYgMTIsMTMgMiw2Ij48L3BvbHlsaW5lPjwvc3ZnPg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1479\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Send Email</tspan></text><g id=\"v-1480\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"a09a925\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1485\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1484\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1481\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"fdbc172\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1483\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1482\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:7--><!--z-index:8--></g><g class=\"joint-labels-layer joint-viewport\"/><g class=\"joint-front-layer\"/><g class=\"joint-tools-layer\"><!--z-index:1--></g></g></svg>", "OTC": "10", "MRC": "100", "channels": 2, "nodes": 6, "freeNodeExec": 10, "nodeExecCharge": "100", "localeData": [{"id": 1697, "appId": "43565430-a271-11ef-b4ae-7f557da0bb13", "locale": "en", "translation": "eJx1jjEOwyAMRe/yZwbWZmftBaIqQkAjpIArDF0i7l7DEmXoZL3n/2Wf4OZcYMZywpEPWNaXQhJjdwFAIYt+2jTIZI+u4BpXSqYUKlJc/zS7oA9v2446o9cJmIfWGvf0oNy2GusRxjsiOPGNZyDbb9xtjZQ3+owxl/0HYThEog==", "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-14 15:44:41", "updatedAt": "2024-11-14 15:47:05", "deletedAt": null}]}, {"id": "5cee2f60-a27d-11ef-b4ae-7f557da0bb13", "name": "System Downtime Alerts", "desc": null, "type": "it", "image": "it", "status": "0", "owner": 0, "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-15 04:11:18", "updatedAt": "2024-11-14 23:48:07", "appData": {"links": [], "startId": "b789727", "modules": {"2076693": {"settings": {}, "input": {}, "process": {"body": "", "senderID": "", "subject": "", "receiverAddress": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "coordinates": {"x": 450, "y": 170, "nodeData": {"title": "Email", "name": "email", "id": "2076693", "isEditable": true, "canDelete": false, "status": "", "moduleType": "email"}}, "type": "email", "typeId": "1.2"}, "b789727": {"settings": {"aparty": "*********", "nodeName": "Start"}, "process": {"cronjob": "5 * * * *", "params": ["MSISDN"], "trigger": "HTTP Trigger"}, "output": {"conditions": {"82a27eabb4": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"2076693\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["2076693"]}}, "input": {}, "type": "appStart", "typeId": "0.1", "coordinates": {"x": 450, "y": 30, "nodeData": {"title": "Start", "name": "appStart", "id": "b789727", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appStart"}}}, "63f2adf": {"settings": {}, "process": {"success": {"code": [], "message": "", "nodeName": "End"}, "customErrors": [{"code": [], "message": ""}], "defaultError": {"code": "E9000", "message": ""}}, "output": {"conditions": {}}, "input": {}, "type": "appEnd", "typeId": "0.2", "coordinates": {"x": 450, "y": 330, "nodeData": {"title": "End", "name": "appEnd", "id": "63f2adf", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appEnd"}}}}, "errors": {"b789727": [{"parameter": "method", "path": "settings", "severity": "error", "msg": "should have required property 'method'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "samplePayload", "path": "settings", "severity": "error", "msg": "should have required property 'samplePayload'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "settings", "path": "", "severity": "warn", "msg": "should match exactly one schema in oneOf"}], "63f2adf": [{"parameter": "code", "path": "process.success", "severity": "warn", "msg": "should NOT have fewer than 1 items"}, {"parameter": "message", "path": "process.success", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}, {"parameter": "message", "path": "process.defaultError", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}]}, "version": "0.3.2", "id": "5cee2f60-a27d-11ef-b4ae-7f557da0bb13"}, "ngage_id": "9a7f854e-fbea-4aca-b7c2-243ffe1f255d", "alignment": "vertical", "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" joint-selector=\"svg\" id=\"v-401\" style=\"overflow: hidden;\"><defs joint-selector=\"defs\"><marker id=\"v-401-1198995455\" orient=\"auto\" overflow=\"visible\" markerUnits=\"userSpaceOnUse\"><path id=\"v-453\" stroke=\"#7547DB\" fill=\"#7547DB\" transform=\"rotate(180)\" d=\"M 10 -5 0 0 10 5 z\"/></marker></defs><g joint-selector=\"layers\" class=\"joint-layers\" transform=\"matrix(1,0,0,1,562.5,234)\"><g class=\"joint-back-layer\"/><g class=\"joint-cells-layer joint-viewport\"><g model-id=\"b5cc5c98-ed56-4daf-9105-e214474d5e53\" data-type=\"standard.Link\" id=\"j_67\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-451\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 475 84 L 475 166\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-450\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 475 84 L 475 166\" marker-end=\"url(#v-401-1198995455)\"/></g><g model-id=\"b2069f7e-6cbc-43f6-af37-d6644a022563\" data-type=\"standard.Link\" id=\"j_68\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-455\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 475 224 L 475 326\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-454\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 475 224 L 475 326\" marker-end=\"url(#v-401-1198995455)\"/></g><!--z-index:1--><g model-id=\"2076693\" data-type=\"email\" id=\"j_64\" class=\"joint-cell joint-type-email joint-element joint-theme-default\" transform=\"translate(450,170)\"><rect joint-selector=\"rect\" id=\"v-426\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-427\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLW1haWwiPjxwYXRoIGQ9Ik00IDRoMTZjMS4xIDAgMiAuOSAyIDJ2MTJjMCAxLjEtLjkgMi0yIDJINGMtMS4xIDAtMi0uOS0yLTJWNmMwLTEuMS45LTIgMi0yeiI+PC9wYXRoPjxwb2x5bGluZSBwb2ludHM9IjIyLDYgMTIsMTMgMiw2Ij48L3BvbHlsaW5lPjwvc3ZnPg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-428\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Email</tspan></text><g id=\"v-429\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"2cfbd7b\" port-group=\"out\" class=\"joint-port-body\" id=\"v-434\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-433\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-430\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"d741b1b\" port-group=\"in\" class=\"joint-port-body\" id=\"v-432\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-431\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:2--><g model-id=\"b789727\" data-type=\"appStart\" id=\"j_65\" class=\"joint-cell joint-type-appstart joint-element joint-theme-default\" transform=\"translate(450,30)\"><rect joint-selector=\"rect\" id=\"v-435\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-436\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXBsYXkiPjxwb2x5Z29uIHBvaW50cz0iNSAzIDE5IDEyIDUgMjEgNSAzIj48L3BvbHlnb24+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-437\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Start</tspan></text><g id=\"v-438\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"221b577\" port-group=\"out\" class=\"joint-port-body\" id=\"v-443\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-442\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-439\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"54552b7\" port-group=\"in\" class=\"joint-port-body\" id=\"v-441\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-440\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:3--><g model-id=\"63f2adf\" data-type=\"appEnd\" id=\"j_66\" class=\"joint-cell joint-type-append joint-element joint-theme-default\" transform=\"translate(450,330)\"><rect joint-selector=\"rect\" id=\"v-444\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-445\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICA8cGF0aCBpZD0iUmVjdGFuZ2xlXzE3NzVfLV9PdXRsaW5lIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSAxNzc1IC0gT3V0bGluZSIgZD0iTTQsMkEyLDIsMCwwLDAsMiw0VjE0YTIsMiwwLDAsMCwyLDJIMTRhMiwyLDAsMCwwLDItMlY0YTIsMiwwLDAsMC0yLTJINE00LDBIMTRhNCw0LDAsMCwxLDQsNFYxNGE0LDQsMCwwLDEtNCw0SDRhNCw0LDAsMCwxLTQtNFY0QTQsNCwwLDAsMSw0LDBaIiBmaWxsPSIjNzU0N2RiIi8+Cjwvc3ZnPgo=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-446\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">End</tspan></text><g id=\"v-447\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"e0ff5aa\" port-group=\"in\" class=\"joint-port-body\" id=\"v-449\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-448\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:4--><!--z-index:5--></g><g class=\"joint-labels-layer joint-viewport\"/><g class=\"joint-front-layer\"/><g class=\"joint-tools-layer\"><g class=\"joint-tools joint-theme-default\"><g class=\"joint-tool joint-theme-default\" data-tool-name=\"button\" model-id=\"63f2adf\" transform=\"matrix(1,0,0,1,495,334)\"><circle r=\"7\" fill=\"#FF1D00\" cursor=\"pointer\" joint-selector=\"button\"/><path d=\"M -3 -3 3 3 M -3 3 3 -3\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" pointer-events=\"none\" joint-selector=\"icon\"/></g></g><!--z-index:1--></g></g></svg>", "OTC": "10", "MRC": "100", "channels": 1, "nodes": 3, "freeNodeExec": 10, "nodeExecCharge": "100", "localeData": [{"id": 1699, "appId": "5cee2f60-a27d-11ef-b4ae-7f557da0bb13", "locale": "en", "translation": "eJx1jjEOwyAMRe/yZwbWZmftBaIqQkAjpIArDF0i7l7DEmXoZL3n/2Wf4OZcYMZywpEPWNaXQhJjdwFAIYt+2jTIZI+u4BpXSqYUKlJc/zS7oA9v2446o9cJmIfWGvf0oNy2GusRxjsiOPGNZyDbb9xtjZQ3+owxl/0HYThEog==", "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-14 17:11:18", "updatedAt": "2024-11-14 18:18:07", "deletedAt": null}]}, {"id": "cac46280-a286-11ef-b4ae-7f557da0bb13", "name": "User Onboarding", "desc": null, "type": "it", "image": "it", "status": "0", "owner": 0, "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-15 05:18:48", "updatedAt": "2024-11-14 23:51:43", "appData": {"links": [], "startId": "b789727", "modules": {"b789727": {"settings": {"aparty": "*********", "nodeName": "Start"}, "process": {"cronjob": "5 * * * *", "params": ["MSISDN"], "trigger": "HTTP Trigger"}, "output": {"conditions": {"82a27eabb4": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"b5887ff\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["b5887ff"]}}, "input": {}, "type": "appStart", "typeId": "0.1", "coordinates": {"x": 470, "y": 30, "nodeData": {"title": "Start", "name": "appStart", "id": "b789727", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appStart"}}}, "63f2adf": {"settings": {}, "process": {"success": {"code": [], "message": "", "nodeName": "End"}, "customErrors": [{"code": [], "message": ""}], "defaultError": {"code": "E9000", "message": ""}}, "output": {"conditions": {}}, "input": {}, "type": "appEnd", "typeId": "0.2", "coordinates": {"x": 480, "y": 710, "nodeData": {"title": "End", "name": "appEnd", "id": "63f2adf", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appEnd"}}}, "b5887ff": {"settings": {"timeout": 10000, "title": "HTTP", "nodeName": "Get User Details", "image": ""}, "input": {}, "process": {"URL": "", "requestType": "", "headers": [{"headerKey": "", "headerValue": ""}], "requestBody": "", "responseCache": "", "callReference": "", "responseType": "", "Value": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"42e0b34\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["42e0b34"]}}, "coordinates": {"x": 470, "y": 130, "nodeData": {"title": "HTTP", "name": "http", "id": "b5887ff", "isEditable": true, "canDelete": false, "status": "", "moduleType": "http"}}, "type": "http", "typeId": "1.2"}, "42e0b34": {"settings": {"timeout": 10000, "title": "HTTP", "nodeName": "Create New Account API", "image": ""}, "input": {}, "process": {"URL": "", "requestType": "", "headers": [{"headerKey": "", "headerValue": ""}], "requestBody": "", "responseCache": "", "callReference": "", "responseType": "", "Value": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"a665e14\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["a665e14"]}}, "coordinates": {"x": 470, "y": 230, "nodeData": {"title": "HTTP", "name": "http", "id": "42e0b34", "isEditable": true, "canDelete": false, "status": "", "moduleType": "http"}}, "type": "http", "typeId": "1.2", "isChoiceLinked": true}, "a665e14": {"settings": {"nodeName": "Choice"}, "input": {}, "process": {"match_conditions": [{"key": "accCreation_status", "condition": "Equals To", "value": "200", "moduleId": "000ca3e", "id": "72dc8b7", "coordinates": {"x": 230, "y": 430}}], "no_match_module_id": "9690a21"}, "output": {}, "isChoiceLinked": false, "coordinates": {"x": 470, "y": 340, "nodeData": {"title": "Choice", "name": "choice", "id": "a665e14", "isEditable": true, "canDelete": false, "status": "", "moduleType": "choice"}}, "type": "choice", "typeId": "0.9"}, "000ca3e": {"settings": {"nodeName": "Send Credentials via Email"}, "input": {}, "process": {"body": "", "senderID": "", "subject": "", "receiverAddress": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "coordinates": {"x": 240, "y": 550, "nodeData": {"title": "Email", "name": "email", "id": "000ca3e", "isEditable": true, "canDelete": false, "status": "", "moduleType": "email"}}, "type": "email", "typeId": "1.2"}, "9690a21": {"settings": {"nodeName": "Repeat"}, "input": {}, "process": {"repeat_count": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "coordinates": {"x": 710, "y": 590, "nodeData": {"title": "Repeat", "name": "repeat", "id": "9690a21", "isEditable": true, "canDelete": false, "status": "", "moduleType": "repeat"}}, "type": "repeat", "typeId": "0.7"}}, "errors": {"b789727": [{"parameter": "method", "path": "settings", "severity": "error", "msg": "should have required property 'method'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "samplePayload", "path": "settings", "severity": "error", "msg": "should have required property 'samplePayload'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "settings", "path": "", "severity": "warn", "msg": "should match exactly one schema in oneOf"}], "63f2adf": [{"parameter": "code", "path": "process.success", "severity": "warn", "msg": "should NOT have fewer than 1 items"}, {"parameter": "message", "path": "process.success", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}, {"parameter": "message", "path": "process.defaultError", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}]}, "version": "0.11.42", "id": "cac46280-a286-11ef-b4ae-7f557da0bb13"}, "ngage_id": "9a7f854e-fbea-4aca-b7c2-243ffe1f255d", "alignment": "vertical", "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" joint-selector=\"svg\" id=\"v-456\" style=\"overflow: hidden;\"><defs joint-selector=\"defs\"><marker id=\"v-456-1198995455\" orient=\"auto\" overflow=\"visible\" markerUnits=\"userSpaceOnUse\"><path id=\"v-508\" stroke=\"#7547DB\" fill=\"#7547DB\" transform=\"rotate(180)\" d=\"M 10 -5 0 0 10 5 z\"/></marker></defs><g joint-selector=\"layers\" class=\"joint-layers\" transform=\"matrix(1,0,0,1,562.5,234)\"><g class=\"joint-back-layer\"/><g class=\"joint-cells-layer joint-viewport\"><g model-id=\"82c2f37\" data-type=\"standard.Link\" id=\"j_137\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-935\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 495 84 L 495 126\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-934\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 495 84 L 495 126\" marker-end=\"url(#v-456-1198995455)\"/></g><g model-id=\"e3984cb\" data-type=\"standard.Link\" id=\"j_138\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-937\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 495 184 L 495 226\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-936\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 495 184 L 495 226\" marker-end=\"url(#v-456-1198995455)\"/></g><g model-id=\"67d0e1f\" data-type=\"standard.Link\" id=\"j_139\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-939\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 495 284 L 495 336\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-938\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 495 284 L 495 336\" marker-end=\"url(#v-456-1198995455)\"/></g><g model-id=\"353ced6\" data-type=\"standard.Link\" id=\"j_140\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-941\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 482 394 L 482 402 C 482 407.3333333333333 478.66666666666663 410 472 410 L 265 410 C 258.3333333333333 410 255 412.66666666666663 255 418 L 255 426\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-940\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 482 394 L 482 402 C 482 407.3333333333333 478.66666666666663 410 472 410 L 265 410 C 258.3333333333333 410 255 412.66666666666663 255 418 L 255 426\" marker-end=\"url(#v-456-1198995455)\"/></g><g model-id=\"e7041e7\" data-type=\"standard.Link\" id=\"j_141\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-943\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 270.5 494 L 271 520 C 270.66666666666663 526.6666666666666 269.66666666666663 530 268 530 L 268 530 C 266 530 265 532.6666666666666 265 538 L 265 546\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-942\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 270.5 494 L 271 520 C 270.66666666666663 526.6666666666666 269.66666666666663 530 268 530 L 268 530 C 266 530 265 532.6666666666666 265 538 L 265 546\" marker-end=\"url(#v-456-1198995455)\"/></g><g model-id=\"4580368\" data-type=\"standard.Link\" id=\"j_142\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-945\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 510 394 L 510 520 C 510 526.6666666666666 513.3333333333333 530 520 530 L 725 530 C 731.6666666666666 530 735 533.3333333333333 735 540 L 735 586\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-944\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 510 394 L 510 520 C 510 526.6666666666666 513.3333333333333 530 520 530 L 725 530 C 731.6666666666666 530 735 533.3333333333333 735 540 L 735 586\" marker-end=\"url(#v-456-1198995455)\"/></g><g model-id=\"05a280b0-9fde-49a4-98ce-298ffbcb414a\" data-type=\"standard.Link\" id=\"j_151\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-947\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 265 604 L 265 680 C 265 686.6666666666666 268.3333333333333 690 275 690 L 495 690 C 501.66666666666663 690 504.*********99994 692.6666666666666 505 698 L 505 706\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-946\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 265 604 L 265 680 C 265 686.6666666666666 268.3333333333333 690 275 690 L 495 690 C 501.66666666666663 690 504.*********99994 692.6666666666666 505 698 L 505 706\" marker-end=\"url(#v-456-1198995455)\"/></g><g model-id=\"2d42f176-251e-4d5b-8e93-09ecbef97078\" data-type=\"standard.Link\" id=\"j_152\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-949\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 735 644 L 735 680 C 735 686.6666666666666 731.6666666666666 690 725 690 L 515 690 C 508.33333333333326 690 504.*********99994 692.6666666666666 505 698 L 505 706\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-948\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 735 644 L 735 680 C 735 686.6666666666666 731.6666666666666 690 725 690 L 515 690 C 508.33333333333326 690 504.*********99994 692.6666666666666 505 698 L 505 706\" marker-end=\"url(#v-456-1198995455)\"/></g><!--z-index:1--><g model-id=\"b789727\" data-type=\"appStart\" id=\"j_143\" class=\"joint-cell joint-type-appstart joint-element joint-theme-default\" transform=\"translate(470,30)\"><rect joint-selector=\"rect\" id=\"v-882\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-883\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXBsYXkiPjxwb2x5Z29uIHBvaW50cz0iNSAzIDE5IDEyIDUgMjEgNSAzIj48L3BvbHlnb24+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-884\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Start</tspan></text><g id=\"v-885\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"f91123e\" port-group=\"out\" class=\"joint-port-body\" id=\"v-890\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-889\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-886\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"5c0fc03\" port-group=\"in\" class=\"joint-port-body\" id=\"v-888\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-887\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><g model-id=\"72dc8b7\" data-type=\"choiceOption\" id=\"j_144\" class=\"joint-cell joint-type-choiceoption joint-element joint-theme-default\" transform=\"translate(230,430)\"><rect joint-selector=\"rect\" id=\"v-892\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" width=\"81\" height=\"60\"/><text joint-selector=\"text\" id=\"v-891\" font-size=\"10\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" y=\"15\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Equals To</tspan></text><rect joint-selector=\"rect1\" id=\"v-893\" width=\"63\" height=\"20\" fill=\"#F2EEEE\" stroke=\"#F2EEEE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" y=\"30\" x=\"5.670000000000001\"/><text joint-selector=\"text1\" id=\"v-894\" font-size=\"9\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" font-weight=\"bold\" y=\"40\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">200</tspan></text><g id=\"v-895\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40.5,60)\"><circle joint-selector=\"portBody\" port=\"42814c6\" port-group=\"out\" class=\"joint-port-body\" id=\"v-900\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-899\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-896\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"0e74554\" port-group=\"in\" class=\"joint-port-body\" id=\"v-898\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-897\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:2--><g model-id=\"63f2adf\" data-type=\"appEnd\" id=\"j_145\" class=\"joint-cell joint-type-append joint-element joint-theme-default\" transform=\"translate(480,710)\"><rect joint-selector=\"rect\" id=\"v-901\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-902\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICA8cGF0aCBpZD0iUmVjdGFuZ2xlXzE3NzVfLV9PdXRsaW5lIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSAxNzc1IC0gT3V0bGluZSIgZD0iTTQsMkEyLDIsMCwwLDAsMiw0VjE0YTIsMiwwLDAsMCwyLDJIMTRhMiwyLDAsMCwwLDItMlY0YTIsMiwwLDAsMC0yLTJINE00LDBIMTRhNCw0LDAsMCwxLDQsNFYxNGE0LDQsMCwwLDEtNCw0SDRhNCw0LDAsMCwxLTQtNFY0QTQsNCwwLDAsMSw0LDBaIiBmaWxsPSIjNzU0N2RiIi8+Cjwvc3ZnPgo=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-903\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">End</tspan></text><g id=\"v-904\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"16c0b5d\" port-group=\"in\" class=\"joint-port-body\" id=\"v-906\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-905\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:3--><g model-id=\"b5887ff\" data-type=\"http\" id=\"j_146\" class=\"joint-cell joint-type-http joint-element joint-theme-default\" transform=\"translate(470,130)\"><rect joint-selector=\"rect\" id=\"v-907\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-908\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWdsb2JlIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjIiIHkxPSIxMiIgeDI9IjIyIiB5Mj0iMTIiPjwvbGluZT48cGF0aCBkPSJNMTIgMmExNS4zIDE1LjMgMCAwIDEgNCAxMCAxNS4zIDE1LjMgMCAwIDEtNCAxMCAxNS4zIDE1LjMgMCAwIDEtNC0xMCAxNS4zIDE1LjMgMCAwIDEgNC0xMHoiPjwvcGF0aD48L3N2Zz4=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-909\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Get User Details</tspan></text><g id=\"v-910\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"c62163a\" port-group=\"out\" class=\"joint-port-body\" id=\"v-915\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-914\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-911\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"f8bd0de\" port-group=\"in\" class=\"joint-port-body\" id=\"v-913\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-912\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:4--><g model-id=\"42e0b34\" data-type=\"http\" id=\"j_147\" class=\"joint-cell joint-type-http joint-element joint-theme-default\" transform=\"translate(470,230)\"><rect joint-selector=\"rect\" id=\"v-916\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-917\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWdsb2JlIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjIiIHkxPSIxMiIgeDI9IjIyIiB5Mj0iMTIiPjwvbGluZT48cGF0aCBkPSJNMTIgMmExNS4zIDE1LjMgMCAwIDEgNCAxMCAxNS4zIDE1LjMgMCAwIDEtNCAxMCAxNS4zIDE1LjMgMCAwIDEtNC0xMCAxNS4zIDE1LjMgMCAwIDEgNC0xMHoiPjwvcGF0aD48L3N2Zz4=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-918\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Create New Account API</tspan></text><g id=\"v-919\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"53bcff0\" port-group=\"out\" class=\"joint-port-body\" id=\"v-924\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-923\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-920\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"545621d\" port-group=\"in\" class=\"joint-port-body\" id=\"v-922\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-921\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:5--><g model-id=\"a665e14\" data-type=\"choice\" id=\"j_148\" class=\"joint-cell joint-type-choice joint-element joint-theme-default\" transform=\"translate(470,340)\"><rect joint-selector=\"rect\" id=\"v-857\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-858\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMC45OSIgaGVpZ2h0PSIxNy43NjEiIHZpZXdCb3g9IjAgMCAyMC45OSAxNy43NjEiPgogIDxnIGlkPSJJY29uX2lvbmljLWlvcy1vcHRpb25zIiBkYXRhLW5hbWU9Ikljb24gaW9uaWMtaW9zLW9wdGlvbnMiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0zLjM3NSAtNS42MjUpIj4KICAgIDxwYXRoIGlkPSJQYXRoXzEzNTkiIGRhdGEtbmFtZT0iUGF0aCAxMzU5IiBkPSJNMTYuMDU1LDI1Ljk2MWEyLjAyMSwyLjAyMSwwLDAsMSwzLjcsMGgzLjhhLjgxLjgxLDAsMCwxLC44MDcuODA3aDBhLjgxLjgxLDAsMCwxLS44MDcuODA3aC0zLjhhMi4wMjEsMi4wMjEsMCwwLDEtMy43LDBINC4xODJhLjgxLjgxLDAsMCwxLS44MDctLjgwN2gwYS44MS44MSwwLDAsMSwuODA3LS44MDdaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwIC01LjQwMSkiIGZpbGw9IiM3NTQ3ZGIiLz4KICAgIDxwYXRoIGlkPSJQYXRoXzEzNjAiIGRhdGEtbmFtZT0iUGF0aCAxMzYwIiBkPSJNNy45ODIsMTYuNGEyLjAyMSwyLjAyMSwwLDAsMSwzLjcsMEgyMy41NThhLjgxLjgxLDAsMCwxLC44MDcuODA3aDBhLjgxLjgxLDAsMCwxLS44MDcuODA3SDExLjY4NWEyLjAyMSwyLjAyMSwwLDAsMS0zLjcsMGgtMy44YS44MS44MSwwLDAsMS0uODA3LS44MDdoMGEuODEuODEsMCwwLDEsLjgwNy0uODA3WiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCAtMi43KSIgZmlsbD0iIzc1NDdkYiIvPgogICAgPHBhdGggaWQ9IlBhdGhfMTM2MSIgZGF0YS1uYW1lPSJQYXRoIDEzNjEiIGQ9Ik0xNi4wNTUsNi44MzZhMi4wMjEsMi4wMjEsMCwwLDEsMy43LDBoMy44YS44MS44MSwwLDAsMSwuODA3LjgwN2gwYS44MS44MSwwLDAsMS0uODA3LjgwN2gtMy44YTIuMDIxLDIuMDIxLDAsMCwxLTMuNywwSDQuMTgyYS44MS44MSwwLDAsMS0uODA3LS44MDdoMGEuODEuODEsMCwwLDEsLjgwNy0uODA3WiIgZmlsbD0iIzc1NDdkYiIvPgogIDwvZz4KPC9zdmc+Cg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-859\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Choice</tspan></text><rect joint-selector=\"rect4\" id=\"v-861\" fill=\"#FFE4EC\" stroke=\"#FFE4EC\" stroke-width=\"1\" x=\"50\" y=\"50\" width=\"80\" height=\"20\" rx=\"6\" ry=\"6\"/><rect joint-selector=\"rect5\" id=\"v-862\" fill=\"#CBFFE7\" stroke=\"#CBFFE7\" stroke-width=\"1\" x=\"-55\" y=\"50\" width=\"60\" height=\"20\" rx=\"6\" ry=\"6\"/><text joint-selector=\"label4\" id=\"v-860\" font-size=\"10\" xml:space=\"preserve\" fill=\"#ED1C24\" text-anchor=\"middle\" pointer-events=\"none\" x=\"90\" y=\"60\"><tspan dy=\"0.3em\" class=\"v-line\">No Match</tspan></text><text joint-selector=\"label5\" id=\"v-863\" font-size=\"10\" xml:space=\"preserve\" fill=\"#075E36\" text-anchor=\"middle\" pointer-events=\"none\" x=\"-25\" y=\"60\"><tspan dy=\"0.3em\" class=\"v-line\">Match</tspan></text><g id=\"v-864\" class=\"joint-port\" transform=\"matrix(1,0,0,1,12,50)\"><circle joint-selector=\"portBody\" port=\"ce3531a\" port-group=\"out\" class=\"joint-port-body\" id=\"v-870\" magnet=\"true\" r=\"4\" fill=\"#31D88A\" stroke=\"#31D88A\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-869\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-865\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40,50)\"><circle joint-selector=\"portBody\" port=\"bc4aadf\" port-group=\"out\" class=\"joint-port-body\" id=\"v-872\" magnet=\"true\" r=\"4\" fill=\"red\" stroke=\"red\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-871\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-866\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"054e55d\" port-group=\"in\" class=\"joint-port-body\" id=\"v-868\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-867\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:6--><g model-id=\"000ca3e\" data-type=\"email\" id=\"j_149\" class=\"joint-cell joint-type-email joint-element joint-theme-default\" transform=\"translate(240,550)\"><rect joint-selector=\"rect\" id=\"v-925\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-926\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLW1haWwiPjxwYXRoIGQ9Ik00IDRoMTZjMS4xIDAgMiAuOSAyIDJ2MTJjMCAxLjEtLjkgMi0yIDJINGMtMS4xIDAtMi0uOS0yLTJWNmMwLTEuMS45LTIgMi0yeiI+PC9wYXRoPjxwb2x5bGluZSBwb2ludHM9IjIyLDYgMTIsMTMgMiw2Ij48L3BvbHlsaW5lPjwvc3ZnPg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-927\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Send Credentials via Email</tspan></text><g id=\"v-928\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"a2cd863\" port-group=\"out\" class=\"joint-port-body\" id=\"v-933\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-932\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-929\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"6fa8f51\" port-group=\"in\" class=\"joint-port-body\" id=\"v-931\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-930\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:7--><g model-id=\"9690a21\" data-type=\"repeat\" id=\"j_150\" class=\"joint-cell joint-type-repeat joint-element joint-theme-default\" transform=\"translate(710,590)\"><rect joint-selector=\"rect\" id=\"v-873\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-874\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMC4xMDQiIGhlaWdodD0iMjMuNDU1IiB2aWV3Qm94PSIwIDAgMjAuMTA0IDIzLjQ1NSI+CiAgPHBhdGggaWQ9Ikljb25faW9uaWMtaW9zLXJlZnJlc2giIGRhdGEtbmFtZT0iSWNvbiBpb25pYy1pb3MtcmVmcmVzaCIgZD0iTTIzLjgyLDE1LjdhLjc4MS43ODEsMCwwLDAtLjc4LjcxMkE4LjUsOC41LDAsMSwxLDE0LjM0OCw3LjI1YS4yLjIsMCwwLDEsLjIxNS4yMDl2Mi42MzRhLjgzOS44MzksMCwwLDAsMS4yODguNzA3bDUuMDM3LTMuNTI0YS44MzcuODM3LDAsMCwwLDAtMS40MTlMMTUuODU2LDIuMzgxYS44MzkuODM5LDAsMCwwLTEuMjg4LjcwN1Y1LjQ4MWEuMjA2LjIwNiwwLDAsMS0uMi4yMDlBMTAuMDEsMTAuMDEsMCwxLDAsMjQuNiwxNi41MzhhLjc3OC43NzgsMCwwLDAtLjc4LS44NDNaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtNC41IC0yLjI1MSkiIGZpbGw9IiM3NTQ3ZGIiLz4KPC9zdmc+Cg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-875\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Repeat</tspan></text><g id=\"v-876\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"d661d23\" port-group=\"out\" class=\"joint-port-body\" id=\"v-881\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-880\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-877\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"7db881c\" port-group=\"in\" class=\"joint-port-body\" id=\"v-879\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-878\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:8--><!--z-index:9--></g><g class=\"joint-labels-layer joint-viewport\"/><g class=\"joint-front-layer\"/><g class=\"joint-tools-layer\"><g class=\"joint-tools joint-theme-default\"><g class=\"joint-tool joint-theme-default\" data-tool-name=\"button\" model-id=\"63f2adf\" transform=\"matrix(1,0,0,1,525.0000305175781,714)\"><circle r=\"7\" fill=\"#FF1D00\" cursor=\"pointer\" joint-selector=\"button\"/><path d=\"M -3 -3 3 3 M -3 3 3 -3\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" pointer-events=\"none\" joint-selector=\"icon\"/></g></g><!--z-index:1--></g></g></svg>", "OTC": "10", "MRC": "100", "channels": 1, "nodes": 7, "freeNodeExec": 10, "nodeExecCharge": "100", "localeData": [{"id": 1700, "appId": "cac46280-a286-11ef-b4ae-7f557da0bb13", "locale": "en", "translation": "eJx1jjEOwyAMRe/yZwbWZmftBaIqQkAjpIArDF0i7l7DEmXoZL3n/2Wf4OZcYMZywpEPWNaXQhJjdwFAIYt+2jTIZI+u4BpXSqYUKlJc/zS7oA9v2446o9cJmIfWGvf0oNy2GusRxjsiOPGNZyDbb9xtjZQ3+owxl/0HYThEog==", "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-14 18:18:48", "updatedAt": "2024-11-14 18:21:43", "deletedAt": null}]}, {"id": "81c9deb0-a287-11ef-b4ae-7f557da0bb13", "name": "Patient Follow Up", "desc": null, "type": "healthcare", "image": "healthcare", "status": "0", "owner": 0, "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-15 05:23:55", "updatedAt": "2024-11-14 23:56:10", "appData": {"links": [], "startId": "b789727", "modules": {"b789727": {"settings": {"aparty": "*********", "nodeName": "Start"}, "process": {"cronjob": "5 * * * *", "params": ["MSISDN"], "trigger": "HTTP Trigger"}, "output": {"conditions": {"82a27eabb4": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"9269db0\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["9269db0"]}}, "input": {}, "type": "appStart", "typeId": "0.1", "coordinates": {"x": 470, "y": 30, "nodeData": {"title": "Start", "name": "appStart", "id": "b789727", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appStart"}}}, "63f2adf": {"settings": {}, "process": {"success": {"code": [], "message": "", "nodeName": "End"}, "customErrors": [{"code": [], "message": ""}], "defaultError": {"code": "E9000", "message": ""}}, "output": {"conditions": {}}, "input": {}, "type": "appEnd", "typeId": "0.2", "coordinates": {"x": 470, "y": 560, "nodeData": {"title": "End", "name": "appEnd", "id": "63f2adf", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appEnd"}}}, "9269db0": {"settings": {"timeout": 10000, "title": "HTTP", "nodeName": "Get Patient Details", "image": ""}, "input": {}, "process": {"URL": "", "requestType": "", "headers": [{"headerKey": "", "headerValue": ""}], "requestBody": "", "responseCache": "", "callReference": "", "responseType": "", "Value": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"34df0f0\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["34df0f0"]}}, "coordinates": {"x": 470, "y": 140, "nodeData": {"title": "HTTP", "name": "http", "id": "9269db0", "isEditable": true, "canDelete": false, "status": "", "moduleType": "http"}}, "type": "http", "typeId": "1.2"}, "34df0f0": {"settings": {"account": "", "nodeName": "Welcome Greeting"}, "input": {}, "process": {"receiverAddress": "", "text_message": "", "senderAddress": "", "moduleName": "SMS"}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"ddff8b4\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["ddff8b4"]}}, "coordinates": {"x": 470, "y": 240, "nodeData": {"title": "SMS", "name": "sms", "id": "34df0f0", "isEditable": true, "canDelete": false, "status": "", "moduleType": "sms"}}, "type": "sms", "typeId": "1.1"}, "ddff8b4": {"settings": {"timeout": 10000, "title": "HTTP", "nodeName": "Generate Survey Link", "image": ""}, "input": {}, "process": {"URL": "", "requestType": "", "headers": [{"headerKey": "", "headerValue": ""}], "requestBody": "", "responseCache": "", "callReference": "", "responseType": "", "Value": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"c0666bd\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["c0666bd"]}}, "coordinates": {"x": 470, "y": 340, "nodeData": {"title": "HTTP", "name": "http", "id": "ddff8b4", "isEditable": true, "canDelete": false, "status": "", "moduleType": "http"}}, "type": "http", "typeId": "1.2"}, "c0666bd": {"settings": {"account": "", "nodeName": "SMS"}, "input": {}, "process": {"receiverAddress": "", "text_message": "", "senderAddress": "", "moduleName": "SMS"}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n// main function is the default method executed after processing current module\nfunction main(){\n  return \"84d6645\";// return end moduleId\n}", "customCodeIds": {"conditionalLink": []}}, "coordinates": {"x": 470, "y": 440, "nodeData": {"title": "SMS", "name": "sms", "id": "c0666bd", "isEditable": true, "canDelete": false, "status": "", "moduleType": "sms"}}, "type": "sms", "typeId": "1.1"}}, "errors": {"b789727": [{"parameter": "method", "path": "settings", "severity": "error", "msg": "should have required property 'method'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "samplePayload", "path": "settings", "severity": "error", "msg": "should have required property 'samplePayload'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "settings", "path": "", "severity": "warn", "msg": "should match exactly one schema in oneOf"}], "63f2adf": [{"parameter": "code", "path": "process.success", "severity": "warn", "msg": "should NOT have fewer than 1 items"}, {"parameter": "message", "path": "process.success", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}, {"parameter": "message", "path": "process.defaultError", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}]}, "version": "0.9.10", "id": "81c9deb0-a287-11ef-b4ae-7f557da0bb13"}, "ngage_id": "9a7f854e-fbea-4aca-b7c2-243ffe1f255d", "alignment": "vertical", "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" joint-selector=\"svg\" id=\"v-950\" style=\"overflow: hidden;\"><defs joint-selector=\"defs\"><marker id=\"v-950-1198995455\" orient=\"auto\" overflow=\"visible\" markerUnits=\"userSpaceOnUse\"><path id=\"v-1002\" stroke=\"#7547DB\" fill=\"#7547DB\" transform=\"rotate(180)\" d=\"M 10 -5 0 0 10 5 z\"/></marker></defs><g joint-selector=\"layers\" class=\"joint-layers\" transform=\"matrix(1,0,0,1,562.5,234)\"><g class=\"joint-back-layer\"/><g class=\"joint-cells-layer joint-viewport\"><g model-id=\"bf9b40c\" data-type=\"standard.Link\" id=\"j_182\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1151\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 495 84 L 495 136\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1150\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 495 84 L 495 136\" marker-end=\"url(#v-950-1198995455)\"/></g><g model-id=\"43a5ada\" data-type=\"standard.Link\" id=\"j_183\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1153\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 495 194 L 495 236\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1152\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 495 194 L 495 236\" marker-end=\"url(#v-950-1198995455)\"/></g><g model-id=\"e226db7\" data-type=\"standard.Link\" id=\"j_184\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1155\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 495 294 L 495 336\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1154\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 495 294 L 495 336\" marker-end=\"url(#v-950-1198995455)\"/></g><g model-id=\"515f2f9d-dbd1-4bab-b0b8-a0a78c1a2538\" data-type=\"standard.Link\" id=\"j_191\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1157\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 495 394 L 495 436\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1156\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 495 394 L 495 436\" marker-end=\"url(#v-950-1198995455)\"/></g><g model-id=\"9b1070be-178d-4d48-914d-a9b6d9be8b0a\" data-type=\"standard.Link\" id=\"j_192\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1159\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 495 494 L 495 556\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1158\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 495 494 L 495 556\" marker-end=\"url(#v-950-1198995455)\"/></g><!--z-index:1--><g model-id=\"b789727\" data-type=\"appStart\" id=\"j_185\" class=\"joint-cell joint-type-appstart joint-element joint-theme-default\" transform=\"translate(470,30)\"><rect joint-selector=\"rect\" id=\"v-1099\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1100\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXBsYXkiPjxwb2x5Z29uIHBvaW50cz0iNSAzIDE5IDEyIDUgMjEgNSAzIj48L3BvbHlnb24+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1101\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Start</tspan></text><g id=\"v-1102\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"d228dd4\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1107\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1106\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1103\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"5791686\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1105\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1104\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:2--><g model-id=\"63f2adf\" data-type=\"appEnd\" id=\"j_186\" class=\"joint-cell joint-type-append joint-element joint-theme-default\" transform=\"translate(470,560)\"><rect joint-selector=\"rect\" id=\"v-1108\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1109\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICA8cGF0aCBpZD0iUmVjdGFuZ2xlXzE3NzVfLV9PdXRsaW5lIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSAxNzc1IC0gT3V0bGluZSIgZD0iTTQsMkEyLDIsMCwwLDAsMiw0VjE0YTIsMiwwLDAsMCwyLDJIMTRhMiwyLDAsMCwwLDItMlY0YTIsMiwwLDAsMC0yLTJINE00LDBIMTRhNCw0LDAsMCwxLDQsNFYxNGE0LDQsMCwwLDEtNCw0SDRhNCw0LDAsMCwxLTQtNFY0QTQsNCwwLDAsMSw0LDBaIiBmaWxsPSIjNzU0N2RiIi8+Cjwvc3ZnPgo=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1110\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">End</tspan></text><g id=\"v-1111\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"79ba86f\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1113\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1112\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:3--><g model-id=\"9269db0\" data-type=\"http\" id=\"j_187\" class=\"joint-cell joint-type-http joint-element joint-theme-default\" transform=\"translate(470,140)\"><rect joint-selector=\"rect\" id=\"v-1114\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1115\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWdsb2JlIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjIiIHkxPSIxMiIgeDI9IjIyIiB5Mj0iMTIiPjwvbGluZT48cGF0aCBkPSJNMTIgMmExNS4zIDE1LjMgMCAwIDEgNCAxMCAxNS4zIDE1LjMgMCAwIDEtNCAxMCAxNS4zIDE1LjMgMCAwIDEtNC0xMCAxNS4zIDE1LjMgMCAwIDEgNC0xMHoiPjwvcGF0aD48L3N2Zz4=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1116\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Get Patient Details</tspan></text><g id=\"v-1117\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"0d3fb2d\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1122\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1121\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1118\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"0469645\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1120\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1119\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:4--><g model-id=\"34df0f0\" data-type=\"sms\" id=\"j_188\" class=\"joint-cell joint-type-sms joint-element joint-theme-default\" transform=\"translate(470,240)\"><rect joint-selector=\"rect\" id=\"v-1123\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1124\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLW1lc3NhZ2Utc3F1YXJlIj48cGF0aCBkPSJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6Ij48L3BhdGg+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1125\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Welcome Greeting</tspan></text><g id=\"v-1126\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"36f083b\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1131\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1130\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1127\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"0fce5cb\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1129\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1128\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:5--><g model-id=\"ddff8b4\" data-type=\"http\" id=\"j_189\" class=\"joint-cell joint-type-http joint-element joint-theme-default\" transform=\"translate(470,340)\"><rect joint-selector=\"rect\" id=\"v-1132\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1133\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWdsb2JlIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjIiIHkxPSIxMiIgeDI9IjIyIiB5Mj0iMTIiPjwvbGluZT48cGF0aCBkPSJNMTIgMmExNS4zIDE1LjMgMCAwIDEgNCAxMCAxNS4zIDE1LjMgMCAwIDEtNCAxMCAxNS4zIDE1LjMgMCAwIDEtNC0xMCAxNS4zIDE1LjMgMCAwIDEgNC0xMHoiPjwvcGF0aD48L3N2Zz4=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1134\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Generate Survey Link</tspan></text><g id=\"v-1135\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"9148bd5\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1140\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1139\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1136\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"e194f8f\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1138\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1137\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:6--><g model-id=\"c0666bd\" data-type=\"sms\" id=\"j_190\" class=\"joint-cell joint-type-sms joint-element joint-theme-default\" transform=\"translate(470,440)\"><rect joint-selector=\"rect\" id=\"v-1141\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1142\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLW1lc3NhZ2Utc3F1YXJlIj48cGF0aCBkPSJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6Ij48L3BhdGg+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1143\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">SMS</tspan></text><g id=\"v-1144\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"9ae783f\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1149\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1148\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1145\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"b9663fc\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1147\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1146\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:7--><!--z-index:8--></g><g class=\"joint-labels-layer joint-viewport\"/><g class=\"joint-front-layer\"/><g class=\"joint-tools-layer\"><g class=\"joint-tools joint-theme-default\"><g class=\"joint-tool joint-theme-default\" data-tool-name=\"button\" model-id=\"63f2adf\" transform=\"matrix(1,0,0,1,515,564)\"><circle r=\"7\" fill=\"#FF1D00\" cursor=\"pointer\" joint-selector=\"button\"/><path d=\"M -3 -3 3 3 M -3 3 3 -3\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" pointer-events=\"none\" joint-selector=\"icon\"/></g></g><!--z-index:1--></g></g></svg>", "OTC": "10", "MRC": "100", "channels": 2, "nodes": 6, "freeNodeExec": 10, "nodeExecCharge": "100", "localeData": [{"id": 1701, "appId": "81c9deb0-a287-11ef-b4ae-7f557da0bb13", "locale": "en", "translation": "eJx1jjEOwyAMRe/yZwbWZmftBaIqQkAjpIArDF0i7l7DEmXoZL3n/2Wf4OZcYMZywpEPWNaXQhJjdwFAIYt+2jTIZI+u4BpXSqYUKlJc/zS7oA9v2446o9cJmIfWGvf0oNy2GusRxjsiOPGNZyDbb9xtjZQ3+owxl/0HYThEog==", "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-14 18:23:55", "updatedAt": "2024-11-14 18:26:10", "deletedAt": null}]}, {"id": "d9d4c7a0-a287-11ef-b4ae-7f557da0bb13", "name": "Patient Appointment Booking", "desc": null, "type": "healthcare", "image": "healthcare", "status": "0", "owner": 0, "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-15 05:26:23", "updatedAt": "2024-11-15 00:01:11", "appData": {"links": [], "startId": "b789727", "modules": {"b789727": {"settings": {"aparty": "*********", "nodeName": "Start"}, "process": {"cronjob": "5 * * * *", "params": ["MSISDN"], "trigger": "HTTP Trigger"}, "output": {"conditions": {"82a27eabb4": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"6d5fad6\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["6d5fad6"]}}, "input": {}, "type": "appStart", "typeId": "0.1", "coordinates": {"x": 1285, "y": 80, "nodeData": {"title": "Start", "name": "appStart", "id": "b789727", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appStart"}}, "isChoiceLinked": true}, "63f2adf": {"settings": {}, "process": {"success": {"code": [], "message": "", "nodeName": "End"}, "customErrors": [{"code": [], "message": ""}], "defaultError": {"code": "E9000", "message": ""}}, "output": {"conditions": {}}, "input": {}, "type": "appEnd", "typeId": "0.2", "coordinates": {"x": 1100, "y": 692.*************, "nodeData": {"title": "End", "name": "appEnd", "id": "63f2adf", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appEnd"}}}, "245a68d": {"settings": {"nodeName": "Whatsapp"}, "process": {"senderId": "", "receiverNumber": "", "message": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"a9d6649e12": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "input": {}, "type": "whatsapp", "typeId": "1.92", "coordinates": {"x": 400, "y": 541.*************, "nodeData": {"title": "Whatsapp", "name": "whatsapp", "id": "245a68d", "isEditable": true, "canDelete": false, "status": "", "moduleType": "whatsapp"}}}, "57622d4": {"settings": {"timeout": 10000, "title": "Voice", "nodeName": "Voice"}, "input": {}, "process": {"URL": "www.google.com", "requestType": "GET", "headers": [{"headerKey": "type", "headerValue": "application/json"}, {"headerKey": "language", "headerValue": "en"}], "requestBody": "NA"}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "coordinates": {"x": 750, "y": 541.*************, "nodeData": {"title": "Voice", "name": "voice", "id": "57622d4", "isEditable": true, "canDelete": false, "status": "", "moduleType": "voice"}}, "type": "voice", "typeId": "1.2"}, "18ae595": {"settings": {"timeout": 10000, "title": "HTTP", "nodeName": "Initiate <PERSON><PERSON><PERSON>", "image": ""}, "input": {}, "process": {"URL": "", "requestType": "", "headers": [{"headerKey": "", "headerValue": ""}], "requestBody": "", "responseCache": "", "callReference": "", "responseType": "", "Value": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "coordinates": {"x": 1100, "y": 541.*************, "nodeData": {"title": "HTTP", "name": "http", "id": "18ae595", "isEditable": true, "canDelete": false, "status": "", "moduleType": "http"}}, "type": "http", "typeId": "1.2"}, "6d5fad6": {"settings": {"nodeName": "Choice"}, "input": {}, "process": {"match_conditions": [{"key": "channel", "condition": "Equals To", "value": "WhatsApp", "moduleId": "245a68d", "id": "72b04b6", "coordinates": {"x": 509.5, "y": 381.*************}}, {"key": "channel", "condition": "Equals To", "value": "Voice", "moduleId": "57622d4", "id": "56a251a", "coordinates": {"x": 859.5, "y": 381.*************}}, {"key": "channel", "condition": "Equals To", "value": "<PERSON><PERSON><PERSON>", "moduleId": "18ae595", "id": "baf9a94", "coordinates": {"x": 1209.5, "y": 381.*************}}], "no_match_module_id": "63f2adf"}, "output": {}, "isChoiceLinked": false, "coordinates": {"x": 1285, "y": 230.9387755102041, "nodeData": {"title": "Choice", "name": "choice", "id": "6d5fad6", "isEditable": true, "canDelete": false, "status": "", "moduleType": "choice"}}, "type": "choice", "typeId": "0.9"}}, "errors": {"b789727": [{"parameter": "method", "path": "settings", "severity": "error", "msg": "should have required property 'method'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "samplePayload", "path": "settings", "severity": "error", "msg": "should have required property 'samplePayload'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "settings", "path": "", "severity": "warn", "msg": "should match exactly one schema in oneOf"}], "63f2adf": [{"parameter": "code", "path": "process.success", "severity": "warn", "msg": "should NOT have fewer than 1 items"}, {"parameter": "message", "path": "process.success", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}, {"parameter": "message", "path": "process.defaultError", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}]}, "version": "0.7.201", "id": "d9d4c7a0-a287-11ef-b4ae-7f557da0bb13"}, "ngage_id": "9a7f854e-fbea-4aca-b7c2-243ffe1f255d", "alignment": "vertical", "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" joint-selector=\"svg\" id=\"v-1160\" style=\"overflow: hidden;\"><defs joint-selector=\"defs\"><marker id=\"v-1160-1198995455\" orient=\"auto\" overflow=\"visible\" markerUnits=\"userSpaceOnUse\"><path id=\"v-1364\" stroke=\"#7547DB\" fill=\"#7547DB\" transform=\"rotate(180)\" d=\"M 10 -5 0 0 10 5 z\"/></marker></defs><g joint-selector=\"layers\" class=\"joint-layers\" transform=\"matrix(1,0,0,1,562.5,234)\"><g class=\"joint-back-layer\"/><g class=\"joint-cells-layer joint-viewport\"><g model-id=\"eb43c64\" data-type=\"standard.Link\" id=\"j_245\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1566\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1310 134 L 1310 226.94\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1565\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1310 134 L 1310 226.94\" marker-end=\"url(#v-1160-1198995455)\"/></g><g model-id=\"fe6db83\" data-type=\"standard.Link\" id=\"j_246\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1568\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 425 595.88 L 425 663 C 425 669.4 428.3333333333333 672.7333333333333 435 673 L 1115 673 C 1121.6666666666665 672.7333333333333 1125 675.4 1125 681 L 1125 688.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1567\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 425 595.88 L 425 663 C 425 669.4 428.3333333333333 672.7333333333333 435 673 L 1115 673 C 1121.6666666666665 672.7333333333333 1125 675.4 1125 681 L 1125 688.82\" marker-end=\"url(#v-1160-1198995455)\"/></g><g model-id=\"a88a710\" data-type=\"standard.Link\" id=\"j_247\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1570\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 775 595.88 L 775 663 C 775 669.4 778.3333333333333 672.7333333333333 785 673 L 1115 673 C 1121.6666666666665 672.7333333333333 1125 675.4 1125 681 L 1125 688.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1569\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 775 595.88 L 775 663 C 775 669.4 778.3333333333333 672.7333333333333 785 673 L 1115 673 C 1121.6666666666665 672.7333333333333 1125 675.4 1125 681 L 1125 688.82\" marker-end=\"url(#v-1160-1198995455)\"/></g><g model-id=\"ae40aa1\" data-type=\"standard.Link\" id=\"j_248\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1572\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1125 595.88 L 1125 688.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1571\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1125 595.88 L 1125 688.82\" marker-end=\"url(#v-1160-1198995455)\"/></g><g model-id=\"4e9b202\" data-type=\"standard.Link\" id=\"j_249\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1574\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1297 284.94 L 1297 352 C 1297 358.46666666666664 1293.6666666666665 361.7*********9995 1287 362 L 545 362 C 538 361.7*********9995 534.6666666666666 364.46666666666664 535 370 L 534.5 377.88\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1573\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1297 284.94 L 1297 352 C 1297 358.46666666666664 1293.6666666666665 361.7*********9995 1287 362 L 545 362 C 538 361.7*********9995 534.6666666666666 364.46666666666664 535 370 L 534.5 377.88\" marker-end=\"url(#v-1160-1198995455)\"/></g><g model-id=\"97966b7\" data-type=\"standard.Link\" id=\"j_250\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1576\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 550 445.88 L 550 512 C 550 518.5*********999 546.6666666666666 521.9333333333333 540 522 L 435 522 C 428.3333333333333 521.9333333333333 425 524.5*********999 425 530 L 425 537.88\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1575\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 550 445.88 L 550 512 C 550 518.5*********999 546.6666666666666 521.9333333333333 540 522 L 435 522 C 428.3333333333333 521.9333333333333 425 524.5*********999 425 530 L 425 537.88\" marker-end=\"url(#v-1160-1198995455)\"/></g><g model-id=\"48a800e\" data-type=\"standard.Link\" id=\"j_251\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1578\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1297 284.94 L 1297 352 C 1297 358.46666666666664 1293.6666666666665 361.7*********9995 1287 362 L 895 362 C 888 361.7*********9995 884.6666666666666 364.46666666666664 885 370 L 884.5 377.88\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1577\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1297 284.94 L 1297 352 C 1297 358.46666666666664 1293.6666666666665 361.7*********9995 1287 362 L 895 362 C 888 361.7*********9995 884.6666666666666 364.46666666666664 885 370 L 884.5 377.88\" marker-end=\"url(#v-1160-1198995455)\"/></g><g model-id=\"662e6a0\" data-type=\"standard.Link\" id=\"j_252\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1580\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 900 445.88 L 900 512 C 900 518.5*********999 896.6666666666666 521.9333333333333 890 522 L 785 522 C 778.3333333333333 521.9333333333333 775 524.5*********999 775 530 L 775 537.88\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1579\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 900 445.88 L 900 512 C 900 518.5*********999 896.6666666666666 521.9333333333333 890 522 L 785 522 C 778.3333333333333 521.9333333333333 775 524.5*********999 775 530 L 775 537.88\" marker-end=\"url(#v-1160-1198995455)\"/></g><g model-id=\"eefffed\" data-type=\"standard.Link\" id=\"j_253\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1582\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1297 284.94 L 1297 352 C 1297 358.46666666666664 1293.6666666666665 361.7*********9995 1287 362 L 1245 362 C 1238 361.7*********9995 1234.6666666666665 364.46666666666664 1235 370 L 1234.5 377.88\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1581\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1297 284.94 L 1297 352 C 1297 358.46666666666664 1293.6666666666665 361.7*********9995 1287 362 L 1245 362 C 1238 361.7*********9995 1234.6666666666665 364.46666666666664 1235 370 L 1234.5 377.88\" marker-end=\"url(#v-1160-1198995455)\"/></g><g model-id=\"0cd9beb\" data-type=\"standard.Link\" id=\"j_254\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1584\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1250 445.88 L 1250 512 C 1250 518.5*********999 1246.6666666666665 521.9333333333333 1240 522 L 1135 522 C 1128.3333333333333 521.9333333333333 1125 524.5*********999 1125 530 L 1125 537.88\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1583\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1250 445.88 L 1250 512 C 1250 518.5*********999 1246.6666666666665 521.9333333333333 1240 522 L 1135 522 C 1128.3333333333333 521.9333333333333 1125 524.5*********999 1125 530 L 1125 537.88\" marker-end=\"url(#v-1160-1198995455)\"/></g><g model-id=\"fe298a0\" data-type=\"standard.Link\" id=\"j_255\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1586\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1325 284.94 L 1325 301 C 1325 307.625850340136 1328.3333333333333 310.9591836734694 1335 311 L 1575 311 C 1581.6666666666665 310.9591836734694 1584.*********9998 307.625850340136 1585 301 L 1585 292 C 1584.*********9998 285.2517006802721 1588.333333333333 281.9183673469388 1595 282 L 1625 282 C 1631.6666666666665 281.9183673469388 1635 285.2517006802721 1635 292 L 1635 483 C 1635 489.5442176870748 1635 496.21088435374145 1635 503 L 1635 633 C 1635 639.5442176870748 1631.6666666666665 642.************* 1625 643 L 1175 643 C 1168.3333333333333 642.************* 1161.6666666666665 642.************* 1155 643 L 1135 643 C 1128.3333333333333 642.************* 1125 646.2108843537414 1125 653 L 1125 688.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1585\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1325 284.94 L 1325 301 C 1325 307.625850340136 1328.3333333333333 310.9591836734694 1335 311 L 1575 311 C 1581.6666666666665 310.9591836734694 1584.*********9998 307.625850340136 1585 301 L 1585 292 C 1584.*********9998 285.2517006802721 1588.333333333333 281.9183673469388 1595 282 L 1625 282 C 1631.6666666666665 281.9183673469388 1635 285.2517006802721 1635 292 L 1635 483 C 1635 489.5442176870748 1635 496.21088435374145 1635 503 L 1635 633 C 1635 639.5442176870748 1631.6666666666665 642.************* 1625 643 L 1175 643 C 1168.3333333333333 642.************* 1161.6666666666665 642.************* 1155 643 L 1135 643 C 1128.3333333333333 642.************* 1125 646.2108843537414 1125 653 L 1125 688.82\" marker-end=\"url(#v-1160-1198995455)\"/></g><!--z-index:1--><g model-id=\"b789727\" data-type=\"appStart\" id=\"j_256\" class=\"joint-cell joint-type-appstart joint-element joint-theme-default\" transform=\"translate(1285,80)\"><rect joint-selector=\"rect\" id=\"v-1499\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1500\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXBsYXkiPjxwb2x5Z29uIHBvaW50cz0iNSAzIDE5IDEyIDUgMjEgNSAzIj48L3BvbHlnb24+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1501\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Start</tspan></text><g id=\"v-1502\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"6fb8e45\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1507\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1506\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1503\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"98e0326\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1505\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1504\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><g model-id=\"72b04b6\" data-type=\"choiceOption\" id=\"j_257\" class=\"joint-cell joint-type-choiceoption joint-element joint-theme-default\" transform=\"translate(509.5,381.*************)\"><rect joint-selector=\"rect\" id=\"v-1509\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" width=\"81\" height=\"60\"/><text joint-selector=\"text\" id=\"v-1508\" font-size=\"10\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" y=\"15\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Equals To</tspan></text><rect joint-selector=\"rect1\" id=\"v-1510\" width=\"63\" height=\"20\" fill=\"#F2EEEE\" stroke=\"#F2EEEE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" y=\"30\" x=\"5.670000000000001\"/><text joint-selector=\"text1\" id=\"v-1511\" font-size=\"9\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" font-weight=\"bold\" y=\"40\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">WhatsApp</tspan></text><g id=\"v-1512\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40.5,60)\"><circle joint-selector=\"portBody\" port=\"bdfaa56\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1517\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1516\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1513\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"0269a6b\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1515\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1514\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:2--><g model-id=\"63f2adf\" data-type=\"appEnd\" id=\"j_258\" class=\"joint-cell joint-type-append joint-element joint-theme-default\" transform=\"translate(1100,692.*************)\"><rect joint-selector=\"rect\" id=\"v-1493\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1494\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICA8cGF0aCBpZD0iUmVjdGFuZ2xlXzE3NzVfLV9PdXRsaW5lIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSAxNzc1IC0gT3V0bGluZSIgZD0iTTQsMkEyLDIsMCwwLDAsMiw0VjE0YTIsMiwwLDAsMCwyLDJIMTRhMiwyLDAsMCwwLDItMlY0YTIsMiwwLDAsMC0yLTJINE00LDBIMTRhNCw0LDAsMCwxLDQsNFYxNGE0LDQsMCwwLDEtNCw0SDRhNCw0LDAsMCwxLTQtNFY0QTQsNCwwLDAsMSw0LDBaIiBmaWxsPSIjNzU0N2RiIi8+Cjwvc3ZnPgo=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1495\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">End</tspan></text><g id=\"v-1496\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"bd11135\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1498\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1497\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><g model-id=\"56a251a\" data-type=\"choiceOption\" id=\"j_259\" class=\"joint-cell joint-type-choiceoption joint-element joint-theme-default\" transform=\"translate(859.5,381.*************)\"><rect joint-selector=\"rect\" id=\"v-1519\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" width=\"81\" height=\"60\"/><text joint-selector=\"text\" id=\"v-1518\" font-size=\"10\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" y=\"15\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Equals To</tspan></text><rect joint-selector=\"rect1\" id=\"v-1520\" width=\"63\" height=\"20\" fill=\"#F2EEEE\" stroke=\"#F2EEEE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" y=\"30\" x=\"5.670000000000001\"/><text joint-selector=\"text1\" id=\"v-1521\" font-size=\"9\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" font-weight=\"bold\" y=\"40\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Voice</tspan></text><g id=\"v-1522\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40.5,60)\"><circle joint-selector=\"portBody\" port=\"3c534a9\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1527\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1526\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1523\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"698ea10\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1525\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1524\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:3--><g model-id=\"245a68d\" data-type=\"whatsapp\" id=\"j_260\" class=\"joint-cell joint-type-whatsapp joint-element joint-theme-default\" transform=\"translate(400,541.*************)\"><rect joint-selector=\"rect\" id=\"v-1528\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1529\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMSIgaGVpZ2h0PSIyMSIgdmlld0JveD0iMCAwIDIxIDIxIj4KICA8ZyBpZD0iR3JvdXBfMjY2OCIgZGF0YS1uYW1lPSJHcm91cCAyNjY4IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjQyNiAwLjQyOSkiPgogICAgPHBhdGggaWQ9IlVuaW9uXzYiIGRhdGEtbmFtZT0iVW5pb24gNiIgZD0iTS4zMDgsMjAuNjg5YTEuMDQ1LDEuMDQ1LDAsMCwxLS4yNTItMS4wODFsMS44NTUtNS41NzdBOS45NzEsOS45NzEsMCwwLDEsNi41NjQsMS4wNTYsOS44NjQsOS44NjQsMCwwLDEsMTEsMGguNkE5Ljk0Nyw5Ljk0NywwLDAsMSwyMSw5LjM5di41ODRhOS45ODUsOS45ODUsMCwwLDEtNC43MzIsOC40ODYsMTAuMDI1LDEwLjAyNSwwLDAsMS01LjI0MiwxLjQ5M0gxMWE5Ljk0LDkuOTQsMCwwLDEtNC4wMzQtLjg2NEwxLjM5MiwyMC45NDVhMS4wNTMsMS4wNTMsMCwwLDEtMS4wODQtLjI1NlptNy4yLTE3Ljc0NGE3LjgzLDcuODMsMCwwLDAtNC4zNDksNy4wM0E3Ljc1MSw3Ljc1MSwwLDAsMCw0LDEzLjQ4M2ExLjA2MSwxLjA2MSwwLDAsMSwuMDYuODEyTDIuNzI4LDE4LjI2OCw2LjcsMTYuOTQ0YTEuMDUxLDEuMDUxLDAsMCwxLC44MDguMDZBNy43NzgsNy43NzgsMCwwLDAsMTEsMTcuODM2aC4wMjFhNy44Nyw3Ljg3LDAsMCwwLDcuODYxLTcuODU4di0uNWE3LjgyOSw3LjgyOSwwLDAsMC03LjM2NS03LjM2NkgxMUE3Ljc3NCw3Ljc3NCwwLDAsMCw3LjUxMywyLjk0NFpNNi41NjQsNi41YS40MDcuNDA3LDAsMCwxLC4zMi0uNGwxLjc4MS0uNDEyYS40MTEuNDExLDAsMCwxLC40NjkuMjM2bC44MjMsMS45MTZhLjQxNi40MTYsMCwwLDEtLjEyLjQ4TDguOCw5LjE2NkE2LjM0OCw2LjM0OCwwLDAsMCwxMS44MywxMi4ybC44NDktMS4wNGEuNDEzLjQxMywwLDAsMSwuNDgtLjExNmwxLjkxNy44MmEuNDE5LjQxOSwwLDAsMSwuMjQuNDcybC0uNDEyLDEuNzhhLjQwNi40MDYsMCwwLDEtLjQuMzE2QTcuOTM2LDcuOTM2LDAsMCwxLDYuNTY0LDYuNVoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0wLjQyNiAtMC40MjgpIiBmaWxsPSIjNzU0N2RiIi8+CiAgPC9nPgo8L3N2Zz4K\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1530\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Whatsapp</tspan></text><g id=\"v-1531\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"38fc54c\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1536\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1535\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1532\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"2f96a28\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1534\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1533\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><g model-id=\"baf9a94\" data-type=\"choiceOption\" id=\"j_261\" class=\"joint-cell joint-type-choiceoption joint-element joint-theme-default\" transform=\"translate(1209.5,381.*************)\"><rect joint-selector=\"rect\" id=\"v-1538\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" width=\"81\" height=\"60\"/><text joint-selector=\"text\" id=\"v-1537\" font-size=\"10\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" y=\"15\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Equals To</tspan></text><rect joint-selector=\"rect1\" id=\"v-1539\" width=\"63\" height=\"20\" fill=\"#F2EEEE\" stroke=\"#F2EEEE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" y=\"30\" x=\"5.670000000000001\"/><text joint-selector=\"text1\" id=\"v-1540\" font-size=\"9\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" font-weight=\"bold\" y=\"40\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Chatbot</tspan></text><g id=\"v-1541\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40.5,60)\"><circle joint-selector=\"portBody\" port=\"0f661b7\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1546\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1545\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1542\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"8caf50a\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1544\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1543\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:4--><g model-id=\"57622d4\" data-type=\"voice\" id=\"j_262\" class=\"joint-cell joint-type-voice joint-element joint-theme-default\" transform=\"translate(750,541.*************)\"><rect joint-selector=\"rect\" id=\"v-1547\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1548\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOC4yNTQiIGhlaWdodD0iMjAuODYxIiB2aWV3Qm94PSIwIDAgMTguMjU0IDIwLjg2MSI+CiAgPHBhdGggaWQ9Ikljb25fb3Blbi1hdWRpby1zcGVjdHJ1bSIgZGF0YS1uYW1lPSJJY29uIG9wZW4tYXVkaW8tc3BlY3RydW0iIGQ9Ik0xMC40MzEsMFYyMC44NjFoMi42MDhWMFpNNS4yMTUsMi42MDhWMTguMjU0SDcuODIzVjIuNjA4Wk0xNS42NDYsNS4yMTVWMTUuNjQ2aDIuNjA4VjUuMjE1Wk0wLDcuODIzdjUuMjE1SDIuNjA4VjcuODIzWiIgZmlsbD0iIzc1NDdkYiIvPgo8L3N2Zz4=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1549\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Voice</tspan></text><g id=\"v-1550\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"5347195\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1555\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1554\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1551\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"9a81b9f\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1553\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1552\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:5--><g model-id=\"18ae595\" data-type=\"http\" id=\"j_263\" class=\"joint-cell joint-type-http joint-element joint-theme-default\" transform=\"translate(1100,541.*************)\"><rect joint-selector=\"rect\" id=\"v-1556\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1557\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWdsb2JlIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjIiIHkxPSIxMiIgeDI9IjIyIiB5Mj0iMTIiPjwvbGluZT48cGF0aCBkPSJNMTIgMmExNS4zIDE1LjMgMCAwIDEgNCAxMCAxNS4zIDE1LjMgMCAwIDEtNCAxMCAxNS4zIDE1LjMgMCAwIDEtNC0xMCAxNS4zIDE1LjMgMCAwIDEgNC0xMHoiPjwvcGF0aD48L3N2Zz4=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1558\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Initiate Chatbot</tspan></text><g id=\"v-1559\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"6f1819e\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1564\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1563\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1560\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"202cbea\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1562\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1561\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:6--><g model-id=\"6d5fad6\" data-type=\"choice\" id=\"j_264\" class=\"joint-cell joint-type-choice joint-element joint-theme-default\" transform=\"translate(1285,230.9387755102041)\"><rect joint-selector=\"rect\" id=\"v-1477\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1478\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMC45OSIgaGVpZ2h0PSIxNy43NjEiIHZpZXdCb3g9IjAgMCAyMC45OSAxNy43NjEiPgogIDxnIGlkPSJJY29uX2lvbmljLWlvcy1vcHRpb25zIiBkYXRhLW5hbWU9Ikljb24gaW9uaWMtaW9zLW9wdGlvbnMiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0zLjM3NSAtNS42MjUpIj4KICAgIDxwYXRoIGlkPSJQYXRoXzEzNTkiIGRhdGEtbmFtZT0iUGF0aCAxMzU5IiBkPSJNMTYuMDU1LDI1Ljk2MWEyLjAyMSwyLjAyMSwwLDAsMSwzLjcsMGgzLjhhLjgxLjgxLDAsMCwxLC44MDcuODA3aDBhLjgxLjgxLDAsMCwxLS44MDcuODA3aC0zLjhhMi4wMjEsMi4wMjEsMCwwLDEtMy43LDBINC4xODJhLjgxLjgxLDAsMCwxLS44MDctLjgwN2gwYS44MS44MSwwLDAsMSwuODA3LS44MDdaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwIC01LjQwMSkiIGZpbGw9IiM3NTQ3ZGIiLz4KICAgIDxwYXRoIGlkPSJQYXRoXzEzNjAiIGRhdGEtbmFtZT0iUGF0aCAxMzYwIiBkPSJNNy45ODIsMTYuNGEyLjAyMSwyLjAyMSwwLDAsMSwzLjcsMEgyMy41NThhLjgxLjgxLDAsMCwxLC44MDcuODA3aDBhLjgxLjgxLDAsMCwxLS44MDcuODA3SDExLjY4NWEyLjAyMSwyLjAyMSwwLDAsMS0zLjcsMGgtMy44YS44MS44MSwwLDAsMS0uODA3LS44MDdoMGEuODEuODEsMCwwLDEsLjgwNy0uODA3WiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCAtMi43KSIgZmlsbD0iIzc1NDdkYiIvPgogICAgPHBhdGggaWQ9IlBhdGhfMTM2MSIgZGF0YS1uYW1lPSJQYXRoIDEzNjEiIGQ9Ik0xNi4wNTUsNi44MzZhMi4wMjEsMi4wMjEsMCwwLDEsMy43LDBoMy44YS44MS44MSwwLDAsMSwuODA3LjgwN2gwYS44MS44MSwwLDAsMS0uODA3LjgwN2gtMy44YTIuMDIxLDIuMDIxLDAsMCwxLTMuNywwSDQuMTgyYS44MS44MSwwLDAsMS0uODA3LS44MDdoMGEuODEuODEsMCwwLDEsLjgwNy0uODA3WiIgZmlsbD0iIzc1NDdkYiIvPgogIDwvZz4KPC9zdmc+Cg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1479\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Choice</tspan></text><rect joint-selector=\"rect4\" id=\"v-1481\" fill=\"#FFE4EC\" stroke=\"#FFE4EC\" stroke-width=\"1\" x=\"50\" y=\"50\" width=\"80\" height=\"20\" rx=\"6\" ry=\"6\"/><rect joint-selector=\"rect5\" id=\"v-1482\" fill=\"#CBFFE7\" stroke=\"#CBFFE7\" stroke-width=\"1\" x=\"-55\" y=\"50\" width=\"60\" height=\"20\" rx=\"6\" ry=\"6\"/><text joint-selector=\"label4\" id=\"v-1480\" font-size=\"10\" xml:space=\"preserve\" fill=\"#ED1C24\" text-anchor=\"middle\" pointer-events=\"none\" x=\"90\" y=\"60\"><tspan dy=\"0.3em\" class=\"v-line\">No Match</tspan></text><text joint-selector=\"label5\" id=\"v-1483\" font-size=\"10\" xml:space=\"preserve\" fill=\"#075E36\" text-anchor=\"middle\" pointer-events=\"none\" x=\"-25\" y=\"60\"><tspan dy=\"0.3em\" class=\"v-line\">Match</tspan></text><g id=\"v-1484\" class=\"joint-port\" transform=\"matrix(1,0,0,1,12,50)\"><circle joint-selector=\"portBody\" port=\"f32727d\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1490\" magnet=\"true\" r=\"4\" fill=\"#31D88A\" stroke=\"#31D88A\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1489\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1485\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40,50)\"><circle joint-selector=\"portBody\" port=\"177ef9e\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1492\" magnet=\"true\" r=\"4\" fill=\"red\" stroke=\"red\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1491\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1486\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"143032b\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1488\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1487\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:7--></g><g class=\"joint-labels-layer joint-viewport\"/><g class=\"joint-front-layer\"/><g class=\"joint-tools-layer\"><!--z-index:1--></g></g></svg>", "OTC": "10", "MRC": "100", "channels": 1, "nodes": 6, "freeNodeExec": 10, "nodeExecCharge": "100", "localeData": [{"id": 1702, "appId": "d9d4c7a0-a287-11ef-b4ae-7f557da0bb13", "locale": "en", "translation": "eJx1jjEOwyAMRe/yZwbWZmftBaIqQkAjpIArDF0i7l7DEmXoZL3n/2Wf4OZcYMZywpEPWNaXQhJjdwFAIYt+2jTIZI+u4BpXSqYUKlJc/zS7oA9v2446o9cJmIfWGvf0oNy2GusRxjsiOPGNZyDbb9xtjZQ3+owxl/0HYThEog==", "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-14 18:26:23", "updatedAt": "2024-11-14 18:31:11", "deletedAt": null}]}, {"id": "eeb42370-a249-11ef-b4ae-7f557da0bb13", "name": "Employee Onboarding Flow", "desc": null, "type": "generic", "image": "generic", "status": "0", "owner": 0, "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-14 22:03:09", "updatedAt": "2024-11-14 16:59:50", "appData": {"links": [], "startId": "b789727", "modules": {"b789727": {"settings": {"aparty": "*********", "nodeName": "Start"}, "process": {"cronjob": "5 * * * *", "params": ["MSISDN"], "trigger": "HTTP Trigger"}, "output": {"conditions": {"82a27eabb4": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"88ea579\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["88ea579"]}}, "input": {}, "type": "appStart", "typeId": "0.1", "coordinates": {"x": 430, "y": 50, "nodeData": {"title": "Start", "name": "appStart", "id": "b789727", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appStart"}}}, "63f2adf": {"settings": {}, "process": {"success": {"code": [], "message": "", "nodeName": "End"}, "customErrors": [{"code": [], "message": ""}], "defaultError": {"code": "E9000", "message": ""}}, "output": {"conditions": {}}, "input": {}, "type": "appEnd", "typeId": "0.2", "coordinates": {"x": 610, "y": 360, "nodeData": {"title": "End", "name": "appEnd", "id": "63f2adf", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appEnd"}}}, "88ea579": {"settings": {"account": "", "nodeName": "RCS"}, "input": {}, "process": {"receiverAddress": "", "text_message": "", "senderAddress": "", "moduleName": "SMS"}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n// main function is the default method executed after processing current module\nfunction main(){\n  return \"84d6645\";// return end moduleId\n}", "customCodeIds": {"conditionalLink": []}}, "coordinates": {"x": 400, "y": 210, "nodeData": {"title": "RCS", "name": "rcs", "id": "88ea579", "isEditable": true, "canDelete": false, "status": "", "moduleType": "rcs"}}, "type": "rcs", "typeId": "1.1"}}, "errors": {"b789727": [{"parameter": "method", "path": "settings", "severity": "error", "msg": "should have required property 'method'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "samplePayload", "path": "settings", "severity": "error", "msg": "should have required property 'samplePayload'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "settings", "path": "", "severity": "warn", "msg": "should match exactly one schema in oneOf"}], "63f2adf": [{"parameter": "code", "path": "process.success", "severity": "warn", "msg": "should NOT have fewer than 1 items"}, {"parameter": "message", "path": "process.success", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}, {"parameter": "message", "path": "process.defaultError", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}], "88ea579": {"code": 802, "msg": "Requested plugin is not available"}}, "version": "0.2.16", "id": "eeb42370-a249-11ef-b4ae-7f557da0bb13"}, "ngage_id": "9a7f854e-fbea-4aca-b7c2-243ffe1f255d", "alignment": "vertical", "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" joint-selector=\"svg\" id=\"v-690\" style=\"overflow: hidden;\"><defs joint-selector=\"defs\"><marker id=\"v-690-1198995455\" orient=\"auto\" overflow=\"visible\" markerUnits=\"userSpaceOnUse\"><path id=\"v-723\" stroke=\"#7547DB\" fill=\"#7547DB\" transform=\"rotate(180)\" d=\"M 10 -5 0 0 10 5 z\"/></marker></defs><g joint-selector=\"layers\" class=\"joint-layers\" transform=\"matrix(1,0,0,1,562.5,234)\"><g class=\"joint-back-layer\"/><g class=\"joint-cells-layer joint-viewport\"><g model-id=\"4017e374-06c3-4f59-907d-de42855e7a53\" data-type=\"standard.Link\" id=\"j_112\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-721\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 455 104 L 455 180 C 455 186.66666666666666 451.66666666666663 190 445 190 L 435 190 C 428.3333333333333 190 425 192.66666666666666 425 198 L 425 206\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-720\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 455 104 L 455 180 C 455 186.66666666666666 451.66666666666663 190 445 190 L 435 190 C 428.3333333333333 190 425 192.66666666666666 425 198 L 425 206\" marker-end=\"url(#v-690-1198995455)\"/></g><!--z-index:1--><g model-id=\"b789727\" data-type=\"appStart\" id=\"j_109\" class=\"joint-cell joint-type-appstart joint-element joint-theme-default\" transform=\"translate(430,50)\"><rect joint-selector=\"rect\" id=\"v-696\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-697\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXBsYXkiPjxwb2x5Z29uIHBvaW50cz0iNSAzIDE5IDEyIDUgMjEgNSAzIj48L3BvbHlnb24+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-698\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Start</tspan></text><g id=\"v-699\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"ec75c95\" port-group=\"out\" class=\"joint-port-body\" id=\"v-704\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-703\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-700\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"60c263f\" port-group=\"in\" class=\"joint-port-body\" id=\"v-702\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-701\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:2--><g model-id=\"63f2adf\" data-type=\"appEnd\" id=\"j_110\" class=\"joint-cell joint-type-append joint-element joint-theme-default available-magnet available-cell\" transform=\"translate(610,360)\"><rect joint-selector=\"rect\" id=\"v-705\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-706\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICA8cGF0aCBpZD0iUmVjdGFuZ2xlXzE3NzVfLV9PdXRsaW5lIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSAxNzc1IC0gT3V0bGluZSIgZD0iTTQsMkEyLDIsMCwwLDAsMiw0VjE0YTIsMiwwLDAsMCwyLDJIMTRhMiwyLDAsMCwwLDItMlY0YTIsMiwwLDAsMC0yLTJINE00LDBIMTRhNCw0LDAsMCwxLDQsNFYxNGE0LDQsMCwwLDEtNCw0SDRhNCw0LDAsMCwxLTQtNFY0QTQsNCwwLDAsMSw0LDBaIiBmaWxsPSIjNzU0N2RiIi8+Cjwvc3ZnPgo=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-707\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">End</tspan></text><g id=\"v-708\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"e153f67\" port-group=\"in\" class=\"joint-port-body available-magnet\" id=\"v-710\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-709\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g><path pointer-events=\"none\" vector-effect=\"non-scaling-stroke\" fill=\"none\" class=\"joint-highlight-stroke joint-theme-default\" stroke-width=\"3\" stroke=\"#FEB663\" d=\"M 0 -4 H 80.79998779296875 V 50 H 0 V -4 Z\" transform=\"matrix(1.037128718480587,0,0,1.0555555555555556,-1.500000000000007,-1.2777777777777786)\"/></g><!--z-index:3--><g model-id=\"88ea579\" data-type=\"rcs\" id=\"j_111\" class=\"joint-cell joint-type-rcs joint-element joint-theme-default\" transform=\"translate(400,210)\"><rect joint-selector=\"rect\" id=\"v-711\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-712\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MC41OSIgaGVpZ2h0PSIzNi4zMzIiIHZpZXdCb3g9IjAgMCA1MC41OSAzNi4zMzIiPgogIDxwYXRoIGlkPSJVbmlvbl81MyIgZGF0YS1uYW1lPSJVbmlvbiA1MyIgZD0iTTE1LjU2NCwzNi4zMzNhNi42ODgsNi42ODgsMCwwLDEtNi42ODEtNi42ODF2LTE3LjZMLjY1NiwzLjgyM0EyLjIzOSwyLjIzOSwwLDAsMSwyLjIzOSwwSDQzLjkwN2E2LjY4OSw2LjY4OSwwLDAsMSw2LjY4Miw2LjY4MnYyMi45N2E2LjY4OSw2LjY4OSwwLDAsMS02LjY4Miw2LjY4MVpNMTIuNzA2LDkuNTM5YTIuMjQ0LDIuMjQ0LDAsMCwxLC42NTYsMS41ODRWMjkuNjUyYTIuMiwyLjIsMCwwLDAsMi4yLDIuMkg0My45MDdhMi4yMDUsMi4yMDUsMCwwLDAsMi4yLTIuMlY2LjY4MmEyLjIwNSwyLjIwNSwwLDAsMC0yLjItMi4ySDcuNjQ2Wm04LjQ0OCwxNy42MjZhMiwyLDAsMCwxLDAtNEgzMC40NmEyLDIsMCwxLDEsMCw0Wm0wLTdhMi4wMDUsMi4wMDUsMCwwLDEsMC00SDM4LjgzNmEyLjAwNSwyLjAwNSwwLDAsMSwwLDRabTAtN2ExLjkzNCwxLjkzNCwwLDAsMS0xLjg2MS0yLDEuOTM1LDEuOTM1LDAsMCwxLDEuODYxLTJIMzguODM2YTEuOTM1LDEuOTM1LDAsMCwxLDEuODYxLDIsMS45MzQsMS45MzQsMCwwLDEtMS44NjEsMloiIGZpbGw9IiM3NTQ3ZGIiLz4KPC9zdmc+Cg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-713\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">RCS</tspan></text><g id=\"v-714\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"f13d89e\" port-group=\"out\" class=\"joint-port-body\" id=\"v-719\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-718\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-715\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"7d52102\" port-group=\"in\" class=\"joint-port-body\" id=\"v-717\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-716\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:4--><g model-id=\"a39543dd-48fa-462f-8938-fe5c52a7d451\" data-type=\"standard.Link\" id=\"j_113\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"pointer-events: none;\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-725\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 429 260 L 610 260 C 616.6666666666666 260 620 263.3333333333333 620 270 L 620 370\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-724\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 429 260 L 610 260 C 616.6666666666666 260 620 263.3333333333333 620 270 L 620 370\" marker-end=\"url(#v-690-1198995455)\"/></g><!--z-index:5--></g><g class=\"joint-labels-layer joint-viewport\"/><g class=\"joint-front-layer\"/><g class=\"joint-tools-layer\"><g class=\"joint-tools joint-theme-default\"><g class=\"joint-tool joint-theme-default\" data-tool-name=\"button\" model-id=\"88ea579\" transform=\"matrix(1,0,0,1,445,214)\"><circle r=\"7\" fill=\"#FF1D00\" cursor=\"pointer\" joint-selector=\"button\"/><path d=\"M -3 -3 3 3 M -3 3 3 -3\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" pointer-events=\"none\" joint-selector=\"icon\"/></g><g class=\"joint-tool joint-theme-default\" data-tool-name=\"plus-button\" model-id=\"88ea579\" transform=\"matrix(1,0,0,1,426,264)\"><circle r=\"7\" fill=\"transparent\" cursor=\"pointer\" cy=\"10px\" joint-selector=\"button\"/><path d=\"M-3 7h6m-3-3v6\" stroke=\"#9A9A9A\" stroke-width=\"1\" transform=\"scale(1.7)\" cursor=\"pointer\" joint-selector=\"icon1\"/></g></g><!--z-index:1--></g></g></svg>", "OTC": "10", "MRC": "100", "channels": 0, "nodes": 3, "freeNodeExec": 10, "nodeExecCharge": "100", "localeData": [{"id": 1692, "appId": "eeb42370-a249-11ef-b4ae-7f557da0bb13", "locale": "en", "translation": "eJx1jjEOwyAMRe/yZwbWZmftBaIqQkAjpIArDF0i7l7DEmXoZL3n/2Wf4OZcYMZywpEPWNaXQhJjdwFAIYt+2jTIZI+u4BpXSqYUKlJc/zS7oA9v2446o9cJmIfWGvf0oNy2GusRxjsiOPGNZyDbb9xtjZQ3+owxl/0HYThEog==", "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-14 11:03:09", "updatedAt": "2024-11-14 11:29:50", "deletedAt": null}]}, {"id": "53f162e0-a270-11ef-b4ae-7f557da0bb13", "name": "Support Ticket Acknowledgement", "type": "generic", "image": "generic", "desc": null, "status": "0", "owner": 0, "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-15 02:38:00", "updatedAt": "2024-11-14 21:09:09", "appData": {"links": [], "startId": "b789727", "modules": {"b789727": {"settings": {"aparty": "*********", "nodeName": "Start"}, "process": {"cronjob": "5 * * * *", "params": ["MSISDN"], "trigger": "HTTP Trigger"}, "output": {"conditions": {"82a27eabb4": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"dde94a5\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["dde94a5"]}}, "input": {}, "type": "appStart", "typeId": "0.1", "coordinates": {"x": 430, "y": 30, "nodeData": {"title": "Start", "name": "appStart", "id": "b789727", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appStart"}}}, "63f2adf": {"settings": {}, "process": {"success": {"code": [], "message": "", "nodeName": "End"}, "customErrors": [{"code": [], "message": ""}], "defaultError": {"code": "E9000", "message": ""}}, "output": {"conditions": {}}, "input": {}, "type": "appEnd", "typeId": "0.2", "coordinates": {"x": 430, "y": 350, "nodeData": {"title": "End", "name": "appEnd", "id": "63f2adf", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appEnd"}}}, "26748a6": {"settings": {}, "input": {}, "process": {"body": "", "senderID": "", "subject": "", "receiverAddress": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "coordinates": {"x": 430, "y": 240, "nodeData": {"title": "Email", "name": "email", "id": "26748a6", "isEditable": true, "canDelete": false, "status": "", "moduleType": "email"}}, "type": "email", "typeId": "1.2"}, "dde94a5": {"settings": {"timeout": 10000, "title": "HTTP", "nodeName": "Get customer Detail", "image": ""}, "input": {}, "process": {"URL": "", "requestType": "", "headers": [{"headerKey": "", "headerValue": ""}], "requestBody": "", "responseCache": "", "callReference": "", "responseType": "", "Value": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"26748a6\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["26748a6"]}}, "coordinates": {"x": 430, "y": 130, "nodeData": {"title": "HTTP", "name": "http", "id": "dde94a5", "isEditable": true, "canDelete": false, "status": "", "moduleType": "http"}}, "type": "http", "typeId": "1.2"}}, "errors": {"b789727": [{"parameter": "method", "path": "settings", "severity": "error", "msg": "should have required property 'method'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "samplePayload", "path": "settings", "severity": "error", "msg": "should have required property 'samplePayload'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "settings", "path": "", "severity": "warn", "msg": "should match exactly one schema in oneOf"}], "63f2adf": [{"parameter": "code", "path": "process.success", "severity": "warn", "msg": "should NOT have fewer than 1 items"}, {"parameter": "message", "path": "process.success", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}, {"parameter": "message", "path": "process.defaultError", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}]}, "version": "0.5.33", "id": "53f162e0-a270-11ef-b4ae-7f557da0bb13"}, "ngage_id": "9a7f854e-fbea-4aca-b7c2-243ffe1f255d", "alignment": "vertical", "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" joint-selector=\"svg\" id=\"v-726\" style=\"overflow: hidden;\"><defs joint-selector=\"defs\"><marker id=\"v-726-1198995455\" orient=\"auto\" overflow=\"visible\" markerUnits=\"userSpaceOnUse\"><path id=\"v-814\" stroke=\"#7547DB\" fill=\"#7547DB\" transform=\"rotate(180)\" d=\"M 10 -5 0 0 10 5 z\"/></marker></defs><g joint-selector=\"layers\" class=\"joint-layers\" transform=\"matrix(1,0,0,1,562.5,234)\"><g class=\"joint-back-layer\"/><g class=\"joint-cells-layer joint-viewport\"><g model-id=\"a90fbd35-4c87-43c3-b2f5-aeb259cb98e0\" data-type=\"standard.Link\" id=\"j_127\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-812\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 455 84 L 455 126\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-811\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 455 84 L 455 126\" marker-end=\"url(#v-726-1198995455)\"/></g><g model-id=\"791ecd69-52ba-4912-b57c-a90c47e43378\" data-type=\"standard.Link\" id=\"j_128\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-816\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 455 184 L 455 236\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-815\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 455 184 L 455 236\" marker-end=\"url(#v-726-1198995455)\"/></g><g model-id=\"fb27b125-377d-4596-a3c1-ac45a1a7a0aa\" data-type=\"standard.Link\" id=\"j_129\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-818\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 455 294 L 455 346\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-817\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 455 294 L 455 346\" marker-end=\"url(#v-726-1198995455)\"/></g><!--z-index:1--><g model-id=\"b789727\" data-type=\"appStart\" id=\"j_123\" class=\"joint-cell joint-type-appstart joint-element joint-theme-default\" transform=\"translate(430,30)\"><rect joint-selector=\"rect\" id=\"v-778\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-779\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXBsYXkiPjxwb2x5Z29uIHBvaW50cz0iNSAzIDE5IDEyIDUgMjEgNSAzIj48L3BvbHlnb24+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-780\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Start</tspan></text><g id=\"v-781\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"8c37122\" port-group=\"out\" class=\"joint-port-body\" id=\"v-786\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-785\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-782\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"b787382\" port-group=\"in\" class=\"joint-port-body\" id=\"v-784\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-783\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:2--><g model-id=\"63f2adf\" data-type=\"appEnd\" id=\"j_124\" class=\"joint-cell joint-type-append joint-element joint-theme-default\" transform=\"translate(430,350)\"><rect joint-selector=\"rect\" id=\"v-787\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-788\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICA8cGF0aCBpZD0iUmVjdGFuZ2xlXzE3NzVfLV9PdXRsaW5lIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSAxNzc1IC0gT3V0bGluZSIgZD0iTTQsMkEyLDIsMCwwLDAsMiw0VjE0YTIsMiwwLDAsMCwyLDJIMTRhMiwyLDAsMCwwLDItMlY0YTIsMiwwLDAsMC0yLTJINE00LDBIMTRhNCw0LDAsMCwxLDQsNFYxNGE0LDQsMCwwLDEtNCw0SDRhNCw0LDAsMCwxLTQtNFY0QTQsNCwwLDAsMSw0LDBaIiBmaWxsPSIjNzU0N2RiIi8+Cjwvc3ZnPgo=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-789\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">End</tspan></text><g id=\"v-790\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"8be9fb6\" port-group=\"in\" class=\"joint-port-body\" id=\"v-792\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-791\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:3--><g model-id=\"26748a6\" data-type=\"email\" id=\"j_125\" class=\"joint-cell joint-type-email joint-element joint-theme-default\" transform=\"translate(430,240)\"><rect joint-selector=\"rect\" id=\"v-793\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-794\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLW1haWwiPjxwYXRoIGQ9Ik00IDRoMTZjMS4xIDAgMiAuOSAyIDJ2MTJjMCAxLjEtLjkgMi0yIDJINGMtMS4xIDAtMi0uOS0yLTJWNmMwLTEuMS45LTIgMi0yeiI+PC9wYXRoPjxwb2x5bGluZSBwb2ludHM9IjIyLDYgMTIsMTMgMiw2Ij48L3BvbHlsaW5lPjwvc3ZnPg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-795\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Email</tspan></text><g id=\"v-796\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"d69ab4b\" port-group=\"out\" class=\"joint-port-body\" id=\"v-801\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-800\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-797\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"5fe4206\" port-group=\"in\" class=\"joint-port-body\" id=\"v-799\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-798\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:4--><g model-id=\"dde94a5\" data-type=\"http\" id=\"j_126\" class=\"joint-cell joint-type-http joint-element joint-theme-default\" transform=\"translate(430,130)\"><rect joint-selector=\"rect\" id=\"v-802\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-803\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWdsb2JlIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjIiIHkxPSIxMiIgeDI9IjIyIiB5Mj0iMTIiPjwvbGluZT48cGF0aCBkPSJNMTIgMmExNS4zIDE1LjMgMCAwIDEgNCAxMCAxNS4zIDE1LjMgMCAwIDEtNCAxMCAxNS4zIDE1LjMgMCAwIDEtNC0xMCAxNS4zIDE1LjMgMCAwIDEgNC0xMHoiPjwvcGF0aD48L3N2Zz4=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-804\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Get customer Detail</tspan></text><g id=\"v-805\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"ab1f4cb\" port-group=\"out\" class=\"joint-port-body\" id=\"v-810\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-809\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-806\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"d35338f\" port-group=\"in\" class=\"joint-port-body\" id=\"v-808\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-807\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:5--><!--z-index:6--></g><g class=\"joint-labels-layer joint-viewport\"/><g class=\"joint-front-layer\"/><g class=\"joint-tools-layer\"><g class=\"joint-tools joint-theme-default\"><g class=\"joint-tool joint-theme-default\" data-tool-name=\"button\" model-id=\"63f2adf\" transform=\"matrix(1,0,0,1,475,354)\"><circle r=\"7\" fill=\"#FF1D00\" cursor=\"pointer\" joint-selector=\"button\"/><path d=\"M -3 -3 3 3 M -3 3 3 -3\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" pointer-events=\"none\" joint-selector=\"icon\"/></g></g><!--z-index:1--></g></g></svg>", "OTC": "10", "MRC": "100", "channels": 1, "nodes": 4, "freeNodeExec": 10, "nodeExecCharge": "100", "localeData": [{"id": 1695, "appId": "53f162e0-a270-11ef-b4ae-7f557da0bb13", "locale": "en", "translation": "eJx1jjEOwyAMRe/yZwbWZmftBaIqQkAjpIArDF0i7l7DEmXoZL3n/2Wf4OZcYMZywpEPWNaXQhJjdwFAIYt+2jTIZI+u4BpXSqYUKlJc/zS7oA9v2446o9cJmIfWGvf0oNy2GusRxjsiOPGNZyDbb9xtjZQ3+owxl/0HYThEog==", "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-14 15:38:00", "updatedAt": "2024-11-14 15:39:09", "deletedAt": null}]}, {"id": "95dd9b60-a270-11ef-b4ae-7f557da0bb13", "name": "Personalized Welcome Message", "desc": null, "type": "generic", "image": "generic", "status": "0", "owner": 0, "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-15 02:39:50", "updatedAt": "2024-11-14 21:14:15", "appData": {"links": [], "startId": "b789727", "modules": {"b789727": {"settings": {"aparty": "*********", "nodeName": "Start"}, "process": {"cronjob": "5 * * * *", "params": ["MSISDN"], "trigger": "HTTP Trigger"}, "output": {"conditions": {"82a27eabb4": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"d997799\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["d997799"]}}, "input": {}, "type": "appStart", "typeId": "0.1", "coordinates": {"x": 1450, "y": 80, "nodeData": {"title": "Start", "name": "appStart", "id": "b789727", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appStart"}}}, "63f2adf": {"settings": {}, "process": {"success": {"code": [], "message": "", "nodeName": "End"}, "customErrors": [{"code": [], "message": ""}], "defaultError": {"code": "E9000", "message": ""}}, "output": {"conditions": {}}, "input": {}, "type": "appEnd", "typeId": "0.2", "coordinates": {"x": 750, "y": 843.7551020408164, "nodeData": {"title": "End", "name": "appEnd", "id": "63f2adf", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appEnd"}}}, "d997799": {"settings": {"timeout": 10000, "title": "HTTP", "nodeName": "Get customer details", "image": ""}, "input": {}, "process": {"URL": "", "requestType": "", "headers": [{"headerKey": "", "headerValue": ""}], "requestBody": "", "responseCache": "", "callReference": "", "responseType": "", "Value": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"2c72000\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["2c72000"]}}, "coordinates": {"x": 1450, "y": 230.9387755102041, "nodeData": {"title": "HTTP", "name": "http", "id": "d997799", "isEditable": true, "canDelete": false, "status": "", "moduleType": "http"}}, "type": "http", "typeId": "1.2", "isChoiceLinked": true}, "2c72000": {"settings": {"nodeName": "Check preferred channel"}, "input": {}, "process": {"match_conditions": [{"key": "cust.channelpref", "condition": "Equals To", "value": "email", "moduleId": "12e33f7", "id": "0241d32", "coordinates": {"x": 859.5, "y": 532.*************}}, {"key": "cust.channelpref", "condition": "Equals To", "value": "SMS", "moduleId": "df14331", "id": "45aa3e6", "coordinates": {"x": 509.5, "y": 532.*************}}, {"key": "cust.channelpref", "condition": "Equals To", "value": "WhatsApp", "moduleId": "5b398df", "id": "b563cb3", "coordinates": {"x": 1559.5, "y": 532.*************}}, {"key": "cust.channelpref", "condition": "Equals To", "value": "RCS", "moduleId": "ffdaa16", "id": "a9b545a", "coordinates": {"x": 1209.5, "y": 532.*************}}], "no_match_module_id": "63f2adf"}, "output": {}, "isChoiceLinked": false, "coordinates": {"x": 1450, "y": 381.*************, "nodeData": {"title": "Choice", "name": "choice", "id": "2c72000", "isEditable": true, "canDelete": false, "status": "", "moduleType": "choice"}}, "type": "choice", "typeId": "0.9"}, "df14331": {"settings": {"account": "", "nodeName": "SMS"}, "input": {}, "process": {"receiverAddress": "", "text_message": "", "senderAddress": "", "moduleName": "SMS"}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "coordinates": {"x": 400, "y": 692.*************, "nodeData": {"title": "SMS", "name": "sms", "id": "df14331", "isEditable": true, "canDelete": false, "status": "", "moduleType": "sms"}}, "type": "sms", "typeId": "1.1"}, "12e33f7": {"settings": {}, "input": {}, "process": {"body": "", "senderID": "", "subject": "", "receiverAddress": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "coordinates": {"x": 750, "y": 692.*************, "nodeData": {"title": "Email", "name": "email", "id": "12e33f7", "isEditable": true, "canDelete": false, "status": "", "moduleType": "email"}}, "type": "email", "typeId": "1.2"}, "ffdaa16": {"settings": {"account": "", "nodeName": "RCS"}, "input": {}, "process": {"receiverAddress": "", "text_message": "", "senderAddress": "", "moduleName": "SMS"}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "coordinates": {"x": 1100, "y": 692.*************, "nodeData": {"title": "RCS", "name": "rcs", "id": "ffdaa16", "isEditable": true, "canDelete": false, "status": "", "moduleType": "rcs"}}, "type": "rcs", "typeId": "1.1"}, "5b398df": {"settings": {"nodeName": "Whatsapp"}, "process": {"senderId": "", "receiverNumber": "", "message": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"a9d6649e12": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"63f2adf\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["63f2adf"]}}, "input": {}, "type": "whatsapp", "typeId": "1.92", "coordinates": {"x": 1450, "y": 692.*************, "nodeData": {"title": "Whatsapp", "name": "whatsapp", "id": "5b398df", "isEditable": true, "canDelete": false, "status": "", "moduleType": "whatsapp"}}}}, "errors": {"b789727": [{"parameter": "method", "path": "settings", "severity": "error", "msg": "should have required property 'method'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "samplePayload", "path": "settings", "severity": "error", "msg": "should have required property 'samplePayload'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "settings", "path": "", "severity": "warn", "msg": "should match exactly one schema in oneOf"}], "63f2adf": [{"parameter": "code", "path": "process.success", "severity": "warn", "msg": "should NOT have fewer than 1 items"}, {"parameter": "message", "path": "process.success", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}, {"parameter": "message", "path": "process.defaultError", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}]}, "version": "0.15.14", "id": "95dd9b60-a270-11ef-b4ae-7f557da0bb13"}, "ngage_id": "9a7f854e-fbea-4aca-b7c2-243ffe1f255d", "alignment": "vertical", "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" joint-selector=\"svg\" id=\"v-49\" style=\"overflow: hidden;\"><defs joint-selector=\"defs\"><marker id=\"v-49-1198995455\" orient=\"auto\" overflow=\"visible\" markerUnits=\"userSpaceOnUse\"><path id=\"v-101\" stroke=\"#7547DB\" fill=\"#7547DB\" transform=\"rotate(180)\" d=\"M 10 -5 0 0 10 5 z\"/></marker></defs><g joint-selector=\"layers\" class=\"joint-layers\" transform=\"matrix(0.8,0,0,0.8,562.5000122070313,234)\"><g class=\"joint-back-layer\"/><g class=\"joint-cells-layer joint-viewport\"><g model-id=\"f43a493\" data-type=\"standard.Link\" id=\"j_148\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1001\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1475 134 L 1475 226.94\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1000\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1475 134 L 1475 226.94\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"d71a309\" data-type=\"standard.Link\" id=\"j_149\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1003\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1475 284.94 L 1475 377.88\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1002\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1475 284.94 L 1475 377.88\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"0e1a0b4\" data-type=\"standard.Link\" id=\"j_150\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1005\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1462 435.88 L 1462 503 C 1462 509.4 1458.6666666666665 512.7333333333333 1452 513 L 895 513 C 888 512.7333333333333 884.6666666666666 515.4 885 521 L 884.5 528.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1004\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1462 435.88 L 1462 503 C 1462 509.4 1458.6666666666665 512.7333333333333 1452 513 L 895 513 C 888 512.7333333333333 884.6666666666666 515.4 885 521 L 884.5 528.82\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"5104a15\" data-type=\"standard.Link\" id=\"j_151\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1007\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 900 596.82 L 900 663 C 900 669.5333333333333 896.6666666666666 672.8666666666666 890 673 L 785 673 C 778.3333333333333 672.8666666666666 775 675.5333333333333 775 681 L 775 688.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1006\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 900 596.82 L 900 663 C 900 669.5333333333333 896.6666666666666 672.8666666666666 890 673 L 785 673 C 778.3333333333333 672.8666666666666 775 675.5333333333333 775 681 L 775 688.82\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"26588bd\" data-type=\"standard.Link\" id=\"j_152\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1009\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1462 435.88 L 1462 503 C 1462 509.4 1458.6666666666665 512.7333333333333 1452 513 L 545 513 C 538 512.7333333333333 534.6666666666666 515.4 535 521 L 534.5 528.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1008\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1462 435.88 L 1462 503 C 1462 509.4 1458.6666666666665 512.7333333333333 1452 513 L 545 513 C 538 512.7333333333333 534.6666666666666 515.4 535 521 L 534.5 528.82\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"792d6e1\" data-type=\"standard.Link\" id=\"j_153\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1011\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 550 596.82 L 550 663 C 550 669.5333333333333 546.6666666666666 672.8666666666666 540 673 L 435 673 C 428.3333333333333 672.8666666666666 425 675.5333333333333 425 681 L 425 688.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1010\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 550 596.82 L 550 663 C 550 669.5333333333333 546.6666666666666 672.8666666666666 540 673 L 435 673 C 428.3333333333333 672.8666666666666 425 675.5333333333333 425 681 L 425 688.82\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"ce11bfc\" data-type=\"standard.Link\" id=\"j_154\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1013\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1462 435.88 L 1462 503 C 1462 509.4 1465.3333333333333 512.7333333333333 1472 513 L 1575 513 C 1581.3333333333333 512.7333333333333 1584.6666666666665 515.4 1585 521 L 1584.5 528.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1012\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1462 435.88 L 1462 503 C 1462 509.4 1465.3333333333333 512.7333333333333 1472 513 L 1575 513 C 1581.3333333333333 512.7333333333333 1584.6666666666665 515.4 1585 521 L 1584.5 528.82\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"be2f754\" data-type=\"standard.Link\" id=\"j_155\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1015\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1600 596.82 L 1600 663 C 1599.*********9998 669.5333333333333 1596.6666666666665 672.8666666666666 1590 673 L 1485 673 C 1478.3333333333333 672.8666666666666 1475 675.5333333333333 1475 681 L 1475 688.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1014\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1600 596.82 L 1600 663 C 1599.*********9998 669.5333333333333 1596.6666666666665 672.8666666666666 1590 673 L 1485 673 C 1478.3333333333333 672.8666666666666 1475 675.5333333333333 1475 681 L 1475 688.82\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"8f0c451\" data-type=\"standard.Link\" id=\"j_156\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1017\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1462 435.88 L 1462 503 C 1462 509.4 1458.6666666666665 512.7333333333333 1452 513 L 1245 513 C 1238 512.7333333333333 1234.6666666666665 515.4 1235 521 L 1234.5 528.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1016\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1462 435.88 L 1462 503 C 1462 509.4 1458.6666666666665 512.7333333333333 1452 513 L 1245 513 C 1238 512.7333333333333 1234.6666666666665 515.4 1235 521 L 1234.5 528.82\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"4f97d1b\" data-type=\"standard.Link\" id=\"j_157\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1019\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1250 596.82 L 1250 663 C 1250 669.5333333333333 1246.6666666666665 672.8666666666666 1240 673 L 1135 673 C 1128.3333333333333 672.8666666666666 1125 675.5333333333333 1125 681 L 1125 688.82\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1018\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1250 596.82 L 1250 663 C 1250 669.5333333333333 1246.6666666666665 672.8666666666666 1240 673 L 1135 673 C 1128.3333333333333 672.8666666666666 1125 675.5333333333333 1125 681 L 1125 688.82\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"646bd90\" data-type=\"standard.Link\" id=\"j_158\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1021\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1490 435.88 L 1490 452 C 1490 458.5850340136054 1493.3333333333333 461.9183673469388 1500 462 L 1590 462 C 1596.6666666666665 461.9183673469388 1599.*********9998 458.5850340136054 1600 452 L 1600 427 C 1599.*********9998 420.00884078327687 1603.333333333333 416.67550744994355 1610 417 L 1640 417 C 1646.6666666666665 416.67550744994355 1650 420.00884078327687 1650 427 L 1650 634 C 1650 640.5034013605442 1650 647.1700680272108 1650 654 L 1650 784 C 1650 790.5034013605441 1646.6666666666665 793.8367346938775 1640 794 L 825 794 C 818.3333333333333 793.8367346938775 811.6666666666665 793.8367346938775 805 794 L 785 794 C 778.3333333333333 793.8367346938775 775 797.1700680272108 775 804 L 775 839.76\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1020\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1490 435.88 L 1490 452 C 1490 458.5850340136054 1493.3333333333333 461.9183673469388 1500 462 L 1590 462 C 1596.6666666666665 461.9183673469388 1599.*********9998 458.5850340136054 1600 452 L 1600 427 C 1599.*********9998 420.00884078327687 1603.333333333333 416.67550744994355 1610 417 L 1640 417 C 1646.6666666666665 416.67550744994355 1650 420.00884078327687 1650 427 L 1650 634 C 1650 640.5034013605442 1650 647.1700680272108 1650 654 L 1650 784 C 1650 790.5034013605441 1646.6666666666665 793.8367346938775 1640 794 L 825 794 C 818.3333333333333 793.8367346938775 811.6666666666665 793.8367346938775 805 794 L 785 794 C 778.3333333333333 793.8367346938775 775 797.1700680272108 775 804 L 775 839.76\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"176b619\" data-type=\"standard.Link\" id=\"j_159\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1023\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 425 746.82 L 425 814 C 425 820.3*********999 428.3333333333333 823.7333333333332 435 824 L 765 824 C 771.6666666666666 823.7333333333332 775 826.3*********999 775 832 L 775 839.76\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1022\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 425 746.82 L 425 814 C 425 820.3*********999 428.3333333333333 823.7333333333332 435 824 L 765 824 C 771.6666666666666 823.7333333333332 775 826.3*********999 775 832 L 775 839.76\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"0803e25\" data-type=\"standard.Link\" id=\"j_160\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1025\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 775 746.82 L 775 839.76\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1024\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 775 746.82 L 775 839.76\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"13f0137\" data-type=\"standard.Link\" id=\"j_161\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1027\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1125 746.82 L 1125 814 C 1125 820.3*********999 1121.6666666666665 823.7333333333332 1115 824 L 785 824 C 778.3333333333333 823.7333333333332 775 826.3*********999 775 832 L 775 839.76\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1026\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1125 746.82 L 1125 814 C 1125 820.3*********999 1121.6666666666665 823.7333333333332 1115 824 L 785 824 C 778.3333333333333 823.7333333333332 775 826.3*********999 775 832 L 775 839.76\" marker-end=\"url(#v-49-1198995455)\"/></g><g model-id=\"bdbef89\" data-type=\"standard.Link\" id=\"j_162\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1029\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 1475 746.82 L 1475 814 C 1475 820.3*********999 1471.6666666666665 823.7333333333332 1465 824 L 785 824 C 778.3333333333333 823.7333333333332 775 826.3*********999 775 832 L 775 839.76\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1028\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 1475 746.82 L 1475 814 C 1475 820.3*********999 1471.6666666666665 823.7333333333332 1465 824 L 785 824 C 778.3333333333333 823.7333333333332 775 826.3*********999 775 832 L 775 839.76\" marker-end=\"url(#v-49-1198995455)\"/></g><!--z-index:1--><g model-id=\"b789727\" data-type=\"appStart\" id=\"j_163\" class=\"joint-cell joint-type-appstart joint-element joint-theme-default\" transform=\"translate(1450,80)\"><rect joint-selector=\"rect\" id=\"v-906\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-907\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXBsYXkiPjxwb2x5Z29uIHBvaW50cz0iNSAzIDE5IDEyIDUgMjEgNSAzIj48L3BvbHlnb24+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-908\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Start</tspan></text><g id=\"v-909\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"42aabf9\" port-group=\"out\" class=\"joint-port-body\" id=\"v-914\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-913\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-910\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"d8e4c13\" port-group=\"in\" class=\"joint-port-body\" id=\"v-912\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-911\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><g model-id=\"0241d32\" data-type=\"choiceOption\" id=\"j_164\" class=\"joint-cell joint-type-choiceoption joint-element joint-theme-default\" transform=\"translate(859.5,532.*************)\"><rect joint-selector=\"rect\" id=\"v-916\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" width=\"81\" height=\"60\"/><text joint-selector=\"text\" id=\"v-915\" font-size=\"10\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" y=\"15\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Equals To</tspan></text><rect joint-selector=\"rect1\" id=\"v-917\" width=\"63\" height=\"20\" fill=\"#F2EEEE\" stroke=\"#F2EEEE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" y=\"30\" x=\"5.670000000000001\"/><text joint-selector=\"text1\" id=\"v-918\" font-size=\"9\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" font-weight=\"bold\" y=\"40\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">email</tspan></text><g id=\"v-919\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40.5,60)\"><circle joint-selector=\"portBody\" port=\"88b9086\" port-group=\"out\" class=\"joint-port-body\" id=\"v-924\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-923\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-920\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"33b6036\" port-group=\"in\" class=\"joint-port-body\" id=\"v-922\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-921\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:2--><g model-id=\"63f2adf\" data-type=\"appEnd\" id=\"j_165\" class=\"joint-cell joint-type-append joint-element joint-theme-default\" transform=\"translate(750,843.7551020408164)\"><rect joint-selector=\"rect\" id=\"v-900\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-901\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICA8cGF0aCBpZD0iUmVjdGFuZ2xlXzE3NzVfLV9PdXRsaW5lIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSAxNzc1IC0gT3V0bGluZSIgZD0iTTQsMkEyLDIsMCwwLDAsMiw0VjE0YTIsMiwwLDAsMCwyLDJIMTRhMiwyLDAsMCwwLDItMlY0YTIsMiwwLDAsMC0yLTJINE00LDBIMTRhNCw0LDAsMCwxLDQsNFYxNGE0LDQsMCwwLDEtNCw0SDRhNCw0LDAsMCwxLTQtNFY0QTQsNCwwLDAsMSw0LDBaIiBmaWxsPSIjNzU0N2RiIi8+Cjwvc3ZnPgo=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-902\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">End</tspan></text><g id=\"v-903\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"ff36f51\" port-group=\"in\" class=\"joint-port-body\" id=\"v-905\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-904\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><g model-id=\"45aa3e6\" data-type=\"choiceOption\" id=\"j_166\" class=\"joint-cell joint-type-choiceoption joint-element joint-theme-default\" transform=\"translate(509.5,532.*************)\"><rect joint-selector=\"rect\" id=\"v-926\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" width=\"81\" height=\"60\"/><text joint-selector=\"text\" id=\"v-925\" font-size=\"10\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" y=\"15\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Equals To</tspan></text><rect joint-selector=\"rect1\" id=\"v-927\" width=\"63\" height=\"20\" fill=\"#F2EEEE\" stroke=\"#F2EEEE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" y=\"30\" x=\"5.670000000000001\"/><text joint-selector=\"text1\" id=\"v-928\" font-size=\"9\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" font-weight=\"bold\" y=\"40\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">SMS</tspan></text><g id=\"v-929\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40.5,60)\"><circle joint-selector=\"portBody\" port=\"102b128\" port-group=\"out\" class=\"joint-port-body\" id=\"v-934\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-933\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-930\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"d3a0bd6\" port-group=\"in\" class=\"joint-port-body\" id=\"v-932\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-931\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:3--><g model-id=\"d997799\" data-type=\"http\" id=\"j_167\" class=\"joint-cell joint-type-http joint-element joint-theme-default\" transform=\"translate(1450,230.9387755102041)\"><rect joint-selector=\"rect\" id=\"v-935\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-936\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWdsb2JlIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjIiIHkxPSIxMiIgeDI9IjIyIiB5Mj0iMTIiPjwvbGluZT48cGF0aCBkPSJNMTIgMmExNS4zIDE1LjMgMCAwIDEgNCAxMCAxNS4zIDE1LjMgMCAwIDEtNCAxMCAxNS4zIDE1LjMgMCAwIDEtNC0xMCAxNS4zIDE1LjMgMCAwIDEgNC0xMHoiPjwvcGF0aD48L3N2Zz4=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-937\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Get customer details</tspan></text><g id=\"v-938\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"45d2124\" port-group=\"out\" class=\"joint-port-body\" id=\"v-943\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-942\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-939\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"d7c58e3\" port-group=\"in\" class=\"joint-port-body\" id=\"v-941\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-940\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><g model-id=\"b563cb3\" data-type=\"choiceOption\" id=\"j_168\" class=\"joint-cell joint-type-choiceoption joint-element joint-theme-default\" transform=\"translate(1559.5,532.*************)\"><rect joint-selector=\"rect\" id=\"v-945\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" width=\"81\" height=\"60\"/><text joint-selector=\"text\" id=\"v-944\" font-size=\"10\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" y=\"15\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Equals To</tspan></text><rect joint-selector=\"rect1\" id=\"v-946\" width=\"63\" height=\"20\" fill=\"#F2EEEE\" stroke=\"#F2EEEE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" y=\"30\" x=\"5.670000000000001\"/><text joint-selector=\"text1\" id=\"v-947\" font-size=\"9\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" font-weight=\"bold\" y=\"40\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">WhatsApp</tspan></text><g id=\"v-948\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40.5,60)\"><circle joint-selector=\"portBody\" port=\"8ee4c2f\" port-group=\"out\" class=\"joint-port-body\" id=\"v-953\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-952\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-949\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"8be3690\" port-group=\"in\" class=\"joint-port-body\" id=\"v-951\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-950\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:4--><g model-id=\"2c72000\" data-type=\"choice\" id=\"j_170\" class=\"joint-cell joint-type-choice joint-element joint-theme-default\" transform=\"translate(1450,381.*************)\"><rect joint-selector=\"rect\" id=\"v-884\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-885\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMC45OSIgaGVpZ2h0PSIxNy43NjEiIHZpZXdCb3g9IjAgMCAyMC45OSAxNy43NjEiPgogIDxnIGlkPSJJY29uX2lvbmljLWlvcy1vcHRpb25zIiBkYXRhLW5hbWU9Ikljb24gaW9uaWMtaW9zLW9wdGlvbnMiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0zLjM3NSAtNS42MjUpIj4KICAgIDxwYXRoIGlkPSJQYXRoXzEzNTkiIGRhdGEtbmFtZT0iUGF0aCAxMzU5IiBkPSJNMTYuMDU1LDI1Ljk2MWEyLjAyMSwyLjAyMSwwLDAsMSwzLjcsMGgzLjhhLjgxLjgxLDAsMCwxLC44MDcuODA3aDBhLjgxLjgxLDAsMCwxLS44MDcuODA3aC0zLjhhMi4wMjEsMi4wMjEsMCwwLDEtMy43LDBINC4xODJhLjgxLjgxLDAsMCwxLS44MDctLjgwN2gwYS44MS44MSwwLDAsMSwuODA3LS44MDdaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwIC01LjQwMSkiIGZpbGw9IiM3NTQ3ZGIiLz4KICAgIDxwYXRoIGlkPSJQYXRoXzEzNjAiIGRhdGEtbmFtZT0iUGF0aCAxMzYwIiBkPSJNNy45ODIsMTYuNGEyLjAyMSwyLjAyMSwwLDAsMSwzLjcsMEgyMy41NThhLjgxLjgxLDAsMCwxLC44MDcuODA3aDBhLjgxLjgxLDAsMCwxLS44MDcuODA3SDExLjY4NWEyLjAyMSwyLjAyMSwwLDAsMS0zLjcsMGgtMy44YS44MS44MSwwLDAsMS0uODA3LS44MDdoMGEuODEuODEsMCwwLDEsLjgwNy0uODA3WiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCAtMi43KSIgZmlsbD0iIzc1NDdkYiIvPgogICAgPHBhdGggaWQ9IlBhdGhfMTM2MSIgZGF0YS1uYW1lPSJQYXRoIDEzNjEiIGQ9Ik0xNi4wNTUsNi44MzZhMi4wMjEsMi4wMjEsMCwwLDEsMy43LDBoMy44YS44MS44MSwwLDAsMSwuODA3LjgwN2gwYS44MS44MSwwLDAsMS0uODA3LjgwN2gtMy44YTIuMDIxLDIuMDIxLDAsMCwxLTMuNywwSDQuMTgyYS44MS44MSwwLDAsMS0uODA3LS44MDdoMGEuODEuODEsMCwwLDEsLjgwNy0uODA3WiIgZmlsbD0iIzc1NDdkYiIvPgogIDwvZz4KPC9zdmc+Cg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-886\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Check preferred channel</tspan></text><rect joint-selector=\"rect4\" id=\"v-888\" fill=\"#FFE4EC\" stroke=\"#FFE4EC\" stroke-width=\"1\" x=\"50\" y=\"50\" width=\"80\" height=\"20\" rx=\"6\" ry=\"6\"/><rect joint-selector=\"rect5\" id=\"v-889\" fill=\"#CBFFE7\" stroke=\"#CBFFE7\" stroke-width=\"1\" x=\"-55\" y=\"50\" width=\"60\" height=\"20\" rx=\"6\" ry=\"6\"/><text joint-selector=\"label4\" id=\"v-887\" font-size=\"10\" xml:space=\"preserve\" fill=\"#ED1C24\" text-anchor=\"middle\" pointer-events=\"none\" x=\"90\" y=\"60\"><tspan dy=\"0.3em\" class=\"v-line\">No Match</tspan></text><text joint-selector=\"label5\" id=\"v-890\" font-size=\"10\" xml:space=\"preserve\" fill=\"#075E36\" text-anchor=\"middle\" pointer-events=\"none\" x=\"-25\" y=\"60\"><tspan dy=\"0.3em\" class=\"v-line\">Match</tspan></text><g id=\"v-891\" class=\"joint-port\" transform=\"matrix(1,0,0,1,12,50)\"><circle joint-selector=\"portBody\" port=\"ea0c4ea\" port-group=\"out\" class=\"joint-port-body\" id=\"v-897\" magnet=\"true\" r=\"4\" fill=\"#31D88A\" stroke=\"#31D88A\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-896\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-892\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40,50)\"><circle joint-selector=\"portBody\" port=\"bd06a59\" port-group=\"out\" class=\"joint-port-body\" id=\"v-899\" magnet=\"true\" r=\"4\" fill=\"red\" stroke=\"red\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-898\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-893\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"7d6d374\" port-group=\"in\" class=\"joint-port-body\" id=\"v-895\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-894\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><g model-id=\"a9b545a\" data-type=\"choiceOption\" id=\"j_169\" class=\"joint-cell joint-type-choiceoption joint-element joint-theme-default\" transform=\"translate(1209.5,532.*************)\"><rect joint-selector=\"rect\" id=\"v-955\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" width=\"81\" height=\"60\"/><text joint-selector=\"text\" id=\"v-954\" font-size=\"10\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" y=\"15\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">Equals To</tspan></text><rect joint-selector=\"rect1\" id=\"v-956\" width=\"63\" height=\"20\" fill=\"#F2EEEE\" stroke=\"#F2EEEE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"6\" ry=\"6\" y=\"30\" x=\"5.670000000000001\"/><text joint-selector=\"text1\" id=\"v-957\" font-size=\"9\" xml:space=\"preserve\" text-anchor=\"middle\" fill=\"#7547DB\" font-weight=\"bold\" y=\"40\" x=\"40.5\"><tspan dy=\"0.3em\" class=\"v-line\">RCS</tspan></text><g id=\"v-958\" class=\"joint-port\" transform=\"matrix(1,0,0,1,40.5,60)\"><circle joint-selector=\"portBody\" port=\"8741b60\" port-group=\"out\" class=\"joint-port-body\" id=\"v-963\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-962\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-959\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"e79892a\" port-group=\"in\" class=\"joint-port-body\" id=\"v-961\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-960\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:5--><g model-id=\"df14331\" data-type=\"sms\" id=\"j_171\" class=\"joint-cell joint-type-sms joint-element joint-theme-default\" transform=\"translate(400,692.*************)\"><rect joint-selector=\"rect\" id=\"v-964\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-965\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLW1lc3NhZ2Utc3F1YXJlIj48cGF0aCBkPSJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6Ij48L3BhdGg+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-966\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">SMS</tspan></text><g id=\"v-967\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"3b4ae20\" port-group=\"out\" class=\"joint-port-body\" id=\"v-972\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-971\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-968\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"d9dfea7\" port-group=\"in\" class=\"joint-port-body\" id=\"v-970\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-969\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:6--><g model-id=\"12e33f7\" data-type=\"email\" id=\"j_172\" class=\"joint-cell joint-type-email joint-element joint-theme-default\" transform=\"translate(750,692.*************)\"><rect joint-selector=\"rect\" id=\"v-973\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-974\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLW1haWwiPjxwYXRoIGQ9Ik00IDRoMTZjMS4xIDAgMiAuOSAyIDJ2MTJjMCAxLjEtLjkgMi0yIDJINGMtMS4xIDAtMi0uOS0yLTJWNmMwLTEuMS45LTIgMi0yeiI+PC9wYXRoPjxwb2x5bGluZSBwb2ludHM9IjIyLDYgMTIsMTMgMiw2Ij48L3BvbHlsaW5lPjwvc3ZnPg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-975\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Email</tspan></text><g id=\"v-976\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"d3e055d\" port-group=\"out\" class=\"joint-port-body\" id=\"v-981\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-980\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-977\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"a7f60f0\" port-group=\"in\" class=\"joint-port-body\" id=\"v-979\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-978\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:7--><g model-id=\"ffdaa16\" data-type=\"rcs\" id=\"j_173\" class=\"joint-cell joint-type-rcs joint-element joint-theme-default\" transform=\"translate(1100,692.*************)\"><rect joint-selector=\"rect\" id=\"v-982\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-983\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MC41OSIgaGVpZ2h0PSIzNi4zMzIiIHZpZXdCb3g9IjAgMCA1MC41OSAzNi4zMzIiPgogIDxwYXRoIGlkPSJVbmlvbl81MyIgZGF0YS1uYW1lPSJVbmlvbiA1MyIgZD0iTTE1LjU2NCwzNi4zMzNhNi42ODgsNi42ODgsMCwwLDEtNi42ODEtNi42ODF2LTE3LjZMLjY1NiwzLjgyM0EyLjIzOSwyLjIzOSwwLDAsMSwyLjIzOSwwSDQzLjkwN2E2LjY4OSw2LjY4OSwwLDAsMSw2LjY4Miw2LjY4MnYyMi45N2E2LjY4OSw2LjY4OSwwLDAsMS02LjY4Miw2LjY4MVpNMTIuNzA2LDkuNTM5YTIuMjQ0LDIuMjQ0LDAsMCwxLC42NTYsMS41ODRWMjkuNjUyYTIuMiwyLjIsMCwwLDAsMi4yLDIuMkg0My45MDdhMi4yMDUsMi4yMDUsMCwwLDAsMi4yLTIuMlY2LjY4MmEyLjIwNSwyLjIwNSwwLDAsMC0yLjItMi4ySDcuNjQ2Wm04LjQ0OCwxNy42MjZhMiwyLDAsMCwxLDAtNEgzMC40NmEyLDIsMCwxLDEsMCw0Wm0wLTdhMi4wMDUsMi4wMDUsMCwwLDEsMC00SDM4LjgzNmEyLjAwNSwyLjAwNSwwLDAsMSwwLDRabTAtN2ExLjkzNCwxLjkzNCwwLDAsMS0xLjg2MS0yLDEuOTM1LDEuOTM1LDAsMCwxLDEuODYxLTJIMzguODM2YTEuOTM1LDEuOTM1LDAsMCwxLDEuODYxLDIsMS45MzQsMS45MzQsMCwwLDEtMS44NjEsMloiIGZpbGw9IiM3NTQ3ZGIiLz4KPC9zdmc+Cg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-984\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">RCS</tspan></text><g id=\"v-985\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"f0baba1\" port-group=\"out\" class=\"joint-port-body\" id=\"v-990\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-989\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-986\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"9bca51e\" port-group=\"in\" class=\"joint-port-body\" id=\"v-988\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-987\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:8--><g model-id=\"5b398df\" data-type=\"whatsapp\" id=\"j_174\" class=\"joint-cell joint-type-whatsapp joint-element joint-theme-default\" transform=\"translate(1450,692.*************)\"><rect joint-selector=\"rect\" id=\"v-991\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-992\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMSIgaGVpZ2h0PSIyMSIgdmlld0JveD0iMCAwIDIxIDIxIj4KICA8ZyBpZD0iR3JvdXBfMjY2OCIgZGF0YS1uYW1lPSJHcm91cCAyNjY4IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjQyNiAwLjQyOSkiPgogICAgPHBhdGggaWQ9IlVuaW9uXzYiIGRhdGEtbmFtZT0iVW5pb24gNiIgZD0iTS4zMDgsMjAuNjg5YTEuMDQ1LDEuMDQ1LDAsMCwxLS4yNTItMS4wODFsMS44NTUtNS41NzdBOS45NzEsOS45NzEsMCwwLDEsNi41NjQsMS4wNTYsOS44NjQsOS44NjQsMCwwLDEsMTEsMGguNkE5Ljk0Nyw5Ljk0NywwLDAsMSwyMSw5LjM5di41ODRhOS45ODUsOS45ODUsMCwwLDEtNC43MzIsOC40ODYsMTAuMDI1LDEwLjAyNSwwLDAsMS01LjI0MiwxLjQ5M0gxMWE5Ljk0LDkuOTQsMCwwLDEtNC4wMzQtLjg2NEwxLjM5MiwyMC45NDVhMS4wNTMsMS4wNTMsMCwwLDEtMS4wODQtLjI1NlptNy4yLTE3Ljc0NGE3LjgzLDcuODMsMCwwLDAtNC4zNDksNy4wM0E3Ljc1MSw3Ljc1MSwwLDAsMCw0LDEzLjQ4M2ExLjA2MSwxLjA2MSwwLDAsMSwuMDYuODEyTDIuNzI4LDE4LjI2OCw2LjcsMTYuOTQ0YTEuMDUxLDEuMDUxLDAsMCwxLC44MDguMDZBNy43NzgsNy43NzgsMCwwLDAsMTEsMTcuODM2aC4wMjFhNy44Nyw3Ljg3LDAsMCwwLDcuODYxLTcuODU4di0uNWE3LjgyOSw3LjgyOSwwLDAsMC03LjM2NS03LjM2NkgxMUE3Ljc3NCw3Ljc3NCwwLDAsMCw3LjUxMywyLjk0NFpNNi41NjQsNi41YS40MDcuNDA3LDAsMCwxLC4zMi0uNGwxLjc4MS0uNDEyYS40MTEuNDExLDAsMCwxLC40NjkuMjM2bC44MjMsMS45MTZhLjQxNi40MTYsMCwwLDEtLjEyLjQ4TDguOCw5LjE2NkE2LjM0OCw2LjM0OCwwLDAsMCwxMS44MywxMi4ybC44NDktMS4wNGEuNDEzLjQxMywwLDAsMSwuNDgtLjExNmwxLjkxNy44MmEuNDE5LjQxOSwwLDAsMSwuMjQuNDcybC0uNDEyLDEuNzhhLjQwNi40MDYsMCwwLDEtLjQuMzE2QTcuOTM2LDcuOTM2LDAsMCwxLDYuNTY0LDYuNVoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0wLjQyNiAtMC40MjgpIiBmaWxsPSIjNzU0N2RiIi8+CiAgPC9nPgo8L3N2Zz4K\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-993\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Whatsapp</tspan></text><g id=\"v-994\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"c999f26\" port-group=\"out\" class=\"joint-port-body\" id=\"v-999\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-998\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-995\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"b4ee4fb\" port-group=\"in\" class=\"joint-port-body\" id=\"v-997\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-996\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:9--></g><g class=\"joint-labels-layer joint-viewport\"/><g class=\"joint-front-layer\"/><g class=\"joint-tools-layer\"><!--z-index:1--></g></g></svg>", "OTC": "10", "MRC": "100", "channels": 3, "nodes": 8, "freeNodeExec": 10, "nodeExecCharge": "100", "localeData": [{"id": 1696, "appId": "95dd9b60-a270-11ef-b4ae-7f557da0bb13", "locale": "en", "translation": "eJx1jjEOwyAMRe/yZwbWZmftBaIqQkAjpIArDF0i7l7DEmXoZL3n/2Wf4OZcYMZywpEPWNaXQhJjdwFAIYt+2jTIZI+u4BpXSqYUKlJc/zS7oA9v2446o9cJmIfWGvf0oNy2GusRxjsiOPGNZyDbb9xtjZQ3+owxl/0HYThEog==", "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-14 15:39:50", "updatedAt": "2024-11-14 15:44:15", "deletedAt": null}]}, {"id": "8ffd7860-a288-11ef-b4ae-7f557da0bb13", "name": "Lab Report Notification", "desc": null, "type": "healthcare", "image": "healthcare", "status": "0", "owner": 0, "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-15 05:31:28", "updatedAt": "2024-11-15 00:03:17", "appData": {"links": [], "startId": "b789727", "modules": {"8865412": {"settings": {"account": "", "nodeName": "Send via SMS"}, "input": {}, "process": {"receiverAddress": "", "text_message": "", "senderAddress": "", "moduleName": "SMS"}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"82c32f3\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["82c32f3"]}}, "coordinates": {"x": 480, "y": 240, "nodeData": {"title": "SMS", "name": "sms", "id": "8865412", "isEditable": true, "canDelete": false, "status": "", "moduleType": "sms"}}, "type": "sms", "typeId": "1.1"}, "b789727": {"settings": {"aparty": "*********", "nodeName": "Start"}, "process": {"cronjob": "5 * * * *", "params": ["MSISDN"], "trigger": "HTTP Trigger"}, "output": {"conditions": {"82a27eabb4": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"7421b42\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["7421b42"]}}, "input": {}, "type": "appStart", "typeId": "0.1", "coordinates": {"x": 480, "y": 30, "nodeData": {"title": "Start", "name": "appStart", "id": "b789727", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appStart"}}}, "63f2adf": {"settings": {}, "process": {"success": {"code": [], "message": "", "nodeName": "End"}, "customErrors": [{"code": [], "message": ""}], "defaultError": {"code": "E9000", "message": ""}}, "output": {"conditions": {}}, "input": {}, "type": "appEnd", "typeId": "0.2", "coordinates": {"x": 480, "y": 460, "nodeData": {"title": "End", "name": "appEnd", "id": "63f2adf", "isEditable": true, "canDelete": false, "status": "", "moduleType": "appEnd"}}}, "7421b42": {"settings": {"timeout": 10000, "title": "HTTP", "nodeName": "Get Lab Report URL", "image": ""}, "input": {}, "process": {"URL": "", "requestType": "", "headers": [{"headerKey": "", "headerValue": ""}], "requestBody": "", "responseCache": "", "callReference": "", "responseType": "", "Value": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n    // main function is the default method executed after processing current module\n    function main(){\n      return \"8865412\";// return end moduleId\n    }", "customCodeIds": {"conditionalLink": ["8865412"]}}, "coordinates": {"x": 480, "y": 140, "nodeData": {"title": "HTTP", "name": "http", "id": "7421b42", "isEditable": true, "canDelete": false, "status": "", "moduleType": "http"}}, "type": "http", "typeId": "1.2"}, "82c32f3": {"settings": {"nodeName": "Send via Email"}, "input": {}, "process": {"body": "", "senderID": "", "subject": "", "receiverAddress": ""}, "output": {"codeModuleMapping": [{"code": "200", "moduleId": "aaaaaa"}, {"code": "400", "moduleId": "aaaaab"}], "conditions": {"8ae5937d71": {"statement": [{"expr": ["", "eq", ""]}], "fallbackcode": "", "isActive": true}}, "fallbackcode": "", "codeActive": true, "customCode": "// Custom code panel\n// main function is the default method executed after processing current module\nfunction main(){\n  return \"84d6645\";// return end moduleId\n}", "customCodeIds": {"conditionalLink": []}}, "coordinates": {"x": 480, "y": 350, "nodeData": {"title": "Email", "name": "email", "id": "82c32f3", "isEditable": true, "canDelete": false, "status": "", "moduleType": "email"}}, "type": "email", "typeId": "1.2"}}, "errors": {"b789727": [{"parameter": "method", "path": "settings", "severity": "error", "msg": "should have required property 'method'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "samplePayload", "path": "settings", "severity": "error", "msg": "should have required property 'samplePayload'"}, {"parameter": "contentType", "path": "settings", "severity": "error", "msg": "should have required property 'contentType'"}, {"parameter": "settings", "path": "", "severity": "warn", "msg": "should match exactly one schema in oneOf"}], "63f2adf": [{"parameter": "code", "path": "process.success", "severity": "warn", "msg": "should NOT have fewer than 1 items"}, {"parameter": "message", "path": "process.success", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}, {"parameter": "message", "path": "process.defaultError", "severity": "warn", "msg": "should NOT be shorter than 1 characters"}]}, "version": "0.7.23", "id": "8ffd7860-a288-11ef-b4ae-7f557da0bb13"}, "ngage_id": "9a7f854e-fbea-4aca-b7c2-243ffe1f255d", "alignment": "vertical", "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" joint-selector=\"svg\" id=\"v-1587\" style=\"overflow: hidden;\"><defs joint-selector=\"defs\"><marker id=\"v-1587-1198995455\" orient=\"auto\" overflow=\"visible\" markerUnits=\"userSpaceOnUse\"><path id=\"v-1639\" stroke=\"#7547DB\" fill=\"#7547DB\" transform=\"rotate(180)\" d=\"M 10 -5 0 0 10 5 z\"/></marker></defs><g joint-selector=\"layers\" class=\"joint-layers\" transform=\"matrix(1,0,0,1,562.5,234)\"><g class=\"joint-back-layer\"/><g class=\"joint-cells-layer joint-viewport\"><g model-id=\"133eae2\" data-type=\"standard.Link\" id=\"j_283\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1726\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 505 84 L 505 136\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1725\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 505 84 L 505 136\" marker-end=\"url(#v-1587-1198995455)\"/></g><g model-id=\"a419569\" data-type=\"standard.Link\" id=\"j_284\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1728\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 505 194 L 505 236\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1727\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 505 194 L 505 236\" marker-end=\"url(#v-1587-1198995455)\"/></g><g model-id=\"a242ac89-17f6-413f-9fd8-0ab3636249bf\" data-type=\"standard.Link\" id=\"j_290\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1730\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 505 294 L 505 346\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1729\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 505 294 L 505 346\" marker-end=\"url(#v-1587-1198995455)\"/></g><g model-id=\"e6ddc34a-4848-497e-8210-3745b1c23cbf\" data-type=\"standard.Link\" id=\"j_291\" class=\"joint-cell joint-type-standard joint-type-standard-link joint-link joint-theme-default\" style=\"\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" joint-selector=\"wrapper\" id=\"v-1732\" stroke-width=\"10\" stroke-linejoin=\"round\" d=\"M 505 404 L 505 456\"/><path fill=\"none\" pointer-events=\"none\" joint-selector=\"line\" id=\"v-1731\" stroke=\"#7547DB\" stroke-width=\"1\" stroke-linejoin=\"round\" class=\"animate-link\" d=\"M 505 404 L 505 456\" marker-end=\"url(#v-1587-1198995455)\"/></g><!--z-index:1--><g model-id=\"8865412\" data-type=\"sms\" id=\"j_285\" class=\"joint-cell joint-type-sms joint-element joint-theme-default\" transform=\"translate(480,240)\"><rect joint-selector=\"rect\" id=\"v-1683\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1684\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLW1lc3NhZ2Utc3F1YXJlIj48cGF0aCBkPSJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6Ij48L3BhdGg+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1685\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Send via SMS</tspan></text><g id=\"v-1686\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"4788551\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1691\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1690\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1687\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"8c21c5a\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1689\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1688\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:2--><g model-id=\"b789727\" data-type=\"appStart\" id=\"j_286\" class=\"joint-cell joint-type-appstart joint-element joint-theme-default\" transform=\"translate(480,30)\"><rect joint-selector=\"rect\" id=\"v-1692\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1693\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXBsYXkiPjxwb2x5Z29uIHBvaW50cz0iNSAzIDE5IDEyIDUgMjEgNSAzIj48L3BvbHlnb24+PC9zdmc+\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1694\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Start</tspan></text><g id=\"v-1695\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"b4eff1c\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1700\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1699\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1696\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"645cf48\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1698\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1697\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:3--><g model-id=\"63f2adf\" data-type=\"appEnd\" id=\"j_287\" class=\"joint-cell joint-type-append joint-element joint-theme-default\" transform=\"translate(480,460)\"><rect joint-selector=\"rect\" id=\"v-1701\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1702\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICA8cGF0aCBpZD0iUmVjdGFuZ2xlXzE3NzVfLV9PdXRsaW5lIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSAxNzc1IC0gT3V0bGluZSIgZD0iTTQsMkEyLDIsMCwwLDAsMiw0VjE0YTIsMiwwLDAsMCwyLDJIMTRhMiwyLDAsMCwwLDItMlY0YTIsMiwwLDAsMC0yLTJINE00LDBIMTRhNCw0LDAsMCwxLDQsNFYxNGE0LDQsMCwwLDEtNCw0SDRhNCw0LDAsMCwxLTQtNFY0QTQsNCwwLDAsMSw0LDBaIiBmaWxsPSIjNzU0N2RiIi8+Cjwvc3ZnPgo=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1703\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">End</tspan></text><g id=\"v-1704\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"e435a04\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1706\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1705\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:4--><g model-id=\"7421b42\" data-type=\"http\" id=\"j_288\" class=\"joint-cell joint-type-http joint-element joint-theme-default\" transform=\"translate(480,140)\"><rect joint-selector=\"rect\" id=\"v-1707\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#B499EE\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow( 0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1708\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWdsb2JlIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjIiIHkxPSIxMiIgeDI9IjIyIiB5Mj0iMTIiPjwvbGluZT48cGF0aCBkPSJNMTIgMmExNS4zIDE1LjMgMCAwIDEgNCAxMCAxNS4zIDE1LjMgMCAwIDEtNCAxMCAxNS4zIDE1LjMgMCAwIDEtNC0xMCAxNS4zIDE1LjMgMCAwIDEgNC0xMHoiPjwvcGF0aD48L3N2Zz4=\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1709\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Get Lab Report URL</tspan></text><g id=\"v-1710\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"09a9126\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1715\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1714\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1711\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"7fe382a\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1713\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1712\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:5--><g model-id=\"82c32f3\" data-type=\"email\" id=\"j_289\" class=\"joint-cell joint-type-email joint-element joint-theme-default\" transform=\"translate(480,350)\"><rect joint-selector=\"rect\" id=\"v-1716\" width=\"50\" height=\"50\" fill=\"#FFFFFF\" stroke=\"#7547DB\" stroke-width=\"1\" shadow-color=\"red\" shadow-blur=\"20\" filter=\"drop-shadow(0px 0px 10px #53535333)\" rx=\"15\" ry=\"15\"/><image joint-selector=\"image\" id=\"v-1717\" xlink:href=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM3NTQ3REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLW1haWwiPjxwYXRoIGQ9Ik00IDRoMTZjMS4xIDAgMiAuOSAyIDJ2MTJjMCAxLjEtLjkgMi0yIDJINGMtMS4xIDAtMi0uOS0yLTJWNmMwLTEuMS45LTIgMi0yeiI+PC9wYXRoPjxwb2x5bGluZSBwb2ludHM9IjIyLDYgMTIsMTMgMiw2Ij48L3BvbHlsaW5lPjwvc3ZnPg==\" width=\"20\" height=\"20\" x=\"16\" y=\"16\"/><text joint-selector=\"text\" id=\"v-1718\" font-size=\"12\" xml:space=\"preserve\" y=\"29\" text-anchor=\"start\" fill=\"#7547DB\" x=\"60\"><tspan dy=\"0\" class=\"v-line\">Send via Email</tspan></text><g id=\"v-1719\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,50)\"><circle joint-selector=\"portBody\" port=\"4948cc3\" port-group=\"out\" class=\"joint-port-body\" id=\"v-1724\" magnet=\"true\" r=\"4\" fill=\"#7547DB\" stroke=\"#7547DB\" x=\"-8\" y=\"-8\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1723\" y=\".3em\" text-anchor=\"start\" transform=\"matrix(1,0,0,1,15,6)\"/></g><g id=\"v-1720\" class=\"joint-port\" transform=\"matrix(1,0,0,1,25,0)\"><circle joint-selector=\"portBody\" port=\"9979a76\" port-group=\"in\" class=\"joint-port-body\" id=\"v-1722\" magnet=\"true\" r=\"4\" fill=\"transparent\" stroke=\"transparent\"/><text joint-selector=\"label\" class=\"label-text joint-port-label\" id=\"v-1721\" y=\"0\" text-anchor=\"middle\" transform=\"matrix(1,0,0,1,25,50)\"/></g></g><!--z-index:6--><!--z-index:7--></g><g class=\"joint-labels-layer joint-viewport\"/><g class=\"joint-front-layer\"/><g class=\"joint-tools-layer\"><g class=\"joint-tools joint-theme-default\"><g class=\"joint-tool joint-theme-default\" data-tool-name=\"button\" model-id=\"63f2adf\" transform=\"matrix(1,0,0,1,525.0000305175781,464)\"><circle r=\"7\" fill=\"#FF1D00\" cursor=\"pointer\" joint-selector=\"button\"/><path d=\"M -3 -3 3 3 M -3 3 3 -3\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" pointer-events=\"none\" joint-selector=\"icon\"/></g></g><!--z-index:1--></g></g></svg>", "OTC": "10", "MRC": "100", "channels": 2, "nodes": 5, "freeNodeExec": 10, "nodeExecCharge": "100", "localeData": [{"id": 1703, "appId": "8ffd7860-a288-11ef-b4ae-7f557da0bb13", "locale": "en", "translation": "eJx1jjEOwyAMRe/yZwbWZmftBaIqQkAjpIArDF0i7l7DEmXoZL3n/2Wf4OZcYMZywpEPWNaXQhJjdwFAIYt+2jTIZI+u4BpXSqYUKlJc/zS7oA9v2446o9cJmIfWGvf0oNy2GusRxjsiOPGNZyDbb9xtjZQ3+owxl/0HYThEog==", "createdBy": 0, "modifiedBy": 0, "createdAt": "2024-11-14 18:31:28", "updatedAt": "2024-11-14 18:33:17", "deletedAt": null}]}]