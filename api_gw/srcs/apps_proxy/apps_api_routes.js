"use strict";
/**
 * List of routes for applications listing and management.
 */
const AppProxy = require("./apps_impl");
const Router = require("express").Router();
const AppValidator = require("app_validation");
const c4b = require("./session").check4Blacklist;
const rid = require("../leap_resources");
const authorizer = require("authorizer");
let types = authorizer.accessTypes;

Router.get("/app/templates", AppProxy.getAppTemplates);
Router.get("/app/templates/type", AppProxy.getAppTemplatesType);
Router.post("/app/templates/clone/", c4b, AppValidator.validateCloneAppTemplate, AppProxy.cloneAppTemplate);
Router.get("/app/templates/:type", AppProxy.getAppTemplatesByType)
Router.get("/app/locale/list/:appId", AppProxy.listAppLocale);
Router.post("/app/locale/:localeId/delete", AppProxy.deleteAppsLocale);
Router.get("/app/locale/:appId/:locale", AppProxy.downloadAppLocale);
// IT Admin is not having write permission to APPS, this is one special case ITAdmin shpuld be able to assign apps
Router.get("/app/copyurl/:appId", AppProxy.copyAppUrl);
Router.get("/app/export/:appId", AppProxy.exportApp);
Router.post("/app/import", c4b, AppProxy.importApp);
Router.get("/app/compress/:appId", AppProxy.compressedApp);

module.exports = Router;