/**
 * @file routes.js
 * @description List of routes for applications listing and management. 
 * This file defines various endpoints for managing applications, including 
 * creating, cloning, updating, and deleting app information.
 *
 * @requires express.Router - Express router for handling HTTP requests.
 * @requires apps_impl - Module responsible for implementing app-related logic.
 * @requires app_validation - Middleware for validating app data.
 * @requires session - Module for session management, specifically checking blacklist status.
 * @requires leap_resources - Resources related to the Leap platform.
 * @requires authorizer - Module for handling authorization and access types.
 * @requires loggingMiddleware - Middleware for logging requests.
 *
 * @version 1.0.0
 * @date 2024-09-26
 * <AUTHOR>
 */

// Import required modules
const AppProxy = require("./apps_impl");
const Router = require("express").Router();
const AV = require("app_validation");
const c4b = require("./session").check4Blacklist;
const rid = require("../leap_resources");
const authorizer = require("authorizer");
const log = require("./loggingMiddleware");
const types = authorizer.accessTypes;

// Define routes
Router.get("/", c4b, log, AppProxy.getApps);
Router.get("/getAppsForAdmin", c4b, AppProxy.getAppsForAdmin);
Router.get("/getAppsForPlatform", c4b, AppProxy.getAppsForPlatform);
Router.get("/eventsperapp/", AppProxy.getCommentsPerApp);
Router.post("/getTriggerEndpoints", c4b, AppProxy.getTriggerEndpoints);
Router.post("/clone/:appId", c4b, AV.validateCloneApp, AppProxy.cloneApp);
Router.get("/:appId", c4b, log, AppProxy.getAppInfo);
Router.post("/", c4b, AV.validateCreateAppInfo, AppProxy.checkForDuplicateName, log, AppProxy.createAppInfo);
Router.post("/ai", c4b, AppProxy.createAppWithAI);
Router.get("/ai/getQueries", c4b, AppProxy.getQueries);
Router.post("/:appId/update", c4b, log, AppProxy.updateAppInfo);
Router.post("/:appId/delete", c4b, log, AppProxy.purgeAppInfo);
Router.post("/:appId/snapshot", AppProxy.takeAppSnapshot);
Router.get("/listAppVersions/:appId", AppProxy.listAppVersions);
Router.post("/getAppSnapshot/:appId", AppProxy.getAppSnapshot);
Router.post("/updateAppSnapshot/:appId", AppProxy.updateAppSnapshot);
Router.post("/updateOTCAndMRC", AppProxy.updateOTCAndMRC);

Router.post("/validate/:moduleId", AV.validate);

// Export router
module.exports = Router;
