"use strict";
/**
 * Implements the api for applications management.
 **/

//api file

const logger = global.logger;

const AppStore = require("app_store");
const AppValidation = require("app_validation");
const message = require("message");
const common = require("common");
const fs = require("fs");
const multer = require("multer");
const path = require("path");
const jsondiffpatch = require("jsondiffpatch");
const semver = require("semver");
const axios = require('axios');
const https = require('https');

const DEFAULT_PAGESIZE = 10;
const error_codes = message.error_codes;
const HTTP = common.http_codes;
const appStates = common.app_states;
const userRoles = common.user_roles_default;
const whiteboard = common.whiteboard;

const uploadLocaleDir = path.join(__dirname, "locale");
const uploadAppsDir = path.join(__dirname, "appdump");
const locales = require("./locales.json");
const utility = require("utility");
const jwt = require('jsonwebtoken');
const ReportsImpl = require("../reports_proxy/appDashboard");

const eventsCodeMap = {
    "-1": "Application CREATED",
    0: "Saved",
    1: "Updated",
    2: "SUBMITTED for Approval",
    3: "Reject",
    4: "WITHDRAWN from Approval request",
    5: "Approved for STAGING",
    6: "Application STAGED",
    7: "SCHEDULED for launch",
    8: "LAUNCHED",
    9: "RETIRED",
    10: "Deleted"
};

const WebSocket = require("ws");

/**
 * List of business level error codes
 **/

module.exports = {


    /**
     * @api {get} /apps AppList
     *
     * @apiDescription The scope of applications retrieved by this api
     *       depends on who is asking.  If an AppDeveloper role is invoking
     *       this api, it will return a list of apps created by him/her.
     *       <p>
     *       On the other hand, if an administrator role invokes this api, an entire list of
     *       apps is given, regardless of who created.
     *       </p>
     *
     * <h2> Note </h2>
     *   The api will provide link headers to allow GUI to support page navigation.
     *   For more info about pagination, look
     *   <ul>
     *   <li> <a href="https://developer.github.com/v3/guides/traversing-with-pagination/">here</a> </li>
     *   <li>and <a href="https://stackoverflow.com/questions/12168624/pagination-response-payload-from-a-restful-api">here.</a> </li>
     *
     * @apiName  List of Apps
     * @apiGroup Application Management
     *
     * @apiParam {String} token //  Search token
     * @apiParam {Integer} page // Page number
     * @apiParam {Integer} size // Page size
     * @apiParam {String} sortf // Sort field based on App's name, status, createdAt, updatedAt.
     * @apiParam {String} order // Sorting order asc or desc
     * @apiParam {String} user // filter based on the user loginid
     *
     * @apiSuccessExample  Success-Response:
     * HTTP/1.1 200 OK
     * [
     *   {
     *     "id": "5937583693",
     *     "name" : "Our first Beautiful App",
     *     "description" : "A demo application to illustrate our first trial at getting hands dirty.",
     *     "createdAt" : "",       // Time in UTC millis
     *     "createdBy" : 1234,     // id of the person who created this app
     *     "updatedAt" : "",       // Time in UTC millis
     *     "updatedBy" : "1234",    // id of the person who last updated it.
     *     "status": "5",
     *   },
     *   { ... }
     * ]
     *
     * @apiError InvalidCredentials The username and/or password were incorrect.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  401 Invalid Authorization
     *   {
     *     "code" : 601
     *     "msg": "Invalid username or password",
     *   }
     *
     * @apiError InvalidJWT  The JSON Web Token submitted to this endpoint is invalid, or cannot be verified.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  401 Invalid Authorization
     *   {
     *     "code" : 604
     *     "msg": "Invalid web session token.",
     *   }
     *
     * @apiError NotAuthorized to user.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  403 Forbidden
     *   {
     *     "error": "Unauthorized",
     *     "code" : 6XX
     *   }
     *
     * @apiError InternalError
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  503 Bad Gateway
     *   {
     *     msg: "Internal Error",
     *     code : 701
     *   }
     *
     * @apiError AppStoreError, AppStore is not reachable/Database is down.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  503 Bad Gateway
     *   {
     *     msg: "App Store not reachable",
     *     code : 702
     *   }
     *
     * @apiError BadInput for size field.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  400 Bad Request
     *   {
     *     msg: "Bad input for size field",
     *     code : 703
     *   }
     *
     * @apiError BadInput for sortf field.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  400 Bad Request
     *   {
     *     msg: "Bad input for sortf field",
     *     code : 704
     *   }
     *
     * @apiError BadInput for order field.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  400 Bad Request
     *   {
     *     msg: "Bad input for order field",
     *     code : 705
     *   }
     *
     */
    listAppVersions: async (req, res) => {
        try {
            logger.trace("Retreiving list of Apps..", req.params.appId);
            let response = await AppStore.listAppVersions(req.params.appId);
            return res.status(HTTP.ok.code).json(response);
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    getAppSnapshot: async (req, res) => {
        try {
            logger.trace("Retreiving app snapshot of App:", req.params.appId, ", with version:", req.body.version);
            let response = await AppStore.findAppVersion(req.params.appId, req.body.version);
            return res.status(HTTP.ok.code).json(response);
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    updateAppSnapshot: async (req, res) => {
        try {
            let response = await AppStore.updateAppVersion(req.body);
            return res.status(HTTP.ok.code).json({
                "message": "App version updated successfully"
            });
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    getApps: async (req, res) => {
        try {
            logger.trace("Retreiving list of Apps..", req.query);
            let opts = prepareListQuery(req);
            opts.ngage_id = [req.invoker.sub];
            let data = await AppStore.listApps(opts);
            const response = preparePaginationHeaders(req, opts, data.count);
            if (response.data.currentPage > response.data.totalPages) {
                return res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.pageDoesNotExists));
            }

            if (opts.isCountRequired == null || opts.isCountRequired == "false" || opts.isCountRequired == false) {
                response.data.appList = data.rows;
                for (let item of response.data.appList) {
                    item.triggers = await ReportsImpl.getTotalTriggers(item.id);
                }
            } else {
                response.data.appList = await getAppsWithEvents(req, data.rows);
                for (let item of response.data.appList) {
                    item.triggers = await ReportsImpl.getTotalTriggers(item.id);
                }
            }
            return res.status(HTTP.ok.code).json(response);
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },
    getAppsForAdmin: async (req, res) => {
        try {
            logger.trace("Retrieving list of Apps..", req.query, " for the admin:" + req.invoker.sub);
            let opts = prepareListQuery(req);
            let users = [req.invoker.sub]
            let url = global.config.pm_fsync.pluginData.ngageBaseUrl + "/api/v3/accounts/users";
            opts.ngage_id = users;

            // Make the axios GET call
            const axiosResponse = await axios.get(url, { //NOSONAR
                headers: {
                    'Authorization': req.headers["authorization"]
                },
                httpsAgent: new https.Agent({
                    rejectUnauthorized: false
                }),
                timeout: 20000
            });

            // Update the users array based on the response
            const responseData = axiosResponse.data;
            if (Array.isArray(responseData.data)) {
                responseData.data.forEach(item => {
                    if (item.id) {
                        users.push(item.id);
                    }
                });
            }

            let data = await AppStore.listApps(opts);
            const response = preparePaginationHeaders(req, opts, data.count);
            if (response.data.currentPage > response.data.totalPages) {
                return res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.pageDoesNotExists));
            }

            if (opts.isCountRequired == null || opts.isCountRequired == "false" || opts.isCountRequired == false) {
                response.data.appList = data.rows;
                for (let item of response.data.appList) {
                    item.triggers = await ReportsImpl.getTotalTriggers(item.id);
                }
            } else {
                response.data.appList = await getAppsWithEvents(req, data.rows);
                for (let item of response.data.appList) {
                    item.triggers = await ReportsImpl.getTotalTriggers(item.id);
                }
            }
            return res.status(HTTP.ok.code).json(response);
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    getAppsForPlatform: async (req, res) => {
        try {
            logger.trace("Retreiving list of Apps..", req.query, " for the admin:" + req.invoker.sub);
            let uniqueNgageIds = await AppStore.listUniqueIds();
            const baseUrl = global.config.pm_fsync.pluginData.ngageBaseUrl + '/api/v1/accounts/users/';
            const timeout = 20000; // 20 seconds
            const agent = new https.Agent({
                rejectUnauthorized: false
            });
            const requests = uniqueNgageIds.map(async (item) => {
                const url = `${baseUrl}${item.ngage_id}`;
                try {
                    const response = await axios.get(url, {
                        headers: {
                            'Authorization': req.headers["authorization"]
                        },
                        timeout: timeout,
                        httpsAgent: agent
                    });
                    return {
                        ngage_id: item.ngage_id,
                        email: response.data.email,
                        organisation: response.data.organisation,
                        username: response.data.name
                    };
                } catch (error) {
                    console.error(`Error fetching data for ngage_id: ${item.ngage_id}`, error);
                    return {
                        ngage_id: item.ngage_id,
                        email: "-",
                        organisation: "-",
                        username: "-",

                    };
                }
            });
            const result = await Promise.all(requests);
            let opts = prepareListQuery(req);
            let data = await AppStore.listApps(opts);
            const response = preparePaginationHeaders(req, opts, data.count);
            if (response.data.currentPage > response.data.totalPages) {
                return res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.pageDoesNotExists));
            }

            if (opts.isCountRequired == null || opts.isCountRequired == "false" || opts.isCountRequired == false) {
                response.data.appList = data.rows;
                for (let item of response.data.appList) {
                    item.triggers = await ReportsImpl.getTotalTriggers(item.id);
                }
            } else {
                response.data.appList = await getAppsWithEvents(req, data.rows);
                for (let item of response.data.appList) {
                    item.triggers = await ReportsImpl.getTotalTriggers(item.id);
                }
            }
            const ngageMap = new Map(result.map(item => [item.ngage_id, { email: item.email, organisation: item.organisation, username: item.username }]));

            // Modify the response object
            response.data.appList = response.data.appList.map(app => {
                const ngageInfo = ngageMap.get(app.ngage_id);
                if (ngageInfo) {
                    return {
                        ...app,
                        email: ngageInfo.email,
                        organisation: ngageInfo.organisation,
                        username: ngageInfo.username,
                    };
                }
                return app;
            });
            return res.status(HTTP.ok.code).json(response);
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    getTriggerEndpoints: async (req, res) => {
        try {
            if (!req.body.appId) {
                return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
            }
            let response = {};
            let developmentURL, productionURL;
            let token;
            const { expiry = '1d', engine } = req.body;
            let secretKey = global.config.authSecret;
            let username = req.invoker.sub;
            developmentURL = global.config.pm_fsync.pluginData.ngageBaseUrl + global.config.app_engine_development.contextPath + "/" + req.body.appId;

            productionURL = global.config.pm_fsync.pluginData.ngageBaseUrl + global.config.app_engine_production.contextPath + "/" + req.body.appId;

            if (engine == 'development') {
                response["url"] = {
                    "development": developmentURL
                }
            }
            else if (engine == 'production') {
                response["url"] = {
                    "production": productionURL
                }
            }
            else {
                response["url"] = {
                    "production": productionURL,
                    "development": developmentURL
                }
            }
            token = jwt.sign({ username }, secretKey, { expiresIn: expiry });
            response["token"] = token;
            return res.status(HTTP.ok.code).json(response);
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    getAllApps: async (req, res) => {
        logger.trace("Retreiving list of total Apps..");
        try {
            let opts = prepareListQuery(req);
            opts.user = undefined;
            let data = await AppStore.listApps(Object.assign({}, opts));
            const response = preparePaginationHeaders(req, opts, data.count);
            if (response.data.currentPage > response.data.totalPages) {
                return res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.pageDoesNotExists));
            }
            response.data.appList = data.rows;
            return res.status(HTTP.ok.code).json(response);
        } catch (error) {
            logger.error("Error while getting all apps detail:", error);
            let response = message.getResponseJson(req.locale, error_codes.internalError);
            response.msg = error.name + ":" + error.message;
            return res.status(HTTP.badGateway.code).json(response);
        }
    },

    /**
     * @api {get} /apps/{appId|appName} AppInfo
     *
     * @apiDescription The scope of applications retrieved by this api
     *       depends on who is asking. If an AppDeveloper role is invoking
     *       this api, it will return a specific AppInfo if it belongs to him/her.
     *       <p>
     *       On the other hand, if an administrator role invokes this api, an entire list of
     *       apps is given, regardless of who created.
     *       </p>
     *
     * @apiName  Fetch Specific Application
     * @apiGroup Application Management
     *
     *
     * @apiSuccessExample  Success-Response:
     * HTTP/1.1 200 OK
     *   {
     *     "id": "5937583693",
     *     "name" : "Our first Beautiful App",
     *     "description" : "A demo application to illustrate our first trial at getting hands dirty.",
     *     "createdAt" : "",       // Time in UTC millis
     *     "createdBy" : 1234,     // id of the person who created this app
     *     "updatedAt" : "",       // Time in UTC millis
     *     "updatedBy" : "1234",    // id of the person who last updated it.
     *     "status": 5, // Current status of the App
     *     "appData": {} // App data representing structure of App
     *   }
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  401 Invalid Authorization
     *   {
     *     "msg": "Invalid username or password",
     *     "code" : 601
     *   }
     *
     * @apiError InvalidJWT  The JSON Web Token submitted to this endpoint is invalid, or cannot be verified.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  401 Invalid Authorization
     *   {
     *     "msg": "Invalid web session token.",
     *     "code" : 604
     *   }
     *
     * @apiError NotAuthorized to user.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  403 Forbidden
     *   {
     *     "error": "Unauthorized",
     *     "code" : 6XX
     *   }
     *
     * @apiError InternalError
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  503 Bad Gateway
     *   {
     *     msg: "Internal Error",
     *     code : 701
     *   }
     *
     * @apiError AppStoreError, AppStore is not reachable/Database is down.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  503 Bad Gateway
     *   {
     *     msg: "App Store not reachable",
     *     code : 702
     *   }
     *
     * @apiError AppNotFound, If application info is not available in database.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  404 Not Found
     *   {
     *     msg: "App not found",
     *     code : 706
     *   }
     *
     **/
    getAppInfo: async (req, res) => {
        try {
            let app = await AppStore.findApp(req.params.appId);
            if (app != null) {
                app.code = HTTP.ok.code;
                app.appData.errors = await AppValidation.getModuleErrors(app.appData);
                let localData = await AppStore.listAppLocale(req.params.appId);
                app.supportedLanguages = "";
                for (let i = 0; i < localData.length; i++) {
                    let element = localData[i];
                    if (i > 0) {
                        app.supportedLanguages += ", ";
                    }
                    app.supportedLanguages += locales[element.locale] || element.locale;
                }
                if (!global.config.api_gw.isCollaborativeAppDevelopmentEnabled) {
                    if (req.invoker.role == "app-developer" && app.owner != req.invoker.id) {
                        logger.warn("App owner and inquisitor doesn't match");
                        return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.appNotFound));
                    }
                }
                return res.status(HTTP.ok.code).json(app);

            } else {
                return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.appNotFound));
            }
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    /**
     * @api {post} /apps/ CreateAppInfo
     *
     * @apiDescription The scope of applications retrieved by this api depends on who is asking. If an AppDeveloper role is invoking
     *       this api, it will allow him/her to create the Application.
     * @apiName  Create Application
     * @apiGroup Application Management
     *
     */
    checkForDuplicateName: async (req, res, next) => {
        let app = await AppStore.findApp(req.body.name);
        if (app !== null) {
            if (app.ngage_id == req.invoker.sub) {
                return res.status(403).json({
                    "message": "Duplicate app name not allowed"
                });
            }
        }
        next();
    },

    createAppInfo: async (req, res) => {
        logger.trace("Executing createAppInfo()...");
        let response;
        try {
            // Create App in draft state.
            req.body.status = 0;
            if (global.config.app_store.generateUUID == false) {
                req.body.id = utility.getUniqueTxnId();
            }
            req.body["ngage_id"] = req.invoker.sub;
            req.body["alignment"] = "vertical";
            req.body["OTC"] = 10.0;
            req.body["MRC"] = 100.0;
            req.body["freeNodeExec"] = 10.0;
            req.body["nodeExecCharge"] = 100.0;
            req.body["channels"] = 0;
            req.body["nodes"] = 2;
            const app = await AppStore.createApp(req.body);
            if (app != null) {
                logger.trace("App Created successfully");
                response = message.getResponseJson(req.locale, error_codes.appCreated, HTTP.resourceCreated.code);
                response.id = app.id;
                let result = res.status(HTTP.resourceCreated.code).json(response);
                whiteboard.publish("mount_app", app.id);
                return result;
            }
        } catch (error) {
            logger.error(error);

            if (error.name == "SequelizeUniqueConstraintError") {
                logger.info("AppId(" + req.body.id + ") conflicts with Original AppId");
                response = message.getResponseJson(req.locale, error_codes.appNameExists);
                response.id = req.body.id;
                return res.status(HTTP.resourceConflict.code).json(response);
            }
        }
        return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
    },

    createAppWithAI: async (req, res) => {
        logger.trace("Executing createWithAI()...");
        let response;
        try {
            let prompt = req.body.prompt;
            const endpoint = 'http://127.0.0.1:3011/createWithAI';
            const headers = {
                Authorization: `Bearer ${req.headers["authorization"].substring(7)}`
            };
            const data = {
                prompt: prompt
            };
            try {
                let responseData = await axios.post(endpoint, data, {
                    "headers": headers,
                    "timeout": 15000
                });
                delete req.body.prompt;
                req.body.desc = responseData.data.desc;
                req.body.name = responseData.data.name;
                req.body.appData = responseData.data.appData;
                req.body.createdBy = req.invoker && req.invoker.id || 0;
                req.body.modifiedBy = req.body.createdBy;
                req.body.owner = req.body.createdBy;
                req.body.createdAt = new Date().getTime();
                req.body.updatedAt = req.body.createdAt;
                req.body.status = 0;
                return module.exports.createAppInfo(req, res);
            }
            catch (e) {
                logger.error("Create with AI failed:" + e);
                return res.status(400).json({
                    "message": "Error occured"
                });
            }
        } catch (error) {
            logger.error(error);
        }
        return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
    },

    getQueries: async (req, res) => {
        logger.trace("Executing getQueries()...");
        let response;
        try {
            const endpoint = 'http://127.0.0.1:3011/getQueries';
            const headers = {
                Authorization: `Bearer ${req.headers["authorization"].substring(7)}`
            };
            try {
                let responseData = await axios.get(endpoint, { //NOSONAR
                    "headers": headers,
                    "timeout": 15000
                });
                return res.status(200).json({
                    ...responseData.data
                });
            }
            catch (e) {
                logger.error("Get Queries failed:" + e);
                return res.status(400).json({
                    "message": "Error occured"
                });
            }
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    /**
     * @api {put} /apps/:appId UpdateAppInfo
     *
     * @apiDescription The scope of applications retrieved by this api depends on who is asking. If an AppDeveloper role is invoking
     *       this api, it will allow him/her to create the Application.
     * @apiName  Update Application
     * @apiGroup Application Management
     *
     */
    updateAppInfo: async (req, res) => {
        logger.trace("Executing updateAppInfo()...", req.params.appId);
        req.body.status = req.query.type ? (req.query.type == "Published" ? 6 : 0) : 0;
        let response;
        try {
            let opts = prepareListQuery(req);
            opts.user = undefined;
            let app = await AppStore.findApp(req.params.appId);
            let appNameList = await AppStore.listAppNames(Object.assign({}, opts));
            if (appNameList.length == 0) {
                return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appNoLongerExists));
            }
            if (app != null) {
                appNameList = appNameList.map(appName => appName.name)
                    .filter(appName => appName != app.name);
                if (app.name != req.body.name && appNameList.includes(req.body.name)) {
                    response = message.getResponseJson(req.locale, error_codes.appNameExists);
                    response.id = req.body.id;
                    return res.status(HTTP.resourceConflict.code).json(response);
                }
            }

            try {
                if (Object.keys(req.body.appData.modules[req.body.appData.endId].process).length == 0) {
                    req.body.appData.modules[req.body.appData.endId].process = {
                        success: {
                            code: [
                            ],
                            message: ""
                        },
                        customErrors: [
                            {
                                code: [],
                                message: ""
                            }
                        ],
                        defaultError: {
                            code: "E9000",
                            message: ""
                        }
                    };
                }
            } catch (e1) {

            }
            let diff = getVersionDetails({
                ...app.appData,
                status: parseInt(app.status, 10)
            }, {
                ...req.body.appData,
                status: parseInt(req.body.status, 10)
            })
            console.log("DEBUG:diff:" + JSON.stringify(diff));
            req.body.appData.version = diff.version;
            req.body.owner = app && app.owner;
            let modules = req.body.appData.modules;
            const channelTypes = ["whatsapp", "sms", "email"];
            let totalNodes = 0;
            let channelNodes = 0;

            for (const key in modules) {
                if (modules.hasOwnProperty(key)) {
                    totalNodes++;

                    const moduleType = modules[key].type;
                    if (channelTypes.includes(moduleType)) {
                        channelNodes++;
                    }
                }
            }
            req.body["channels"] = channelNodes;
            req.body["nodes"] = totalNodes;
            let status = (req.body != null) ? await AppStore.updateApp(req.body) : null;
            if (status != null) {
                logger.trace("App Updated successfully");
                response = message.getResponseJson(req.locale, error_codes.appUpdated, HTTP.ok.code);
                response.id = req.body.id;
                if (req.body.appData)
                    response.errors = await AppValidation.getModuleErrors(req.body.appData);
                response.version = diff.version;
                res.status(HTTP.ok.code).json(response);
                if (req.body.appData) {
                    updateApplocale(req.body);
                }
                try {
                    if (diff.isAppChanged && global.config.api_gw.captureAppSnapshot) {
                        await AppStore.createAppSnapshot({
                            appid: req.body.id,
                            version: diff.version,
                            comment: diff.type,
                            appData: req.body.appData,
                            historyObject: diff.delta,
                            owner: req.body.owner,
                            createdBy: req.body.owner,
                            type: req.query.type ? (req.query.type == "Published" ? "Live" : "Draft") : "Draft"
                        });
                        whiteboard.publish("mount_app", req.body.id);
                    }
                } catch (error) {
                    console.log(error);
                    //ignore
                }
                return;
            }
        } catch (error) {
            logger.error(error);
            if (error.name == "SequelizeUniqueConstraintError") {
                logger.info("AppId(" + req.body.id + ") conflicts with Original AppId");
                response = message.getResponseJson(req.locale, error_codes.appExists);
                response.id = req.body.id;
                return res.status(HTTP.resourceConflict.code).json(response);
            }
        }
        return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
    },

    updateOTCAndMRC: async (req, res) => {
        try {
            let app = await AppStore.findApp(req.body.id);
            app.OTC = req.body.OTC;
            app.MRC = req.body.MRC;
            app.freeNodeExec = req.body.freeNodeExec;
            app.nodeExecCharge = req.body.nodeExecCharge;
            await AppStore.updateApp(app);
            return res.status(200).json({ message: 'OK' });
        } catch (error) {
            console.error('Error updating app:', error);
            return res.status(502).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    /**
     * @api {patch} /apps/:appId/assign assignApp
     *
     * @apiDescription This api either re-assigns the current application to a new owner or un-assigns an application from the current owner.
     *
     * @apiName  Re-assign/Un-assign Application
     * @apiGroup Application Management
     *
     */

    /**
     * @api {post} /api/templates/clone/ CloneApp from template.
     * @api {post} /api/templates/clone/ CloneApp from template.
     *
     * @apiDescription The scope of applications retrieved by this api depends on who is asking.
     *       If an AppDeveloper role is invoking this api, it will allow him/her to create an Application using Template Id.
     * @apiName  Clone Application using Template
     * @apiGroup Application Management
     *
     */
    cloneAppTemplate: async (req, res) => {
        req.body.status = 0;
        try {
            logger.trace("Executing cloneAppTemplate()...TemplateId: " + req.body.appTemplateId);
            let template = await AppStore.findAppTemplate(req.body.appTemplateId);
            logger.trace("Executing cloneAppTemplate()...Template: " + template);
            if (template != null) {
                req.body.appData = template.appData;
                return module.exports.createAppInfo(req, res);
            } else {
                return res.status(HTTP.resourceConflict.code).json(message.getResponseJson(req.locale, error_codes.appTemplateNoLongerExists));
            }
        } catch (error) {
            logger.error(error);
        }
        return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
    },

    cloneApp: async (req, res) => {
        let response;
        try {
            req.body["ngage_id"] = req.invoker.sub;
            logger.trace("Executing cloneApp()...from appId: " + req.params.appId + " to " + req.body.id);
            const appTemplate = await AppStore.findApp(req.params.appId);
            logger.trace("Executing cloneApp()...ApInfo: " + appTemplate);
            if (appTemplate != null) {
                try {
                    // Create App in draft state.
                    req.body.status = 0;
                    req.body.appData = appTemplate.appData;
                    req.body["OTC"] = 10.0;
                    req.body["MRC"] = 100.0;
                    req.body["freeNodeExec"] = 10.0;
                    req.body["nodeExecCharge"] = 100.0;

                    let app = await AppStore.createApp(req.body);
                    if (app != null) {
                        logger.trace("App Created successfully");
                        response = message.getResponseJson(req.locale, error_codes.appCreated, HTTP.resourceCreated.code);
                        response.id = req.body.id;
                        whiteboard.publish("mount_app", req.body.id);
                        return res.status(HTTP.resourceCreated.code).json(response);
                    }
                } catch (error) {
                    logger.error(error);

                    if (error.name == "SequelizeUniqueConstraintError") {
                        logger.info("AppId(" + req.body.id + ") conflicts with Original AppId");
                        response = message.getResponseJson(req.locale, error_codes.appNameExists);
                        response.id = req.body.id;
                        return res.status(HTTP.resourceConflict.code).json(response);
                    }
                }
            } else {
                return res.status(HTTP.resourceConflict.code).json(message.getResponseJson(req.locale, error_codes.appNotFound));
            }
        } catch (error) {
            logger.error(error);
        }
        return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
    },

    /**
     * @api {purge} /apps/{appId|appName} PurgeAppInfo
     *
     * @apiDescription The scope of applications retrieved by this api
     *       depends on who is asking. If an AppDeveloper role is invoking
     *       this api, it will soft delete the App from AppStore if app created by him/her.
     *       <p>
     *       On the other hand, if an administrator role invokes this api, it will soft delete the App.
     *       </p>
     *
     * @apiName  Purge a Specific Application
     * @apiGroup Application Management
     *
     *
     * @apiSuccessExample  Success-Response:
     * HTTP/1.1 200 OK
     *   {
     *      "code": 200,
     *      "msg": "App deleted successfully"
     *   }
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  401 Invalid Authorization
     *   {
     *     "msg": "Invalid username or password",
     *     "code" : 601
     *   }
     *
     * @apiError InvalidJWT  The JSON Web Token submitted to this endpoint is invalid, or cannot be verified.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  401 Invalid Authorization
     *   {
     *     "msg": "Invalid web session token.",
     *     "code" : 604
     *   }
     *
     * @apiError NotAuthorized to user.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  403 Forbidden
     *   {
     *     "error": "Unauthorized",
     *     "code" : 6XX
     *   }
     *
     * @apiError InternalError
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  503 Bad Gateway
     *   {
     *     msg: "Internal Error",
     *     code : 701
     *   }
     *
     * @apiError AppStoreError, AppStore is not reachable/Database is down.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  503 Bad Gateway
     *   {
     *     msg: "App Store not reachable",
     *     code : 702
     *   }
     *
     * @apiError AppNotFound, If application info is not available in database.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  404 Not Found
     *   {
     *     msg: "App not found",
     *     code : 706
     *   }
     *
     **/
    purgeAppInfo: async (req, res) => {

        logger.trace("Executing purgeApp(" + req.params.appId + ")...");
        logger.trace("API Request Params|AppId:" + req.params.appId + "|InvokerId: " + req.invoker && req.invoker.id);
        const result = await AppStore.deleteApp(req.params.appId, true);
        if (result) {
            whiteboard.publish("unmount_app", req.params.appId);
            return res.status(HTTP.ok.code).json(message.getResponseJson(req.locale, error_codes.appDeleted, HTTP.ok.code));
        } else {
            return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.appNotFound));
        }
    },

    takeAppSnapshot: async (req, res) => {

        logger.trace("Executing takeAppSnapshot(" + req.params.appId + ")...");
        logger.trace("API Request Params|AppId:" + req.params.appId + "|InvokerId: " + req.invoker && req.invoker.id);
        const result = await AppStore.createAppSnapshot({
            appId: req.params.appId,
            createdBy: req.invoker && req.invoker.id,
            comment: req.body.comment
        });
        if (result) {
            return res.status(HTTP.ok.code).json(message.getResponseJson(req.locale, error_codes.appDeleted, HTTP.ok.code));
        } else {
            return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.appNotFound));
        }
    },



    /**
     * @api {get} /api/templates AppTemplateList
     *
     * @apiDescription The scope of applications retrieved by this api
     *       depends on who is asking.  If an AppDeveloper role is invoking
     *       this api, it will return a list of apps created by him/her.
     *       <p>
     *       On the other hand, if an administrator role invokes this api, an entire list of
     *       apps is given, regardless of who created.
     *       </p>
     *
     * <h2> Note </h2>
     *   The api will provide link headers to allow GUI to support page navigation.
     *   For more info about pagination, look
     *   <ul>
     *   <li> <a href="https://developer.github.com/v3/guides/traversing-with-pagination/">here</a> </li>
     *   <li>and <a href="https://stackoverflow.com/questions/12168624/pagination-response-payload-from-a-restful-api">here.</a> </li>
     *
     * @apiName  List of Apps
     * @apiGroup Application Management
     *
     * @apiParam {String} token //  Search token
     * @apiParam {Integer} page // Page number
     * @apiParam {Integer} size // Page size
     * @apiParam {String} sortf // Sort field based on App's name, status, createdAt, updatedAt.
     * @apiParam {String} order // Sorting order asc or desc
     * @apiParam {String} user // filter based on the user loginid
     *
     * @apiSuccessExample  Success-Response:
     * HTTP/1.1 200 OK
     * [
     *   {
     *     "id": "5937583693",
     *     "name" : "Our first Beautiful App",
     *     "description" : "A demo application to illustrate our first trial at getting hands dirty.",
     *     "createdAt" : "",       // Time in UTC millis
     *     "createdBy" : 1234,     // id of the person who created this app
     *     "updatedAt" : "",       // Time in UTC millis
     *     "updatedBy" : "1234",    // id of the person who last updated it.
     *     "status": "5",
     *   },
     *   { ... }
     * ]
     *
     * @apiError InvalidCredentials The username and/or password were incorrect.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  401 Invalid Authorization
     *   {
     *     "code" : 601
     *     "msg": "Invalid username or password",
     *   }
     *
     * @apiError InvalidJWT  The JSON Web Token submitted to this endpoint is invalid, or cannot be verified.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  401 Invalid Authorization
     *   {
     *     "code" : 604
     *     "msg": "Invalid web session token.",
     *   }
     *
     * @apiError NotAuthorized to user.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  403 Forbidden
     *   {
     *     "error": "Unauthorized",
     *     "code" : 6XX
     *   }
     *
     * @apiError InternalError
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  503 Bad Gateway
     *   {
     *     msg: "Internal Error",
     *     code : 701
     *   }
     *
     * @apiError AppStoreError, AppStore is not reachable/Database is down.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  503 Bad Gateway
     *   {
     *     msg: "App Store not reachable",
     *     code : 702
     *   }
     *
     * @apiError BadInput for size field.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  400 Bad Request
     *   {
     *     msg: "Bad input for size field",
     *     code : 703
     *   }
     *
     * @apiError BadInput for sortf field.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  400 Bad Request
     *   {
     *     msg: "Bad input for sortf field",
     *     code : 704
     *   }
     *
     * @apiError BadInput for order field.
     *
     * @apiErrorExample  Error-Response
     *   HTTP/1.1  400 Bad Request
     *   {
     *     msg: "Bad input for order field",
     *     code : 705
     *   }
     *
     */
    getAppTemplates: async (req, res) => {

        logger.trace("Executing getAppTemplates");
        const templates = await AppStore.getAppTemplates();
        if (templates != null) {
            let apps = {};
            apps = message.getResponseJson(req.locale, error_codes.appsListed, HTTP.ok.code);
            apps.data = templates;
            return res.status(HTTP.ok.code).json(apps);
        } else {
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    getAppTemplatesByType: async (req, res) => {

        logger.trace("Executing getAppTemplatesByType");
        const templates = await AppStore.getAppTemplatesByType(req.params.type);
        if (templates != null) {
            let apps = {};
            apps = message.getResponseJson(req.locale, error_codes.appsListed, HTTP.ok.code);
            apps.data = templates;
            return res.status(HTTP.ok.code).json(apps);
        } else {
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    getAppTemplatesType: async (req, res) => {

        logger.trace("Executing getAppTemplatesByType");
        const templates = await AppStore.getAppTemplatesType();
        if (templates != null) {
            let apps = {};
            apps = message.getResponseJson(req.locale, error_codes.appsListed, HTTP.ok.code);
            apps.data = templates;
            return res.status(HTTP.ok.code).json(apps);
        } else {
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    downloadAppLocale: async (req, res) => {

        logger.trace("Download AppLocale Templates... for locale:" + req.params.locale);
        try {
            let templates = await AppStore.getAppLocaleTemplate(req.params.appId, req.params.locale);
            if (templates != null) {
                let translation = templates.translation;
                if (typeof translation == "string") {
                    translation = JSON.parse(translation);
                }
                return res.status(HTTP.ok.code).json(translation);
            } else {
                return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.appNotFound));
            }
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    listAppLocale: async function (req, res) {
        try {
            logger.trace("List App Locale is executing...");
            let finalList = [];
            const appInfo = await AppStore.findApp(req.params.appId);
            if (appInfo == null) {
                return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.appNotFound));
            }
            let appsLocale = await AppStore.listAppLocale(req.params.appId);
            appsLocale.forEach(applocale => {
                let tmpAppLocale = {
                    isDefault: false
                };
                tmpAppLocale.id = applocale.id;
                tmpAppLocale.appId = applocale.appId;
                tmpAppLocale.locale = applocale.locale;
                tmpAppLocale.description = locales[applocale.locale] || applocale.locale;
                if (applocale.locale == "en") {
                    tmpAppLocale.isDefault = true;
                }
                finalList.push(tmpAppLocale);
            });
            return res.status(HTTP.ok.code).json(finalList);
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    deleteAppsLocale: async function (req, res) {
        try {
            logger.trace("Delete locale us executing...:");
            let result = await AppStore.deleteAppLocale(req.params.localeId);
            logger.trace("Deleting the applocale result is:" + result);
            return res.status(HTTP.ok.code).json(message.getResponseJson(req.locale, error_codes.success));
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
        }
    },

    getCommentsPerApp: async function (req, res) {
        try {
            logger.trace("Get Comments per App is executing...");
            let eventsPerApp = await AppStore.eventsCountPerApp();
            return res.status(HTTP.ok.code).json({
                code: 0,
                msg: eventsPerApp
            });
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, HTTP.appStoreError));
        }
    },

    copyAppUrl: async (req, res) => {
        logger.trace("Copy App URL link is Executing for App:" + req.params.appId);
        try {

            const appInfo = await AppStore.findApp(req.params.appId);
            if (appInfo == null) {
                return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.appNotFound));
            }
            let appUrl = "Not found";
            if (appInfo.appData != null) {
                let config;
                if (appInfo.status == 2 || appInfo.status == 5) {
                    config = global.config.app_engine_staging;
                } else if (appInfo.status == 6) {
                    config = global.config.app_engine_production;
                }
                if (config != null) {
                    appUrl = config.protocol + "://" + config.host + ":" + config.port + config.contextPath + "/" + req.params.appId + "?";
                    logger.trace("Request app URL is: " + appUrl);
                    let startId = appInfo.appData.startId;
                    let params = appInfo.appData.modules[startId].settings.params;
                    let requestInputParams = '';
                    let i = 1;
                    params.forEach(param => {
                        requestInputParams = requestInputParams + param + "=<" + param + ">";
                        if (i != params.length) {
                            requestInputParams += "&";
                        }
                        i++;
                    });

                    logger.trace("The application input parameters are:" + requestInputParams);
                    appUrl = appUrl + requestInputParams;
                }
            }
            return res.status(HTTP.ok.code).json({
                code: 0,
                msg: appUrl
            });
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, HTTP.internalError));
        }
    },

    exportApp: async (req, res) => {
        logger.trace("App Export Request AppId:" + req.params.appId);
        let appInfo = await AppStore.findApp(req.params.appId);
        logger.trace(appInfo);
        if (appInfo == null) {
            logger.trace("step1");
            return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.appNotFound));
        } else if (appInfo.appData != null) {
            appInfo.appData.id = req.params.appId;
            appInfo.localeData = await AppStore.listAppLocale(req.params.appId, { download: true });
            logger.trace("step2");
            res.json(appInfo);
        } else {
            logger.trace("Else");
            res.json({});
        }
    },

    importApp: async (req, res) => {
        logger.info("Upload Application Dump");

        // Ensure the upload directory exists
        if (!fs.existsSync(uploadAppsDir)) {
            fs.mkdirSync(uploadAppsDir);
        }

        // Configure multer storage
        let storage = multer.diskStorage({  //NOSONAR
            destination: (req, file, callback) => {
                callback(null, uploadAppsDir);
            },
            filename: (req, file, callback) => {
                callback(null, file.originalname);
            }
        });

        // Configure multer with a file size limit
        let uploadFile = multer({
            storage: storage,
            limits: { fileSize: 10 * 1024 * 1024 }, // Limit file size to 10 MB
            fileFilter: (req, file, callback) => {
                let ext = path.extname(file.originalname);
                if (ext !== ".json") {
                    logger.error("Extension not supported for:", file.originalname);
                    callback(new Error("fileExtNotSupport"), false);
                } else if (!file.originalname.startsWith('AppExportData_')) {
                    logger.error("Filename is not in the specified format");
                    callback(new Error("fileNameInvalid"), false);
                } else {
                    callback(null, true);
                }
            }
        }).single("file");

        // Handle file upload
        uploadFile(req, res, (err) => {
            if (err) {
                logger.error("File upload failed with err:", err);
                if (err.code === 'LIMIT_FILE_SIZE') {
                    return res.status(HTTP.badRequest.code)
                        .json(message.getResponseJson(req.locale, error_codes.fileSizeLimitExceeded));
                } else if (err.message === "fileExtNotSupport") {
                    return res.status(HTTP.notAcceptable.code)
                        .json(message.getResponseJson(req.locale, error_codes.fileExtNotSupport));
                } else if (err.message === "fileNameInvalid") {
                    return res.status(HTTP.badRequest.code)
                        .json(message.getResponseJson(req.locale, error_codes.fileNameInvalid));
                } else {
                    return res.status(HTTP.badRequest.code)
                        .json(message.getResponseJson(req.locale, error_codes.fileUploadFail));
                }
            } else {
                logger.info("File Uploaded Successfully");
                let file_path = req.file.path;
                fs.readFile(file_path, async (err, data) => {
                    if (err) {
                        logger.error("Error in file read", err);
                        return res.status(HTTP.badRequest.code)
                            .json(message.getResponseJson(req.locale, error_codes.fileUploadFail));
                    } else {
                        try {
                            let app = JSON.parse(data.toString());
                            let oldApp = await AppStore.findApp(app.id);
                            if (oldApp != null) {
                                if (req.invoker.role === "app-developer" && !global.config.api_gw.isCollaborativeAppDevelopmentEnabled) {
                                    if (oldApp.owner !== req.invoker.id) {
                                        logger.error("Scope is not allowed for user:", req.invoker.id);
                                        return res.status(HTTP.resourceConflict.code)
                                            .json(message.getResponseJson(req.locale, error_codes.invalidScope));
                                    }
                                }
                                if (oldApp.ngage_id === req.invoker.sub) {
                                    logger.error("App already existing");
                                    return res.status(HTTP.badGateway.code).json({
                                        "message": "App already existing"
                                    });
                                } else if (oldApp.status === 0) {
                                    app.status = 0;
                                    app.modifiedBy = req.invoker && req.invoker.id;
                                    app.owner = app.modifiedBy;
                                    app.updatedAt = new Date().getTime();
                                    let status = await AppStore.updateApp(app);
                                    if (status === true) {
                                        logger.info("AppLocale Updated successfully");
                                        return res.status(HTTP.ok.code)
                                            .json(message.getResponseJson(req.locale, error_codes.uploadSuccess, 0));
                                    } else {
                                        logger.error("AppLocale Update failed.");
                                        return res.status(HTTP.badGateway.code)
                                            .json(message.getResponseJson(req.locale, error_codes.appStoreError));
                                    }
                                } else {
                                    logger.error("Application is not in Draft state.");
                                    return res.status(HTTP.badGateway.code)
                                        .json(message.getResponseJson(req.locale, error_codes.appForbidden));
                                }
                            } else {
                                app.status = 0;
                                req.body = app;
                                req.body.owner = req.invoker && req.invoker.id || 0;
                                req.body.createdBy = req.body.owner;
                                req.body.modifiedBy = req.body.owner;
                                return module.exports.createAppInfo(req, res);
                            }
                        } catch (e) {
                            logger.error("Unsupported format.", e);
                            return res.status(HTTP.notAcceptable.code)
                                .json(message.getResponseJson(req.locale, error_codes.fileFormatNotSupported));
                        }
                    }
                });
            }
        });
    },

    compressedApp: async (req, res) => {
        logger.trace("App Export Request AppId: " + req.params.appId);
        let appInfo = await AppStore.findCompressedApp(req.params.appId);
        logger.trace("Compressed app:", appInfo);

        if (appInfo == null) {
            logger.trace("step1");
            return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.appNotFound));
        } else if (appInfo.appData != null) {
            // Validate and sanitize appId
            const appId = req.params.appId.replace(/[^a-zA-Z0-9-_]/g, ''); // Allow only safe characters
            let query = `INSERT INTO apps_master VALUES ('${appInfo.id}', '${appInfo.name}', '${appInfo.desc}', '${appInfo.status}', '${appInfo.appData}', ${appInfo.owner}, ${appInfo.createdBy}, ${appInfo.modifiedBy}, NULL, '${appInfo.createdAt}', '${appInfo.updatedAt}', NULL);`;

            // Construct the file path safely
            const fn = path.join(__dirname, "../../", `${appId}.sql`);
            logger.trace("Insert query:", query);
            logger.trace("Filename:", fn);

            // Write the file
            fs.writeFileSync(fn, query);
            res.json(appInfo);
        } else {
            res.json({});
        }
    },
};

function preparePaginationHeaders(req, opts, totalApps) {
    const page = parseInt(opts.page, 10);
    let size = parseInt(opts.size, 10);
    size = size > totalApps ? totalApps : size;
    const lastPage = Math.ceil(totalApps / size);
    let response = message.getResponseJson(req.locale, error_codes.appsListed, HTTP.ok.code);
    response.data = {};
    response.data.pageSize = size;
    response.data.totalApps = totalApps;
    response.data.prevPage = page > 1 ? page - 1 : page;
    response.data.nextPage = page < lastPage ? page + 1 : lastPage;
    response.data.totalPages = lastPage;
    response.data.currentPage = page;
    return response;
}

function prepareListQuery(req) {
    let opts = {};
    opts.token = req.query.token && req.query.token.replace(/_/g, '\\_') || "";
    opts.page = req.query.page || 1;
    opts.size = req.query.size || global.config.api_gw.appsApi.defaultPageSize || DEFAULT_PAGESIZE;
    opts.sortf = "updatedAt";
    opts.order = "desc";
    if (req.query.sortf != null) {
        opts.sortf = req.query.sortf;
        opts.order = req.query.order;
    }
    opts.user = req.query.user != null ? req.query.user : undefined;
    opts.status = req.query.status || undefined;
    if (!global.config.api_gw.isCollaborativeAppDevelopmentEnabled) {
        opts.owner = (req.invoker.role == userRoles.appDeveloper) ? req.query.owner || req.invoker && req.invoker.id : undefined;
    }
    opts.startTime = req.query.startTime || undefined;
    opts.endTime = req.query.endTime || undefined;
    opts.isCountRequired = req.query.countflag || false;
    logger.trace("AppStore Request params|" + JSON.stringify(opts));
    return opts;
}

async function updateApplocale(app) {
    try {
        if (app.locale == null) app.locale = "en";
        let translation = {};
        let appData = app.appData;
        if (appData != null && appData.modules) {

            let keys = Object.keys(appData.modules);
            for (let i = 0; i < keys.length; i++) {
                let key = keys[i];
                if (appData.modules[key].type == "appEnd") {
                    translation = appData.modules[key].process;
                    break
                }
            }
            translation.menu_titles = {};
            translation.sms_titles = {};
            translation.menu_navigation_options = {};
            for (let i = 0; i < keys.length; i++) {
                let key = keys[i];
                try {
                    let plugin = appData.modules[key];
                    logger.error("Type", plugin.type);
                    switch (plugin.type) {
                        case "sms":
                            translation.sms_titles[key] = plugin.process.short_message;
                            break;
                        case "appMenu": {
                            let nav_data = parseMenuNavOpts({}, plugin.menuNavigationOptions);
                            let data = parseMenuName({}, plugin.process.menu);
                            translation.menu_titles[key] = data;
                            translation.menu_navigation_options = Object.assign(translation.menu_navigation_options, nav_data);
                        } break;
                        default:
                            logger.error("skipping", plugin.type);
                    }
                } catch (error) { }
            }
            logger.trace("After: " + JSON.stringify(translation.menu_titles));

        } else {
            logger.trace("Appdata is null");
        }
        app.translation = translation;
        logger.trace("Trans: " + JSON.stringify(app.translation));
        await AppStore.updateAppLocale(app);
    } catch (error) {
        logger.error(error);
    }
}


function parseMenuName(menuTranslationData, data) {
    data.children.items.forEach(element => {
        if (element.type == "QUESTION") {
            menuTranslationData[element.uid] = element.question;
        } else {
            menuTranslationData[element.uid] = element.name;
        }
        if (element.children.items.length > 0) {
            menuTranslationData = parseMenuName(menuTranslationData, element);
        }
    });
    return menuTranslationData;
}

function parseMenuNavOpts(menuTranslationData, data) {
    Object.keys(data).forEach(element => {
        menuTranslationData[element] = data[element].message;
    });
    return menuTranslationData;
}

function getVersionDetails(oldInfo, newIInfo) {
    let delta = null;
    let version = oldInfo.version || "0.0.0";
    let type = "patch";
    let isAppChanged = false;
    try {
        newIInfo.version = version;
        delta = jsondiffpatch.diff(oldInfo, newIInfo);
        if (delta != null && delta.modules != null) {
            console.log("Came inside");
            if (Object.keys(newIInfo.modules).length > Object.keys(oldInfo.modules).length) {
                type = "minor";
            } else {
                for (let [key, value] of Object.entries(delta.modules)) {
                    if (Array.isArray(value) && value.length == 3) {
                        type = "minor";
                        break;
                    }
                }
            }
            isAppChanged = true;
            version = semver.inc(version, type);
        }
        if (delta != null && delta.status != null) {
            isAppChanged = true;
            version = semver.inc(version, type);
        }
    } catch (error) {
        //ignore
    }

    return {
        version,
        delta,
        type,
        isAppChanged
    };
}

function getAppsWithEvents(req, apps) {
    return new Promise((resolve) => {
        if (apps == null || apps.length === 0) {
            return resolve([]);
        }

        // Async function to handle the process
        const processApps = async () => {
            let finalRes = [];
            let eventsPerApp = await AppStore.eventsCountPerApp();
            let tmpcount = 0;

            // Use Promise.all to await all async operations within the apps array
            await Promise.all(apps.map(async (element) => {
                // Find the event count for the current app
                for (let i = 0; i < eventsPerApp.length; i++) {
                    if (element.id === eventsPerApp[i].appId) {
                        tmpcount = eventsPerApp[i].count;
                        break;
                    } else {
                        tmpcount = 0;
                    }
                }
                element.count = tmpcount;
                finalRes.push(element);
            }));

            // Resolve the Promise with the final result
            resolve(finalRes);
        };

        // Call the async function
        processApps().catch((err) => {
            logger.error("Error processing apps:", err);
            resolve([]); // Return an empty array in case of error
        });
    });
}

