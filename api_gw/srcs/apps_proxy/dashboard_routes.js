"use strict";
/**\
 * List of routes for IT admin dashboard.
\**/

const AppProxy = require("./apps_impl");
const Router = require("express").Router();
const c4b = require("../session").check4Blacklist;
const AV = require("app_validation");
const rid = require("../leap_resources");
const authorizer = require("authorizer");
let types = authorizer.accessTypes;
let overviewPermsObj = [{ resId: rid.USERS, resType: types.accessRead },
{ resId: rid.APPS, resType: types.accessRead }];

module.exports = Router;
