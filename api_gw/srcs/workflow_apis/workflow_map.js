module.exports = {
    events: {
        SUBMIT: {
            startState: ['0'],
            targetState: 1,
            actionCode: 2,
            role: ["root", "app-developer"],
            notify: {
                IND: [],
                GRP: ["mkt-admin"]
            }
        },
        WITHDRAW: {
            startState: ['1'],
            targetState: 0,
            actionCode: 4,
            role: ["root", "app-developer"],
            notify: {
                IND: [],
                GRP: ["mkt-admin"]
            }
        },
        REJECT: {
            startState: ['1', '2', '5'],
            targetState: 0,
            actionCode: 3,
            role: ["root", "it-admin", "mkt-admin"],
            notify: {
                IND: ["app-developer"],
                GRP: []
            }
        },
        ApproveForStaging: {
            startState: ['1'],
            targetState: 2,
            actionCode: 5,
            role: ["root", "mkt-admin"],
            notify: {
                IND: ["app-developer"],
                GRP: ["it-admin"]
            }
        },
        STAGE: {
            startState: ['2'],
            targetState: 5,
            actionCode: 6,
            role: ["it-admin"],
            notify: {
                IND: ["app-developer"],
                GRP: ["mkt-admin"]
            }
        },
        ScheduleForLaunch: {
            startState: ['1', '4', '5', '7'],
            targetState: 4,
            actionCode: 7,
            role: ["root", "mkt-admin"],
            notify: {
                IND: ["app-developer"],
                GRP: ["it-admin"]
            }
        },
        LAUNCH: {
            startState: ['1', '4', '5', '7'],
            targetState: 6,
            actionCode: 8,
            role: ["root", "mkt-admin"],
            notify: {
                IND: ["app-developer"],
                GRP: ["it-admin"]
            }
        },
        RETIRE: {
            startState: ['4', '6', '8'],
            targetState: 7,
            actionCode: 9,
            role: ["root", "mkt-admin"],
            notify: {
                IND: ["app-developer"],
                GRP: ["it-admin"]
            }
        },
        ScheduleForRetire: {
            startState: ['4', '6', '8'],
            targetState: 8,
            actionCode: 10,
            role: ["root", "mkt-admin"],
            notify: {
                IND: ["app-developer"],
                GRP: ["it-admin"]
            }
        }
    },
    eventsMessage: {
        0: "$performerName created and saved $appName app for approval",
        1: "$performerName modified $appName app",
        2: "$performerName submitted $appName app for approval",
        3: "$performerName rejected $appName app",
        4: "$performerName withdrawn $appName app",
        5: "$performerName approved the $appName app for staging",
        6: "$performerName staged the $appName app",
        7: "$performerName scheduled for launch $appName app",
        8: "$performerName launched the $appName app",
        9: "$performerName retired the $appName app",
        10: "$performerName scheduled for retire the $appName app"
    }
};
