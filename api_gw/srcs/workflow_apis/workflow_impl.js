/**
 * Implements the api for plugins management.
 **/
const logger = global.logger;
const common = require("common");
const whiteboard = common.whiteboard;
const httpErrorCodes = common.http_codes;
const workFlowMap = require('./workflow_map');
const AppStore = require("app_store");
const message = require("message");
const error_codes = message.error_codes;
const defaultRoles = common.user_roles_default;
const rid = require("../leap_resources");

module.exports = {
    
}

function prepareEventsData(usersList, appInfo, performer, action, comment, targetStatus) {
    let eventsData = {
        appHistoryData: {},
        eventsList: []
    }
    eventsData.appHistoryData = {
        appId: appInfo.id,
        appName: appInfo.name,
        status: targetStatus,
        event: action,
        remarks: comment,
        performer: performer
    }

    usersList.forEach(userId => {
        logger.trace("Preparing events data for user:" + userId);
        let userData = {
            appId: appInfo.id,
            appName: appInfo.name,
            userId: userId,
            action: action,
            comment: comment,
            is_read: 0,
            performer: performer
        }
        eventsData.eventsList.push(userData);
    });

    logger.trace("The request events data is:" + JSON.stringify(eventsData));
    return eventsData;
}
