"use strict";
/**
 * Implements the api for applications management.
 **/


const curlToJsonConvert = require("curl-to-json-convert");


module.exports = {
    ConvertCurl: async (req, res) => {
        try {
            const { curl } = req.body;
            if (!curl) {
                return res.status(400).send({ error: 'curl parameter is required' });
            }
            const parsed = curlToJsonConvert(curl);
            res.status(200).json(parsed);
        } catch (error) {
            res.status(400).send({ error: 'Failed to parse curl command', details: error.message });
        }
    }
};
