"use strict"
const logger = global.logger,
  message = require("message"),
  error_codes = message.error_codes,
  Influx = require("influx"),
  influxQueris = require("./influxQueries");
const influxDBInfo = global.config.influxDB;
const thresholdValue = influxDBInfo.thresholdValue;
const thresholdValue80 = Number(thresholdValue * 0.8).toFixed(0);

module.exports = {
  influxRequest: (req, reqJSON) => {
    if (logger.isTraceEnabled()) {
      logger.trace("Received InfluxDB request for the request object: " + JSON.stringify(reqJSON));
    }

    return new Promise((resolve, reject) => {
      let influx = new Influx.InfluxDB({
        host: influxDBInfo.host || "127.0.0.1",
        port: influxDBInfo.port || 8086,
        database: influxDBInfo.database || "telegraf",
        username: influxDBInfo.username,
        password: influxDBInfo.password
      });

      // Use an async function inside the promise executor
      prepareInfluxQuery(reqJSON)
        .then(query => {
          return influx.query(query);
        })
        .then(result => {
          resolve(prepareResponse(req, result, reqJSON));
        })
        .catch(err => {
          logger.error(err);
          reject(err);
        });
    });
  }
}

async function prepareInfluxQuery(reqJOSN) {

  if (logger.isTraceEnabled()) {
    logger.trace("Preparing influx query...");
  }
  async function LicenceThresholdCrossed() {
    if (logger.isTraceEnabled()) {
      logger.trace("Preparing influx query for LicenceThresholdCrossed...");
    }
    return queryPlaceHolderReplace(reqJOSN, influxQueris.LicenceThresholdCrossed + influxQueris.LicenceThreshold80Crossed);
  }

  async function LicenceCapacityUtilization() {
    if (logger.isTraceEnabled()) {
      logger.trace("Preparing influx query for LicenceCapacityUtilization...");
    }
    return queryPlaceHolderReplace(reqJOSN, influxQueris.LicenceCapacityUtilization);
  }

  async function LicenceCapacityThresholds() {
    if (logger.isTraceEnabled()) {
      logger.trace("Preparing influx query for LicenceCapacityThresholds...");
    }
    let query = influxQueris.threshold10To25
    query += influxQueris.threshold25To50
    query += influxQueris.threshold50To80
    query += influxQueris.threshold80To100
    query += influxQueris.threshold100To110
    query += influxQueris.threshold110To120;
    return queryPlaceHolderReplace(reqJOSN, query);
  }

  async function InterfaceAvgTransactions() {
    if (logger.isTraceEnabled()) {
      logger.trace("Preparing influx query for InterfaceAvgTransactions...");
    }
    return queryPlaceHolderReplace(reqJOSN, influxQueris.LicenceCapacityUtilization);
  }
  const queryMap = {
    licencethresholdcrossed: await LicenceThresholdCrossed,
    licenceCapacityUtilization: await LicenceCapacityUtilization,
    licenceCapacityThresholds: await LicenceCapacityThresholds,
    interfaceavgTransactions: await InterfaceAvgTransactions
  }
  return queryMap[reqJOSN.reportType]();
}

function prepareResponse(req, result, reqJOSN) {
  if (logger.isTraceEnabled()) {
    logger.trace("Preparing response...");
  }

  function LicenceThresholdCrossedRes() {
    if (logger.isTraceEnabled()) {
      logger.trace("Preparing response for LicenceThresholdCrossedRes...");
    }
    let res;
    if (result[0] == null) {
      res = {
        LicencedThresholdCrossed: 0,
        Licensed80ThresholdCrossed: 0
      }
    }
    else {
      res = {
        LicencedThresholdCrossed: result[0][0] == null ? 0 : result[0][0].count,
        Licensed80ThresholdCrossed: result[1][0] == null ? 0 : result[1][0].count
      }
    }
    return res;
  }

  function LicenceCapacityUtilizationRes() {
    if (logger.isTraceEnabled()) {
      logger.trace("Preparing response for LicenceCapacityUtilizationRes...");
    }
    let xaxis = [message.getResponseJson(req.locale, error_codes.xAxis).msg],
      utilizationData = [message.getResponseJson(req.locale, error_codes.UtilizationData).msg],
      licencedValue = [message.getResponseJson(req.locale, error_codes.licencedValue).msg, null],
      licenceThreshold = [message.getResponseJson(req.locale, error_codes.licenceThreshold).msg, null];

    result.forEach(element => {
      if (element == null) {
        xaxis.push(0);
        utilizationData.push(0);
      } else {
        let date = new Date(element.time);
        xaxis.push(date.getTime());
        if (element.TPS)
          utilizationData.push(element.TPS);
        else utilizationData.push(0);
        licencedValue.push(thresholdValue);
        licenceThreshold.push(thresholdValue80);
      }
    });
    return {
      chart: [xaxis, utilizationData, licencedValue, licenceThreshold],
      licencedValue: thresholdValue,
      licenceThreshold: thresholdValue80
    }
  }

  function LicenceCapacityThresholdsRes() {
    if (logger.isTraceEnabled()) {
      logger.trace("Preparing response for LicenceCapacityThresholdsRes...");
    }
    let category = ["10-25%", "25-50%", "50-80%", "80-100%", "100-110%", "110-120%"];
    let chart = [message.getResponseJson(req.locale, error_codes.TVC).msg];
    result.forEach(element => {
      if (element == null) {
        chart.push(0);
      } else if (!(element[0] && element[0].count)) {
        chart.push(0);
      } else {
        chart.push(element[0].count);
      }
    });
    return {
      category,
      chart
    };
  }

  function InterfaceAvgTransRes() {
    if (logger.isTraceEnabled()) {
      logger.trace("Preparing response for InterfaceAvgTransRes...");
    }
    let xaxis = [message.getResponseJson(req.locale, error_codes.xAxis).msg],
      utilizationData = ["OverAll"];

    result.forEach(element => {
      if (element == null) {
        xaxis.push(0);
        utilizationData.push(0);
      } else {
        let date = new Date(element.time);
        xaxis.push(dateformat(date.getTime(), reqJOSN.interval));
        if (element.TPS)
          utilizationData.push(element.TPS);
        else utilizationData.push(0);
      }
    });
    return [xaxis, utilizationData]
  }
  const resMap = {
    licencethresholdcrossed: LicenceThresholdCrossedRes,
    licenceCapacityUtilization: LicenceCapacityUtilizationRes,
    licenceCapacityThresholds: LicenceCapacityThresholdsRes,
    interfaceavgTransactions: InterfaceAvgTransRes
  }
  return resMap[reqJOSN.reportType]();
}

let divider = {
  "1m": 60,
  "10m": 600,
  "30m": 1800,
  "3h": 10800,
  "12h": 43200
};

function queryPlaceHolderReplace(reqJOSN, query) {
  reqJOSN.interval = modifyInterval(reqJOSN.endTime - reqJOSN.startTime);
  reqJOSN.startTime *= 1000000;
  reqJOSN.endTime *= 1000000;
  query = query.split("$interval").join(reqJOSN.interval);
  query = query.split("$divide").join(divider[reqJOSN.interval]);
  query = query.split("$thresholdValue").join(thresholdValue);
  query = query.split("$startTime").join(reqJOSN.startTime);
  query = query.split("$endTime").join(reqJOSN.endTime);
  return query;
}

function dateformat(timestamp, interval) {
  let formatedDate = new Date(timestamp);
  let day = formatedDate.getDate();
  let month = formatedDate.getMonth() + 1;
  let year = formatedDate.getFullYear();
  switch (interval) {
    case "1h":
    case "1M":
      return timestamp;
    case "1d":
    case "1w":
      return (year + "-" + month + "-" + day);
    default:
      return timestamp;
  }
}

function modifyInterval(durationInMillisec) {
  let interval;
  logger.info("DURATION:", durationInMillisec);
  if (durationInMillisec < 3600000) interval = "1m";  //less than 1hr
  else if (durationInMillisec >= 3600000 && durationInMillisec < 86400000) interval = "10m"; //1hr to 1day
  else if (durationInMillisec >= 86400000 && durationInMillisec < 1296000000) interval = "30m"; //1 day to 15days
  else if (durationInMillisec >= 1296000000 && durationInMillisec < 2592000000) interval = "3h"; //15 days to 30day
  else interval = "12h"; //> 30day
  return interval;
}
