"use strict"
const common = require("common");
const HTTP = common.http_codes;
const Influx = require("influx");
const ESClient = require("./elastiClient");
const influxDBInfo = global.config.influxDB;
const crypto = require('crypto');
let index = "leap";

let influx = new Influx.InfluxDB({
  host: influxDBInfo.host || "127.0.0.1",
  port: influxDBInfo.port || 8086,
  database: influxDBInfo.database || "telegraf",
  username: influxDBInfo.username,
  password: influxDBInfo.password
});

function getRandomInt(min, max) {
  const range = max - min + 1;
  const randomBytes = crypto.randomBytes(4); // 4 bytes = 32 bits
  const randomValue = randomBytes.readUInt32BE(0) % range; // Get a value within the range
  return min + randomValue; // Shift the value to the desired range
}

module.exports = {
  getAppReports: async (req, res) => {
    let totalSumQuery = `select sum(count) from plugin_stats where appId='${req.body.appId}';`
    let totalSuccessQuery = `select sum(count) from plugin_stats where appId='${req.body.appId}' and code='0';`
    let totalFailureQuery = `select sum(count) from plugin_stats where appId='${req.body.appId}' and code!='0';`
    let totalData = await fetchData(totalSumQuery);
    let successData = await fetchData(totalSuccessQuery);
    let failureData = await fetchData(totalFailureQuery);
    return res.status(HTTP.ok.code).json({
      "totalCount": totalData[0].sum,
      "success": successData[0].sum,
      "progress": 0,
      "failed": failureData[0].sum,
      "lapsed": 0
    });
  },

  getTotalTriggers: async (appId) => {
    let req = {
      body: {
        appId: appId,
        name: "getAllData"
      }
    }
    let count = getRandomInt(10, 1000);
    // try {
    //   let query = await getElasticQuery(req.body);
    //   let elsResponse = await ESClient.getEsResponse(index, query);
    //   count = elsResponse.hits.total.value;
    // }
    // catch (e) {

    // }
    return count;
  },

  getDetailedAppReport: async (req, res) => {
    let page = parseInt(req.query.page, 10) || 1; // Specify radix 10
    let size = parseInt(req.query.size, 10) || 10; // Specify radix 10
    const from = (page - 1) * size;
    let endReponse = [];
    let query = await getElasticQuery(req.body);

    // Check if query is valid
    if (!query) {
      console.error("Error: getElasticQuery returned undefined.");
      return res.status(HTTP.internalServerError.code).json({
        message: "Failed to generate query for Elasticsearch"
      });
    }

    // Proceed if query is valid
    query.from = from;
    query.size = size;
    let elsResponse = await ESClient.getEsResponse(index, query);
    const totalCount = elsResponse.hits.total.value;
    const totalPages = Math.ceil(totalCount / size);
    let transactionArray = elsResponse.hits.hits;

    for (var i = 0; i < transactionArray.length; i++) {
      let response = {};
      let envelope = transactionArray[i];
      response["transaction_id"] = envelope._source.sid;
      response["user_id"] = envelope._source.uid;
      response["trigger"] = envelope._source.im;
      response["startTime"] = envelope._source.st;
      response["endTime"] = envelope._source.et;
      let flows = envelope._source.p;
      let flows_data = [];
      for (var j = 0; j < flows.length; j++) {
        let flow = flows[j];
        let flowTemp = {};
        let variables = {
          "settings": "",
          "process": ""
        }
        if (flow.vars) {
          variables = JSON.parse(flow.vars);
        }
        flowTemp["mid"] = flow.mtid;
        flowTemp["rtt"] = flow.mrt;
        flowTemp["status"] = flow.ocode == "0" ? "SUCCESS" : "FAILURE";
        flowTemp["reason"] = flow.ocode + " has occurred";
        flowTemp["startTime"] = flow.mst;
        flowTemp["endTime"] = flow.met;
        flowTemp["variables"] = {
          "settings": variables.settings,
          "process": variables.process
        }
        flows_data.push(flowTemp);
      }
      response["flows_data"] = flows_data;
      endReponse.push(response);
    }

    res.set('X-Total-Count', totalCount);
    res.set('X-Page', page);
    res.set('X-PerPage', size);
    res.set('x-totalpages', totalPages);
    let finalResponse = {
      "total": totalCount,
      "success": totalCount,
      "data": endReponse
    }
    return res.status(HTTP.ok.code).json(finalResponse);
  }

}

async function fetchData(query) {
  try {
    const result = await influx.query(query);
    return result;
  } catch (error) {
    throw error;
  }
}

async function getLastNDataPoints(measurement, field, limit) {
  const query = `SELECT "${field}" FROM "${measurement}" ORDER BY time DESC LIMIT ${limit}`;
  return await fetchData(query);
}

// Example function to get data between a specific time range
async function getDataBetweenTimeRange(measurement, field, startTime, endTime) {
  const query = `SELECT "${field}" FROM "${measurement}" WHERE time >= ${startTime} AND time <= ${endTime}`;
  return await fetchData(query);
}

async function getElasticQuery(options) {
  if (options.name == 'getAllData') {
    return {
      "query": {
        "bool": {
          "must": [
            {
              "match": {
                "aid.keyword": options.appId
              }
            }
          ],
          "must_not": [],
          "should": []
        }
      },
      "from": 0,
      "sort": [],
      "aggs": {}
    }
  }
  else if (options.name == 'getTimeSeriesData') {
    return {
      "query": {
        "bool": {
          "must": [
            {
              "match": {
                "aid.keyword": options.appId
              }
            },
            {
              "range": {
                "st": {
                  "gte": options.startTime,
                  "lte": options.endTime
                }
              }
            }
          ],
          "must_not": [],
          "should": []
        }
      },
      "from": 0,
      "sort": [],
      "aggs": {}
    }
  }
}