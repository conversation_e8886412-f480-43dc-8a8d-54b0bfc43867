"use strict"
const elasticsearch = require("elasticsearch");
const redis = require("redis");
const common = require("common");
const HTTP = common.http_codes;

global.logger = require("log4js").getLogger();
global.logger.level = "error";

const clientRed = redis.createClient(global.config.whiteboard);

clientRed.on("error", function (error) {
  global.logger.error(error);
})

module.exports = {
  getSMSReportStats: async (req, res) => {
    let currentTime = new Date();
    let currentMonth = Number(currentTime.getMonth()) + 1;
    if (currentMonth < 10) {
      currentMonth = "0" + currentMonth;
    }

    const client = new elasticsearch.Client({
      host: global.config.reportServer.hosts[0].host,
    });
    client.search({
      index: "leap." + currentTime.getFullYear() + "." + currentMonth + "." + currentTime.getDate(),
      body: {
        "query": {
          "bool": {
            "must": [
              {
                "match": {
                  "s": "0"
                }
              },
              {
                "range":
                {
                  "st":
                  {
                    "gte": currentTime.getTime() - 3500000,
                    "lte": currentTime.getTime()
                  }
                }
              }
            ]
          }
        },
        "from": 0,
        "size": 0,
        "aggs": {
          "st": {
            "histogram": {
              "field": "st",
              "interval": 3500000,
              "order": {
                "_key": "desc"
              }
            },
            "aggs": {
              "bn": {
                "terms": {
                  "field": "bn",
                  "size": 1000
                },
                "aggs": {
                  "s": {
                    "terms": {
                      "field": "s",
                      "size": 1000
                    },
                    "aggs": {
                      "bnPrice": {
                        "terms": {
                          "field": "bnPrice",
                          "size": 1000
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }).then(async function (response) {
      const smsResult = [];
      for (let buck = 0; buck < response.aggregations.st.buckets.length; buck++) {
        let bucket, pn, sn;
        if (response.aggregations.st.buckets.length == 0) {
          break;
        }
        bucket = response.aggregations.st.buckets[buck];
        for (let p = 0; p < bucket.bn.buckets.length; p++) {
          pn = bucket.bn.buckets[p];
          for (let se = 0; se < pn.s.buckets.length; se++) {
            sn = pn.s.buckets[se];
            for (let bPricen = 0; bPricen < sn.bnPrice.buckets.length; bPricen++) {
              let bPriceValue = sn.bnPrice.buckets[bPricen];
              let bundle_name = pn.key;
              let count = Number(sn.doc_count);
              let price = Number(bPriceValue.key);
              let currentCount = 0;
              await getRedisKeyValuePair(bundle_name).then(key => {
                currentCount = key;
                let grHourly = Number(count) * Number(price);
                if (currentCount != undefined) {
                  currentCount = count + Number(currentCount);
                  clientRed.hset("kenyaSuccessCount", bundle_name, currentCount);
                  let grCummulative = Number(currentCount) * Number(price);
                  smsResult.push({
                    bundle_name: pn.key,
                    hourly_success_count: count,
                    cumulative_success_count: currentCount,
                    generated_revenue_hourly: grHourly,
                    generated_revenue_cummulative: grCummulative
                  });
                } else {
                  clientRed.hmset("kenyaSuccessCount", bundle_name, count);
                  smsResult.push({
                    bundle_name: pn.key,
                    hourly_success_count: count,
                    cumulative_success_count: count,
                    generated_revenue_hourly: grHourly,
                    generated_revenue_cummulative: grHourly
                  });
                }
              }).catch(e => {
                global.logger.error("ERROR", e);
              });
            }
          }
        }
      }
      return res.status(HTTP.ok.code).json(smsResult);
    }, function (error) {
      let empty = [];
      return res.status(HTTP.badRequest.code).json(empty);
    });
  }
}

function getRedisKeyValuePair(bundle_name) {
  return new Promise((resolve, reject) => {
    clientRed.hget("kenyaSuccessCount", bundle_name, function (err, object) {
      resolve(object);
      if (err) {
        reject(err);
      }
    });
  });
}
