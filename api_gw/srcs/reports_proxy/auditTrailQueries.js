const workflowUrls = ["/leap_gw/workflow/*/SUBMIT/", "/leap_gw/workflow/*/REJECT/", "/leap_gw/workflow/*/WITHDRAW/", "/leap_gw/workflow/*/ApproveForStaging/", "/leap_gw/workflow/*/STAGE/", "/leap_gw/workflow/*/ScheduleForLaunch/", "/leap_gw/workflow/*/LAUNCH/", "/leap_gw/workflow/*/RETIRE/", "/leap_gw/workflow/*/ScheduleForRetire/"];

const auditURLArr = ["/leap_gw/users/createUserWithoutPassword", "/leap_gw/users/authenticate", "/leap_gw/users/changePassword", "/leap_gw/users/resetPassword", "/leap_gw/plugins/soap", "/leap_gw/plugins/soap/deploy", "/leap_gw/apps/*", "/leap_gw/apps/*/update"];

const regexUrls = [{
    "mtd": ["DELETE", "POST"],
    "url": "/leap_gw/apps[\/0-9]*"
}, {
    "mtd": ["DELETE"],
    "url": "/leap_gw/pm/[0-9]+"
}];

const wildcardUrls = ["/leap_gw/api/*/assign", "/leap_gw/users/deleteUser/*", "/leap_gw/users/activate/*", "/leap_gw/users/deactivate/*", "/leap_gw/apps/clone/*", "/leap_gw/api/app/archive/*"]

module.exports = {
    getAuditQuery: (from, size, displayFields, sort, searchString, startTime, endTime) => {

        let myQuery = {
            "from": from,
            "size": size,
            "_source": displayFields,
            "sort": sort,
            "query": {
                "bool": {
                    "must": [{
                        "query_string": {
                            "query": searchString,
                            "analyze_wildcard": true,
                            "default_field": "*"
                        }
                    },
                    {
                        "bool": {
                            "should": [{
                                "terms": {
                                    "url": auditURLArr
                                }
                            }],
                            "must_not": [
                                {
                                    "term": {
                                        "mtd": "OPTIONS"
                                    }
                                }
                            ]
                        }
                    }]
                }
            }
        };

        let temp_arr = myQuery.query.bool.must[1].bool.should;
        global.logger.trace("bool obj:", temp_arr);
        workflowUrls.forEach(url => {
            let temp_query = getCompoundQuery_wildcard(url);
            temp_arr.push(temp_query);
        })
        regexUrls.forEach(urlObj => {
            let temp_query = getCompoundQuery_regexp(urlObj.mtd, urlObj.url);
            temp_arr.push(temp_query);
        })
        wildcardUrls.forEach(url => {
            let temp_query = {
                "wildcard": {
                    "url": url
                }
            };
            temp_arr.push(temp_query);
        })
        myQuery.query.bool.must[1].bool.should = temp_arr;
        if (startTime != null && endTime != null) {
            temp_arr = myQuery.query.bool.must;
            let timeFilterObj = {
                "range": {
                    "t": {
                        "gte": startTime,
                        "lte": endTime,
                        "format": "epoch_millis"
                    }
                }
            };
            temp_arr.push(timeFilterObj);
            myQuery.query.bool.must = temp_arr;
        }

        global.logger.trace("Final Es Query", JSON.stringify(myQuery));
        return myQuery;
    }
};

function getCompoundQuery_regexp(mtd, url) {
    return {
        "bool": {
            "must": [
                {
                    "terms": {
                        "mtd": mtd
                    }
                },
                {
                    "regexp": {
                        "url": url
                    }
                }
            ]
        }
    }
}

function getCompoundQuery_wildcard(url) {
    return {
        "bool": {
            "must": [
                {
                    "term": {
                        "mtd": "POST"
                    }
                },
                {
                    "wildcard": {
                        "url": url
                    }
                }
            ]
        }
    }
}