module.exports = {
  LicenceThresholdCrossed: "select count(tps) from tail where time>$startTime and time <$endTime and tps>$thresholdValue;",
  LicenceThreshold80Crossed: "select (sum(tps)/$divide) from tail where time>$startTime and time <$endTime and tps>=(0.8*$thresholdValue);",
  LicenceCapacityUtilization: "select (sum(tps)/$divide) as TPS from tail  where time >$startTime and time <$endTime group by time($interval)",
  threshold10To25: "select count(tps) from tail where time>$startTime and time <$endTime and tps>(0.1*$thresholdValue) and tps<=(0.25*$thresholdValue);",
  threshold25To50: "select count(tps) from tail where time>$startTime and time <$endTime and tps>(0.25*$thresholdValue) and tps<=(0.50*$thresholdValue);",
  threshold50To80: "select count(tps) from tail where time>$startTime and time <$endTime and tps>(0.5*$thresholdValue) and tps<=(0.8*$thresholdValue);",
  threshold80To100: "select count(tps) from tail where time>$startTime and time <$endTime and tps>(0.8*$thresholdValue) and tps<=($thresholdValue);",
  threshold100To110: "select count(tps) from tail where time>$startTime and time <$endTime and tps>($thresholdValue) and tps<=(1.1*$thresholdValue);",
  threshold110To120: "select count(tps) from tail where time>$startTime and time <$endTime and tps>(1.1*$thresholdValue) and tps<=(1.2*$thresholdValue);"
};
