"use strict";
/**
 * Implements the api for applications management.
 **/

const logger = global.logger;

const AppStore = require("app_store");
const message = require("message");
const common = require("common");

const DEFAULT_PAGESIZE = 10;
const error_codes = message.error_codes;
const HTTP = common.http_codes;
const whiteboard = common.whiteboard;

whiteboard.on("health_report", msg => {
  try {
    global.logger.warn("Received the health report", msg);
    msg = JSON.parse(msg);
    let result = module.exports.createOrUpdateHealthReport(msg);
    global.logger.trace("Health report save result", result);
  } catch (error) {
    global.logger.error("Failed to update the Health resport", error);
  }
});

whiteboard.subscribe("health_report");

module.exports = {
  getHealthReports: async (req, res) => {
    try {
      logger.trace("Retreiving list of Health Reports..", req.query);
      let opts = prepareListQuery(req);
      const headers = await AppStore.getHealthCount(Object.assign({}, opts));
      if (opts.page > headers.lastPage) {
        return res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.pageDoesNotExists));
      }
      const response = preparePaginationHeaders(req, res, opts, headers);
      response.data.reportList = await AppStore.listHealthReports(opts);
      return res.status(HTTP.ok.code).json(response);
    } catch (error) {
      console.error("Faile to list the Health reports", error);
      return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
    }
  },

  getHealthReport: async (req, res) => {
    logger.trace("Executing getHealthReport(" + req.params.nid + ")...");
    try {
      if (req.query.role == "it-admin") {
        let app = await AppStore.findHealthReport(req.params.nid);
        return res.status(HTTP.ok.code).json(app);
      } else {
        logger.warn("Report owner and inquisitor doesn't match");
        return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.appNotFound));
      }
    } catch (error) {
      logger.error(error);
      return res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.appStoreError));
    }
  },

  createOrUpdateHealthReport: async (data) => {
    logger.trace("Executing createOrUpdateHealthReport(", data, ")...");
    let response;
    try {
      let report = await AppStore.findHealthReport(data.nid);
      if (report != null) {
        report = await AppStore.updateHealthReport(data)
        logger.trace("Report updated successfully");
      } else {
        report = await AppStore.createHealthReport(data);
        logger.trace("Report created successfully");
      }
      response = message.getResponseJson(null, error_codes.appCreated, HTTP.resourceCreated.code);
      response.nid = report.nid;
      return response;
    } catch (error) {
      logger.error(error);

      if (error.name == "SequelizeUniqueConstraintError") {
        logger.info("ReportId(" + data.nid + ") conflicts with Original ReportID");
        response = message.getResponseJson(null, error_codes.appNameExists);
        response.nid = data.nid;
        return response;
      }
    }
    return message.getResponseJson(null, error_codes.appStoreError);
  }
};

function preparePaginationHeaders(req, res, opts, headers) {
  res.set("totalRecords", headers.totalRecords);
  res.set("pageSize", opts.size);
  if (headers.totalRecords > 0) {
    let header = "/enginehealth?token=" + opts.token + "&size=" + headers.pageSize;
    header += "&page=" + req.query.page;
    res.set("firstPage", header + 1);
    res.set("prevPage", header + headers.prevPage);
    res.set("currPage", header + opts.page);
    res.set("nextPage", header + headers.nextPage);
    res.set("lastPage", header + headers.lastPage);
  }
  let response = message.getResponseJson(req.locale, error_codes.appsListed, HTTP.ok.code);
  response.data = {};
  response.data.pageSize = headers.pageSize;
  response.data.totalRecords = headers.totalRecords;
  response.data.lastPage = headers.lastPage;
  response.data.currentPage = parseInt(opts.page, 10);
  return response;
}

function prepareListQuery(req) {
  let opts = {};
  opts.token = req.query.token && req.query.token.replace(/_/g, '\\_') || "";
  opts.page = req.query.page || 1;
  opts.size = req.query.size || global.config.api_gw.appsApi.defaultPageSize || DEFAULT_PAGESIZE;
  if (req.query.sortf != null) {
    opts.sortf = req.query.sortf;
    opts.order = req.query.order;
  }
  opts.cid = req.query.cid != null ? req.query.cid : undefined;
  opts.hostname = req.query.hostname || undefined;
  logger.trace("HealthReport Request params|" + JSON.stringify(opts));
  return opts;
}
