"use strict";
/**
 *    List of routes for reports API.
 */

const Router = require("express").Router(),
    c4b = require("../session.js").check4Blacklist,
    rid = require("../leap_resources"),
    authorizer = require("authorizer"),
    smsDashboardReports = require("./smsDashboard"),
    appDashboard = require("./appDashboard")

let types = authorizer.accessTypes;

Router.get("/smsDashboard", smsDashboardReports.getSMSReportStats);
Router.post("/getAppReports", appDashboard.getAppReports);
Router.post("/getDetailedAppReport", appDashboard.getDetailedAppReport)

module.exports = Router;