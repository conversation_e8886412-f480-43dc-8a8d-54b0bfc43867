const elasticsearch = require("elasticsearch"),
    logger = global.logger;

const client = new elasticsearch.Client(global.config.reportServer.hosts[0]);

module.exports = {
    getEsResponse: (index, query) => {
        return new Promise((resolve, reject) => {
            let elasticConf = {
                index: index + "*",
                body: query,
                requestTimeout: global.config.reportServer.requestTimeout
            };

            if (global.config.reportServer.elkVersion == 7) {
                delete elasticConf.type;
            }

            client.search(elasticConf)
                .then((esResponse) => {
                    resolve(esResponse);
                })
                .catch((err) => {
                    logger.error("Elasticsearch is down:", err);
                    reject(err);
                });
        });
    }
};
