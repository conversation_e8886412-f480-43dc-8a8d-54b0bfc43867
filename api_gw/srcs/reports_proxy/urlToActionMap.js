const urlMap = Object.freeze({
    users: {
        authenticate: 'logged in.',
        createUser: 'created',
        createUserWithoutPassword: 'created',
        deactivate: 'deactivated',
        deleteUser: 'deleted',
        activate: 'activated',
        updateUser: 'updated',
        logout: 'logged out'
    },
    workflow: {
        SUBMIT: 'submitted',
        WITHDRAW: 'withdrew',
        REJECT: 'rejected',
        ApproveForStaging: 'approved for staging',
        STAGE: 'staged',
        LAUNCH: 'launched',
        RETIRE: 'retired'
    },
    apps: {
        archive: 'archived'
    },
    api: {
        assign: 'assigned'
    },
    plugins: {
        activate: 'activated',
        deactivate: 'deactivated'
    }
});

module.exports.urlMap = urlMap;
