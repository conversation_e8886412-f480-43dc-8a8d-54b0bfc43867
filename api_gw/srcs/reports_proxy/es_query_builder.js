const bodybuilder = require('bodybuilder');

module.exports = {
  prepareESQuery: async (json) => {
    global.logger.trace("Preparing ES query for report:" + json.reportType);
    try {
      // Function expressions
      const getSummary = async () => {
        let query = {
          "size": 0,
          "query": {
            "bool": {
              "must": [{ "match_all": {} }, { "range": { "st": { "gte": json.startTime, "lte": json.endTime, "format": "epoch_millis" } } }]
            }
          },
          "aggs": {
            "uid": { "cardinality": { "field": "uid", "precision_threshold": 10000 } },
            "aid": { "cardinality": { "field": "aid", "precision_threshold": 10000 } }
          }
        };
        return query;
      };

      const getTopUsedApps = async () => {
        let query = bodybuilder()
          .size(0)
          .query('range', 'st', {
            "gte": json.startTime,
            "lte": json.endTime,
            "format": "epoch_millis"
          })
          .aggregation('terms', 'aid', 'aid', {
            "size": 5,
            "order": { "_count": "desc" }
          })
          .build();
        return query;
      };

      const getTopSuccessApps = async () => {
        let query = bodybuilder()
          .size(0)
          .query('match_phrase', 's', { 'query': 0 })
          .query('range', 'st', {
            "gte": json.startTime,
            "lte": json.endTime,
            "format": "epoch_millis"
          })
          .aggregation('terms', 'aid', 'aid', {
            "size": 5,
            "order": { "_count": "desc" }
          })
          .build();
        return query;
      };

      const getTopFailApps = async () => {
        let query = bodybuilder()
          .size(0)
          .query('range', 'st', {
            "gte": json.startTime,
            "lte": json.endTime,
            "format": "epoch_millis"
          })
          .notFilter('match_phrase', 's', { 'query': 0 })
          .aggregation('terms', 'aid', 'aid', {
            "size": 5,
            "order": { "_count": "desc" }
          })
          .build();
        return query;
      };

      const getTop20AppsTotal = async () => {
        let query = bodybuilder()
          .size(0)
          .query('range', 'st', {
            "gte": json.startTime,
            "lte": json.endTime,
            "format": "epoch_millis"
          })
          .aggregation('terms', 'aid', 'aid', {
            "size": 20,
            "order": { "_count": "desc" }
          })
          .build();
        return query;
      };

      const getTop20AppsSuccess = async () => {
        let query = bodybuilder()
          .size(0)
          .query('match_phrase', 's', { 'query': 0 })
          .query('range', 'st', {
            "gte": json.startTime,
            "lte": json.endTime,
            "format": "epoch_millis"
          })
          .aggregation('terms', 'aid', 'aid', {
            "size": 20,
            "order": { "_count": "desc" }
          })
          .build();
        return query;
      };

      const getTop20AppsUsers = async () => {
        let query = bodybuilder()
          .size(0)
          .query('range', 'st', {
            "gte": json.startTime,
            "lte": json.endTime,
            "format": "epoch_millis"
          })
          .aggregation('terms', 'aid', 'aid', {
            "size": 20,
            "order": { "_count": "desc" }
          })
          .build();
        return query;
      };

      const getAppModuleWiseTransactions = async () => {
        let query = bodybuilder()
          .size(0)
          .query('match_phrase', 'aid', { 'query': json.appId })
          .query('range', 'st', {
            "gte": json.startTime,
            "lte": json.endTime,
            "format": "epoch_millis"
          })
          .aggregation('date_histogram', 'st', 'time', {
            'interval': json.interval,
            "time_zone": json.timeZone
          },
            agg => agg.aggregation('nested', { path: 'p' }, 'path',
              agg => agg.aggregation('terms', 'p.inf', 'module')
            ))
          .build();
        return query;
      };

      const getAppActiveUsers = async () => {
        let query = bodybuilder()
          .size(0)
          .query('match_phrase', 'aid', { 'query': json.appId })
          .query('range', 'st', {
            "gte": json.startTime,
            "lte": json.endTime,
            "format": "epoch_millis"
          })
          .aggregation('date_histogram', 'st', 'time', {
            'interval': json.interval,
            "time_zone": json.timeZone,
            "min_doc_count": 1
          })
          .build();
        return query;
      };

      const getAppErrorCodes = async () => {
        let query = bodybuilder()
          .size(0)
          .query('match_phrase', 'aid', { 'query': json.appId })
          .query('range', 'st', {
            "gte": json.startTime,
            "lte": json.endTime,
            "format": "epoch_millis"
          })
          .aggregation('terms', 's', 'time', {
            'size': 10, "order": { "_count": "desc" }
          })
          .build();
        return query;
      };

      const getAppTransactions = async () => {
        let query = bodybuilder()
          .size(0)
          .query('match_phrase', 'aid', { 'query': json.appId })
          .query('range', 'st', {
            "gte": json.startTime,
            "lte": json.endTime,
            "format": "epoch_millis"
          })
          .aggregation('date_histogram', 'st', 'time', {
            'interval': json.interval,
            "time_zone": json.timeZone,
            "min_doc_count": 1
          },
            agg => agg.aggregation('terms', 's', 'status', {
              'size': 10,
              'order': { '_count': 'desc' }
            }))
          .build();
        return query;
      };

      const getAppAvgResponse = async () => {
        let query = bodybuilder()
          .size(0)
          .query('match_phrase', 'aid', { 'query': json.appId })
          .query('range', 'st', {
            "gte": json.startTime,
            "lte": json.endTime,
            "format": "epoch_millis"
          })
          .aggregation('date_histogram', 'st', 'time', {
            'interval': json.interval,
            "time_zone": json.timeZone,
            "min_doc_count": 1
          },
            agg => agg.aggregation('avg', '', 'responseSum', { "field": "rt" }))
          .build();
        return query;
      };

      const getInterfaceApps = async () => {
        let query = bodybuilder()
          .size(0)
          .query('nested', { path: 'p' },
            que => que.query('range', 'p.mst', {
              "gte": json.startTime,
              "lte": json.endTime,
              "format": "epoch_millis"
            })
              .query('query_string', "query", "p.inf:" + json.moduleName + "*", {
                "analyze_wildcard": true,
                "default_field": "*"
              }))
          .aggregation('nested', { path: 'p' }, 'p',
            aggs => aggs.aggregation('date_histogram', 'p.mst', 'moduleTime', {
              "interval": json.interval,
              "time_zone": "Asia/Kolkata",
              "min_doc_count": 1
            }, aggs => aggs.aggregation('terms', 'p.inf', 'module',
              aggs => aggs.aggregation('reverse_nested', {}, 'reverseAgg',
                aggs => aggs.aggregation('cardinality', 'aid', 'application')))))
          .build();
        global.logger.trace("Prepared Query:" + JSON.stringify(query));
        return query;
      };

      const getInterfaceTransactions = async () => {
        let query = bodybuilder()
          .size(0)
          .query('nested', { path: 'p' },
            que => que.query('range', 'p.mst', {
              "gte": json.startTime,
              "lte": json.endTime,
              "format": "epoch_millis"
            })
              .query('query_string', "query", "p.inf:" + json.moduleName + "*", {
                "analyze_wildcard": true,
                "default_field": "*"
              }))
          .aggregation('nested', { path: 'p' }, 'p',
            aggs => aggs.aggregation('date_histogram', 'p.mst', 'moduleTime', {
              "interval": json.interval,
              "time_zone": "Asia/Kolkata",
              "min_doc_count": 1
            }, aggs => aggs.aggregation('terms', 'p.inf', 'module',
              aggs => aggs.aggregation('reverse_nested', {}, 'reverseAgg',
                aggs => aggs.aggregation('sum', 'rt', 'responseSum')))))
          .build();
        return query;
      };

      const queryMap = {
        "summary": getSummary,
        "topUsedApps": getTopUsedApps,
        "topSuccessApps": getTopSuccessApps,
        "topFailApps": getTopFailApps,
        "top20AppsTotal": getTop20AppsTotal,
        "top20AppsSuccess": getTop20AppsSuccess,
        "top20AppsUsers": getTop20AppsUsers,
        "appModuleWiseTransactions": getAppModuleWiseTransactions,
        "appActiveUsers": getAppActiveUsers,
        "appErrorCodes": getAppErrorCodes,
        "appTransactions": getAppTransactions,
        "appAvgResponse": getAppAvgResponse,
        "interfaceApps": getInterfaceApps,
        "interfaceTransactions": getInterfaceTransactions
      };

      if (!queryMap[json.reportType]) {
        throw new Error(`Invalid report type: ${json.reportType}`);
      }

      return await queryMap[json.reportType]();
    } catch (error) {
      throw new Error(`Error while preparing query for ${json.reportType}: ${error.message}`);
    }
  }
};
