const AppStore = require("app_store");
const request = require("request");
const WorkflowMap = require("../workflow_apis/workflow_map");
const SessionManager = require("../session");
const WebSocket = require("ws");

const apigw = global.config.api_gw.protocol + "://" + global.config.api_gw.host + ":" + global.config.api_gw.port + global.config.api_gw.contextPath;

global.userInfoById = {};
global.userInfoByName = {};
global.userInfoByLoginId = {};

let msgs = {
  LAUNCH: 'This App Auto-Launched by System',
  RETIRE: 'This App Auto-Retired by System'
};

async function unlockExpiredApps() {
  try {
    let list = await AppStore.listAppLocks();
    list.forEach(async lock => {
      let res = await AppStore.unLockApp(lock);
      res == 1 && global.wss &&
        global.wss.clients.forEach(client => {
          if (client !== null && client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
              ...lock,
              msg: "You can acquire lock on this App",
              isLocked: false,
              method: "unlock"
            }));
          }
        });
    });
  } catch (error) {
    global.logger.error(error);
  }
}

async function stateTransition(token, startState, targetState) {
  let list = await AppStore.listApps({ scheduleTimeRequired: true, status: WorkflowMap.events[startState].targetState, size: 100 });
  list.rows.forEach(async appInfo => {
    let scheduleTime = new Date(appInfo.scheduleTime);
    if (new Date().getTime() >= scheduleTime.getTime()) {
      let payload = { comment: msgs[targetState], scheduleTime: scheduleTime.getTime() };
      let response = await updateState(payload, token, appInfo.id, targetState);
      let status = response && response.code;
      global.logger.warn("PROCESSED:App", appInfo.name, ",START:", startState, ",TARGET", targetState, ",TIME", scheduleTime, ",STATUS:", status);
    }
  });
}

function getRootJwt() {
  return new Promise(resolve => {
    try {

      let config = global.config.authServer;
      let authserverUrl = config.protocol + "://" + config.host + ":" + config.port + config.ctxPath + "/authenticate";


      let opts = {
        method: 'POST',
        uri: authserverUrl,
        body: {
          username: global.config.authServer.features.defaultRootUser.options.username,
          password: global.config.authServer.features.defaultRootUser.options.password
        },
        json: true // Automatically stringifies the body to JSON
      };

      request(opts, async function (error, response, body) {
        if (error == null && response != null && body != null) {
          let ip = "localhost";
          let token = await SessionManager.createOrGet(body.accessToken, ip);
          global.logger.info("Scheduler authenticated...");
          return resolve(token);
        } else {
          resolve(null);
        }
      });
    } catch (error) {
      global.logger.error(error);
    }
    global.logger.info("Scheduler authentication failed...");
    resolve(null);
  });
}

function updateState(payload, token, appId, action) {
  return new Promise(resolve => {
    let opts = {
      method: 'POST',
      uri: apigw + "/workflow/" + appId + "/" + action,
      headers: {
        Authorization: "Bearer " + token,
        Connection: "keep-alive",
        "Content-Type": "application/json"
      },
      body: payload,
      json: true // Automatically stringifies the body to JSON
    }
    request(opts, function (error, response, body) {
      if (error == null && response != null && body != null) {
        resolve(body);
      } else {
        resolve(null);
      }
    });
  });
}
