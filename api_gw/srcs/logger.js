"use strict";
/**
 ** logger module that initializes log4js settings.
 **/
const log4js = require("log4js"),
    path = require("path"),
    fs = require("fs"),
    fileStreamRotator = require("file-stream-rotator"),
    Morgan = require("morgan"),
    jwt = require('jsonwebtoken');
var logger;

// For this module to be initialized successfully for the first time,
// 'global.config.log' variable should have been set.
if (!(global.config && global.config.api_gw && global.config.api_gw.log)) {
    throw new Error("logger.js -- cannot initialize, because 'global.config' is not set.");
}
const leapGWlog = global.config.api_gw.log;

// create the logs folder if not existing.
let logdir;
if (leapGWlog.logdir) {
    logdir = path.resolve(leapGWlog.logdir);
} else {
    logdir = path.join(__dirname, "/logs");
}
if (!fs.existsSync(logdir)) {
    fs.mkdirSync(logdir);
}

// set up CDR"s logging
var accessLogStream = fileStreamRotator.getStream({
    date_format: "YYYYMMDDHH",
    filename: path.join(leapGWlog.logdir, leapGWlog.cdr + "_%DATE%.cdr"),
    frequency: "daily",
    verbose: false
});
Morgan.token("username", function (req) {
    try {
        if (req.headers && req.headers.authorization) {
            let decoded = jwt.decode(req.headers['authorization'].substring(7));
            return decoded && decoded.loginId;
        } else {
            return req.body.loginId || req.body.username;
        }
    } catch (err) {
        logger.error("Error in logging username in access cdrs:", err);
    }
});
/**
 *  if User is getting added,deleted,deactivated - user is the event [userid:username]
 *  if app is getting created,archived
 *  plugins -uploaded,enabled/disabled,deleted - pluginId:pluginName(servicename,wsdl)- event
 */
Morgan.token("event", function (req, res) {
    try {
        if (req.originalUrl.split("/")[2] == "users") {
            return req.params.userId ? req.params.userId : req.body.loginId;
        } else if (req.originalUrl.split("/")[2] == "apps") {
            return req.params.appId ? req.params.appId : req.body.id;
        } else if (req.originalUrl.split("/")[2] == "workflow") {
            return req.params.appId ? req.params.appId : req.body.appId;
        } else if (req.originalUrl.split("/")[2] == "pm") {
            return req.params.pluginId ? req.params.pluginId : req.body.pluginId;
        } else if (req.originalUrl.split("/")[2] == "plugins") {
            return req.files && req.files.toString();
        }
    } catch (err) {
        logger.error("Error in logging event in access cdrs:", err);
    }
});
Morgan.token("process", function (req, res) {
    try {
        return process.pid;
    }
    catch (err) {
        logger.error("Error in logging event in access cdrs:", err);
    }
});
Morgan.token("transactionId", function (req, res) {
    try {
        return req.txnId;
    }
    catch (err) {
        logger.error("Error in logging event in access cdrs:", err);
    }
});

var morgan = Morgan(leapGWlog.cdrFormat, { stream: accessLogStream });

// Setup log4js logging
// !! NOTE:- Ravindranath @18th December 2017
// There is a change in the way log4js requires appenders (changed in version 2.x)

let logConfig = leapGWlog.log4js;
Object.keys(logConfig.appenders).forEach(appender => {
    try {
        if (!path.isAbsolute(logConfig.appenders[appender].filename)) {
            logConfig.appenders[appender].filename = path.join(leapGWlog.logdir, logConfig.appenders[appender].filename);
        }
    } catch (error) {
        //ignore
    }
});

Object.keys(logConfig.categories).forEach(category => {
    try {
        logConfig.categories[category].appenders = [logConfig.categories[category].appender];
        delete logConfig.categories[category].appender;
    } catch (error) {
        //ignore
    }
});

log4js.configure(logConfig);
logger = log4js.getLogger("apigw");

if (logConfig.categories.pluginCDR != null) {
    global.pluginCDR = log4js.getLogger(logConfig.categories.pluginCDR.appenders[0]);
} else {
    global.pluginCDR = logger;
}
logger.level = logConfig.categories.default.level;

logger.morgan = morgan;
global.log4js = log4js;
global.logger = logger;

module.exports = logger;
module.exports.morgan = morgan;
