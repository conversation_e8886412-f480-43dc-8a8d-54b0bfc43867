/**
 * Implements the api for configapi management.
 **/
const logger = global.logger;
const AppMacros = require("app_macros");
const common = require("common");
const httpCodes = common.http_codes;
const appStates = common.app_states;
const appActions = common.app_transition;
const operators = common.operators;
const userStatuses = common.user_statuses;

module.exports = {

    list: function (req, res) {
        logger.trace("Executing configApi list...");
        return res.status(httpCodes.ok.code).json(global.config.api_gw.appsApi.configApis);
    },
    get: function (req, res) {
        let api = req.params.api;
        logger.trace("Executing " + api + "...");
        switch (api) {
            case "operators": return res.status(httpCodes.ok.code).json(operators);
            case "macros": return res.status(httpCodes.ok.code).json(AppMacros.macros);
            case "appstates": return res.status(httpCodes.ok.code).json(appStates);
            case "appactions": return res.status(httpCodes.ok.code).json(appActions);
            case "userstatus": return res.status(httpCodes.ok.code).json(userStatuses);
            default: return getDefault(api, res);
        }
    },
    info: function (req, res) {
        return res.status(httpCodes.ok.code).json({
            releaseInfo: common.releaseInfo,
            ...global.config.api_gw.studio
        });
    }
};

function getDefault(api, res) {
    if (global.config.api_gw.appsApi[api]) {
        return res.status(httpCodes.ok.code).json(global.config.api_gw.appsApi[api]);
    } else {
        return res.status(httpCodes.notImplemented.code).json({ msg: "API:" + api + " not Implemented" });
    }
}
