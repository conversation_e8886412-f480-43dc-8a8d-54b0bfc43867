/**
 * Implements the api for plugins management.
 **/

const PM = require("pluginmanager");
const logger = global.logger;
const common = require("common");
const HTTP = common.http_codes;
const message = require("message");
const pfsync = require("./pm_impl");

const error_codes = message.error_codes;

module.exports = {
    getPalttePlugins: async (req, res) => {
        logger.trace("Executing getPalttePlugins for category...:" + req.query.category);
        try {
            let result = await PM.getPalttePlugins(req.query.category);
            if (result == null) {
                return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.pluginInternalError));
            } else {
                let httpCode = HTTP.ok.code;
                if (result.code == 801) {
                    httpCode = HTTP.resourceNotFound.code;
                }
                return res.status(httpCode).json(result);
            }
        } catch (error) {
            logger.error(error);
            return res.status(HTTP.internalServerError.code).json(message.getResponseJson(req.locale, error_codes.pluginInternalError));
        }
    },

    getPluginMetaInfo: (req, res) => {
        logger.trace("Executing getPluginMetaInfo for pluginID.....:" + req.params.pluginId);

        PM.getMetaData(req.params.pluginId).then(async (result) => {
            if (result == null) {
                return res.status(HTTP.resourceNotFound.code).json(result);
            } else {
                let data = await pfsync.retrievePluginSettings(result.typeId);
                result.properties.settings = data.properties && data.properties.dev || result.properties.settings;
                logger.trace("getPluginMetaInfo: ", req.params.pluginId, "result.properties.settings:", result.properties.settings);
                return res.status(HTTP.ok.code).json(result);
            }
        }).catch((e) => {
            logger.error(e);
            return res.status(HTTP.internalServerError.code).json(message.getResponseJson(req.locale, error_codes.pluginInternalError));
        });
    },

    executePlugin: async (req, res) => {
        logger.trace("Executing plugin for pluginID.....:" + req.params.pluginId);
        let options = {
            mode: req.query.mode,
            pageno: req.query.pageno,
            pagesize: req.query.pagesize
        };
        let moduleContext = req.body;
        moduleContext.pluginName = req.params.pluginId;
    
        try {
            logger.trace("Request body is:" + req.body);
            let result = await PM.execute(moduleContext, options);
            logger.trace("Response body is:" + JSON.stringify(result));
            if (result.code === error_codes.success) {
                return res.status(HTTP.ok.code).json(result);
            } else {
                return res.status(HTTP.badGateway.code).json(result);
            }
        } catch (error) { // Use "error" instead of "err"
            if (error.code === error_codes.pluginNotFound) {
                let pluginNotFound = message.getResponseJson(req.locale, error_codes.pluginNotFound);
                pluginNotFound.error = error; // Use "error" instead of "err"
                return res.status(HTTP.resourceNotFound.code).json(pluginNotFound);
            } else if (error.code) { // Use "error" instead of "err"
                let pluginInternalError = message.getResponseJson(req.locale, error_codes.pluginInternalError);
                pluginInternalError.error = error; // Use "error" instead of "err"
                return res.status(HTTP.internalServerError.code).json(pluginInternalError);
            }
            return res.status(HTTP.internalServerError.code).json(message.getResponseJson(req.locale, error_codes.pluginInternalError));
        }
    }
    
};
