"use strict";
/**
 * List of routes for plugin management server.
 */

const pm_proxy = require("./pm_impl");

const Router = require("express").Router();
const c4b = require("../session.js").check4Blacklist;

const rid = require("../leap_resources");
const authorizer = require("authorizer");
let types = authorizer.accessTypes;

Router.get("/", c4b, pm_proxy.listPlugins);
Router.get("/getPluginsEnabled", pm_proxy.getPluginsEnabled);
Router.get("/categories", c4b, pm_proxy.getCategories);
Router.post("/:pluginId/update", c4b, pm_proxy.changePluginStatus);
Router.post("/:pluginId/delete", c4b, pm_proxy.deletePlugin);

Router.get("/settings/:pluginId", c4b, pm_proxy.getPluginSettings);
Router.post("/settings/:pluginId", c4b, pm_proxy.savePluginSettings);

module.exports = Router;
