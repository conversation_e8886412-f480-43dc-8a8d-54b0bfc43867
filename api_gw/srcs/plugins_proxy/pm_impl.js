const request = require("request"),
  qs = require("querystring"),
  common = require("common"),
  KPI = require("app_kpi"),
  http_codes = common.http_codes,
  message = require("message"),
  path = require("path"),
  fs = require("fs"),
  logger = global.logger,
  error_codes = message.error_codes,
  whiteboard = common.whiteboard;


const KPI_KEY = KPI.KEYS.appstore;

let protocol = "http", host = "127.0.0.1", port = 4999, cp = "/pm_fsync";
if (global.config && global.config.pm_fsync) {
  protocol = global.config.pm_fsync.protocol;
  host = global.config.pm_fsync.host;
  port = global.config.pm_fsync.port;
  cp = global.config.pm_fsync.contextPath;
}

const pm_fsync_uri = protocol + "://" + host + ":" + port + cp + "/plugins";

const configPath = path.join('/prd/leap', 'config.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

module.exports = {

  listPlugins: (req, res) => {
    try {
      if (req.invoker.role != "it-admin") {
        logger.error("Scope is not allowed for user");
        return res.status(http_codes.resourceConflict.code).json(message.getResponseJson(req.locale, error_codes.invalidScope));
      }

      let startTime = new Date().getTime();
      let uri = pm_fsync_uri + "?" + qs.stringify(req.query);
      request.get(uri).pipe(res);
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listPlugins");
    } catch (error) {
      logger.error(error);
      res.status(http_codes.internalServerError.code).json(error);
    }
  },

  getPluginsEnabled: (req, res) => {
    try {
      let pluginsStatus = {};

      let nodeData = config;

      pluginsStatus = Object.keys(nodeData).reduce((acc, key) => {
        acc[key] = nodeData[key].enabled ? true : false;
        return acc;
      }, {});

      res.status(200).json(pluginsStatus);
    } catch (error) {
      logger.error(error);
      res.status(http_codes.internalServerError.code).json(error);
    }
  },


  getCategories: (req, res) => {
    try {
      if (req.invoker.role == "app-developer" || req.invoker.role == "app-developer-read") {
        logger.error("Scope is not allowed for user");
        return res.status(http_codes.resourceConflict.code).json(message.getResponseJson(req.locale, error_codes.invalidScope));
      }

      let startTime = new Date().getTime();
      let uri = pm_fsync_uri + "/categories?" + qs.stringify(req.query);
      request.get(uri).pipe(res);
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listPluginCategories");
    } catch (error) {
      logger.error(error);
      res.status(http_codes.internalServerError.code).json(error);
    }
  },

  changePluginStatus: (req, res) => {
    try {
      if (req.invoker.role !== "it-admin") {
        logger.error("Scope is not allowed for user");
        return res.status(http_codes.resourceConflict.code).json(message.getResponseJson(req.locale, error_codes.invalidScope));
      }

      let startTime = new Date().getTime();

      // Construct the URL securely
      const pmFsyncUri = new URL(pm_fsync_uri); // pm_fsync_uri should be a trusted base URI, not from user input
      pmFsyncUri.pathname = `${pmFsyncUri.pathname}/${encodeURIComponent(req.params.pluginId)}`; // Append pluginId safely

      // Make the PATCH request using a trusted URL
      request.patch(pmFsyncUri.toString())
        .json(req.body)
        .pipe(res);

      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "updatePluginStatus");
    } catch (error) {
      logger.error(error);
      res.status(http_codes.internalServerError).json(error);
    }
  },


  deletePlugin: (req, res) => {
    try {
      if (req.invoker.role != "it-admin") {
        logger.error("Scope is not allowed for user");
        return res.status(http_codes.resourceConflict.code).json(message.getResponseJson(req.locale, error_codes.invalidScope));
      }

      let startTime = new Date().getTime();
      let uri = pm_fsync_uri + "/" + req.params.pluginId;
      request.delete(uri).pipe(res);
      whiteboard.publish("unmount_pm_settings", req.params.pluginId);
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "deletePlugin");
    } catch (error) {
      logger.error(error);
      res.status(http_codes.internalServerError).json(error);
    }
  },

  getPluginSettings: (req, res) => {
    try {
      if (req.invoker.role !== "it-admin") {
        logger.error("Scope is not allowed for user");
        return res.status(http_codes.resourceConflict.code).json(message.getResponseJson(req.locale, error_codes.invalidScope));
      }

      let startTime = new Date().getTime();

      // Construct the URL securely
      const pmFsyncUri = new URL(pm_fsync_uri); // pm_fsync_uri should be a trusted base URI from config
      pmFsyncUri.pathname = `${pmFsyncUri.pathname}/settings/${encodeURIComponent(req.params.pluginId)}`;

      // Make the GET request using a trusted URL
      request.get(pmFsyncUri.toString()).pipe(res);

      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getPluginSettings");
    } catch (error) {
      logger.error(error);
      res.status(http_codes.internalServerError).json(error);
    }
  },


  retrievePluginSettings: (pluginId) => {
    return new Promise((resolve, reject) => {
      try {
        let startTime = new Date().getTime();
        let id;
        let key = global.plugin_settings_mapping[pluginId];
        if (global.plugin_settings.hasOwnProperty(key)) {
          id = global.plugin_settings[key].id;
        }
        if (id != null) {
          let uri = pm_fsync_uri + "/settings/" + id;
          request({ url: uri }, function (error, response, body) {
            // Do more stuff with 'body' here
            //console.log(error, response, body);
            resolve(JSON.parse(body));
          });
        } else {
          resolve({});
        }

        KPI.emit(KPI_KEY, startTime, new Date().getTime(), "retrievePluginSettings");
      } catch (error) {
        logger.error(error);
        reject(error);
      }

    });
  },

  savePluginSettings: (req, res) => {
    try {
      if (req.invoker.role !== "it-admin") {
        logger.error("Scope is not allowed for user");
        return res.status(http_codes.resourceConflict.code).json(message.getResponseJson(req.locale, error_codes.invalidScope));
      }

      let startTime = new Date().getTime();

      // Construct the URL securely
      const pmFsyncUri = new URL(pm_fsync_uri); // Ensure pm_fsync_uri is a trusted base URI
      pmFsyncUri.pathname = `${pmFsyncUri.pathname}/settings/${encodeURIComponent(req.params.pluginId)}`;

      // Make the POST request using the trusted URL
      request.post(pmFsyncUri.toString())
        .json(req.body)
        .pipe(res);

      // Publish to whiteboard and emit KPI metrics
      whiteboard.publish("mount_pm_settings", req.params.pluginId);
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "updatePluginSettings");
    } catch (error) {
      logger.error(error);
      res.status(http_codes.internalServerError).json(error);
    }
  }

};
