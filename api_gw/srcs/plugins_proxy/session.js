/**
 *   Module that maintains and manages user session
 *
 *   @author: ravindra<PERSON>.<EMAIL>
 *
 **/

"use strict";
const jwt = require("jsonwebtoken");
const utility = require("utility");
const message = require("message");
const common = require("common");
const redis_man = common.redis_man;
const error_codes = message.error_codes;
const jwtDecode = require('jwt-decode');

// Defines how long after session expiry, the refreshtoken api may be called.
const DEFAULT_REFRESH_VALIDITY = "30m";
const DEFAULT_CLEANUP_VALIDITY = "2d";

module.exports = {

  /*
   * Called by client (typically after successful login).
   * Creates a new entry into the redis cache.
   *
   */
  createOrGet: async function (jwToken, deviceRef) {

    global.logger.debug("Session::CreateOrGet() executing...", jwToken);

    // Check if a session for this user already exists.
    let decoded = jwt.decode(jwToken);
    let session = await this.getSession(decoded.id, deviceRef);
    let result;

    try {
      if (session && !isEmptyObject(session)) {
        let decSesToken = jwt.decode(session.token);
        let connection = await redis_man.getConnection("login_sessionstore");
        result = await connection.exists("blacklist:" + decSesToken.id + ":" + decSesToken.iat);
      }
      // If session exists, and is still valid, use that session's
      // jwt insetad of the one obtained from authserver,only if the JWT is not blacklisted.
      if (session && !isEmptyObject(session) && this.isValid(session.token) && result === 0) {
        global.logger.trace("Found a valid existing session for %s:%s. Reusing...", decoded.id, deviceRef);
        return session.token;
      } else {
        // If no session, create a new session entry.
        await this.setSession(decoded.id, deviceRef, { token: jwToken, refreshValidity: decoded.refreshValidity });
        return jwToken;
      }
    } catch (error) {
      global.logger.error("Error while creating/getting session:", error);
    }
  },

  /**
   * Returns a session for a given userId, ipaddr combination.
   * If only userId is provided, retrieves all sessions of this user.
   *
   **/
  getSession: async function (userId, deviceRef) {
    let connection = await redis_man.getConnection("login_sessionstore");
    return connection.hgetall(getUserKey(userId, deviceRef));
  },

  /**
   * Checks if a given user session is still valid
   * i.e. it is not timedout.
   **/
  isValid: function (token) {
    let secretKey = global.config.authSecret;
    try {
      return jwt.verify(token, secretKey);
    } catch (error) {
      //Possible errors:https://www.npmjs.com/package/jsonwebtoken#errors--codes
      global.logger.error("Error while JWT veification:", error);
      return false;
    }
  },


  setSession: async function (userId, deviceRef, sessionData) {
    let connection = await redis_man.getConnection("login_sessionstore");
    let key = getUserKey(userId, deviceRef);
    await connection.hmset(key, sessionData);
    let cleanupDuration = global.config.api_gw.sessionCache.cleanupDuration || DEFAULT_CLEANUP_VALIDITY;
    let cleanupDurationInSec = utility.toSeconds(cleanupDuration);
    await connection.expire(key, cleanupDurationInSec);
    return true;
  },

  delete: async function (token, deviceRef) {
    let connection = await redis_man.getConnection("login_sessionstore");
    let key = getUserKey(token, deviceRef);
    return connection.del(key);
  },

  check4Blacklist: async function (req, res, next) {
    let token, decoded;
    if (req.headers.hasOwnProperty("authorization")) {
      token = req.headers["authorization"].substring(7);
    } else {
      return res.status(401).json(message.getResponseJson(req.locale, error_codes.authorizationMissing));
    }
    let secretKey = global.config.authSecret;
    try {
      decoded = jwtDecode(token);
    } catch (error) {
      if (error.name == 'JsonWebTokenError')
        return res.status(401).json(message.getResponseJson(req.locale, 606));
      else if (error.name == 'TokenExpiredError') {
        if (req.path != '/refreshToken')
          return res.status(401).json(message.getResponseJson(req.locale, 607));
        else {
          decoded = jwt.decode(token);
        }
      }
    }
    req.invoker = decoded;
    next();
  },

  add2Blacklist: async function (token, requestPath) {
    let decoded = jwt.decode(token);
    let expiryTime = decoded.exp * 1000 - new Date().getTime();
    let refreshValidity = decoded.refreshValidity * 1000;
    if (expiryTime > 0) {
      let connection = await redis_man.getConnection("login_sessionstore");
      await connection.set("blacklist:" + decoded.id + ":" + decoded.iat, 1, 'PX', expiryTime);
      return true;
    } else {
      if (requestPath == '/refreshToken') {
        let connection = await redis_man.getConnection("login_sessionstore");
        await connection.set("blacklist:" + decoded.id + ":" + decoded.iat, 1, 'PX', refreshValidity);
        return true;
      } else {
        return false;
      }
    }
  }
};

/* Returns the user's key for session map lookup */
function getUserKey(userId, deviceRef) {
  // extract the userId from jwt, suffix it with deviceRef
  // to obtain the session key.
  return userId + ":" + deviceRef;
}

function isEmptyObject(obj) {
  return Object.keys(obj).length === 0 && obj.constructor === Object;
}
