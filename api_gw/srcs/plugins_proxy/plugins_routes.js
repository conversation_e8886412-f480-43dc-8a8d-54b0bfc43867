"use strict";
/**
 * List of routes for plugin management.
 */

const pluginsProxy = require("./plugins_impl");

const Router = require("express").Router();
const c4b = require("./session.js").check4Blacklist;

const rid = require("../leap_resources");
const authorizer = require("authorizer");
let types = authorizer.accessTypes;

Router.get("/:pluginId", c4b, pluginsProxy.getPluginMetaInfo);
Router.post("/database/:pluginId/exec", c4b, pluginsProxy.executePlugin);
Router.post("/fileSystem/:pluginId/exec", c4b, pluginsProxy.executePlugin);
Router.post("/channelModules/:pluginId/exec", c4b, pluginsProxy.executePlugin);
Router.post("/soap/:typeId/:pluginId", c4b, pluginsProxy.executePlugin);

module.exports = Router;
