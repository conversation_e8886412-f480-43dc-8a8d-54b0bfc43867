"use strict";
/**
 * List of routes for plugin management.
 */

const pluginsProxy = require("./plugins_impl");
const Router = require("express").Router();
const c4b = require("../session").check4Blacklist;
const rid = require("../leap_resources");
const authorizer = require("authorizer");
const types = authorizer.accessTypes;

Router.get("/", c4b, pluginsProxy.getPalttePlugins);

module.exports = Router;
