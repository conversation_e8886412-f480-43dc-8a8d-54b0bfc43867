{"name": "leap_api_gw", "version": "3.0.9", "description": "An api server gateway for all REST api of LEAP.", "author": "<EMAIL>", "license": "ISC", "main": "./srcs/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js"}, "dependencies": {"ajv": "^6.12.3", "app_kpi": "file:../../libs/app_kpi", "app_macros": "file:../../libs/app_macros", "app_store": "file:../../libs/app_store", "app_validation": "file:../../libs/app_validation", "archiver": "^5.2.0", "authorizer": "file:../../libs/authorizer", "axios": "^0.9.0", "bluebird": "^3.7.2", "bodybuilder": "^2.2.21", "colors": "^1.4.0", "commander": "^5.1.0", "common": "file:../../libs/common", "concat-stream": "^2.0.0", "config-tree": "file:../../libs/config-tree", "curl-to-json-convert": "^1.0.9", "elasticsearch": "^16.7.1", "express": "^4.17.1", "express-rate-limit": "^5.2.6", "file-stream-rotator": "^0.5.7", "form-data": "^3.0.0", "http-proxy-middleware": "^1.0.4", "influx": "^5.5.2", "into-stream": "^5.1.1", "ioredis": "^4.17.3", "jsondiffpatch": "^0.4.1", "jsonwebtoken": "^8.5.1", "jwt-decode": "^3.1.2", "jwt-simple": "^0.5.6", "line-reader": "^0.4.0", "lodash": "^4.17.15", "log4js": "6.3.0", "message": "file:../../libs/message", "mkdirp": "^1.0.4", "morgan": "^1.10.0", "multer": "^1.4.2", "multiparty": "^4.2.1", "oam": "file:../../libs/oam", "parse-curl": "^0.2.6", "pluginmanager": "file:../../libs/PluginManager", "query-string": "^6.13.1", "redis": "^3.0.2", "request": "^2.88.2", "request-promise": "^4.2.5", "rimraf": "^3.0.2", "semver": "^7.3.2", "utility": "file:../../libs/utility", "uuid": "^8.2.0", "ws": "^7.3.1", "zip-a-folder": "0.0.12"}}