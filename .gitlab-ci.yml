variables:
  VERSION: "0.0.1"

stages:
  - prepare
  - sonar
  - build
  - notify

before_script:
  - export BASE_DIR="$CI_PROJECT_DIR/studioDeploy"
  - export DEPLOY_DIR="$BASE_DIR/deploy"
  - export DELIVERABLE_DIR="$BASE_DIR/deliverables"
  - export LIBS="$DEPLOY_DIR/libs"
  - export COMPONENTS="$DEPLOY_DIR/components"
  - export EXTLIBS="$DEPLOY_DIR/extlibs"
  - export CONFIG="$DEPLOY_DIR/config"
  - export DOCKER_DIR="$DEPLOY_DIR/docker"
  - echo "DEPLOY_DIR is set to $DEPLOY_DIR"
  - echo "DELIVERABLE_DIR is set to $DELIVERABLE_DIR"
  - echo "LIBS is set to $LIBS"
  - echo "COMPONENTS is set to $COMPONENTS"
  - echo "EXTLIBS is set to $EXTLIBS"
  - echo "CONFIG is set to $CONFIG"
  - echo "DOCKER_DIR is set to $DOCKER_DIR"

prepare_environment:
  image: node10_python_redis:0.0.1
  tags:
    - shell_build
  stage: prepare
  script:
    - npm config set proxy http://************:3128/
    - npm config set https-proxy http://************:3128/
    - export npm_config_nodedir=/prd/leap/node-v22.0.0
    - echo "Making the directory"
    - mkdir -p $BASE_DIR $DOCKER_DIR $DEPLOY_DIR $COMPONENTS $LIBS $EXTLIBS $CONFIG $DELIVERABLE_DIR
    - echo "Moving the necessary modules to deploy";
    - mv api_gw app_engine pm_fsync tps_agent cdr_processor/cdr_sweeper app_session_cleaner docker session_extender $COMPONENTS;
    - mv app_kpi app_macros app_store app_validation common message PluginManager preference_store config-tree msisdn-validator oam authorizer utility $LIBS;
    - mv extlibs/* $EXTLIBS
    - echo "Installing independent dependencies"
    - cd $LIBS/msisdn-validator
    - npm i --production
    - cd $LIBS/utility 
    - npm i --production --legacy-peer-deps
    - cd $LIBS/oam
    - npm i --production
    - cd $LIBS/config-tree
    - npm i --production
    - echo "Installing first level dependencies"
    - cd $LIBS/common
    - npm i --production
    - cd $LIBS/message
    - npm i --production
    - cd $EXTLIBS/sequelize-oracle
    - npm i --production
    - cd $EXTLIBS/smpp
    - npm i --production
    - cd $EXTLIBS/xml2json
    - npm i --production
    - cd $EXTLIBS/xmlrpc
    - npm i --production
    - echo "Installing second level dependencies"
    - cd $LIBS/app_kpi
    - npm i --production
    - cd $LIBS/app_macros
    - npm i --production
    - cd $LIBS/app_store
    - npm i --production
    - cd $LIBS/PluginManager
    - npm i --production
    - cd $LIBS/app_validation
    - npm i --production
    - cd $LIBS/preference_store
    - npm i --production
    - echo "Installing third level dependencies"
    - cd $COMPONENTS/api_gw
    - npm i --production
    - cd $COMPONENTS/app_engine
    - npm i --production
    - cd $COMPONENTS/cdr_sweeper
    - npm i --production
    - cd $COMPONENTS/pm_fsync
    - npm i --production
    - cd $COMPONENTS/tps_agent
    - npm i --production
    - cd $COMPONENTS/app_session_cleaner
    - npm i --production
    - cd $COMPONENTS/session_extender
    - npm i --production
    - cd $CI_PROJECT_DIR
    - tar -czf studioDeploy.tar.gz studioDeploy/
  artifacts:
    paths:
      - studioDeploy.tar.gz
    expire_in: 1 hour

sonarqube:
  stage: sonar
  tags:
    - shell_build
  script:
    - echo "Sonarqube to run here"
    - unset http_proxy
    - curl -X GET "http://************:8800/job/STUDIO_SONAR/buildWithParameters?token=studio_build&VERSION=$CI_COMMIT_SHORT_SHA"
  after_script:
    - echo "${CI_JOB_STATUS}" > build_status
  artifacts:
    paths:
      - build_status

build_images:
  stage: build
  tags:
    - shell_build
  script:
    - unset DOCKER_TLS_CERTDIR
    - tar -xzf studioDeploy.tar.gz
    - echo "Creating Docker components"
    - echo "Creating API_GW docker component"
    - mkdir -p $DOCKER_DIR/api_gw
    - cp -RL $COMPONENTS/api_gw/* $DOCKER_DIR/api_gw
    - docker build -t api_gw:$CI_COMMIT_SHORT_SHA $DOCKER_DIR/api_gw
    - docker save -o api_gw.tar api_gw:$CI_COMMIT_SHORT_SHA
    - echo "Pushing to ECR Registry"
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ID.dkr.ecr.$AWS_REGION.amazonaws.com
    - aws ecr describe-repositories --repository-names api_gw || aws ecr create-repository --repository-name api_gw
    - docker tag api_gw:$CI_COMMIT_SHORT_SHA $ECR_BASE_URL/api_gw:$CI_COMMIT_SHORT_SHA
    - docker push $ECR_BASE_URL/api_gw:$CI_COMMIT_SHORT_SHA
    - docker rmi $ECR_BASE_URL/api_gw:$CI_COMMIT_SHORT_SHA
    - echo "Creating PM_FSYNC docker component"
    - mkdir -p $DOCKER_DIR/pm_fsync
    - cp -RL $COMPONENTS/pm_fsync/* $DOCKER_DIR/pm_fsync
    - docker build -t pm_fsync:$CI_COMMIT_SHORT_SHA $DOCKER_DIR/pm_fsync
    - docker save -o pm_fsync.tar pm_fsync:$CI_COMMIT_SHORT_SHA
    - echo "Pushing to ECR Registry"
    - aws ecr describe-repositories --repository-names pm_fsync || aws ecr create-repository --repository-name pm_fsync
    - docker tag pm_fsync:$CI_COMMIT_SHORT_SHA $ECR_BASE_URL/pm_fsync:$CI_COMMIT_SHORT_SHA
    - docker push $ECR_BASE_URL/pm_fsync:$CI_COMMIT_SHORT_SHA
    - docker rmi $ECR_BASE_URL/pm_fsync:$CI_COMMIT_SHORT_SHA
    - echo "Creating APP_ENGINE docker component"
    - mkdir -p $DOCKER_DIR/app_engine
    - cp -RL $COMPONENTS/app_engine/* $DOCKER_DIR/app_engine
    - docker build -t app_engine:$CI_COMMIT_SHORT_SHA $DOCKER_DIR/app_engine
    - docker save -o app_engine.tar app_engine:$CI_COMMIT_SHORT_SHA
    - echo "Pushing to ECR Registry"
    - aws ecr describe-repositories --repository-names app_engine || aws ecr create-repository --repository-name app_engine
    - docker tag app_engine:$CI_COMMIT_SHORT_SHA $ECR_BASE_URL/app_engine:$CI_COMMIT_SHORT_SHA
    - docker push $ECR_BASE_URL/app_engine:$CI_COMMIT_SHORT_SHA
    - docker rmi $ECR_BASE_URL/app_engine:$CI_COMMIT_SHORT_SHA
    - echo "Creating CDR_SWEEPER docker component"
    - mkdir -p $DOCKER_DIR/cdr_sweeper
    - cp -RL $COMPONENTS/cdr_sweeper/* $DOCKER_DIR/cdr_sweeper
    - docker build -t cdr_sweeper:$CI_COMMIT_SHORT_SHA $DOCKER_DIR/cdr_sweeper
    - docker save -o cdr_sweeper.tar cdr_sweeper:$CI_COMMIT_SHORT_SHA
    - echo "Pushing to ECR Registry"
    - aws ecr describe-repositories --repository-names cdr_sweeper || aws ecr create-repository --repository-name cdr_sweeper
    - docker tag cdr_sweeper:$CI_COMMIT_SHORT_SHA $ECR_BASE_URL/cdr_sweeper:$CI_COMMIT_SHORT_SHA
    - docker push $ECR_BASE_URL/cdr_sweeper:$CI_COMMIT_SHORT_SHA
    - docker rmi $ECR_BASE_URL/cdr_sweeper:$CI_COMMIT_SHORT_SHA
    - echo "Creating Session Extender docker component"
    - mkdir -p $DOCKER_DIR/session_extender
    - cp -RL $COMPONENTS/session_extender/* $DOCKER_DIR/session_extender
    - docker build -t session_extender:$CI_COMMIT_SHORT_SHA $DOCKER_DIR/session_extender
    - docker save -o session_extender.tar session_extender:$CI_COMMIT_SHORT_SHA
    - echo "Pushing to ECR Registry"
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ID.dkr.ecr.$AWS_REGION.amazonaws.com
    - aws ecr describe-repositories --repository-names session_extender || aws ecr create-repository --repository-name session_extender
    - docker tag session_extender:$CI_COMMIT_SHORT_SHA $ECR_BASE_URL/session_extender:$CI_COMMIT_SHORT_SHA
    - docker push $ECR_BASE_URL/session_extender:$CI_COMMIT_SHORT_SHA
    - docker rmi $ECR_BASE_URL/session_extender:$CI_COMMIT_SHORT_SHA
    - mkdir -p $DOCKER_DIR/config-tree
    - cp -RL $LIBS/config-tree/* $DOCKER_DIR/config-tree
    - docker build -t config-tree:$CI_COMMIT_SHORT_SHA $DOCKER_DIR/config-tree
    - docker save -o config-tree.tar config-tree:$CI_COMMIT_SHORT_SHA
    - echo "Pushing to ECR Registry"
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ID.dkr.ecr.$AWS_REGION.amazonaws.com
    - aws ecr describe-repositories --repository-names config-tree || aws ecr create-repository --repository-name config-tree
    - docker tag config-tree:$CI_COMMIT_SHORT_SHA $ECR_BASE_URL/config-tree:$CI_COMMIT_SHORT_SHA
    - docker push $ECR_BASE_URL/config-tree:$CI_COMMIT_SHORT_SHA
    - docker rmi $ECR_BASE_URL/config-tree:$CI_COMMIT_SHORT_SHA

final_notification:
  stage: notify
  tags:
    - shell_build
  script:
    - echo "Sending final notification"
    - unset http_proxy
    - curl -X GET "http://************:8800/job/STUDIO_FINALNOTIFICATION/buildWithParameters?token=studio_build&VERSION=$CI_COMMIT_SHORT_SHA"
  after_script:
    - echo "${CI_JOB_STATUS}" > build_status
  artifacts:
    paths:
      - build_status
