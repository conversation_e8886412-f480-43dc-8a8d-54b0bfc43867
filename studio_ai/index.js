
const express = require('express');
const bodyParser = require('body-parser');
const { getJsonConstant, getAllModules } = require('./configs')
const natural = require('natural');
const tokenizer = new natural.WordTokenizer();
const { v4 } = require("uuid");
const axios = require('axios');

// Initialize Express app
const app = express();

// Disable Express framework disclosure
app.disable('x-powered-by');

// Middleware to parse JSON bodies
app.use(bodyParser.json());


function parseUserInput(input) {
  const tokens = tokenizer.tokenize(input);
  const modules = getAllModules();
  return tokens.filter(token => modules.includes(token.toLowerCase()));
}

function generatePluginSequence(input) {
  const parsedInput = parseUserInput(input);
  const sequence = ["start", ...parsedInput, "end"];
  return sequence.join(',');
}

function callOpenAI(prompt) {
  const data = {
    "model": "gpt-3.5-turbo",
    "messages": [{ "role": "user", "content": "I have a specific set of workflow: http, zendesk, freshdesk, etc. These nodes are always constant. Help me to generate flows based on my input.\n\nplugins available: [\n 'appStart',\n 'appEnd',\n 'whatsapp',\n 'voice',\n 'email',\n 'sms',\n 'webhook',\n 'rcs',\n 'http',\n 'choice',\n 'repeat',\n 'ussd',\n 'messenger',\n 'instagram',\n 'line',\n 'telegram',\n 'wechat',\n 'keypress',\n 'hangup',\n 'jump',\n 'datetime',\n 'callforward',\n 'wait',\n 'addcontact',\n 'googlesheet',\n 'database',\n 'language',\n 'waitforresponse',\n 'workday',\n 'shopify',\n 'servicenow',\n 'openai',\n 'hubspot',\n 'zapier',\n 'salesforce',\n 'amazons3',\n 'amazonsns',\n 'adp',\n 'github',\n 'monday',\n 'quickbooks',\n 'stripe',\n 'slack',\n 'facebook',\n 'linkedin',\n 'zendesk',\n 'excel',\n 'mail',\n 'teams',\n 'onedrive',\n 'zoho',\n 'freshdesk',\n 'dialogflow',\n 'script',\n 'menu'\n].\n\nInput: User will give what flow he wants. He will tell in natural language.\nOutput: Flow obtained in string separated by comma.\n\nExample:\nUser Input,Processed Sequence\n'I first want to hit http node, then whatsapp node, then http node, then zendesk node', 'start,http,whatsapp,http,zendesk,end'\n'I first want to hit zendesk, then whatsapp', 'start,zendesk,whatsapp,end'\n'First I will hit whatsapp two times and then hit zendesk', 'start,whatsapp,whatsapp,zendesk,end'\n'Just use http three times', 'start,http,http,http,end'\n'Start with zendesk and finish with sms', 'start,zendesk,sms,end'\n'After http, use whatsapp and then go back to http', 'start,http,whatsapp,http,end'\n'Http followed by three consecutive zendesk nodes', 'start,http,zendesk,zendesk,zendesk,end'\n'Begin with whatsapp, end with freshdesk', 'start,whatsapp,freshdesk,end'\n'Sms to whatsapp and back to sms', 'start,sms,whatsapp,sms,end'\n'Start, then hit openai, followed by zendesk, and end', 'start,openai,zendesk,end'\n'Hit http, then cycle through whatsapp, sms, and zendesk', 'start,http,whatsapp,sms,zendesk,end'\n'Start with openai, then double hit on zendesk, followed by freshdesk', 'start,openai,zendesk,zendesk,freshdesk,end'\n'Just a single use of http', 'start,http,end'\n" + prompt }], "temperature": 0.7
  };

  const config = {
    method: 'post',
    url: 'https://api.openai.com/v1/chat/completions', // Update the engine if needed
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ***************************************************`
    },
    data: data
  };

  return axios(config)
    .then(response => {
      return response.data.choices[0].message.content; // Extracting the relevant content
    })
    .catch(error => {
      console.error('Error calling OpenAI:', error);
    });
}

// Middleware to parse JSON bodies
app.use(bodyParser.json());

// Define a POST route
app.post('/createWithAI', async (req, res) => {
  //let sequence = generatePluginSequence(req.body.prompt);
  let sequence = await callOpenAI(req.body.prompt);
  let plugins = sequence.split(",");
  let appJson = {
    "modules": {}
  };
  let previousUniqueId = null;
  let yStepUp = 120;
  let x = 610;
  let initialY = (plugins.length - 1) * yStepUp;
  let y = initialY + 20;
  for (var i = plugins.length - 1; i >= 0; i--) {
    let tempJson = {};
    let uniqueId = v4().slice(0, 7);
    tempJson = getJsonConstant(plugins[i]);
    if (previousUniqueId != null) {
      tempJson.output.customCodeIds.conditionalLink[0] = previousUniqueId;
    }
    tempJson.coordinates.x = x;
    tempJson.coordinates.y = y;
    y = y - yStepUp;
    previousUniqueId = uniqueId;
    appJson["modules"][uniqueId] = tempJson;
    if (i == 0) {
      appJson["startId"] = uniqueId;
      appJson["links"] = [];
    }
  }
  res.status(200).json({
    "name": "App " + v4().slice(0, 3),
    "desc": req.body.prompt.slice(0, 31),
    "appData": appJson
  });
});

// Start the server
const PORT = 3011;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
