{"maxerr": 50, "bitwise": true, "camelcase": false, "curly": true, "eqeqeq": true, "forin": true, "immed": false, "indent": false, "latedef": true, "newcap": true, "noarg": true, "noempty": true, "nonew": false, "plusplus": false, "quotmark": false, "undef": true, "unused": true, "strict": true, "trailing": true, "maxparams": false, "maxdepth": false, "maxstatements": false, "maxcomplexity": false, "maxlen": false, "asi": true, "boss": true, "debug": false, "eqnull": false, "es5": false, "esnext": true, "moz": false, "evil": false, "expr": true, "funcscope": false, "globalstrict": false, "iterator": false, "lastsemic": true, "laxbreak": true, "laxcomma": false, "loopfunc": false, "multistr": true, "proto": true, "scripturl": false, "smarttabs": false, "shadow": true, "sub": false, "supernew": false, "validthis": false, "browser": true, "couch": false, "devel": true, "dojo": false, "jquery": false, "mootools": false, "node": true, "nonstandard": false, "prototypejs": false, "rhino": false, "worker": false, "wsh": false, "yui": false, "noyield": true, "nomen": false, "onevar": false, "passfail": false, "white": false, "globals": {"describe": true, "it": true, "before": true, "afterEach": true, "beforeEach": true, "after": true}}