/**
 *  A supposedly smart authorization handling module, that checks the given
 *  user's permissions against a given opreation and returns a authority value boolean.
 *
 *  <AUTHOR>
 **/

"use strict";

// An enumeration about the types of resource access
const AccessType = {
  accessNone: 0,    // binary 0000-0000
  accessExec: 1,    // binary 0000-0001
  accessWrite: 2,    // binary 0000-0010
  accessRead: 4,    // binary 0000-0001
};

const functions = {

  /*
   * Check if the claims contain the requested access on a resource
   * @param claims: {JSONObject}
   * @param resourceId: {String} A string of the form abc.de.f.g (with or without dots).
   *                      if dots present, it indicates a sub-resource
   * @param accessType: An enumeration value indicating the desired access check
   *                      i.e. read, write, exec, or a combination of these using
   *                      bitwise AND separator.  Ex: read & write & exec
   *
   * @return {boolean} true, if access is available, false otherwise.
   *
   * Example:
   * In the below use case,
   * if these claims, resourceId and accessType are passed as parameters,
   *  claims => { "1": 2, "2.3": 1, "5.1.2": 4 }
   *  resourceId => "2" or 2 or "channel" (channel is a string representation of resource id)
   *  accessType => 4 (read access)
   * then
   *  it should return false  // no access for resourceId 2 is present in claims.
   */
  checkPerms: function (claims, resourceId, accessType) {

    if (claims == null) return false;
    if (typeof claims == "string") {
      try {
        claims = JSON.parse(claims);
      } catch (e) {
        return false;
      }
    }

    if (typeof accessType == "string")
      return checkPermsForStringPermsNotation(claims, resourceId, accessType);
    else
      return checkPermsForBitMaskPermsNotation(claims, resourceId, accessType);
  }

};

function checkPermsForBitMaskPermsNotation(claims, resourceId, accessType) {
  // breakdown the resource into an array separeted by dot.
  let resourceArray = resourceId.split(".");

  // Loop through resource construct from leaf to root.
  // I.e. if resourceId is 5.2.1.1,
  // then
  //  1st iteration => 5.2.1.1
  //  2nd iteration => 5.2.1
  //  3rd iteration => 5.2
  //  4th iteration => 5
  //
  // in each iteration, find a match (exact or wildcard) in the claims
  let resourceCount = resourceArray.length;
  for (let i = resourceCount; i > 0; i--) {

    if (i == resourceCount) {
      // first, try exact matching...
      let exactPattern = resourceArray.slice(0, i).join(".");
      if (claims && claims.hasOwnProperty(exactPattern)) {
        return (claims[exactPattern].perms & accessType) == accessType;
      }
    }
    // else check if user has wildcard access on sub-resources.
    else if (i < resourceCount) {
      let wildcardPattern = resourceArray.slice(0, i).join(".") + ".*";
      if (claims && claims.hasOwnProperty(wildcardPattern)) {
        return (claims[wildcardPattern].perms & accessType) == accessType;
      }
    }
  }

  // check if user has wildcard resource access, in which case no further checks.
  return claims.hasOwnProperty("*");
}

function checkPermsForStringPermsNotation(claims, resourceId, accessType) {
  // breakdown the resource into an array separeted by dot.
  let resourceArray = resourceId.split(".");

  // Loop through resource construct from leaf to root.
  // I.e. if resourceId is 5.2.1.1,
  // then
  //  1st iteration => 5.2.1.1
  //  2nd iteration => 5.2.1
  //  3rd iteration => 5.2
  //  4th iteration => 5
  //
  // in each iteration, find a match (exact or wildcard) in the claims
  let resourceCount = resourceArray.length;
  for (let i = resourceCount; i > 0; i--) {

    if (i == resourceCount) {
      // first, try exact matching...
      let exactPattern = resourceArray.slice(0, i).join(".");
      if (claims.perms && claims.perms.hasOwnProperty(exactPattern)) {
        return claims.perms[exactPattern].indexOf(accessType) != -1;
      }
    }
    // else check if user has wildcard access on sub-resources.
    else if (i < resourceCount) {
      let wildcardPattern = resourceArray.slice(0, i).join(".") + ".*";
      if (claims.perms && claims.perms.hasOwnProperty(wildcardPattern)) {
        return claims.perms[wildcardPattern].indexOf(accessType) != -1;
      }
    }
  }

  // check if user has wildcard resource access, in which case no further checks.
  return claims.perms.hasOwnProperty("*");
}

module.exports = functions;
module.exports.accessTypes = AccessType;
