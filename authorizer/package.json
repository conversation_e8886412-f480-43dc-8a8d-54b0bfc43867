{"_from": "authorizer@^2.1.2", "_id": "authorizer@2.1.2", "_inBundle": false, "_integrity": "sha1-5laXKoLKUZ0pUkRHx0Z84tJrhQs=", "_location": "/authorizer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "authorizer@^2.1.2", "name": "authorizer", "escapedName": "authorizer", "rawSpec": "^2.1.2", "saveSpec": null, "fetchSpec": "^2.1.2"}, "_requiredBy": ["/"], "_resolved": "http://cots.comviva.com/api/npm/Global_NPM_Repo/authorizer/-/authorizer-2.1.2.tgz", "_shasum": "e656972a82ca519d29524447c7467ce2d26b850b", "_spec": "authorizer@^2.1.2", "_where": "/data/git/deploy/components/api_gw", "author": {"name": "<EMAIL>"}, "bundleDependencies": false, "deprecated": false, "description": "A utility to check if a user has access to a resource to perform a specific action.", "devDependencies": {"chai": "^4.1.2"}, "keywords": ["authorizer"], "license": "ISC", "main": "index.js", "name": "authorizer", "publishConfig": {"registry": "http://cots.mahindracomviva.com/api/npm/Comviva-Npm-Repo/"}, "scripts": {"test": "mocha"}, "version": "2.1.2"}