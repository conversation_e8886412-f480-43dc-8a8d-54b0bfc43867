/**
 *  Tests the authorization permissions.
 **/

"use strict";
const chai = require('chai');
const authorizer = require('./index.js');
var expect = chai.expect;

let types = authorizer.accessTypes;

describe('Test suite for simple claims.....', function () {

  // sample claims data.
  const simpleClaims = { "1": types.accessExec, "2": types.accessNone };
  let claimsString = JSON.stringify(simpleClaims);

  it('Given simple claims:' + claimsString + ', exec access on "1" should succeed', function () {
    let result = authorizer.checkPerms(simpleClaims, "1", types.accessExec)
    expect(result).to.be.true;
  });

  it('Given simple claims:' + claimsString + ', write access on "1" should fail.', function () {
    let result = authorizer.checkPerms(simpleClaims, "1", types.accessWrite)
    expect(result).to.be.false;
  });

  it('Given simple claims:' + claimsString + ', Read access on "1" should fail.', function () {
    let result = authorizer.checkPerms(simpleClaims, "1", types.accessRead)
    expect(result).to.be.false;
  });

  it('Given simple claims:' + claimsString + ', Read access on on non-existent resource "7" should fail.', function () {
    let result = authorizer.checkPerms(simpleClaims, "7", types.accessExec)
    expect(result).to.be.false;
  });

  it('Given simple claims:' + claimsString + ', Read access on on "2" should fail.', function () {
    let result = authorizer.checkPerms(simpleClaims, "2", types.accessRead)
    expect(result).to.be.false;
  });

  it('Given simple claims:' + claimsString + ', Exec access on on "2" should fail.', function () {
    let result = authorizer.checkPerms(simpleClaims, "2", types.accessExec)
    expect(result).to.be.false;
  });

  it('Given simple claims:' + claimsString + ', Write access on on "2" should fail.', function () {
    let result = authorizer.checkPerms(simpleClaims, "2", types.accessWrite)
    expect(result).to.be.false;
  });

});


describe('Test suite for nested resources...', function () {

  let nestedResourceClaims = {
    "1": 7,    // all perms on resource 1
    "1.1": 4,  // Read perm on sub-resource 1.1
    "1.1.2": 1  // No perms on sub-resource 1.1.2
  }
  let claimsString = JSON.stringify(nestedResourceClaims);

  it('On claims ' + claimsString + ', read access for resource "1.1" should succeed.', function () {
    let result = authorizer.checkPerms(nestedResourceClaims, "1.1", types.accessRead);
    expect(result).to.be.true;
  })

  it('On claims ' + claimsString + ', read access for non-existing claim on resource "1.5" should fail.', function () {
    let result = authorizer.checkPerms(nestedResourceClaims, "1.5", types.accessRead);
    expect(result).to.be.false;
  })

  it('On claims ' + claimsString + ', write access for resource "1.1" should fail.', function () {
    let result = authorizer.checkPerms(nestedResourceClaims, "1.1", types.accessWrite);
    expect(result).to.be.false;
  })

  it('On claims ' + claimsString + ', exec access for resource "1.1" should fail.', function () {
    let result = authorizer.checkPerms(nestedResourceClaims, "1.1", types.accessExec);
    expect(result).to.be.false;
  })

  it('On claims ' + claimsString + ', exec access for resource "1.1.2" should succeed.', function () {
    let result = authorizer.checkPerms(nestedResourceClaims, "1.1.2", types.accessExec);
    expect(result).to.be.true;
  })
});

describe('Test suite for accessing wildcard claims ...', () => {
  let wildcardClaims = { "*": 7 /* access on every resource type */ }
  let claimsString = JSON.stringify(wildcardClaims);

  it('On wildcardClaims: ' + claimsString + ', read access should pass', () => {
    let result = authorizer.checkPerms(wildcardClaims, "1", types.accessRead);
    expect(result).to.be.true;
  })

  it('On wildcardClaims: ' + claimsString + ', write access should pass', () => {
    let result = authorizer.checkPerms(wildcardClaims, "1", types.accessWrite);
    expect(result).to.be.true;
  })

  it('On wildcardClaims: ' + claimsString + ', exec access should pass', () => {
    let result = authorizer.checkPerms(wildcardClaims, "1", types.accessExec);
    expect(result).to.be.true;
  })

  it('On wildcardClaims: ' + claimsString + ', nested resource resource (1.2.3.4) access should pass', () => {
    let result = authorizer.checkPerms(wildcardClaims, "resource-1.2.3.4", types.accessExec);
    expect(result).to.be.true;
  })

  it('On wildcardClaims: ' + claimsString + ', nested resource resource (1.2) access should fail, if explicitly prohibited.', () => {
    let result = authorizer.checkPerms(wildcardClaims, "resource-1.2.3.4", types.accessExec);
    expect(result).to.be.true;
  })
});



describe('Wildcard exceptions test suite...', () => {
  let claims = {
    "*": 7,    // all access on resources...
    "5": 1     // except, exec on resource type 5.
  }
  let claimsString = JSON.stringify(claims);

  it('On claims ' + claimsString + ', access to resource 1 should pass.', () => {
    let result = authorizer.checkPerms(claims, "1", types.accessExec);
    expect(result).to.be.true;
  });

  it('On claims ' + claimsString + ', read access to explicitly forbidden resource 5 should fail.', () => {
    let result = authorizer.checkPerms(claims, "5", types.accessRead);
    expect(result).to.be.false;
  });

  it('On claims ' + claimsString + ', write access to explicitly forbidden resource 5 should fail.', () => {
    let result = authorizer.checkPerms(claims, "5", types.accessWrite);
    expect(result).to.be.false;
  });

  it('On claims ' + claimsString + ', exec access to explicitly granted resource 5 should succeed.', () => {
    let result = authorizer.checkPerms(claims, "5", types.accessWrite);
    expect(result).to.be.false;
  });
});


describe("Testing access perms for sub resources with partial wildcards...", () => {

  let claims = {
    "1.2.*": 7,    // all access on resources that start with 1.2
    "1.2.3": 2,    // except for resource id 1.2.3, which has ony write
    "res-1.2.5.1": 1   // for 1.2.5.1, only exec perm
  }
  let claimsString = JSON.stringify(claims);


  it("On claims " + claimsString + ", read access check on resource 1.2.4 should pass.", () => {
    let result = authorizer.checkPerms(claims, "1.2.4", types.accessRead);
    expect(result).to.be.true;
  })

})

