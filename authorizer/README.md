<p align="center"><img src="authorizer.png" /></p>
# Brief
<PERSON><PERSON><PERSON><PERSON><PERSON> (a.k.a authorizer) is a sanskrit name for someone or something that 
can authorize things.

This is a tiny utility module that can decipher the *claims* data contained in 
authserver's JWT.  Claims is a json object that contains info about resources
and their respective permissions.  So, it could be said that this module is a 
helper module for any NodeJS application that requires consistent deciphering
of authserver's claims.

# Quick overview
If you are writing a service (typically web service) that allows authenticated / 
authorized conditional access to underlying resources, then you would need to
decode the JWT, extract the claims and then check claims contained therein,
to decide whether to allow access or not.


```javascript
app.get('/books', req, res) {
    // Decode jwt
    let jwt = req.headers('Authorization'); // read token
    let jwtInfo = jwt.verify(jwt);  // verify the token
    
    // read the claims and allow access, if user has 
    // at least read perm on books resource
    var authorizer = require('adhikaarika');
    var types = authorizer.accessTypes;
    
    if (authorizer.checkPerms(
        jwtInfo.claims,     // claims as contained in jwt
        "5",                // assume book resource is identified by 5
        types.accessRead
    ) {
        //TODO: Now it's OK to allow books listing.
    } else {
        res.status(403).json({msg: "No read access on books resource"});
    }
}
```

<br />
# Installation
```npm install adhikaarika```

<br>
# Basic Usage
```javascript
const authorizer = require('adhikaarika');
...

app.get("/books", req, res) {
    ...
    // Somewhere down the line in your code,
    // Let us check if user has read perms on books resource type '5'
    claims = jwt.claims; // assuming req has authorization hdr containing jwt
    let allow = authorizer.checkPerms(claims, '5', authorizer.AccessTypes.accessRead);
    
    if (allow) {
        // proceed with request processing...
    }
}
```

<br><br>
# Authorizer in detail... All about Claims, Resources and Permissions

## Claims
Claims is a JSON object that contains resources and their respective permissions
as key:value pairs.  For example, the following is a claims object, which 
indicates that the holder of this claims has permission value '1' 
(exec permission), on a resource identified as "5".
<br>
```{ "5" : 1 }```


### Resource
A resources is a numeric identifier to an entity that corresponds to real world.
Below are some examples of various types of resources.

- A single number (without any dots) such as, ```"1"``` identifies a top level resource. ex: user
- A number having one dot ```1.1``` identifies a a sub resource within a top resource. ex: user.admin
- A number with 2 dots ```1.1.2``` identifies a sub resource 2 levels deep. ex: user.admin.it

<br>
### Permissions
Permission is a bitmask number to identify read, write, exec permission values. 
This is similar to popular file permissions concept in unix operating systems.
<br>
The adhikaarika library provides enum constants for ease of use, instead of
having to remember the bitmask values.
```javascript
// An enumeration about the types of resource access
const AccessType = {
  accessNone:   0,    // binary 0000-0000
  accessExec:   1,    // binary 0000-0001
  accessWrite:  2,    // binary 0000-0010
  accessRead:   4,    // binary 0000-0001
};

...
...
module.exports.accessTypes = AccessType;
```

In your application code, you may access these permission types, as below:
```javascript
const authorizer = require('adhikaarika');
const types = authorizer.accessTypes;
...
```

<br>
Now that we know about resources and permissions, it is time to get into the 
nitty-gritties of claims.  Consider the example claims object below, to 
understand different combinations of claims.

```javascript
  let claims = {
    "1" :  7        // On resource '1', user has all (read|write|exec) perms
    "1.2 : 4,       // On a sub-resource '1.2', user has read perm
    "1.2.3" : 2,    // On sub-resource 1.2.3, user has write perm
    "*******" : 1   // On sub-resource *******, user has exec perm
  }
```

<br>
### Wildcard notation for resources
If your app deals with resources that are repeating sub-types (such as 1, 1.2,
1.2.1, 1.2.3, 1.2,3.4, ...), and you have to give common permissions to all 
sub-resources beginning with 1, then wildcard notation is your best aid.
<br>
Instead of 
```javascript
{
    "1": <perm>,
    "1.2": <perm>,
    "1.2.1": <perm>,
    "1.2.3": <perm>,
    "*******": <perm>
}
```
it can be concisely stored as
```javascript
{
    "1.*" : <perm>
}
```

if resources starting with 1.2 have a different type of perms, then,
```javascript
{
    "1": <perms1>,
    "1.2.*" : <perms2>,
}
```

# Join In!
I'm happy to receive bug reports, fixes, documentation enhancements, and any other improvements.
