/**
 *  Defines a named list of standard HTTP response codes, as per standard.
 *  <AUTHOR>
 **/

module.exports = {
  // success codes
  ok: { code: 200, msg: "success" },
  resourceCreated: { code: 201, msg: "Resource Created" },
  accepted: { code: 202, msg: "Request Accepted" },
  noContent: { code: 204, msg: "No Content" },

  // client side errors
  badRequest: { code: 400, msg: "Bad Request." },
  invalidAuth: { code: 401, msg: "Invalid authorization." },
  paymentRequired: { code: 402, msg: "Payment required." },
  forbidden: { code: 403, msg: "Forbidden." },
  resourceNotFound: { code: 404, msg: "Resource Not Found." },
  methodNotAllowed: { code: 405, msg: "Method not allowed." },
  notAcceptable: { code: 406, msg: "Not Acceptable" },
  resourceConflict: { code: 409, msg: "Resource conflict." },
  payloadTooLarge: { code: 413, msg: "Payload too large" },
  tooManyRequests: { code: 429, msg: "Too Many Requests" },

  // server errors
  notImplemented: { code: 501, msg: "Service not implemented." },
  internalServerError: { code: 500, msg: "Internal Server Error." },
  badGateway: { code: 503, msg: "Bad Gateway." },
  gatewayTimeout: { code: 504, msg: "Gateway timeout." },

  response: function (code) {
    switch (code) {
      case 200: return this.ok;
      case 201: return this.resourceCreated;
      case 202: return this.accepted;
      case 204: return this.noContent;
      case 400: return this.badRequest;
      case 401: return this.invalidAuth;
      case 402: return this.paymentRequired;
      case 403: return this.forbidden;
      case 404: return this.resourceNotFound;
      case 405: return this.methodNotAllowed;
      case 406: return this.notAcceptable;
      case 409: return this.resourceConflict;
      case 413: return this.payloadTooLarge;
      case 429: return this.tooManyRequests;
      case 500: return this.internalServerError;
      case 501: return this.notImplemented;
      case 503: return this.badGateway;
      case 504: return this.gatewayTimeout;
    }
  }
};
