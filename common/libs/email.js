const nodemailer = require("nodemailer");
const EmailTemplates = require("swig-email-templates");
const path = require("path");
const templates = new EmailTemplates({ root: path.join(__dirname, "../email_templates") });

var settings = {};
var connects = {};

module.exports = {
  init: (key, opts) => {
    // Ensure secure connection options
    opts.secure = true;  // Use SSL/TLS
    opts.port = opts.port || 465;  // Default to port 465 for secure SMTP
    opts.host = opts.host || "smtp.gmail.com";  // Specify a secure SMTP host
    opts.tls = { rejectUnauthorized: true }; // Enforce TLS certificate validation

    settings[key] = opts;
    return key;
  },

  test: (key) => {
    if (settings[key]) {
      // Ensure only secure connections by verifying settings
      if (
        (settings[key].service && settings[key].service.toLowerCase().includes("http")) ||
        (settings[key].host && settings[key].host.toLowerCase().includes("http"))
      ) {
        console.warn("Warning: Insecure protocol detected. Use secure SMTPS configurations.");
        return Promise.resolve(false);
      }

      // Create transport directly with nodemailer (without nodemailer-smtp-transport)
      let transport = nodemailer.createTransport({
        host: settings[key].host,
        port: settings[key].port,
        secure: settings[key].secure,  // true for 465, false for other ports
        auth: settings[key].auth,
        tls: settings[key].tls
      });

      return new Promise(resolve => {
        transport.verify(err => {
          if (err) {
            resolve(false);
          } else {
            resolve(true);
          }
        });
      });
    }
    return Promise.resolve(false);
  },

  connect: (key) => {
    if (settings[key]) {
      let transport = nodemailer.createTransport({
        host: settings[key].host,
        port: settings[key].port,
        secure: settings[key].secure,
        auth: settings[key].auth,
        tls: settings[key].tls
      });

      return new Promise(resolve => {
        transport.verify(err => {
          if (err) {
            resolve(false);
          } else {
            connects[key] = transport;
            resolve(true);
          }
        });
      });
    }
    return Promise.resolve(false);
  },

  sendMail: (key, opts, context) => {
    let transport;
    return new Promise((resolve, reject) => {
      try {
        templates.render(opts.template, context, (err, html) => {
          if (err) {
            return reject(err);
          }

          let mailOptions = {
            ...opts,
            html: html
          };

          if (connects[key] == null) {
            transport = nodemailer.createTransport({
              host: settings[key].host,
              port: settings[key].port,
              secure: settings[key].secure,
              auth: settings[key].auth,
              tls: settings[key].tls
            });
          }

          transport.verify(e1 => {
            if (e1) {
              connects[key] = null;
              reject(e1);
            } else {
              connects[key] = transport;

              transport.sendMail(mailOptions, (e2, res) => {
                if (e2) {
                  connects[key] = null;
                  reject(e2);
                } else {
                  resolve(res);
                }
              });
            }
          });
        });
      } catch (e) {
        reject(e);
      }
    });
  }
};
