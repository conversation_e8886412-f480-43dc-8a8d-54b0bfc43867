let myArgs = process.argv.slice(2);
if (myArgs.length < 2) {
  console.error("Version number and Build number arguments is required");
  process.exit(0);
}
const fs = require("fs");
const release = require("./release.json");
release.version = myArgs[0];
release.buildNo = myArgs[1];
release.date = new Date();
console.log(require("./version"));
fs.writeFileSync("./release.json", JSON.stringify(release, null, 2));
