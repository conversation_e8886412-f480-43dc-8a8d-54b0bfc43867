module.exports = {
  save: {
    code: 0,
    name: "Save",
    description: "Action performed by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to Save the App"
  },
  submit: {
    code: 1,
    name: "Submit",
    description: "AppDeveloper Submits App to MarketingAdmin for Approval"
  },
  withdraw: {
    code: 2,
    name: "Withdraw",
    description: "Action performed by <PERSON>pp<PERSON><PERSON><PERSON><PERSON> to withdraw the App from Approval state"
  },
  hold: {
    code: 3,
    name: "Hold",
    escription: "MarketingAdmin is reviewing the App and put on hold for review"
  },
  reject: {
    code: 4,
    name: "Reject",
    description: "MarketingAdmin rejects the App for correction, AppDeveloper has to rework on the App"
  },
  stage: {
    code: 5,
    name: "Stage",
    description: "MarketingAdmin stages the App in staging server for handset simulation"
  },
  launch: {
    code: 6,
    name: "Launch",
    description: "MarketingAdmin launches the App for live server"
  },
  scheduleStage: {
    code: 7,
    name: "Stage",
    description: "MarketingAdmin schedule for staging the App in staging server for handset simulation"
  },
  scheduleLaunch: {
    code: 8,
    name: "Launch",
    description: "MarketingAdmin schedule for launching the App for live server"
  },
  delete: {
    code: 9,
    name: "Delete",
    description: "AppDeveloper or MarketingAdmin delete the App"
  },
  purge: {
    code: 10,
    name: "Purge",
    description: "MarketingAdmin deletes the App permenantly"
  }
};
