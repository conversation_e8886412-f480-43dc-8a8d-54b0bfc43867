require("events").EventEmitter.defaultMaxListeners = 100;
const ReplyError = require("ioredis").ReplyError;
module.exports = {
  app_states: require("./libs/app_states"),
  app_transitions: require("./libs/app_transitions"),
  http_codes: require("./libs/http_codes"),
  operator_aliases: require("./libs/operator_aliases"),
  operators: require("./libs/operators"),
  redis_man: require("./libs/redis_man"),
  version: require("./libs/version"),
  user_roles_default: require("./libs/user_roles_default"),
  whiteboard: require("./libs/whiteboad"),
  ConfigWatcher: require("./libs/RedisKeySpaceNotifier"),
  FreeFlowStates: require("./libs/free_flow_states"),
  email: require("./libs/email"),
  releaseInfo: require("./libs/release.json"),
  ReplyError
};
