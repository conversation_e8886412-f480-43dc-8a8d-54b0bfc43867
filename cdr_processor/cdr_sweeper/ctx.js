
const ncrHandler = require("./fsm/redis-connector");
const ncesHandler = require("./fsm/es-connector");
const syncHandler = require("./fsm/cdr-sync.js");
const readyHandler = require("./fsm/cdr-sweeper");
const diskChkHandler = require("./fsm/disk-space-handler.js");

const TransitionStates = {
  ncr: "NotConnectedToRedis",  // not-connected-to-redis
  nces: "NotConnectedToES",     // not-connected-to-es
  sync: "SyncingBacklog",       // copies from reliable q to main q
  diskchk: "DiskCheck",       // copies from reliable q to main q
  ready: "Ready",                // ready
  fse: "FileIOError"           // file-system-error
};

const FiniteStateMachine = {
  diskchk: { handler: diskChkHandler, success: "ncr", failure: "diskchk" },
  ncr: { handler: ncr<PERSON><PERSON><PERSON>, success: "nces", redisError: "ncr" },
  nces: { handler: nces<PERSON><PERSON><PERSON>, success: "sync", failure: "nces" },
  sync: { handler: syncHandler, success: "ready", failure: "ncr" },
  ready: { handler: readyHandler, success: "ready", esError: "nces", failure: "diskchk" }
};

// initially starts as not-connected-to-redis

module.exports = {
  run: async function () {
    let state = "diskchk";
    while (true) {
      if (global.logger.isTraceEnabled())
        global.logger.trace("Executing %s handler...", TransitionStates[state]);
      let rc = await FiniteStateMachine[state].handler.run();
      if (global.logger.isTraceEnabled())
        global.logger.trace("Executed %s handler, result:%s", TransitionStates[state], rc);

      // set next state depending on the return code.
      state = getNextState(state, rc);
      if (global.logger.isTraceEnabled())
        global.logger.trace("Transitioned to new state '%s'.", state);
    }
  }
};

function getNextState(cur, rc) {
  let newState = FiniteStateMachine[cur][rc];
  (newState != null);
  return newState;
}
