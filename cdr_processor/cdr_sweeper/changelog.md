CDR Sweeper Changelog - v3.0.9
------------------------------

<table>
  <tr>
    <td>v3.0.9</td>
    <td>
      <ul>
        <li>Upgraded the config-tree to make use of config watcher for Auto configuration reload.</li>
        <li>Upgraded the Third party libraries.</li>
        <li>Removed the unused Third party libraries.</li>
        <li>OAM Client to emit alerts.</li>
        <li>Handled the Redis re-connectivity failures in FSM flow. Introduced the configuration redisRetryInterval.</li>
        <li>Check elasticsearch mode enabled in es-connector state to skip this step</li>
        <li>Introduced the isDiskCheckEnabled configuration to enable/disable the Disk space check</li>
        <li>Handled the errors in CDR sync state and also unreferenced the temp variables which is holding the Array of CDRs in temp queue.</li>
        <li>Restricted the empty LEAP CDR File creation.</li>
        <li>Skipping the LEAP CDR record if cross recordMaxSize configuration.</li>
        <li>Introduced the CDR File prefix configuration. (LEAP_<ID>.cdr and LEAP_<ID>.ctl)</li>
        <li>Added Logger isTraceEnable check to improve the performance by avoid costly operations like JSON.stringify involved. </li>
        <li>Handled the fixed number of records to CDR file based on batchSize4FS configuration.</li>
        <li>Node Cluster improvements. Individual instances handling the pop-push and writing CDR is taken care and verified.</li>
        <li>Handled the Auto log rotation for submit cdr files.</li>
        <li>Code cleanup and formatted all js files.</li>
        <li>Accept the password for config-tree in commandline</li>
        <li>Check CDR Internal queue criteria to push to FS, batch size or timeout</li>
        <li>Handled the Redis OOM</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td>v3.0.8</td>
    <td>
      <ul>
        <li>Initial version</li>
      </ul>
    </td>
  </tr>
</table>