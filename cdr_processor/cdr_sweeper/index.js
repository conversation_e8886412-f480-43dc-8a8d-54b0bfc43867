"use strict";
/***
 *
 *  cdrSweeper -- entry point file.
 *
 ***/
process.env.UV_THREADPOOL_SIZE = 16;
const path = require("path");
const fs = require("fs");
const args = require("commander");
const cluster = require("cluster");
const ConfigTree = require("config-tree");
const OAM = require("oam");
const os = require("os");
require("colors");
const common = require("common");
const RedisStore = common.redis_man;

global.logger = console;
/**
 * Module variable definitions here.
 * Note: The modules may be initialized *later*, after initBasic() succeeds.
 * This helps us to "require" the modules in a synchronous manner
 * without side-effects.
 **/
initBasic()
  .then(function () {
    global.logger.warn("Finished with basic initialization.");
    global.logger.warn("------------------------------------");
    global.logger.warn();

    // Find out if we have to run in single-proc mode or cluster-mode.
    global.clusterSize = getClusterSize();
    return startServer(global.clusterSize);
  })
  .catch(function (e) {
    console.error("error while starting cdrSweeper...", e);
    OAM.raiseCriticalAlert("cdrSweeper")
      .then(() => {
        process.exit(1);
      }).catch(err => {
        console.error("Failed to raise Critical alert for cdrSweeper process", err);
        process.exit(1);
      });
  });

/**
 * Performs level-0 initialization, which include:
 * => reading / parsing command-line arguments
 * => Setting up global vars for other files / modules
 * => Reading configuration from config server
 **/
async function initBasic() {
  global.clusterId = 1;
  args
    .version(require("./package.json").version)
    .option("-H, --host [host]", "Config server host -- defaults to", process.env.CONFIG_HOST || "127.0.0.1")
    .option("-p, --port [port]", "Config server port -- defaults to", process.env.CONFIG_PORT || 6380)
    .option("-n, --db [db]", "Config server db index -- defaults to", process.env.CONFIG_DB || 0)
    .option("-a, --password [password]", "config server password", process.env.CONFIG_AUTH || undefined)
    .parse(process.argv);

  printAppInfo();

  global.args = args.opts();
  let opts = {
    args: global.args,
    configTasks: [
      "getModuleInfoEx",
      "getDBInfo",
      "getGlobalInfo"
    ],
    keys2read: {
      getModuleInfoEx: ["cdrSweeper", "app_engine"],
      getDBInfo: ["reportServer"],
      getGlobalInfo: ["oam"]
    }
  };

  const ConfigProxy = new ConfigTree(opts);

  return ConfigProxy.readConfig()
    .then(async () => {
      setConfigurations();
      ConfigProxy.on("reload_config", async (pattern, channel, key) => {
        try {
          console.log("CONFIG_CHANGE_EVENT", pattern, channel, key);
          if (channel.includes("cdrSweeper")) {
            setConfigurations();
          }
        } catch (e) {
          global.logger.error("Failed reload the Config", e);
        }
      });
    })
    .catch(e => {
      console.error("Failed to start cdrSweeper, Connection to:", global.args.host, global.args.port, "Failed", e);
      OAM.raiseCriticalAlert("cdrSweeper_configserver_conn")
        .then(() => {
          process.exit(1);
        }).catch(err => {
          console.error("Failed to raise Critical alert for cdrSweeper process", err);
          process.exit(1);
        });
    });
}

function setConfigurations() {
  console.log("Initializing configuration @", new Date());
  delete require.cache[require.resolve("./logger")];
  require("./logger");
  global.componentName = "cdrSweeper";
  global.config.cdrSweeper.transferMode = Number(global.config.cdrSweeper.transferMode) || 1;
  global.tmode = (global.config.cdrSweeper.transferMode == 0) ? "Elasticsearch" : "File System";
  global.config.cdrSweeper.esRetryInterval = Number(global.config.cdrSweeper.esRetryInterval) || 10000;
  global.config.reportServer.maxRetries = Number(global.config.reportServer.maxRetries) || 30;
  global.config.reportServer.requestTimeout = Number(global.config.reportServer.requestTimeout) || 45000;
  global.config.reportServer.maxSockets = Number(global.config.reportServer.maxSockets) || 10;
  global.config.reportServer.deadTimeout = Number(global.config.reportServer.deadTimeout) || 60000;
  global.config.reportServer.pingTimeout = Number(global.config.reportServer.pingTimeout) || 60;
  if (global.config.reportServer.keepAlive == null) global.config.reportServer.keepAlive = true;
  else
    global.config.reportServer.keepAlive = Boolean(global.config.reportServer.keepAlive);
  global.config.cdrSweeper.cdrPollingTimeout = Number(global.config.cdrSweeper.cdrPollingTimeout) || 10;
  global.config.cdrSweeper.redisRetryInterval = Number(global.config.cdrSweeper.redisRetryInterval) || 2000;
  global.batchSize = getCDRBatchSize();
  global.config.cdrSweeper.recordMaxSize = Number(global.config.cdrSweeper.recordMaxSize) || 30000;
  if (global.config.cdrSweeper.isDiskCheckEnabled == null) global.config.cdrSweeper.isDiskCheckEnabled = true;
  else
    global.config.cdrSweeper.isDiskCheckEnabled = Boolean(global.config.cdrSweeper.isDiskCheckEnabled);
  global.popQ = global.config.cdrSweeper.queueName || "LEAP_CDRQ";
  global.pushQ = global.popQ + "_TMP";
  global.config.cdrSweeper.filePrefix = global.config.cdrSweeper.filePrefix || "LEAP"
  if (cluster.worker && cluster.worker.id) {
    global.pushQ += "_c" + cluster.worker.id;
    global.config.cdrSweeper.filePrefix += "_c" + cluster.worker.id;
  }
  global.config.cdrSweeper.minFreeDiskSpace = Number(global.config.cdrSweeper.minFreeDiskSpace) || 1;

  console.log("[Configuration] LOG level => " + global.logger.level);
  console.log("[Configuration] Transfer mode => " + global.tmode);
  console.log("[Configuration] ES Max Retries => " + global.config.reportServer.maxRetries);
  console.log("[Configuration] ES Retry interval => " + global.config.reportServer.esRetryInterval);
  console.log("[Configuration] ES bulk upload size => " + global.batchSize);
  console.log("[Configuration] ES requestTimeout => " + global.config.reportServer.requestTimeout);
  console.log("[Configuration] ES deadTimeout => " + global.config.reportServer.deadTimeout);
  console.log("[Configuration] ES pingTimeout => " + global.config.reportServer.pingTimeout);
  console.log("[Configuration] ES maxSockets => " + global.config.reportServer.maxSockets);
  console.log("[Configuration] ES keepAlive => " + global.config.reportServer.keepAlive);
  console.log("[Configuration] CDR Polling interval => " + global.config.cdrSweeper.cdrPollingTimeout);
  console.log("[Configuration] Redis Retry interval => " + global.config.cdrSweeper.redisRetryInterval);
  console.log("[Configuration] CDR pop batch size => " + global.batchSize);
  console.log("[Configuration] Disk space check enabled => " + global.config.cdrSweeper.isDiskCheckEnabled);
  console.log("[Configuration] Max record size => " + global.config.cdrSweeper.recordMaxSize);
}

/**
 * Runs the initializer code for each of the worker's instance
 * (if running in cluster mode),
 * or runs this code, directly if running in non-cluster mode.
 **/
async function initCall() {
  try {
    global.logger.warn("Config loaded successfully");
    await OAM.init(global.config.oam);
    OAM.emit("clearAlert", "cdrSweeper_configserver_conn");
    RedisStore.init({
      config: global.config.app_engine.cdrqueue,
      oid: "cdrsweeper_cdrqueue_conn"
    });
    require("./ctx").run();
    OAM.emit("clearAlert", "cdrSweeper");
  } catch (e) {
    console.error("Error while starting cdrSweeper. ", e);
    OAM.raiseCriticalAlert("cdrSweeper")
      .then(() => {
        process.exit(1);
      }).catch(err => {
        console.error("Failed to raise Critical alert for cdrSweeper process", err);
        process.exit(1);
      });
  }
}

function getClusterSize() {
  let configuredCPUs = global.config.cdrSweeper.clusterMode && global.config.cdrSweeper.clusterSize || 1;
  global.cpus = os.cpus().length;
  return Math.min(configuredCPUs, global.cpus);
}

function startServer(clusterSize = 1) {
  if (clusterSize > 1) {
    console.warn("Running the cdrSweeper in cluster mode...");
  } else {
    console.warn("Running the cdrSweeper in single-proc mode...");
  }
  if (cluster.isMaster && clusterSize > 1) {
    console.log("Master cluster setting up " + clusterSize + " workers...");
    for (let i = 0; i < clusterSize; i++) {
      cluster.fork();
    }
    cluster.on("online", function (worker) {
      console.log("Worker " + worker.process.pid + " is online");
    });
    cluster.on("exit", function (worker, code, signal) {
      console.log("Worker " + worker.process.pid + " died with code: " + code + ", and signal: " + signal);
      for (let id in cluster.workers) {
        cluster.workers[id].kill();
      }
      // exit the master process
      process.exit(0);
    });
  } else {
    global.clusterId = (cluster.worker) ? cluster.worker.id : 1;
    initCall();
  }
}

/**
 *  Prints application title and version.
 **/
function printAppInfo() {
  let banner = fs.readFileSync(path.join(__dirname, "./logo.txt")).toString();
  console.log(banner.green);
  console.log(require("./package.json").version.bold);
}

process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);

process.on("uncaughtException", (e) => {
  console.error("Uncaught Expection", e);
});

process.on("unhandledRejection", (reason, p) => {
  console.error("Unhandled Expection", reason, p);
});

function shutdown() {
  global.logger.error("Received kill signal. Initiating shutdown...");
  OAM.init(global.config.oam);
  OAM.raiseCriticalAlert("cdrSweeper")
    .then(() => {
      process.exit(1);
    }).catch(err => {
      console.error("Failed to raise Critical alert for cdrSweeper process", err);
      process.exit(1);
    });
}

function getCDRBatchSize() {
  let batchSize;
  // based on transfer mode, look out for corresponding settings.
  if (global.config.cdrSweeper.transferMode == 0) { //write to es
    // read batchsize for es.
    batchSize = Number(global.config.cdrSweeper.batchSize4ES) || 100;
  } else {    // write to fs
    // read the batch size for pushing to filesys.
    batchSize = Number(global.config.cdrSweeper.batchSize4FS) || 100;
  }
  return batchSize;
}
