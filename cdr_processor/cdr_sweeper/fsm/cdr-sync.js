/***
  *  Implements sync state for cdr sweeper.
  *  @brief
  *   if the cdr-sweeper is terminated abruptly, there is a
  *   chance that there are cdr entries in the secondary
  *   quueue which are left uncleared.
  *   And when the process restarts, it has to first
  *   process this secondary queue, before attempting
  *   to pop from main queue.
  *
  *   The way to do this is:
  *   1. Move all items from secondary queue to front of main queue.
  *   2. when all records from 2ndary queue are moved, transition to ready state.
  *
***/

"use strict";
const common = require("common");
const RedisStore = common.redis_man;
const FSMCode = require("./fsm-err-codes.js");

module.exports = {
  run: () => {
    return new Promise(async resolve => {
      try {
        // Obtain connection to Redis and find
        // available cdrs in 2nd queue.
        let conn = await RedisStore.getConnection();
        if (conn == null) {
          global.logger.error("Failed to obtain redis connection.");
          return resolve(FSMCode.failure);
        }
        let cdrs = await conn.llen(global.pushQ);
        let total = 0;

        // where there are CDRS in 2nd queue,
        // pop n push them to main q.
        while (cdrs > 0) {
          let pickSize = Math.min(global.batchSize, cdrs);
          let pipelinePOP = conn.pipeline();
          for (let i = 0; i < pickSize; i++) {
            pipelinePOP.rpop(global.pushQ);
          }

          // execute the pipeline.
          if (global.logger.isInfoEnabled())
            global.logger.info("Popping from queue:%s...", global.pushQ);
          let results = await pipelinePOP.exec();
          // if there are records, add to main q.
          if (results.length > 0) {
            let pipelinePUSH = conn.pipeline();
            for (let i = results.length; i > 0; i--) {
              pipelinePUSH.rpush(global.popQ, results[i - 1][1]);
            }
            results = null;
            let result = await pipelinePUSH.exec();
            total += result.length;
            result = null;
          } else {
            return resolve(FSMCode.failure);
          }

          // recompute the remaining CDRs.
          cdrs = await conn.llen(global.pushQ);
        }
        if (global.logger.isInfoEnabled())
          if (total == 0)
            global.logger.info("No CDRs found in backup queue.");
          else
            global.logger.info("Moved %d CDRs to main queue.", total);
        return resolve(FSMCode.success);
      } catch (e) {
        global.logger.error("Some error while syncup:", e);
        setTimeout(() => { resolve(FSMCode.failure); }, global.config.cdrSweeper.redisRetryInterval);
      }
    });
  }
};
