/**
 *  Makes and/or retries a connection to ES instance.
 **/

"use strict";
const elasticsearch = require("elasticsearch");
const FSMCode = require("./fsm-err-codes.js");
const OAM = require("oam");
const elasticsearch_conn_oid = "elasticsearch_conn";

// Create an instance of elasticsearch client and connect to the instance.
var esClient;

module.exports = {
  run: () => {
    // try to ping to elasticsearch. If success return, else
    // continue pinging periodically.
    return new Promise(async resolve => {
      try {
        if (global.config.cdrSweeper.transferMode == 0) {
          let esClient = getClient();
          esClient.ping({ requestTimeout: global.config.reportServer.requestTimeout })
            .then(() => {
              OAM.emit("clearAlert", elasticsearch_conn_oid);
              return resolve(FSMCode.success);
            })
            .catch(e => {
              OAM.emit("criticalAlert", elasticsearch_conn_oid);
              global.logger.error("Failed to connect to ES. Will retry after %d seconds", global.config.reportServer.esRetryInterval / 1000, e);
              setTimeout(() => { resolve(FSMCode.failure); }, global.config.reportServer.esRetryInterval);
            });
        } else {
          return resolve(FSMCode.success);
        }
      } catch (error) {
        global.logger.error("Failed to connect to ES. ", e);
        return resolve(FSMCode.failure);
      }
    });
  },

  getConnection: () => {
    return getClient();
  }
};

function getClient() {
  if (esClient) {
    return esClient;
  } else {
    if (global.logger.isInfoEnabled())
      global.logger.info("Creating ES Client:", global.config.reportServer);
    esClient = new elasticsearch.Client({
      host: global.config.reportServer.hosts[0].host,
      requestTimeout: global.config.reportServer.requestTimeout,
      maxRetries: global.config.reportServer.maxRetries,
      maxSockets: global.config.reportServer.maxSockets,
      keepAlive: global.config.reportServer.keepAlive,
      deadTimeout: global.config.reportServer.deadTimeout,
      pingTimeout: global.config.reportServer.pingTimeout
    });
    return esClient;
  }
}
