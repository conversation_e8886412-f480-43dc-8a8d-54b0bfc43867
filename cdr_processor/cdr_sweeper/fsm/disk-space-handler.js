/**
 *  This state handler runs, whenever system is going low on disk-space.
 *  It keeps checking space availability at regular intervals, to see
 *  if it is safe to resume operations. If safe, then switches to 'ready' state.
 *
 **/

"use strict";
const disk = require("diskusage");
const FSMCode = require("./fsm-err-codes.js");
const os = require("os");
const path = os.platform() === "win32" ? "c:" : "/";
const OAM = require("oam");
const diskspace_oid = "diskspace_full";

const MB = 1024 * 1000;
const GB = MB * 1000;
const DEFAULT_MIN_FREE_DISK_SPACE = GB;  // 1 Gigabyte
const DEFAULT_DISK_CHK_INTERVAL = 60 * 1000;  // 1 minute.

module.exports = {
  run: () => {
    return new Promise(resolve => {
      if (!global.config.cdrSweeper.isDiskCheckEnabled) {
        return resolve(FSMCode.success);
      }
      let minFreeDiskSpace = global.config.cdrSweeper.minFreeDiskSpace * DEFAULT_MIN_FREE_DISK_SPACE;

      let diskChkInterval = global.config.cdrSweeper.diskChkInterval.length > 0 ? global.config.cdrSweeper.diskChkInterval[0] : DEFAULT_DISK_CHK_INTERVAL;
      disk.check(path, (err, info) => {
        if (err) {
          OAM.emit("warningAlert", diskspace_oid);
          global.logger.error("Error while running disk statistics check.", err);
        } else {
          if (global.logger.isTraceEnabled())
            global.logger.trace("total:%d GB, available:%d GB, free:%d GB, minimum required:%d GB",
              info.total / GB, info.available / GB, info.free / GB, minFreeDiskSpace / GB);

          // if min free is not available, continue waiting
          if (info.available < minFreeDiskSpace) {
            OAM.emit("criticalAlert", diskspace_oid);
            global.logger.warn("Not enough disk space (%d GB) available. Will retry after %d seconds.",
              minFreeDiskSpace / GB, diskChkInterval / 1000);
            setTimeout(() => { resolve(FSMCode.failure); }, diskChkInterval);
          } else {
            OAM.emit("clearAlert", diskspace_oid);
            if (global.logger.isInfoEnabled())
              global.logger.info("OK. Sufficient disk space available. Moving to next state...");
            resolve(FSMCode.success);
          }
        }
      });
    });
  }
};
