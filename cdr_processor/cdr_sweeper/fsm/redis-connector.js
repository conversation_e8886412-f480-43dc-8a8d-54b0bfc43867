const common = require("common");
const RedisStore = common.redis_man;
const FSMCode = require("./fsm-err-codes.js");
/**
 * Creates connection to Redis (cdr store).
 **/
module.exports = {
  run: async () => {
    return new Promise(async resolve => {
      try {
        let conn = await RedisStore.getConnection();
        if (conn != null)
          return resolve(FSMCode.success);
      } catch (error) {
        global.logger.error(error);
      }
      global.logger.error("Retry connecting to CDR Queue server after " + global.config.cdrSweeper.redisRetryInterval + "ms");
      setTimeout(() => { resolve(FSMCode.redisError); }, global.config.cdrSweeper.redisRetryInterval);
    });
  }
};
