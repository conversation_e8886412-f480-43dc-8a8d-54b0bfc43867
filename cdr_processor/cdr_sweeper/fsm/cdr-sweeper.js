/**
 * The business logic of writing CDRs to their target, goes here.
 **/
"use strict";
const FSMCode = require("./fsm-err-codes.js");
const esClient = require("./es-connector.js");
const fs = require("fs");
const path = require("path");
const common = require("common");
const ReplyError = common.ReplyError;
const RedisStore = common.redis_man;

var fileStream = null;  // for writing errors
var cdrQueue = [];
var timedOut = false; // used for run cycles management.

setInterval(() => { timedOut = true; }, 60000);
module.exports = {
  run: async () => {
    /**
     *  We process this as a cycle of pull->populate->push operations.
     *  pull => pull from cdr-store
     *  populate => add to local queue
     *  push => push to destination
     *
     *  Each 'run' cycle will do the following:
     *  1. Are we ready to push to target? This is decided by either one of the following conditions
     *    1.1 => either we timedout
     *    1.2 => or we have enough to push to target
     *  1. check if we timedout (meaning: in a prev cycle, we pulled, and populated,
     *     but we didn't push, bcoz we haven't accumulated "enough"), or .
     *     1.1 if we timedout, we push, whatever so far accumulated.
     *     1.2 if not, we continue to pull more and populate
     *
     *  2. Pull a batchsize and populate
     *
     **/
    return new Promise(async resolve => {
      try {
        //step#1: Check if we are 'ready' to push to target.
        let cdrsInBatch = cdrQueue.length;
        if (global.logger.isTraceEnabled())
          global.logger.trace("Checking push readyness... QUEUE SIZE:", cdrsInBatch, ", TIMEOUT:", timedOut, ", BATCH_SIZE:", global.batchSize);

        if (cdrsInBatch % 2 === 0 && cdrsInBatch >= global.batchSize || timedOut && cdrsInBatch > 0) {
          // batch is full. Time to send to destination.
          let result = await send2Target();
          if (result != FSMCode.success) {
            return resolve(FSMCode.esError);
          } else {
            if (global.logger.isInfoEnabled())
              global.logger.info("Removing completed cdrs from backup store...");
            let conn = await RedisStore.getConnection();
            result = conn.del(global.pushQ);
            if (global.logger.isInfoEnabled())
              global.logger.info("Finished removing from backup queue.");
            cdrQueue = [];
          }
          timedOut = false;   // batch process over. Starting a new time cycle.
        }

        // step#2: we come here, when we aren't ready for push. We need more cdrs
        // start filling the local queue with CDR's
        let pop = await populate2Queue();
        return resolve(pop);
      } catch (e) {
        global.logger.error("Exception in CDR Sweeper state", e);
        return resolve(FSMCode.failure);
      }
    });
  }
};

/////////////////////////////////////////////////////////
// internal funcs..
/////////////////////////////////////////////////////////
var lastId;
function getId() {
  let id = new Date().getTime().toString();
  if (lastId == id) {
    id++;
  }
  lastId = id;
  return lastId;
}
/** Pushes cdrs to file system. **/
function push2FS(cdrs) {
  // create a new batchfile and dump cdr's to it.
  let basename = global.config.cdrSweeper.filePrefix + "_" + getId();
  let outputPath = path.resolve(global.config.cdrSweeper.outputPath);
  let file = path.join(outputPath, basename + ".cdr");
  let ctlFile = path.join(outputPath, basename + ".ctl");
  if (global.logger.isDebugEnabled())
    global.logger.debug("Writing batch to file %s...", file);
  return new Promise((resolve, reject) => {
    fs.writeFile(file, cdrs, (err) => {
      if (err) { global.logger.fatal(err); reject(err); }
      else {
        cdrs = null;
        fs.writeFile(ctlFile, "", (err) => {
          if (err) reject(err);
          else {
            resolve(FSMCode.success);
          }
        });
      }
    });
  });
}

/** Pushes cdrs to elasticsearch instance. **/
function push2ES(cdrs) {
  if (global.logger.isDebugEnabled()) {
    global.logger.debug("Posting Request=>", JSON.stringify(cdrs));
  }
  return esClient.getConnection().bulk({ body: cdrs })
    .then((response) => {
      handleESResponse(response);
      if (global.logger.isTraceEnabled())
        global.logger.trace("Finished with writing batch to ES.");
      return FSMCode.success;
    })
    .catch((e) => {
      global.logger.error("Error while sending data to elasticsearch:", e);
      let retry = handleESError(e, cdrs);
      return retry ? FSMCode.esError : FSMCode.success;
    });
}

function checkRedisErrors(a) {
  return a && a.length > 0 && a[0][0] instanceof ReplyError;
}

function checkDataExists(a) {
  return a && a.constructor === Array && a.length > 0 && a[0][1] != null;
}

/** adds data into local batch q from cdr store **/
function populate2Queue() {
  return new Promise(async resolve => {
    try {
      let cdrsInBatch = cdrQueue.length;
      let conn = await RedisStore.getConnection();
      let cdrsInStore = await conn.llen(global.popQ);
      let cdrsNeeded = Math.min((global.batchSize - cdrsInBatch), cdrsInStore);

      if (global.logger.isTraceEnabled())
        global.logger.trace("QUEUESTATS::: TOTAL RECORDS:", cdrsInStore, ",BATCH_SIZE:", global.batchSize, ",POPING:", cdrsNeeded);

      // create a redis pipeline to download the available CDR's in store.
      if (cdrsNeeded > 0) {
        let pipeline = conn.pipeline();
        for (let i = 0; i < cdrsNeeded; i++) {
          pipeline.brpoplpush(global.popQ, global.pushQ, global.config.cdrSweeper.cdrPollingTimeout);
        }
        let results = await pipeline.exec();
        if (checkRedisErrors(results)) {
          for (let i = 0; i < cdrsNeeded; i++) {
            pipeline.rpop(global.popQ);
            pipeline.rpop(global.pushQ);
          }
          results = await pipeline.exec();
        }
        if (checkDataExists(results)) {
          if (global.logger.isTraceEnabled())
            global.logger.trace("Received %d cdr's from store.", results.length);
          // There is some data in the response.  Add the cdrs to processing queue...
          for (let i = 0; i < results.length; i++) {
            try {
              enqueue(results[i][1]);
            } catch (e) {
              global.logger.error("Exception while writing cdr", results[i][1], e);
            }
          }
          return resolve(FSMCode.success)
        }
      }
      if (global.logger.isTraceEnabled())
        global.logger.trace("No data in cdr store. Waiting for %d seconds...", global.config.cdrSweeper.cdrPollingTimeout / 1000);
      setTimeout(() => { resolve(FSMCode.success); }, global.config.cdrSweeper.cdrPollingTimeout);
    } catch (e) {
      global.logger.error("Error while trying to pop from store:", e);
      return resolve(FSMCode.failure);
    }
  });
}

function enqueue(record) {
  if (record != null && record.length > 0) {
    record.split(/\r?\n/).filter(value => value.length != 0)
      .forEach(item => {
        if (item.length <= global.config.cdrSweeper.recordMaxSize) {
          cdrQueue.push(item);
        } else {
          global.logger.error("CDR_SKIPPED|MaxAllowedSize:" + global.config.cdrSweeper.recordMaxSize + "|LEN:" + item.length + "|PAYLOAD:" + item);
        }
      });
    record = null;
  }
}

function send2Target() {
  if (global.logger.isTraceEnabled())
    global.logger.trace("Preparing to transport a %d CDRs to %s...", cdrQueue.length, global.tmode);
  if (cdrQueue.length <= 0) return FSMCode.success;

  let cdrs = cdrQueue.slice().join('\n');
  if (global.config.cdrSweeper.transferMode == 0)
    return push2ES(cdrs)
  else
    return push2ES(cdrs);
}

function handleESResponse(response) {
  if (global.logger.isDebugEnabled()) {
    global.logger.debug("Elasticsearch Response =>", JSON.stringify(response));
  }
  if (response && response.errors) {
    let fst = getErrorFileHandle();
    response.items.forEach((item) => {
      if (item && item.update && item.update.status != 200 && item.update.status != 201)
        fst.write(JSON.stringify(item, null, 2) + "\n");
    });
  }
}

function handleESError(e, cdrs) {
  if (e.status == 400) {  // bad request (malformed payload)
    global.logger.warn("Elasticsearch rejected the payload: %s. Ignoring...", cdrs);
    cdrQueue.length = 0;  // empty the que, so we don't process the same msg again.
    return false;         // no retry for a malformed payload.
  } else {
    global.logger.warn("Elasticsearch returned a status code of : %d.", e.status);
    return true;
  }
}

function getErrorFileHandle() {
  if (fileStream == null)
    fileStream = fs.createWriteStream(path.resolve("./errors.cdr"), { flags: "a" });
  return fileStream;
}
