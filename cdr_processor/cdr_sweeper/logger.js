"use strict";
/**
 ** logger module that initializes log4js settings.
 **/

const log4js = require("log4js");
const path = require("path");
const fs = require("fs");
let logger;

// create the logs folder if not existing.
let logdir;
if (global.config.cdrSweeper.log.logdir) {
  logdir = path.resolve(global.config.cdrSweeper.log.logdir);
} else {
  logdir = path.join(__dirname, "/logs");
}
if (!fs.existsSync(logdir)) {
  fs.mkdirSync(logdir);
}
let logConfig = global.config.cdrSweeper.log.log4js;

Object.keys(logConfig.appenders).forEach(appender => {
  try {
    if (!path.isAbsolute(logConfig.appenders[appender].filename)) {
      logConfig.appenders[appender].filename = path.join(global.config.cdrSweeper.log.logdir, logConfig.appenders[appender].filename);
    }
  } catch (error) {
    //ignore
  }
});

Object.keys(logConfig.categories).forEach(category => {
  try {
    logConfig.categories[category].appenders = [logConfig.categories[category].appender];
    delete logConfig.categories[category].appender;
  } catch (error) {
    //ignore
  }
});

log4js.configure(logConfig);
logger = log4js.getLogger();
logger.level = logConfig.categories.default.level;
global.log4js = log4js;
global.logger = logger;
module.exports = logger;
