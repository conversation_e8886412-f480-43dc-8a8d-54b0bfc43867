"use strict";
/***
 *
 *  nodeLogger -- entry point file.
 *
 ***/
process.env.UV_THREADPOOL_SIZE = 16;
// Essential required modules
const fs = require("fs");
const path = require("path");
const args = require("commander");
const cluster = require("cluster");
const ConfigTree = require("config-tree");
const OAM = require("oam");
const os = require("os");
const _ = require("lodash");
const targz = require("targz");
const rimraf = require("rimraf");
const ELKClient = require("./libs/elastic_client");
const date = require("date-and-time");
const DATE_FORMAT = "YYYY_MM_DD_HH_mm";
const reader = require("./libs/reader");

const mkdirp = require("mkdirp");
const Async = require("async");

var reader_flag = true;
var compressor_flag = false;

global.logger = console;
/**
 * Module variable definitions here.
 * Note: The modules may be initialized *later*, after initBasic() succeeds.
 * This helps us to "require" the modules in a synchronous manner
 * without side-effects.
 **/
initBasic()
  .then(function () {
    global.logger.warn("Finished with basic initialization.");
    global.logger.warn("------------------------------------");
    global.logger.warn();

    // Find out if we have to run in single-proc mode or cluster-mode.
    global.clusterSize = getClusterSize();
    startServer(global.clusterSize);
  })
  .catch(function (e) {
    console.error("Error while starting nodeLogger...", e);
    OAM.raiseCriticalAlert("nodeLogger")
      .then(() => {
        process.exit(1);
      }).catch(err => {
        console.error("Failed to Raise Critical alert for nodeLogger process", err);
        process.exit(1);
      });
  });

/**
 * Performs level-0 initialization, which include:
 * => reading / parsing command-line arguments
 * => Setting up global vars for other files / modules
 * => Reading configuration from config server
 **/
async function initBasic() {
  global.clusterId = 1;
  args
    .version(require("./package.json").version)
    .option("-H, --host [host]", "Config server host -- defaults to", process.env.CONFIG_HOST || "127.0.0.1")
    .option("-p, --port [port]", "Config server port -- defaults to", process.env.CONFIG_PORT || 6380)
    .option("-n, --db [db]", "Config server db index -- defaults to", process.env.CONFIG_DB || 0)
    .option("-a, --password [password]", "config server password", process.env.CONFIG_AUTH || undefined)
    .parse(process.argv);

  global.args = args.opts();

  let opts = {
    args: global.args,
    configTasks: [
      "getModuleInfoEx",
      "getDBInfo",
      "getGlobalInfo"
    ],
    keys2read: {
      getModuleInfoEx: ["cdrSweeper", "nodeLogger"],
      getDBInfo: ["reportServer"],
      getGlobalInfo: ["oam"]
    }
  };

  const ConfigProxy = new ConfigTree(opts);

  return ConfigProxy.readConfig()
    .then(async () => {
      //Start:Added for redis to mariadb change
      if (process.env.LEAP_CONFIG_DBTYPE === 'mysql') {
        var reportUrl = global.config.reportServer.hosts[0].host;
        var elasticHostName = (reportUrl.lastIndexOf('@') != -1) ? reportUrl.substring(reportUrl.lastIndexOf('@') + 1, reportUrl.lastIndexOf(':')) : reportUrl.substring(reportUrl.lastIndexOf('/') + 1, reportUrl.lastIndexOf(':'));
        var elasticPort = reportUrl.substring(reportUrl.lastIndexOf(':') + 1);

        lineReader.eachLine('/etc/hosts', function (line) {
          if (line.includes(elasticHostName)) {
            const temSplit = line.split(' ');
            console.log("host mapped=>", line);
            if (reportUrl.lastIndexOf('@') != -1) {
              let firstPart = reportUrl.substring(0, reportUrl.lastIndexOf('@'));
              global.config.reportServer.hosts[0].host = firstPart + "@" + temSplit[0] + ":" + elasticPort;
            } else {
              let firstPart = reportUrl.substring(0, reportUrl.lastIndexOf('/') + 1);
              global.config.reportServer.hosts[0].host = firstPart + temSplit[0] + ":" + elasticPort;
            }

            return false; // stop reading
          }
        });
      }
      //End:Added for redis to mariadb change
      setConfigurations();
      ConfigProxy.on("reload_config", async (pattern, channel, key) => {
        try {
          console.log("CONFIG_CHANGE_EVENT", pattern, channel, key);
          setConfigurations();
          if (channel.includes("reportServer")) {
            ELKClient.serviceQuitter();
          }
        } catch (e) {
          global.logger.error("Failed reload the Config", e);
        }
      });
    })
    .catch(e => {
      console.error("Failed to start nodeLogger, Connection to:", global.args.host, global.args.port, "Failed", e);
      OAM.raiseCriticalAlert("nodeLogger_configserver_conn")
        .then(() => {
          process.exit(1);
        }).catch(err => {
          console.error("Failed to Raise Critical alert for nodeLogger process", err);
          process.exit(1);
        });
    });
}

//////////////////////////////////////////////////////////////////////////////////////
// Internal helper functions
//////////////////////////////////////////////////////////////////////////////////////

function setConfigurations() {
  console.log("Initializing configuration @", new Date());
  delete require.cache[require.resolve("./libs/logger")];
  require("./libs/logger");

  global.componentName = "nodeLogger";
  global.config.cdrSweeper.batchSize4FS = Number(global.config.cdrSweeper.batchSize4FS) || 100;
  global.config.nodeLogger.batch = Number(global.config.nodeLogger.batch) || 100;
  global.config.reportServer.maxRetries = Number(global.config.reportServer.maxRetries) || 30;
  global.config.reportServer.retryTimeOut = Number(global.config.nodeLogger.retryTimeOut) || 60000;

  global.config.reportServer.requestTimeout = Number(global.config.reportServer.requestTimeout) || 45000;
  global.config.reportServer.maxSockets = Number(global.config.reportServer.maxSockets) || 10;
  global.config.reportServer.deadTimeout = Number(global.config.reportServer.deadTimeout) || 60000;
  global.config.reportServer.pingTimeout = Number(global.config.reportServer.pingTimeout) || 60;
  if (global.config.reportServer.keepAlive == null) global.config.reportServer.keepAlive = true;
  else
    global.config.reportServer.keepAlive = Boolean(global.config.reportServer.keepAlive);
  global.config.nodeLogger.intervalSize = Number(global.config.nodeLogger.intervalSize) || 10000;
  global.config.nodeLogger.compressionInterval = Number(global.config.nodeLogger.compressionInterval) || 600000;
  global.config.nodeLogger.fileBatchSize = Number(global.config.nodeLogger.fileBatchSize) || 50;
  global.config.nodeLogger.corrupted = global.config.nodeLogger.corrupted && path.resolve(global.config.nodeLogger.corrupted) || path.resolve(global.config.nodeLogger.path, "../corrupted");
  global.config.cdrSweeper.filePrefix = global.config.cdrSweeper.filePrefix || "LEAP"
  if (cluster.worker && cluster.worker.id) {
    global.pushQ + "_" + cluster.worker.id;
    global.config.cdrSweeper.filePrefix += "_c" + cluster.worker.id;
  }

  console.log("[Configuration] LOG level => " + global.logger.level);
  console.log("[Configuration] CDR Compression interval => " + global.config.nodeLogger.compressionInterval);
  console.log("[Configuration] CDR Polling Interval => " + global.config.nodeLogger.intervalSize);
  console.log("[Configuration] CDR Minimum records => " + global.config.cdrSweeper.batchSize4FS);
  console.log("[Configuration] CDR Batch processing no.of files => " + global.config.nodeLogger.fileBatchSize);
  console.log("[Configuration] ES Max Retries => " + global.config.reportServer.maxRetries);
  console.log("[Configuration] ES Retry interval => " + global.config.reportServer.retryTimeOut);
  console.log("[Configuration] ES bulk upload size => " + global.config.nodeLogger.batch);
  console.log("[Configuration] ES requestTimeout => " + global.config.reportServer.requestTimeout);
  console.log("[Configuration] ES deadTimeout => " + global.config.reportServer.deadTimeout);
  console.log("[Configuration] ES pingTimeout => " + global.config.reportServer.pingTimeout);
  console.log("[Configuration] ES maxSockets => " + global.config.reportServer.maxSockets);
  console.log("[Configuration] ES keepAlive => " + global.config.reportServer.keepAlive);
  console.log("[Configuration] CDR PATH => " + global.config.nodeLogger.path);
  console.log("[Configuration] CDR PROC => " + global.config.nodeLogger.proc);
  console.log("[Configuration] CDR CORRUPTED => " + global.config.nodeLogger.corrupted);
  console.log("[Configuration] CDR BACKUP => " + global.config.nodeLogger.backup);
}

function myProcessor() {
  if (reader_flag) {
    //checking active copy on watched folder
    ELKClient.serviceCheck().then(res => {
      if (global.logger.isTraceEnabled())
        global.logger.trace("Services Running, status: ", res);
      fs.writeFileSync(global.config.nodeLogger.log.logdir + "/es_down.log", "");
      runner();
    }).catch(err => {
      if (global.logger.isTraceEnabled())
        global.logger.trace("Can't Start Now. Start Check Failed!", err);
      fs.appendFileSync(global.config.nodeLogger.log.logdir + "/es_down.log", "ElasticSearch is Down!" + "\n");
    });
  }
}

function compressor() {
  return new Promise(resolve => {
    compressor_flag = false;
    // compress files into tar.gz archive
    fs.readdir(global.config.nodeLogger.proc, (err, items) => {
      if (!err) {
        items = _.filter(items, f => { return f.includes(".cdr"); });
        if (items.length > 0) {
          let fileTime = date.format(new Date(), DATE_FORMAT);
          let filename = "CDR_" + fileTime + ".tar.gz";
          if (global.logger.isTraceEnabled())
            global.logger.trace("Compressing processed files now...", filename);

          targz.compress({
            src: global.config.nodeLogger.proc,
            dest: path.join(global.config.nodeLogger.backup, filename),
            tar: {
              entries: items
            }
          }, err => {
            if (err) {
              global.logger.fatal("Compression failed for the processed cdrs -> ", err);
              resolve("done");
            } else {
              if (global.logger.isInfoEnabled())
                global.logger.trace("Compressing processed files now..." + cluster.worker.id, filename);
              rimraf(global.config.nodeLogger.proc + "/" + global.config.cdrSweeper.filePrefix + "*.cdr", () => {
                if (global.logger.isInfoEnabled())
                  global.logger.info("Processed folder has been cleaned successfully.." + cluster.worker.id);
                resolve("done");
              });
            }
          });
        }
      }
      if (global.logger.isInfoEnabled())
        global.logger.info("Proc Folder is empty... ");
      resolve("done");
    });
  });
}

function runner() {
  if (global.logger.isTraceEnabled())
    global.logger.trace("NodeLogger Started Scan");
  fs.readdir(global.config.nodeLogger.path, (err, items) => {
    items = fileSelector(items);
    if (items.length === 0) {
      if (global.logger.isInfoEnabled())
        global.logger.info("Scan Complete! No files present in initial scan.");
      if (compressor_flag) {
        if (global.logger.isInfoEnabled())
          global.logger.info("Compressor is enabled... Processed files will be compressed and moved to back up path...");
        compressor().then(msg => {
          reader_flag = true;
          if (global.logger.isInfoEnabled())
            global.logger.info("Compression has been done!!! ", msg);
        });
      } else {
        reader_flag = true;
      }
    } else {
      if (global.logger.isInfoEnabled())
        global.logger.info("Scan Complete! Started NodeLogger with Files :" + items.length);
      initializer(items);
    }
  });
}

function initializer(items) {
  let time_running = 0;
  Async.everySeries(items, (filename, callback) => {
    try {
      reader_flag = false;
      if (global.logger.isInfoEnabled())
        global.logger.info("Processing file : ", filename);
      reader.processFile(filename)
        .then(result => {
          time_running += result.real_time;
          if (global.logger.isInfoEnabled())
            global.logger.info("Processing ", filename, " completed in ", result.time.toFixed(2), "min");
          callback(null);
        }).catch((err) => {
          global.logger.error("Failed to process File ", filename, err);
          callback(err);
        });
    } catch (error) {
      global.logger.error("Failed to process File ", filename, error);
      callback(error);
    }
  }, (err) => {
    if (err == null) {
      if (global.logger.isInfoEnabled())
        global.logger.info("NodeLogger Current Batch Completed in ", time_running.toFixed(3), " sec");
    } else {
      global.logger.error("NodeLogger found errors in Curent Batch & Stopped with:", err);
    }
    if (compressor_flag) {
      if (global.logger.isInfoEnabled())
        global.logger.info("Compressor is enabled in initalizer ... Processed files will be compressed and moved to back up path... ");
      compressor()
        .then(msg => {
          runner();
        });
    } else {
      runner();
    }
  });
}


/**
 * Runs the initializer code for each of the worker's instance
 * (if running in cluster mode),
 * or runs this code, directly if running in non-cluster mode.
 **/
async function initCall() {
  try {
    global.logger.warn("Config loaded successfully");
    await OAM.init(global.config.oam);
    OAM.emit("clearAlert", "nodeLogger_configserver_conn");

    createBasicFolders();

    setInterval(() => { myProcessor(); }, global.config.nodeLogger.intervalSize);
    myProcessor();
    setInterval(() => { compressor_flag = true; }, global.config.nodeLogger.compressionInterval);
    compressor();
    OAM.emit("clearAlert", "nodeLogger");
  } catch (e) {
    console.error("Error while starting nodeLogger process.", e);
    OAM.raiseCriticalAlert("nodeLogger")
      .then(() => {
        process.exit(1);
      }).catch(err => {
        console.error("Failed to Raise Critical alert for nodeLogger process", err);
        process.exit(1);
      });
  }
}

function getClusterSize() {
  let configuredCPUs = global.config.cdrSweeper.clusterMode && global.config.cdrSweeper.clusterSize || 1;
  global.cpus = os.cpus().length;
  return Math.min(configuredCPUs, global.cpus);
}

function startServer(clusterSize = 1) {
  if (clusterSize > 1) {
    console.warn("Running the nodeLogger in cluster mode...");
  } else {
    console.warn("Running the nodeLogger in single-proc mode...");
  }
  if (cluster.isMaster && clusterSize > 1) {
    console.log("Master cluster setting up " + clusterSize + " workers...");
    for (let i = 0; i < clusterSize; i++) {
      cluster.fork();
    }
    cluster.on("online", worker => {
      console.log("Worker " + worker.process.pid + " is online");
    });
    cluster.on("exit", (worker, code, signal) => {
      console.log("Worker " + worker.process.pid + " died with code: " + code + ", and signal: " + signal);
      for (let id in cluster.workers) {
        cluster.workers[id].kill();
      }
      // exit the master process
      process.exit(0);
    });
  } else {
    global.clusterId = (cluster.worker) ? cluster.worker.id : 1;
    initCall();
  }
}

process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);

process.on("uncaughtException", (e) => {
  console.error("Uncaught Expection", e);
});

process.on("unhandledRejection", (reason, p) => {
  console.error("Unhandled Expection", reason, p);
});

function shutdown() {
  global.logger.error("Received kill signal. Initiating shutdown...");
  OAM.raiseCriticalAlert("nodeLogger")
    .then(() => {
      process.exit(1);
    }).catch(err => {
      console.error("Failed to Raise Critical alert for nodeLogger process", err);
      process.exit(1);
    });
}

/**
 * The most important function of this Component.
 * Decides to which file to process first and checks for the ctl exists.
 * @param {Array} files
 */
function fileSelector(files) {
  if (global.logger.isTraceEnabled())
    global.logger.trace("file Selector will check for the control files and arrange the files in order...");
  let cdrfiles = _.filter(files, f => {
    if (global.config.cdrSweeper.clusterMode)
      return f.startsWith(global.config.cdrSweeper.filePrefix) && f.endsWith(".cdr");
    return f.includes(".cdr");
  });
  let ctlfiles = _.filter(files, f => {
    if (global.config.cdrSweeper.clusterMode)
      return f.startsWith(global.config.cdrSweeper.filePrefix) && f.endsWith(".ctl");
    return f.includes(".ctl");
  });
  ctlfiles = _.map(ctlfiles, fileNameFinder);
  cdrfiles = _.orderBy(cdrfiles, ["asc"]);
  cdrfiles = _.filter(cdrfiles, (f, i) => { return i <= global.config.nodeLogger.fileBatchSize });
  let file2process = _.filter(cdrfiles, f => { return ctlfiles.indexOf(fileNameFinder(f)) >= 0 });
  if (global.logger.isTraceEnabled())
    global.logger.trace("Files to be processed in this batch : ", file2process);
  return file2process.sort();
}

function fileNameFinder(name) {
  return name.split(".")[0];
}

function createBasicFolders() {
  if (global.logger.isInfoEnabled())
    global.logger.info("Creating Basic Directories....");
  mkdirp.sync(global.config.nodeLogger.path);
  mkdirp.sync(global.config.nodeLogger.proc);
  mkdirp.sync(global.config.nodeLogger.backup);
  mkdirp.sync(global.config.nodeLogger.corrupted);
}
