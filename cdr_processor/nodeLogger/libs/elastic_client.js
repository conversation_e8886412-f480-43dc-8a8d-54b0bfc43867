const elasticsearch = require("elasticsearch");
const Async = require("async");
const OAM = require("oam");
const elasticsearch_conn_oid = "elasticsearch_conn";

var esClient;
module.exports = {
    serviceQuitter: () => {
        try {
            let esClient = getClient();
            if (esClient) {
                if (global.logger.isTraceEnabled())
                    global.logger.trace("Quitting Elastic CLient");
                esClient.close();
                esClient = null;
            }
        } catch (error) {
            global.logger.error("Something went wrong while closing esClient", error);
        }
    },
    serviceCheck: () => {
        return new Promise((resolve, reject) => {
            getClient().ping({
                requestTimeout: global.config.reportServer.requestTimeout
            }, error => {
                if (error) {
                    OAM.emit("criticalAlert", elasticsearch_conn_oid);
                    esClient = null;
                    return reject("Elasticsearch is Down");
                } else {
                    OAM.emit("clearAlert", elasticsearch_conn_oid);
                    return resolve("Elasticsearch has agreed upon our terms.");
                }
            });
        });
    },
    elasticSink: (readymade, currentLineNr, lineCount) => {
        return new Promise(resolve => {
            let running_flag = true;
            let took = 0;
            let count = 0;
            let error = null;
            let startTime = new Date().getTime();
            Async.whilst(
                cb => {
                    return cb(null, running_flag);
                },
                cb => {
                    getClient().bulk({
                        body: readymade
                    }, (err, resp) => {
                        took = resp && resp.took;
                        if (count < global.config.reportServer.maxRetries) {
                            if (err || resp.errors) {
                                count++;
                                OAM.emit("criticalAlert", elasticsearch_conn_oid);
                                esClient = null;
                                if (err) {
                                    global.logger.error("ES:Bulk upload failed", err);
                                    global.logger.warn("Failed to Dump Data to ES, Retry in " + global.config.reportServer.retryTimeOut + " ms, Retry:" + count);
                                } else {
                                    global.logger.warn("Failed to Dump Data to ES with some JSON Fails : Retry in " + global.config.reportServer.retryTimeOut + " ms", JSON.stringify(resp));
                                }
                                error = err || "Batch Failed with Error";
                                setTimeout(() => { cb(null, count); }, global.config.reportServer.retryTimeOut);
                            } else {
                                OAM.emit("clearAlert", elasticsearch_conn_oid);
                                running_flag = false;
                                error = null;
                                cb(null);
                            }
                        } else {
                            running_flag = false;
                            cb("max retires exceeded", count);
                        }
                    });
                },
                error => {
                    let endTime = (new Date().getTime() - startTime);
                    let percentage = percentageCalculator(lineCount, currentLineNr);
                    if (error) {
                        global.logger.fatal("ES failed to accept @ " + percentage + " JSON of length " + (readymade.length / 2) + " after " + count + " retries.", error);
                        resolve(false);
                    } else {
                        if (global.logger.isInfoEnabled()) {
                            global.logger.info("ES: " + took + "ms|Delay: " + endTime + "ms|Status: " + percentage + "|Bucket Length: " + (readymade.length / 2) + "|Retry: " + count + "|");
                        }
                        resolve(true);
                    }
                }
            );
        });
    }
};

function getClient() {
    if (esClient != null) {
        return esClient;
    } else {
        if (global.logger.isInfoEnabled())
            global.logger.info("Creating ES Client:", global.config.reportServer);
        esClient = new elasticsearch.Client({
            host: global.config.reportServer.hosts[0].host,
            requestTimeout: global.config.reportServer.requestTimeout,
            maxRetries: global.config.reportServer.maxRetries,
            maxSockets: global.config.reportServer.maxSockets,
            keepAlive: global.config.reportServer.keepAlive,
            deadTimeout: global.config.reportServer.deadTimeout,
            pingTimeout: global.config.reportServer.pingTimeout
        });
        return esClient;
    }
}

//file display overall completion
function percentageCalculator(lineCount, lineNumber) {
    if (lineCount && lineCount != 0) {
        return ((lineNumber / lineCount) * 100).toFixed(2) + "%";
    } else {
        return "#" + lineNumber;
    }
}
