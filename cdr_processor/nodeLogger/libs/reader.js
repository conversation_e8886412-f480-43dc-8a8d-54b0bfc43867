const fs = require("fs");
const fs_e = require("fs-extra");
const path = require("path");
const EventStream = require("event-stream");
const ELKClient = require("./elastic_client");

module.exports = {
    processFile: (file) => {
        let flag = true;
        // Waits for Flag to be true;
        function ensureFlagIsSet() {
            return new Promise(resolve => {
                (function waitForFlag() {
                    if (flag) return resolve();
                    setTimeout(waitForFlag, 30);
                })();
            });
        }

        return new Promise(async (resolve, reject) => {
            let batch = [];
            let startTime = new Date().getTime();
            try {
                let lineNr = 0;
                let lineCount = await getNumberOfLines(file);
                if (global.logger.isTraceEnabled())
                    global.logger.trace("File:" + file + ", COUNT:" + lineCount + ", BATCH:" + global.config.cdrSweeper.batchSize4FS);
                let actualCount = lineCount % 2 == 0 ? lineCount : lineCount - 1;
                if (lineCount % 2 == 1 && actualCount < global.config.cdrSweeper.batchSize4FS)
                    return resolve(moveFile(file, startTime, lineCount, true));

                let rl = fs.createReadStream(path.join(global.config.nodeLogger.path, file))
                    .pipe(EventStream.split())
                    .pipe(EventStream.mapSync(line => {
                        lineNr++;
                        let event = null;
                        try {
                            //Advance Function for File Parsing.
                            if (line != null && line.trim().length > 0) {
                                event = JSON.parse(line);
                            }
                            if (event != null) {
                                batch.push(event);
                            }
                        } catch (e) {
                            global.logger.fatal("Caught in Parsing the Line #" + lineNr + " of File :" + file + " with Err: " + e);
                            event = null;
                            if (lineNr % 2 == 1) {
                                batch.pop();
                            }
                        }

                        if (global.logger.isTraceEnabled())
                            global.logger.trace("FILENAME:" + file + ", Line #" + lineNr + ", Batch size:" + batch.length + ", actualCount:" + actualCount + ", ES BATCH:" + global.config.nodeLogger.batch);
                        if (batch.length >= global.config.nodeLogger.batch && batch.length % 2 == 0) {
                            rl.pause();
                            ensureFlagIsSet().then(async () => {
                                flag = false;
                                if (global.logger.isTraceEnabled())
                                    global.logger.trace("Uploading the batch size:", batch.length);
                                let bulkStatus = await ELKClient.elasticSink(batch, lineNr, lineCount);
                                if (bulkStatus) {
                                    if (global.logger.isTraceEnabled())
                                        global.logger.trace("Uploaded the batch size:", lineCount);
                                    flag = true;
                                    batch.length = 0;
                                    rl.resume();
                                } else {
                                    rl.destroy();
                                    global.logger.error("Failed Deposit the Batch @ #" + lineNr + " line for File : " + file + ", count:" + lineCount);
                                    return resolve(moveFile(file, startTime, lineCount, true));
                                }
                            });
                        }
                    }).on("error", () => {
                        global.logger.fatal("Came from Read on Err :", file);
                        if (global.logger.isTraceEnabled())
                            global.logger.trace("Program is using " + (process.memoryUsage().heapUsed / 1024 / 1024).toFixed(2) + " MB of Heap for file : " + file);

                        reject("Err in ReadStreaming the File");
                    }).on("end", () => {
                        if (global.logger.isTraceEnabled())
                            global.logger.trace("File reading completed:", file, ",Current batch size is ", batch.length);
                        if (batch.length > 0) {
                            ensureFlagIsSet().then(async () => {
                                flag = false;
                                if (global.logger.isTraceEnabled())
                                    global.logger.trace("Uploading the batch size:", batch.length);
                                let bulkStatus = await ELKClient.elasticSink(batch, lineNr, lineCount);
                                if (bulkStatus) {
                                    if (global.logger.isTraceEnabled())
                                        global.logger.trace("Uploaded the batch size:", count);
                                    flag = true;
                                    return resolve(moveFile(file, startTime, lineCount));
                                } else {
                                    global.logger.error("Failed Deposit the Batch @ #" + lineNr + " line for File : " + file + ", count:" + lineCount);
                                    return resolve(moveFile(file, startTime, lineCount, true));
                                }
                            });
                        } else {
                            let waitStartTime = new Date().getTime();
                            if (global.logger.isTraceEnabled())
                                global.logger.trace("Waiting for bulk upload to complete");
                            ensureFlagIsSet().then(() => {
                                let endTime = (new Date().getTime() - waitStartTime) / 1000;
                                if (global.logger.isTraceEnabled())
                                    global.logger.trace("Took " + endTime + " s for bulk upload to complete");
                                return resolve(moveFile(file, startTime, lineCount));
                            });
                        }
                    }));
            } catch (error) {
                global.logger.error("Failed process File : ", file, error);
                reject("Failed Deposit the Last Batch for File:" + file);
            }
        });
    }
};

function moveFile(filename, startTime, lineCount, isCorrupted = false) {
    return new Promise((resolve, reject) => {
        try {
            let endTime = new Date().getTime() - startTime;
            let destPath = isCorrupted ? global.config.nodeLogger.corrupted : global.config.nodeLogger.proc;
            destPath = path.join(destPath, filename);
            fs_e.move(path.join(global.config.nodeLogger.path, filename), destPath, { clobber: true }, err => {
                if (err) {
                    global.logger.error("Failed to move file:" + filename, err);
                } else {
                    let ctlFile = path.parse(filename).name + ".ctl";
                    if (isCorrupted) {
                        if (global.logger.isInfoEnabled())
                            global.logger.info("Moved file ", destPath, " to Corrupted, Removing the ctl file ", ctlFile);
                    } else {
                        if (global.logger.isInfoEnabled())
                            global.logger.info("Moved file", destPath, "to Processed, Removing the ctl file", ctlFile);
                    }
                    fs.unlinkSync(path.join(global.config.nodeLogger.path, ctlFile));
                }
                if (isCorrupted) {
                    global.logger.fatal("PROCESSED|CORRUPTED FILE:" + filename + "|PROCESSTIME:" + endTime + "ms|TOTAL LINES:" + lineCount);
                } else {
                    global.logger.fatal("PROCESSED|FILE:" + filename + "|PROCESSTIME:" + endTime + "ms|TOTAL LINES:" + lineCount);
                }
                if (global.logger.isTraceEnabled())
                    global.logger.trace("Program is using " + (process.memoryUsage().heapUsed / 1024 / 1024).toFixed(2) + " MB of Heap for file : " + filename);
                return resolve({
                    time: ((new Date().getTime() - startTime) / 1000 / 60),
                    real_time: endTime
                });
            });
        } catch (error) {
            global.logger.error("Encountered the exception while moving file to destination", error);
            reject(error);
        }
    });
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////
// Internal Helper Functions //
//////////////////////////////////////////////////////////////////////////////////////////////////////////

//Function to get Line Count
function getNumberOfLines(file) {
    return new Promise((resolve, reject) => {
        let count = 1;
        fs.createReadStream(path.join(global.config.nodeLogger.path, file))
            .on("data", chunk => {
                for (let i = 0; i < chunk.length; i++)
                    if (chunk[i] == 10) count++;
            })
            .on("end", () => {
                resolve(count);
            })
            .once("error", () => {
                reject("Error While Getting Line Count!");
            });
    });
}
