Node LOGGER Changelog - v3.0.9
------------------------------

<table>
  <tr>
    <td>v3.0.9</td>
    <td>
      <ul>
        <li>Upraded the config-tree to make use of config watcher for Auto configuration reload.</li>
        <li>Upgraded the Third party libraries.</li>
        <li>Removed the unused Third party libraries.</li>
        <li>OAM Client to emit alerts.</li>
        <li>Error handling for elasticsearch upload failures. Elasticsearch bulk upload retry, upon multiple failure move cdr file to corrupted and pcik next file</li>
        <li>Identify the corrupted files and move to corrupted folder</li>
        <li>Enabled the Persistent, Keep-Alive connections on elasticsearch port.</li>
        <li>Backup file name convention CDR.2020-7-8_23:5:33.tar.gz to CDR_2020_07_09_00_59.tar.gz for easy unzip.</li>
        <li>PENDING: Node Cluster improvements required. Each Instance should pick the unique file for processing.</li>
        <li>Accept the password for config-tree in commandline</li>
        <li>Cleanup the proc folder on start of nodelogger</li>
        <li>Skip the compression for empty proc folder</li>
        <li>Parallel processing of files with cluster mode on</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td>v3.0.8</td>
    <td>
      <ul>
        <li>Initial version</li>
      </ul>
    </td>
  </tr>
</table>