/**
 *  <PERSON><PERSON><PERSON> for LEAP - PluginManager
 *  PluginsManager entry point
 *  <AUTHOR>
 **/

const message = require("message");
const error_codes = message.error_codes;
const pm_fsync_cli = require("./pm_fsync_cli");
const PIPE = '|';

global.pecounter = 0;

module.exports = {
    init: async () => {
        console.log("Initializing the Plugin Manager");
        // Calling load plugins function.
        global.logger.warn("Loading plugins...");
        try {
            if (!global.pluginsInfo) {
                global.pluginsInfo = {};
            }
            await pm_fsync_cli.sync();
            global.logger.warn("plugins loaded successfully...");
            return true;
        } catch (e) {
            global.logger.error("Error occured while loading the plugins:", e);
            return false;
        }
    },

    /**
     *List Plugins
     *@param {string} category
     *@param {string =} category is the name of the palette category. If specified, the api wil only return sub items under the matching category. If not specified, returns all.
     */
    getPalttePlugins: async (category) => {
        let all = await pm_fsync_cli.getPluginStatus();
        let response = [];
        global.pluginsInfo.pluginsList.forEach(async element => {
            if (parseInt(element.id, 10) <= 9) {
                response.push(element);
            } else if (parseInt(element.id, 10) > 9 && all[element.id]) {
                response.push(element);
            }
        });
        if (category == null || category === "all" || category === "") {
            return response;
        } else {
            if (findCategory(response, category)) {
                let response1 = [];
                response.forEach((item) => {
                    if (item.category.type === category) {
                        response1.push(item);
                    }
                });
                return response1;
            } else {
                return message.getResponseJson(null, error_codes.categoryNotFound);
            }
        }
    },

    /**
     *Get Meta Info
     *@param {string} pluginID
     *@param {string =} Name of the plugin. If requested plugin not exits it will return the plugin not found error else will return the meta info for requested pligin.
     */
    getMetaData: async (pluginID) => {
        try {
            if (global.logger.isTraceEnabled()) {
                global.logger.trace("Get meta info request for plugin:" + pluginID);
            }
            //Check wether the requested plugin exists, if not return plugin not found error
            if (global.pluginsInfo.pluginsMap && !global.pluginsInfo.pluginsMap.hasOwnProperty(pluginID)) {
                if (global.logger.isTraceEnabled()) {
                    global.logger.trace("The requested plugin is not exists:" + pluginID);
                }
                return message.getResponseJson(null, error_codes.pluginNotFound);
            }
            return await global.pluginsInfo.pluginsMap && global.pluginsInfo.pluginsMap[pluginID].getMetaDataInfo();
        } catch (e) {
            global.logger.error("Exception occurred while getting the meta data for plugin:" + pluginID, e);
            return Promise.reject(e);
        }
    },

    /**
     * validate
     *
     * @param {any} pluginName
     * @param {any} module
     * @returns validation result
     */
    validate: (pluginName, contextData) => {
        try {
            if (global.logger.isTraceEnabled()) {
                global.logger.trace("Module validate request for plugin:" + pluginName);
            }
            //Check wether the requested plugin exists, if not return plugin not found error
            if (!global.pluginsInfo.pluginsMap.hasOwnProperty(pluginName)) {
                if (global.logger.isTraceEnabled()) {
                    global.logger.trace("The requested plugin is not exists:" + pluginName);
                }
                return message.getResponseJson(null, error_codes.pluginNotFound);
            }
            return global.pluginsInfo.pluginsMap[pluginName].validate(contextData);
        } catch (e) {
            global.logger.error("Exception occurred while validtaing plugin:" + pluginName, e);
            return message.getResponseJson(null, error_codes.pluginInternalError);
        }
    },

    /**
     * initialize
     *
     * @param {any} contextData
     * @returns response coming from external interfaces
     */
    initialize: async (contextData) => {
        global.logger.trace("Module initialize request for plugin:" + contextData.pluginName);
        //Check wether the requested plugin exist, if not return plugin not found error
        if (!global.pluginsInfo.pluginsMap.hasOwnProperty(contextData.pluginName)) {
            global.logger.trace("The requested plugin does not exist:" + contextData.pluginName);
            return null;
        }
        return await global.pluginsInfo.pluginsMap[contextData.pluginName].init(contextData);
    },

    /**
     * execute
     *
     * @param {any} context
     * @returns response coming from external interfaces
     */
    execute: async (context, options) => {
        let res;
        let startTime = new Date().getTime();

        try {
            if (global.logger.isTraceEnabled()) {
                global.logger.trace("Module execute request for plugin:" + context.pluginName);
            }
            //Check wether the requested plugin exist, if not return plugin not found error
            if (global.pluginsInfo.pluginsMap == null || !global.pluginsInfo.pluginsMap.hasOwnProperty(context.pluginName)) {
                if (global.logger.isTraceEnabled()) {
                    global.logger.trace("The requested plugin does not exist:" + context.pluginName);
                }
                return message.getResponseJson(null, error_codes.pluginNotFound);
            }
            res = await global.pluginsInfo.pluginsMap[context.pluginName].exec(context, options);
            res.pecounter = global.pecounter;
            res.peThrottleValue = global.peThrottleValue;

            switch (context.pluginName) {
                case "appStart":
                    if (res.code == 898) {
                        writeCDR(context.appId, context.mid, context.pluginName, global.pecounter, res.code, global.peThrottleValue, res.msg);
                    }
                    break;
            }
        } catch (e) {
            global.logger.error("Exception occurred while executing plugin:" + context.pluginName, e);
            res = message.getResponseJson(null, error_codes.pluginInternalError);
            if (e.code != null) {
                res.msg = e.msg + ", Original Error Code:" + e.code;
            }
            if (e.msg != null) {
                res.msg = res.msg + " Trace:" + e.msg;
            }
        }
        res.responseTime = new Date().getTime() - startTime;
        return res;
    },

    convertJson2Schema: async (pluginID, json) => {
        let schema = await global.pluginsInfo.pluginsMap[pluginID].getMetaDataInfo();
        return fillData(schema.properties.process, json);
    },

    getModeSettings: async (mode) => {
        return await pm_fsync_cli.getModeSettings(mode);
    },

    getPluginStatus: async (pluginId) => {
        return await pm_fsync_cli.getPluginStatus(pluginId);
    }

};

/**
 *find Category
 *@param {string} category
 *Check wether the requested category exists or not.
 */
function findCategory(response, category) {
    return (response.filter(item => item.category.type === category).length > 0);
}

function fillData(prop, json) {
    switch (prop.type) {
        case "object":
            let keys = Object.keys(prop.properties);
            for (let i = 0; i < keys.length; i++) {
                let property = keys[i];
                prop.properties[property] = fillData(prop.properties[property], json[property]);
            }
            break;
        case "array":
            let properties = [];

            for (let i = 0; i < json.length; i++) {
                let iprops = JSON.parse(JSON.stringify(prop.items));
                properties.push(fillData(iprops, json[i]));
            }
            prop.items.properties = properties;
            break;
        case "number":
        case "integer":
        case "boolean":
        case "string":
        default:
            if (json != null) {
                prop.value = json;
            }
    }
    return prop;
}

function writeCDR(appId, mid, pluginName, resTime, statusCode, reqObj, res) {
    try {
        if (global.pluginCDR.isTraceEnabled()) {
            global.pluginCDR.error(appId + PIPE + mid + PIPE + pluginName + PIPE + statusCode + PIPE + resTime + PIPE + reqObj + PIPE + res);
        }
    } catch (e) {
        global.logger.error("Expection occured while writing CDRS:", e);
    }
}
