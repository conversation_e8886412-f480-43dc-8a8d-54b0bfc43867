/**
 * @file        pluginsSyncManager.js
 * @description This module handles plugin synchronization with the PM server. It interacts with the 
 *              PM sync service to fetch plugin information, manage files, and monitor plugin statuses.
 *              It also sets up event listeners for plugin creation, deletion, and resynchronization.
 *              
 * @module      pluginsSyncManager
 * @version     1.0.0
 * @created     2024-09-25
 * @lastUpdated 2024-09-25
 * 
 * @dependencies
 * - utility: Used for common utility functions like checksumming files.
 * - request: For making HTTP requests to the PM sync server.
 * - fs: To interact with the file system for reading, writing, and creating files/directories.
 * - path: For resolving file and directory paths.
 * - common: For shared functionalities like event handling via whiteboard.
 * - OAM: To manage and emit alerts for monitoring.
 * 
 * <AUTHOR>
 */

const utility = require("utility");
const request = require("request");
const fs = require("fs");
const path = require("path");
const common = require("common");
const OAM = require("oam");

const whiteboard = common.whiteboard;
const basedir = path.join(__dirname, "/plugins");
const oamAlertOid = global.componentName + "_pmserver_conn";

global.categories = {};
let rescann = 0;

let pmFsyncUrl;
let reqSync;

module.exports = {
  sync: async () => {
    try {
      let protocol = "http", host = "127.0.0.1", port = 4999;
      if (global.config && global.config.pm_fsync) {
        protocol = global.config.pm_fsync.protocol;
        host = global.config.pm_fsync.host
        port = global.config.pm_fsync.port;
      }
      pmFsyncUrl = `${protocol}://${host}:${port}/pm_fsync`;

      global.logger.warn("PM_SERV_PROTOCOL:" + protocol);
      global.logger.warn("PM_SERV_HOST:" + host);
      global.logger.warn("PM_SERV_PORT:" + port);
      global.logger.warn("PM_SERV_URL:" + pmFsyncUrl);

      reqSync = false;
      await loadPlugins();

      whiteboard.on("resync_plugin", async () => {
        await loadPlugins();
      });
      whiteboard.on("delete_plugin", async message => { // Removed parentheses
        try {
          const abspath = path.join(__dirname, "plugins", message); // Changed to const
          utility.rmdir(path.dirname(abspath));
          await loadPlugins();
        } catch (error) {
          global.logger.error(error); // Ensure logger is correctly referenced
        }
      });
      whiteboard.on("create_plugin", async _message => { // Renamed message to _message
        await loadPlugins();
      });
      whiteboard.subscribe("resync_plugin");
      whiteboard.subscribe("delete_plugin");
      whiteboard.subscribe("create_plugin");
    } catch (error) {
      global.logger.error(error);
    }
  },

  getModeSettings: (mode) => {
    let result = {};
    return new Promise((resolve) => {
      let uri = pmFsyncUrl + "/settings/" + mode;
      global.logger.trace("getModeSettings PMFSYNC URI:", uri);
      request.get(uri,
        async (error, response, body) => {
          try {
            if (error !== null) {
              global.logger.error(error);
              if (error.code === "ECONNREFUSED") {
                OAM.emit("criticalAlert", oamAlertOid);
              }
            } else {
              OAM.emit("clearAlert", oamAlertOid);
              //global.logger.trace("PMFSYNC Response:", body);
              if (typeof body === "string") {
                try {
                  result = JSON.parse(body);
                } catch (e) {
                  global.logger.error(e);
                }
              } else if (typeof body === "object") {
                result = body;
              }
            }
          } catch (error) {
            global.logger.error(error);
            if (error.code === "ECONNREFUSED") {
              OAM.emit("criticalAlert", oamAlertOid);
            }
          }
          resolve(result);
        });
    });
  },

  getPluginStatus: () => {
    let result = {};
    return new Promise((resolve) => {
      let uri = pmFsyncUrl + "/status/";
      global.logger.trace("getPluginStatus PMFSYNC URI:", uri);
      request.get(uri,
        async (error, response, body) => {
          try {
            if (error !== null) {
              global.logger.error(error);
              if (error.code === "ECONNREFUSED") {
                OAM.emit("criticalAlert", oamAlertOid);
              }
            } else {
              OAM.emit("clearAlert", oamAlertOid);
              if (typeof body === "string") {
                try {
                  result = JSON.parse(body);
                } catch (e) {
                  global.logger.error(e);
                }
              } else if (typeof body === "object") {
                result = body;
              }
            }
          } catch (error) {
            global.logger.error(error);
            if (error.code === "ECONNREFUSED") {
              OAM.emit("criticalAlert", oamAlertOid);
            }
          }
          resolve(result);
        });
    });
  }
};

async function sync_foldernfile(tree) {

  if (tree === null) return;
  let filename = path.join(basedir, tree.path);
  global.logger.trace("Synchronizing " + filename);

  if (tree.type === "directory") {
    if (!fs.existsSync(filename)) {
      fs.mkdirSync(filename);
    }
    tree.children
      .forEach(child => {
        sync_foldernfile(child);
      });
  } else {
    if (fs.existsSync(filename)) {
      let cks = await utility.checksum(filename);
      global.logger.trace(filename + "|" + cks + "|" + tree.cksum);
      if (cks !== tree.cksum) {
        let bool = await retriveFileBuffer(tree.path);
        global.logger.info(filename + " Plugin creations status: " + bool);
      }
    } else {
      global.logger.trace("Create Plugin " + filename);
      let bool = await retriveFileBuffer(tree.path);
      global.logger.info(filename + " Plugin creations status: " + bool);
    }
  }
}

function loadPlugins() {
  return new Promise((resolve) => {
    let uri = pmFsyncUrl + "/";
    global.logger.trace("loadPlugins PMFSYNC URI:", uri);
    if (!reqSync) {
      reqSync = true;
      request.get(uri,
        async (error, response, body) => {
          try {
            if (error !== null) {
              global.logger.error(error);
              if (error.code === "ECONNREFUSED") {
                OAM.emit("criticalAlert", oamAlertOid);
              }
            } else {
              OAM.emit("clearAlert", oamAlertOid);
              if (typeof body === "string") {
                try {
                  body = JSON.parse(body);
                } catch (e) {
                  global.logger.error(e);
                }
                await sync_foldernfile(body);
                reqSync = false;
              }
            }
            await scanPlugins();
          } catch (error) {
            global.logger.error(error);
            if (error.code === "ECONNREFUSED") {
              OAM.emit("criticalAlert", oamAlertOid);
            }
            reqSync = false;
          }
          resolve();
        });
    }
  });
}

function createFile(filename, buffer) {
  fs.writeFileSync(filename, buffer);
}

function retriveFileBuffer(filepath) {
  return new Promise((resolve) => {

    let filename = path.join(basedir, filepath);
    try {
      let uri = pmFsyncUrl + "/fsbuffer?file=" + filepath;
      global.logger.trace("Download plugin " + filename + ": " + uri);
      request.get(uri,
        async (error, response, body) => {
          try {
            if (error) global.logger.error(error);
            global.logger.trace("Received plugin data for " + filename);
            if (typeof body === "string") {
              body = JSON.parse(body);
            }
            let buffer = Buffer.from(body.data);
            await createFile(filename, buffer);
            resolve(true);
          } catch (error) {
            global.logger.error("Download plugin " + filename + " failed", error);
            resolve(false);
          }
        });

    } catch (error) {
      global.logger.error("Download plugin " + filename + " failed", error);
      resolve(false);
    }
  });
}

async function scanPlugins() {
  try {
    global.logger.warn("Scanning Plugins");
    let scanfile = "./plugins/pluginsScanner";
    if (fs.existsSync(path.resolve(scanfile))) {
      global.logger.trace("pluginsScanner.js file exists");
      delete require.cache[require.resolve(scanfile)];
    } else {
      global.logger.trace("pluginsScanner.js file does not exists");
    }

    let scanner = require(scanfile);
    let result = await scanner.pluginsInfo();
    let pluginsInfo = result;

    global.logger.warn("Plugins scan completed");

    if (pluginsInfo && pluginsInfo.pluginsList) {
      let results = {};
      pluginsInfo.pluginsList.forEach(item => {
        results[item.category.id] = item.category;
      });
      global.categories = results;
      global.pluginsInfo = pluginsInfo;
    }
    rescann = 0;
  } catch (error) {
    if (rescann < 5) {
      rescann++;
      setTimeout(scanPlugins, 1000);
    }
  }
}
