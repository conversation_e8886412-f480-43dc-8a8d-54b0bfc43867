"use strict";
/**
 * @class CDRQueue
 * @version 1.0.0
 * @since 03 April 2018
 * @description CDR Queue manager which helps to push CDR records to to Redis QUEUE
 *
 *
 * <AUTHOR>
 */

const fs = require("fs"),
  mkdirp = require("mkdirp"),
  common = require("common"),
  redis_man = common.redis_man;

const KPI = require("app_kpi");
const OAM = require("oam");
const path = require("path");

if (global.config.app_engine.cdrqueue == null) {
  global.config.app_engine.cdrqueue = {
    host: "127.0.0.1",
    port: 6379,
    db: 1,
    log_key: "LEAP_CDRQ",
    limit: 10000,
    fallback_cdr_path: "/data/leap/cdrs"
  };
}

if (global.config.app_engine.cdrqueue.log_key == null) {
  global.config.app_engine.cdrqueue.log_key = "LEAP_CDRQ";
}

if (global.config.app_engine.cdrqueue.fallback_cdr_path == null) {
  global.config.app_engine.cdrqueue.fallback_cdr_path = "/data/leap/cdrs";
}

if (global.config.app_engine.cdrqueue.limit == null) {
  global.config.app_engine.cdrqueue.limit = 10000;
}

const cdrPath = global.config.app_engine.cdrqueue.fallback_cdr_path;

const LOG_KEY = global.config.app_engine.cdrqueue.log_key || "LEAP_CDRQ",
  log2queueKey = KPI.KEYS.cdrQ,
  sessionstoreKey = KPI.KEYS.sessionstore,
  clusterId = global.clusterId,
  cdrqueue_alert_oid = "session_cleaner_cdrqueue_conn",
  sessionstore_alert_oid = "session_cleaner_sessionstore_conn";

let isMapped = false;

module.exports = {
  init: async () => {
    global.logger.warn("Initializing the CDR Queue");
    redis_man.init({
      key: log2queueKey,
      config: global.config.app_engine.cdrqueue,
      oid: cdrqueue_alert_oid
    });

    redis_man.init({
      key: sessionstoreKey,
      config: global.config.app_engine.sessionstore,
      oid: sessionstore_alert_oid
    });

    let connection = await redis_man.getConnection(log2queueKey);
    connection.set("status", "connected");
    connection.get("status", (err, result) => {
      if (err) {
        global.logger.warn("CDR Queue conn status: Not Connected");
        OAM.emit("criticalAlert", cdrqueue_alert_oid);
      } else {
        global.logger.warn("CDR Queue conn status: Connected", result);
        OAM.emit("clearAlert", cdrqueue_alert_oid);
      }
    });

    connection = await redis_man.getConnection(sessionstoreKey);
    connection.set("status", "connected");
    connection.get("status", (err, result) => {
      if (err) {
        global.logger.warn("Session Store conn status: Not Connected");
        OAM.emit("criticalAlert", sessionstore_alert_oid);
      } else {
        global.logger.warn("Session Store conn status: Connected", result);
        OAM.emit("clearAlert", sessionstore_alert_oid);
      }
    });

    return connection;
  },

  log2queue: async logEntry => {
    let startTime = new Date().getTime();
    try {
      global.logger.trace("Writing CDR:", logEntry);
      let connection = await redis_man.getConnection(log2queueKey);
      connection && connection.lpush(LOG_KEY, logEntry);
      KPI.emit(log2queueKey, startTime, new Date().getTime(), "redis");
    } catch (e) {
      writeFile(logEntry, path.join(cdrPath, log2queueKey + clusterId + ".cdr"));
      KPI.emit(log2queueKey, startTime, new Date().getTime(), "cdr");
    } finally {
      logEntry = null;
    }
  },

  // Get keys which are going to expiry in next 5 seconds
  getExpiringKeys: () => {
    let delta = global.config.app_engine.sessionstore.delta || 5;
    return new Promise((resolve, reject) => {
        redis_man.getConnection(sessionstoreKey)
            .then(connection => {
                return connection.keys("*").then(list => {
                    if (list) {
                        // Use a regular for loop instead of forEach to handle async calls properly
                        let promises = list.map(key => {
                            return connection.ttl(key).then(ttlVal => {
                                if (ttlVal > 0 && ttlVal < delta) {
                                    return connection.get(key).then(val => {
                                        global.sessionKeyQueue.push(val);
                                        return connection.del(key).then(() => {
                                            global.logger.trace("KEY:", key, ",Value:", val);
                                        });
                                    });
                                }
                            });
                        });
                        // Wait for all promises to complete
                        return Promise.all(promises);
                    }
                });
            })
            .then(() => {
                resolve(true);
            })
            .catch(e => {
                global.logger.error("Failed to getExpiringKeys", e);
                resolve(false);
            });
    });
}


};

/** create and write file * */
function writeFile(buffer, filePath) {

  if (isMapped == false) {
    mkdirp(cdrPath, (err) => { });
    isMapped = true;
  }

  fs.appendFile(filePath, buffer + "\n", (err) => {
    if (err) throw err;
  });
}
