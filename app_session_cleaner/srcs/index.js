"use strict";
const args = require("commander");
const cluster = require("cluster");
const ConfigTree = require("config-tree");
const message = require("message");
const OAM = require("oam");
const common = require("common");

global.logger = console;
global.componentName = "session_cleaner";
global.sessionKeyQueue = [];
/**
 * Module variable definitions here.
 * Note: The modules may be initialized *later*, after initBasic() succeeds.
 * This helps us to "require" the modules in a synchronous manner
 * without side-effects.
 **/
initBasic()
  .then(function () {
    global.logger.warn("Finished with basic initialization.");
    global.logger.warn("------------------------------------");
    global.logger.warn();

    // Find out if we have to run in single-proc mode or cluster-mode.
    global.clusterSize = getClusterSize(); // Ensure this function correctly sets global.clusterSize
    if (global.clusterSize > 1) {
      return startServer(global.clusterSize); // Use global.clusterSize here
    } else {
      return startServer(1); // Running in single-proc mode
    }
  })
  .catch(function (e) {
    global.logger.error("error while starting LEAP platform...", e);
    OAM.raiseCriticalAlert("session_cleaner")
      .then(() => {
        process.exit(1);
      }).catch(err => {
        console.error("Failed to Critical alert for Session Cleaner process", err);
        process.exit(1);
      });
  });


/**
 * Performs level-0 initialization, which include:
 * => reading / parsing command-line arguments
 * => Setting up global vars for other files / modules
 * => Reading configuration from config server
 **/
async function initBasic() {
  global.clusterId = 1;
  args
    .version(common.version)
    .option("-H, --host [host]", "Config server host -- defaults to", process.env.CONFIG_HOST || "127.0.0.1")
    .option("-p, --port [port]", "Config server port -- defaults to", process.env.CONFIG_PORT || 6380)
    .option("-n, --db [db]", "Config server db index -- defaults to", process.env.CONFIG_DB || 0)
    .option("-a, --password [password]", "config server password", process.env.CONFIG_AUTH || undefined)
    .parse(process.argv);

  global.args = args.opts();
  global.opts = {
    args: global.args,
    configTasks: [
      "getModuleInfoEx",
      "getGlobalInfo"
    ],
    keys2read: {
      getModuleInfoEx: ["app_engine", "session_cleaner"],
      getGlobalInfo: ["timezone", "kpi", "oam", "certificates"]
    }
  }
  const ConfigProxy = new ConfigTree(global.opts);
  return ConfigProxy.readConfig()
    .then(async () => {
      require("./logger").init();
      ConfigProxy.on("reload_config", async (pattern, channel, key) => {
        try {
          global.logger.info("CONFIG_CHANGE_EVENT", pattern, channel, key);
          if (channel.includes("session_cleaner")) {
            delete require.cache[require.resolve("./logger")];
            require("./logger").init();
          }
        } catch (e) {
          global.logger.error("Failed reload the Config", e);
        }
      });
    })
    .catch(e => {
      global.logger.error("Failed to start LEAP Platform, Connection to:", global.args.host, global.args.port, "Failed", e);
      OAM.emit("criticalAlert", "session_cleaner_configserver_conn");
      process.exit(1);
    });
}

/**
 * Runs the initializer code for each of the worker's instance
 * (if running in cluster mode),
 * or runs this code, directly if running in non-cluster mode.
 **/
async function initCall() {
  try {
    global.logger.warn("Config loaded successfully");
    await OAM.init(global.config.oam);

    OAM.emit("clearAlert", "session_cleaner_configserver_conn");
    await require("./libs/RedisCli").init();

    message.init({
      autoReload: true,
      defaultLocale: global.config.defaultLanguage,
      defaultKey: "E9000"
    });

    //START POLLER
    const SessionExpiryPoller = require("./jobs/SessionExpiryPoller");
    new SessionExpiryPoller().start();
    const TimeoutCDRHandler = require("./jobs/TimeoutCDRHandler");
    new TimeoutCDRHandler().start();

    OAM.emit("clearAlert", "session_cleaner");
  } catch (e) {
    console.error("Error while starting Session Cleaner. ", e);
    OAM.emit("criticalAlert", "session_cleaner");
    process.exit(1);
  }
}


function getClusterSize() {
  return 1;
}

function startServer(clusterSize) {
  if (clusterSize > 1) {
    global.logger.warn("Running the session_cleaner in cluster mode...");
  } else {
    global.logger.warn("Running the session_cleaner in single-proc mode...");
  }

  if (cluster.isMaster && clusterSize > 1) {
    global.logger.info("Master cluster setting up " + clusterSize + " workers...");
    for (let i = 0; i < clusterSize; i++) {
      cluster.fork();
    }

    cluster.on("online", function (worker) {
      global.logger.info("Worker " + worker.process.pid + " is online");
    });

    cluster.on("exit", function (worker, code, signal) {
      global.logger.info("Worker " + worker.process.pid + " died with code: " + code + ", and signal: " + signal);
      
      // Use Object.keys to iterate over the workers
      Object.keys(cluster.workers).forEach(id => {
        if (cluster.workers[id]) {
          cluster.workers[id].kill();
        }
      });

      // Exit the master process
      process.exit(0);
    });
  } else {
    global.clusterId = (cluster.worker) ? cluster.worker.id : 1;
    initCall();
  }
}
process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);

process.on("uncaughtException", (e) => {
  global.logger.error("Uncaught Expection", e);
});

process.on("unhandledRejection", (reason, p) => {
  global.logger.error("Unhandled Expection", reason, p);
});

function shutdown() {
  global.logger.error("Received kill signal. Initiating shutdown...");
  OAM.init(global.config.oam);
  OAM.raiseCriticalAlert("session_cleaner")
    .then(() => {
      process.exit(1);
    }).catch(err => {
      console.error("Failed to Critical alert for Session Cleaner process", err);
      process.exit(1);
    });
}
