const Poller = require("../libs/Poller");
const RedisCli = require("../libs/RedisCli");

class SessionExpiryPoller {
  constructor(interval = 1000) {
    this.poller = new Poller(interval);
  }

  start() {
    this.poller.onPoll(() => {
      this.run();
      this.poller.poll(); // Go for the next poll
    });
    this.poller.poll();
  }

  async run() {
    global.logger.trace("START-Session Expiry Poller, QUEUE:", global.sessionKeyQueue.length);
    await RedisCli.getExpiringKeys();
    global.logger.trace("END-Session Expiry Poller, QUEUE:", global.sessionKeyQueue.length);
  }
}

module.exports = SessionExpiryPoller;
