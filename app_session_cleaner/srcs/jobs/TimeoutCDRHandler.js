const Poller = require("../libs/Poller");
const CDRQUEUE = require("../libs/RedisCli");

class TimeoutCDRHandler {
  constructor(interval = 1000) {
    this.poller = new Poller(interval);
  }

  start() {
    this.poller.onPoll(() => {
      this.run();
      this.poller.poll(); // Go for the next poll
    });
    this.poller.poll();
  }

  async run() {
    // Get keys which are going to expiry in next 5 seconds
    while (global.sessionKeyQueue && global.sessionKeyQueue.length > 0) {
      try {
        const data = JSON.parse(global.sessionKeyQueue.pop());
        data.path.push({
          tid: Date.now(),
          mid: data.endModuleId,
          inf: "appModules",
          hn: 'MCKL-5242',
          mtid: 0.2,
          mst: -1,
          met: -1,
          mrt: 60,
          ocode: 'V9015',
          mcode: 'V9015',
          si: null,
          ts: '',
          ext: 'Session Timeout'
        });
        this.writeCDR(data);
      } catch (e) {
        global.logger.error("Failed to Write timeout CDR", e);
      }
    }
  }

  writeCDR(data) {
    let cdrEnvelope, logEntry;
    try {
      let rt = appResponseTime(data.path);
      let stype = "-1";
      let netType = "-1";
      let plen = data.path.length;

      let scode = -1;
      if (data.path[plen - 1] != null) {
        scode = data.path[plen - 1].mcode;
      } else if (data.path[plen] != null) {
        scode = data.path[plen].mcode;
      }
      logEntry = {
        nid: global.nodeid,
        sid: String(data.id),
        oid: String(data.originAppId) || "-1",
        aid: String(data.appId) || "-1",
        uid: String(data.userId),
        im: data.imode || "QUERY_STRING",
        st: data.startTime,
        et: new Date().getTime(),
        rt: rt || (new Date().getTime() - data.startTime),
        ut: stype && String(stype) || "-1",
        nt: netType && String(netType) || "-1",
        ln: data.locale,
        s: scode,
        p: data.path
      };

      if (data.imode == "CPS_XML") {
        logEntry.sa = data.dataTable["$SENDERADDRESS"];
        logEntry.cid = data.dataTable["$CAMPAIGNID"];
        logEntry.cpid = data.dataTable["$CPID"];
        logEntry.it = data.dataTable["$INTERFACETYPE"];
        logEntry.lc = data.dataTable["$LOCATION"];
        logEntry.cc = data.dataTable["$CAMPAIGNCATEGORY"];
        logEntry.acid = data.dataTable["$ACCOUNTID"];
        logEntry.usid = data.dataTable["$USERID"];
      }
      cdrEnvelope = {
        "index": {
          "_index": "leap." + toDate(logEntry.st),
          "_type": "doc",
          "_id": logEntry.sid + logEntry.aid + logEntry.uid
        }
      };
      CDRQUEUE.log2queue(JSON.stringify(cdrEnvelope) + "\n" + JSON.stringify(logEntry));
    } catch (e) {
      global.logger.error("Failed to process Logentry , path:" + data.path, e);
    } finally {
      cdrEnvelope = null;
      logEntry = null;
      data = null;
    }

  }
}

module.exports = TimeoutCDRHandler;

function appResponseTime(pathArray) {
  let respTime = 0;
  if (pathArray)
    for (let i = 0; i < pathArray.length; i++) {
      respTime += Number(pathArray[i].mrt);
    }
  return respTime;
}

function toDate(epochDate) {
  let date = new Date(epochDate);
  try {
    return (pad(date.getFullYear(), 4) + "." + pad((date.getMonth() + 1), 2) + "." + pad(date.getDate(), 2));
  } finally {
    date = null;
  }
}

function pad(n, width, z = "0") {
  n = n + "";
  return n.length >= width ? n : new Array(width - n.length + 1).join(z) + n;
}
