"use strict";
/**
 ** logger module that initializes log4js settings.
 **/

const log4js = require("log4js");
const path = require("path");
const fs = require("fs");
const mkdirp = require("mkdirp");

var logger;

module.exports = {
    init: () => {
        // For this module to be initialized successfully for the first time,
        // "global.config.log" variable should have been set.
        if (!(global.config && global.config.session_cleaner && global.config.session_cleaner.log)) {
            throw new Error("logger.js -- cannot initialize, because 'global.config' is not set.");
        }

        // create the logs folder if not existing.
        let logdir;
        if (global.config.session_cleaner.log.logdir != null) {
            logdir = path.resolve(global.config.session_cleaner.log.logdir);
        } else {
            logdir = path.join(__dirname, "/logs");
        }
        if (!fs.existsSync(logdir)) {
            mkdirp.sync(logdir);
        }
        let logConfiguration = global.config.session_cleaner.log.log4js;
        Object.keys(logConfiguration.appenders).forEach(appender => {
            try {
                if (logConfiguration.appenders[appender].type != "console") {
                    logConfiguration.appenders[appender].filename = path.join(logdir, logConfiguration.appenders[appender].filename);
                }
            } catch (e) {
                console.error(e);
            }
        });

        Object.keys(logConfiguration.categories).forEach(category => {
            try {
                logConfiguration.categories[category].appenders = [logConfiguration.categories[category].appender];
                delete logConfiguration.categories[category].appender;
            } catch (error) {
                //ignore
            }
        });
        log4js.configure(logConfiguration);
        logger = log4js.getLogger(logConfiguration.categories.default.appenders[0]);
        logger.level = logConfiguration.categories.default.level;
        logger.warn("Log4j initialized");
        global.log4js = log4js;
        global.logger = logger;
    }
};
