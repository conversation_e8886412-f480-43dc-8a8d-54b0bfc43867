const fs = require("fs");
const path = require("path");
const ResourceBundle = require("./ResourceBundle");

// Handle to a resource file.
var pathsep = path.sep,
  autoReload,
  prefix,
  defaultLocale,
  defaultKey,
  directory,
  directoryPermissions,
  extension,
  locales,
  updateFiles;

/**
  * Retrieves a locale specific message string, given a msg code.
  * release will have a localization library in place.
  **/
module.exports = {

  init: (opts = {}) => {
    locales = {};

    // auto reload locale files when changed
    autoReload = (typeof opts.autoReload === "boolean") ? opts.autoReload : false;
    // json files prefix
    prefix = (typeof opts.prefix === "string") ? opts.prefix : "translations";
    // where to store json files
    directory = (typeof opts.directory === "string") ?
      path.resolve(opts.directory) : path.join(__dirname, "../resources");
    // permissions when creating new directories
    directoryPermissions = (typeof opts.directoryPermissions === "string") ?
      parseInt(opts.directoryPermissions, 8) : null;
    // setting defaultLocale
    defaultLocale = (typeof opts.defaultLocale === "string") ? opts.defaultLocale : "en";
    // setting defaultKey
    defaultKey = (typeof opts.defaultKey === "string") ? opts.defaultKey : "-1";
    // where to store json files
    extension = (typeof opts.extension === "string") ? opts.extension : ".json";

    // when missing locales we try to guess that from directory
    opts.locales = opts.locales || guessLocales(directory);

    // implicitly read all locales
    if (Array.isArray(opts.locales)) {

      opts.locales.forEach(function (l) {
        read(l);
      });

      // auto reload locale files when changed
      if (autoReload) {

        // watch changes of locale files (it's called twice because fs.watch is still unstable)
        fs.watch(directory, function (event, filename) {
          let localeFromFile = guessLocaleFromFile(filename);

          if (localeFromFile && opts.locales.indexOf(localeFromFile) > -1) {
            read(localeFromFile);
          }
        });
      }
    }
  },

  get: (locale, key) => {
    if (key == null) {
      key = defaultKey;
    }
    return getLocaleMsg(locale, key)
  },

  getResponseJson: (locale, key, overrideCode) => {
    if (key == null) {
      key = defaultKey;
    }

    let response = {
      code: key,
      msg: getLocaleMsg(locale, key)
    };

    if (overrideCode || overrideCode === 0) {
      response.code = overrideCode;
    }
    return response;
  },

  replacePlaceHolders: (locale, key, opts) => {
    if (key == null) {
      key = defaultKey;
    }

    let response = {
      ...opts,
      code: key,
      msg: getLocaleMsg(locale, key)
    };
    if (opts != null) {
      Object.keys(opts).forEach(key => {
        let regex = new RegExp('<' + key + '>', 'g');
        response.msg = response.msg.replace(regex, opts[key]);
      });
    }
    return response;
  }
};

module.exports.init({});

/* loads the resource file (once and only once) and returns its handle */
function getLocaleMsg(locale, key) {

  if (locales["_" + locale]) {
    return locales["_" + locale].get(key);
  }
  return locales["_" + defaultLocale].get(key);
}

/**
 * tries to guess locales by scanning the given directory
 */
function guessLocales(directory) {
  let entries = fs.readdirSync(directory);
  let localesFound = [];

  for (let i = entries.length - 1; i >= 0; i--) {
    if (entries[i].match(/^\./)) continue;
    let localeFromFile = guessLocaleFromFile(entries[i]);
    if (localeFromFile) localesFound.push(localeFromFile);
  }

  // Custom compare function - replace with your own logic as needed
  localesFound.sort((a, b) => {
    // Example: Compare locales based on length
    return a.length - b.length; // Sort by length of locale strings
    // Or you can define your own logic, e.g., by specific locale patterns
  });

  return localesFound;
}


/**
 * tries to guess locales from a given filename
 */
function guessLocaleFromFile(filename) {
  let extensionRegex = new RegExp(extension + "$", "g");
  let prefixRegex = new RegExp("^" + prefix, "g");

  if (prefix && !filename.match(prefixRegex)) return false;
  if (extension && !filename.match(extensionRegex)) return false;
  return filename.replace(prefix, "").replace(extensionRegex, "");
}

/**
   * try reading a file
   */
function read(locale) {
  let localeFile = {},
    file = getStorageFilePath(locale);
  try {
    localeFile = fs.readFileSync(file);
    try {
      // parsing filecontents to locales[locale]
      locales[locale] = new ResourceBundle(JSON.parse(localeFile));
    } catch (parseError) {
      console.error("unable to parse locales from file (maybe " +
        file + " is empty or invalid json?): ", parseError);
    }
  } catch (readError) {
    // unable to read, so intialize that file
    // locales[locale] are already set in memory, so no extra read required
    // or locales[locale] are empty, which initializes an empty locale.json file

    // since the current invalid locale could exist, we should back it up
    if (fs.existsSync(file)) {
      fs.renameSync(file, file + ".invalid");
    }
    write(locale);
  }
}

/**
   * try writing a file in a created directory
   */
function write(locale) {
  let stats, target, tmp;
  const indent = 2; // Set the desired indentation level for JSON output

  // don't write new locale information to disk if updateFiles isn't true
  if (!updateFiles) {
    return;
  }

  // creating directory if necessary
  try {
    stats = fs.lstatSync(directory);
  } catch (e) {
    fs.mkdirSync(directory, directoryPermissions);
  }

  // first time init has an empty file
  if (!locales[locale]) {
    locales[locale] = {};
  }

  // writing to tmp and rename on success
  try {
    target = getStorageFilePath(locale);
    tmp = target + ".tmp";
    fs.writeFileSync(tmp, JSON.stringify(locales[locale], null, indent), "utf8");
    stats = fs.statSync(tmp);
    if (stats.isFile()) {
      fs.renameSync(tmp, target);
    } else {
      console.error("unable to write locales to file (either " +
        tmp + " or " + target + " are not writeable?): ");
    }
  } catch (e) {
    console.error("unexpected error writing files (either " +
      tmp + " or " + target + " are not writeable?): ", e);
  }
}


/**
   * basic normalization of filepath
   */
function getStorageFilePath(locale) {
  // changed API to use .json as default, #16
  let ext = extension || ".json",
    filepath = path.normalize(directory + pathsep + prefix + locale + ext),
    filepathJS = path.normalize(directory + pathsep + prefix + locale + ".js");
  // use .js as fallback if already existing
  try {
    if (fs.statSync(filepathJS)) {
      extension = ".js";
      return filepathJS;
    }
  } catch (e) {
  }
  return filepath;
}
