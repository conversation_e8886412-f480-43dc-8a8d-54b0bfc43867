module.exports = Object.freeze({
  success: 0,
  invalidJSONBody: 600,
  apigwGenericError: 699,
  authorizationMissing: 605,
  authServerError: 609,
  userActivated: 610,
  userDeactivated: 611,
  userAlreadyActivated: 641,
  userAlreadyDeactivated: 642,
  userSuspended: 620,
  resourcePermissionDenied: 625,
  serviceNotAvailable: 700,
  internalError: 701,
  appStoreError: 702,
  invalidSizeField: 703,
  invalidSortfField: 704,
  invalidOrderField: 705,
  appNotFound: 706,
  appCreated: 707,
  appUpdated: 708,
  appDeleted: 709,
  appsListed: 710,
  appUndefined: 711,
  invalidAppSchema: 712,
  invalidJson: 713,
  missingId: 714,
  missingName: 715,
  missingAppData: 716,
  missingStartId: 717,
  missingModules: 718,
  missingModuleType: 719,
  missingModuleCoordinates: 720,
  missingModuleSettings: 721,
  missingModuleInput: 722,
  missingModuleProcess: 723,
  missingModuleOutput: 724,
  missingEndId: 725,
  undefinedStartModule: 726,
  undefinedEndModule: 727,
  invalidId: 728,
  invalidName: 729,
  invalidAppData: 730,
  invalidStartId: 731,
  invalidModules: 732,
  invalidModuleType: 733,
  invalidModuleCoordinates: 734,
  invalidModuleSettings: 735,
  invalidModuleInput: 736,
  invalidModuleProcess: 737,
  invalidModuleOutput: 738,
  appExists: 739,
  invalidPageField: 740,
  invalidStatusField: 741,
  appNoLongerExists: 742,
  pageDoesNotExists: 743,
  appForbidden: 744,
  missingAppTemplateId: 745,
  appTemplateNoLongerExists: 746,
  undefinedModule: 747,
  appNameMinLimitUnmet: 748,
  appNameMaxLimitExceeds: 749,
  appDescMaxLimitExceeds: 750,
  invalidContentType: 751,
  appOwnerUpdated: 752,
  appNameExists: 753,
  incorrectStatusField: 754,
  actionNotAllowed: 760,
  invalidEvent: 761,
  invalidInput: 762,
  invalidScope: 763,
  ESError: 781,
  ESEmptyData: 782,
  reportTypeError: 783,
  influxDBError: 784,
  appArchiveProgress: 785,
  notPermitted: 786,
  assigningSameUser: 787,

  pluginInternalError: 800,
  categoryNotFound: 801,
  pluginNotFound: 802,
  ECONNREFUSED: 803,
  settingsNotFound: 804,
  queryNotFound: 805,
  notAllowed: 806,
  ESOCKETTIMEDOUT: 807,
  resourceNotFound: 808,
  smsBindResponseFail: 809,
  smsBindResponseTimeOut: 810,
  //SOAP plugin status codes
  uploadDirNotFound: 851,
  fileUploadFail: 852,
  fileExtNotSupport: 853,
  internalUploadErr: 854,
  fileNameInvalid: 855,
  fileFormatNotSupported: 856,
  deleteFileErr: 859,
  confirmPluginErr: 860,
  pluginOverwritingErr: 861,
  badRequest: 862,
  payloadTooLarge: 863,
  readDirError: 870,
  emptyDir: 871,
  wsdlParsingErr: 872,
  pluginCheckErr: 873,
  wsdlDirDelErr: 874,
  createPluginErr: 875,
  deployErr: 876,
  soapExecErr: 880,
  soapTestErr: 881,
  soapFaultCode: 882,
  uploadSuccess: 890,
  deleteSuccess: 891,
  pluginExists: 892,
  pluginCreated: 893,
  pluginOverwritingSuccess: 894,
  pluginOverwritingAbort: 895,
  pluginEmptyResponse: 897,
  pluginExecBusy: 898,
  pluginExecTimeOut: 899,
  pluginStoreError: 900,
  invalidPluginId: 901,
  builtInPlugins: 902,
  pluginDeleteSuccess: 903,
  pluginDeleteFailed: 904,
  settingsPayloadRequired: 905,
  settingsUpdatedSuccessfully: 906,
  settingsUpdateFailed: 907,
  pluginPageDoesNotExists: 908,
  pluginInfoNotFound: 909,
  pluginPayloadRequired: 910,
  invalidPluginStatus: 911,
  pluginActivatedSuccessfully: 912,
  pluginDeactivatedSuccessfully: 913,
  pluginActivationFailed: 914,
  pluginDeactivationFailed: 915,
  acl_internalerror: 999,
  acl_filename_toolarge: 945,
  acl_malicious_file: 946,
  acl_file_ext_notsupported: 947,
  acl_dir_nopermission: 948,
  acl_payloadTooLarge: 949,
  acl_mindiskspace: 950,
  acl_too_many_files: 951,
  acl_already_cancelled: 952,
  acl_file_notfound: 954,
  acl_listed: 960,
  acl_pageDoesNotExists: 961,
  acl_list_created: 962,
  acl_name_exists: 963,
  acl_not_found: 964,
  acl_deleted: 965,
  acl_updated: 966,
  acl_linked: 967,
  update_link_list: 968,
  ussdServiceCreated: 1000,
  missingUssdName: 1001,
  ussdNameMinLimitUnmet: 1002,
  invalidUssdName: 1003,
  ussdNameMaxLimitExceeds: 1004,
  ussdDescMaxLimitExceeds: 1005,
  ussdServiceExists: 1006,
  ussdShortcodeExists: 1007,
  UssdShortcodeNotFound: 1008,
  shortcodePurgedSuccessfully: 1009,
  shortcodeUpdatedSuccessfully: 1010,
  shortcodesListed: 1010,
  gatewaysListed: 1010,
  ussdGwConfigCreated: 1011,
  missingUssdGwConfigName: 1012,
  ussdGwConfigNameMinLimitUnmet: 1013,
  invalidUssdGwConfigName: 1014,
  ussdGwConfigNameMaxLimitExceeds: 1015,
  ussdGwConfigExists: 1016,
  ussdGwConfigNotFound: 1017,
  ussdGwConfigPurgedSuccessfully: 1018,
  ussdGwConfigUpdatedSuccessfully: 1019,
  ParamNotAvailable: 1020,
  commandNotFound: 1021,
  serverError: 1022,
  noResponse: 1023,
  connectionFail: 1024,
  successfullConnection: 1025,
  serverTimeout: 1026,
  connectionRefused: 1027,
  serverClosed: 1028,
  ussdServiceDescError: 1029,
  xAxis: "xAxis",
  total: "total",
  successMsg: "successMsg",
  failure: "failure",
  average: "average",
  averageRes: "averageRes",
  activeUsers: "activeUsers",
  errorCount: "errorCount",
  totalApps: "totalApps",
  UtilizationData: "UtilizationData",
  licencedValue: "licencedValue",
  licenceThreshold: "licenceThreshold",
  TVC: "TVC",
  Draft: "Draft",
  ApprovalPending: "ApprovalPending",
  ApprovedForStaging: "ApprovedForStaging",
  ScheduledForStaging: "ScheduledForStaging",
  ScheduledForLaunching: "ScheduledForLaunching",
  Staged: "Staged",
  Launched: "Launched",
  Retired: "Retired"
});
