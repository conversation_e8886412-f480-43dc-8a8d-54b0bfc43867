const util = require("util");

class ResourceBundle {
  constructor(resource) {
    this.resource = resource;
  }

  get(key) {
    if (arguments.length <= 1) {
      return this.resource[key];
    } else {
      let params;
      if (arguments.length === 2 && arguments[1] instanceof Array) {
        params = [this.resource[key]].concat(arguments[1]);
      } else {
        params = Array.prototype.slice.call(arguments);
        params[0] = this.resource[key];
      }
      return util.format.apply(util, params);
    }
  }
}

module.exports = ResourceBundle;
