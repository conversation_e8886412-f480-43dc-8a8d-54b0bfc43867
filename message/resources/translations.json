{"0": "Success", "E9000": "Your request cannot be processed now. Please try later.", "2xx series of messages...": "", "200": "Success", "400": "Bad Request", "401": "Unauthorized", "402": "Payment required", "403": "Forbidden", "404": "Page not found", "405": "Method not allowed", "406": "Not acceptable", "407": "Proxy authentication required", "408": "Request Timeout", "409": "Conflict", "410": "Gone", "411": "Length required", "412": "Precondition failed", "413": "Payload too large", "414": "URI too large", "415": "Unsupported Media type", "416": "Request range not satisfiable", "417": "Exception Failed", "420": "Misdirected Request", "422": "Unprocessable Entity", "423": "Locked", "424": "Failed Dependency", "425": "Too Early", "426": "Upgrade Required", "428": "Precondition Required", "429": "Too Many Requests", "431": "Request header fields too large", "451": "Unavailable for legal reason", "500": "Internal Server Error", "501": "Not Implemented", "502": "Service Unavaiable", "503": "Bad Gateway", "504": "Gateway Timeout", "505": "HTTP Version Not Supported", "506": "<PERSON><PERSON><PERSON> also Negotiates", "507": "Insufficient Storage", "508": "Loop Detected", "510": "Not Extended", "511": "Network Authentication Required", "6xx series -- Authserver specific.": "", "600": "Invalid JSON body", "699": "Something went wrong please try later", "601": "Missing username", "602": "Invalid Username or Password", "603": "OTP verification failed.", "605": "Authorization missing", "606": "JWT invalid", "607": "JWT expired", "608": "Old Password is incorrect", "609": "Auth Server is down", "610": "User Activated Successfully", "611": "User Deactivated Successfully", "612": "Length of the LoginID exceeded", "613": "Length of the firstname exceeded", "614": "Length of the email Id exceeded", "615": "Missing OTP", "616": "Missing password", "617": "Missing Old password", "618": "Missing New password", "619": "Old and New password cannot be same", "620": "Password matches with one of the last 5 passwords", "621": "User logged out. Cannot issue JWT", "622": "User not found", "623": "JWT blacklisted", "624": "User Suspended", "625": "Resource Permission Denied", "641": "User Already Activated", "642": "User Already Deactivated", "7xx series -- Application Management specific.": "", "700": "Service not available", "701": "Application Management Internal Error", "702": "App Store not reachable", "703": "Bad input for size field", "704": "Bad input for sortf field", "705": "Bad input for order field", "706": "App Not found in AppStore", "707": "App created successfully", "708": "App updated successfully", "709": "App deleted successfully", "710": "Apps listed successfully", "711": "App is not defined", "712": "Invalid App <PERSON>hema", "713": "Invalid JSON body", "714": "App id field is missing", "715": "App name field is missing", "716": "appData field is missing in appData", "717": "startId field is missing in appData", "718": "modules field is missing in appData", "719": "type field is missing in module", "720": "coordinates field is missing in module", "721": "settings field is missing in module", "722": "input field is missing in module", "723": "process field is missing in module", "724": "output field is missing in module", "725": "End module id is not found", "726": "Start module is not defined", "727": "End module is not defined", "728": "Invalid App:id", "729": "Invalid App:name", "730": "Invalid App:appData", "731": "Invalid App:startId", "732": "Invalid App:modules", "733": "Invalid AppModule:type", "734": "Invalid AppModules:coordinates", "735": "Invalid AppModules:settings", "736": "Invalid AppModules:input", "737": "Invalid AppModules:process", "738": "Invalid AppModules:output", "739": "Resource conflicts, App already exists", "740": "Page does not exists", "741": "Bad input for status field", "742": "Resource conflicts, App no longer exists", "743": "Page does not exist", "744": "Access forbidden for App", "745": "appTemplateId field is missing in request Payload", "746": "Resource conflicts, App Template no longer exists", "747": "Module undefined", "748": "App name should have Minimum 5 characters", "749": "App name should not exceed more than 32 characters", "750": "App description field MAX limit exceeds", "751": "Invalid Content Type", "752": "App owner updated successfully", "753": "App name already exists", "754": "Incorrect status field", "760": "Currently application is not in the state to perform this action", "761": "Invalid events", "762": "Invalid input", "763": "User dont have the permission", "781": "Elastic search Error", "782": "ESEmptyData", "783": "reportTypeError", "784": "InfluxDB search error", "785": "App Archive request accepted, You receive notification shortly", "786": "Permitted to assign application to app-developer only", "787": "Assigning application to current owner is not allowed", "8xx series -- PluginManager specific.": "", "800": "Plugins internal error", "801": "Category is not found", "802": "Requested plugin is not available", "803": "Not able to connnect to external interface", "804": "Settings not found", "805": "Query object is not available", "806": "Permission is not allowed", "807": "Socket time out", "808": "Resource not found/Connection refused", "809": "SMSC BIND response fail", "810": "SMSC BIND response timeout", "811": "<PERSON><PERSON><PERSON> name is missing", "812": "Host is missing", "813": "port is missing", "814": "path is missing", "815": "origin time stamp invalid format", "816": "subscriber NAI is not allowed", "817": "subscriber number type not correct", "818": "Message capability flag type is not correct", "850-899 series -- SOAP plugin specific.": "", "851": "WSDL/XSD root upload directory not found", "852": "WSDL/XSD upload failed", "853": "File extension not supported", "854": "Internal error while uploading the file", "855": "Filename should be <appName>_<locale>.json format", "856": "Unsupported file format is being uploaded", "859": "File deletion unsuccesful", "860": "Error while performing plugin confirmation step", "861": "Plugin Overwriting Error", "862": "Request should have either 'Yes' or 'No' in query", "863": "Uploaded files exceed the max size", "870": "Error while reading WSDL/XSD directory", "871": "Empty directory. No WSDL/XSD to process", "872": "Error while parsing the WSDL/XSD", "873": "SOAP plugin check error", "874": "WSDL directory deletion unsuccessful", "875": "Create plugin error", "876": "Error in deploy method", "880": "SOAP execution failed", "881": "SOAP testing failed", "882": "SOAP Execution Fault Error", "890": "File upload successful", "891": "File delete successful", "892": "Plugin already exists. Check for user's confirmation", "893": "Plugin deployed successfully", "894": "Plugin overwriting successful", "895": "Plugin overwriting aborted", "896": "Page does not exist", "898": "System is busy, Please try again later.", "899": "Plugin request execution timeout", "900": "Plugin Store Internal error", "901": "Invalid plugin ID", "902": "built-in plugin cannot be deleted", "903": "Plugin deleted successfully", "904": "Failed to delete Plugin", "905": "Payload is required to Update the Plugin settings", "906": "Plugin settings updated successfully", "907": "Failed to update Plugin settings", "908": "Plugin page does not exists", "909": "Plugin not found", "910": "Payload is required", "911": "Invalid status Field", "912": "Plugin Activation is Successful", "913": "Plugin Deactivation is Successful", "914": "Plugin Activation is Failed", "915": "Plugin Deactivation is Failed", "945": "<filename> Filename is too large", "946": "Sorry! you have uploaded malicious file.", "947": "<filename> is having allowed file extention. This format is not allowed", "948": "File server is not ready to receive files at this moment", "949": "File you have uploaded <filename> is crossing maximum size limits. Max size: <maxSize> GB", "950": "There is no enough space in the file server, please try again later", "951": "You have reached maximum allowed file uploads, please try again later", "952": "You have already cancelled the file uploads", "953": "File you are trying to delete is found in the system", "960": "ACL listed successfully", "961": "ACL page not found", "962": "ACL created successfully, Resource ID: <listId>", "963": "ACL name is conflicting with existing resource", "964": "ACL not found", "965": "ACL deleted successfully, Resource ID: <listId>", "966": "ACL updated successfully", "967": "ACL Linked successfully", "968": "Updated associated Acl List successfully", "999": "ACL-Internal Error", "1000": "USSD Service created successfully", "1001": "USSD Service name field is missing", "1002": "Service name should have Minimum 5 characters", "1003": "Invalid USSD Service name", "1004": "Service name should not exceed more than 32 characters", "1005": "Service description field MAX limit exceeds", "1006": "Service name already exists", "1007": "USSD Shortcode already exists", "1008": "USSD Shortcode does not exists", "1009": "USSD Shortcode Purged Successfully", "1010": "USSD Shortcode Updated Successfully", "1011": "USSD GW Configuration created successfully", "1012": "USSD GW name field is missing", "1013": "USSD GW name should have Minimum 5 characters", "1014": "Invalid USSD USSD GW name", "1015": "USSD GW name should not exceed more than 32 characters", "1016": "USSD GW name already exists", "1017": "USSD GW Configuration does not exists", "1018": "USSD GW Purged Successfully", "1019": "USSD GW Updated Successfully", "1020": "Params not availabe for required app", "1021": "No commands to execute", "1022": "Server connection is lost", "1023": "No Response From Server Received", "1024": "Error in settings , could not connect to server", "1025": "Connection Successful", "1026": "Server connection timeout", "1027": "Connection refused/Connetion already exist", "1028": "Server has been closed", "1029": "Special Characters are not allowed", "SAMVAADAK Message resource": "", "S9000": "Request successfully accepted, it will processed", "S9001": "Request successfully processed", "V9001": "InvokerId is missing", "V9002": "InvokerId is not registered for console logging", "V9003": "appId is missing in the request", "V9004": "MSISDN query parameter is missing in the request", "V9005": "MSISDN is not valid", "V9006": "Mising some query parameters", "V9007": "Application not found in App store", "V9008": "Application you are trying to access is not available in this engine", "V9009": "Access denied because of missing/wrong credentials", "V9010": "Application is incomplete End module is missing", "V9011": "USSD Service you are trying is not available", "V9012": "USSD Shortcode you have dailed is invalid", "V9013": "Invalid shortcode", "V9014": "Invalid Input", "V9015": "Dear Customer, you will receive notification", "V9016": "Gateway is not active", "V9017": "Rejecting the request: Unexpected query string parameters are being supplied in request", "xAxis": "X", "total": "total", "successMsg": "success", "failure": "failure", "average": "average", "averageRes": "averageRes", "activeUsers": "activeUsers", "errorCount": "errorCount", "totalApps": "totalApps", "UtilizationData": "Utilization Data", "licencedValue": "Licenced Value", "licenceThreshold": "Licence Threshold", "TVC": "Threshold Value Crossed", "Draft": "Draft", "ApprovalPending": "Approval Pending", "ApprovedForStaging": "Approved For Staging", "ScheduledForStaging": "Scheduled For Staging", "ScheduledForLaunching": "Scheduled For Launching", "Staged": "Staged", "Launched": "Launched", "Retired": "Retired", "S9006": "Please configure single menu item less than the prescribed limit"}