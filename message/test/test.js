/**
 *  Tests the authorization permissions.
 **/

"use strict";
const chai = require("chai"),
	expect = chai.expect,
	message = require("../src/message"),
	path = require("path"),
	fs = require("fs");

let dir = path.join(__dirname, "./resources");


describe("Test suite for Message Resource Bundle.....", () => {

	describe("Test suite for get method, autoReload disabled ", () => {

		before(() => {
			message.init({
				prefix: "message",
				directory: dir,
				defaultKey: "default",
				defaultLocale: "en_US",
				autoReload: false,
				extension: ".json"
			});
		});

		it("Given Zero arguments", () => {
			let result = message.get();
			expect(result).to.be.equals("Default");
		});

		it("Given locale argument but no key", () => {
			let result = message.get("zh");
			expect(result).to.be.equals("Default");
		});

		it("Given locale and key arguments", () => {
			let result = message.get("en_US", "name");
			expect(result).to.be.equals("demoUser");
		});

		it("Given locale and key arguments, but key is not definined in resource", () => {
			let result = message.get("en_US", "unknown");
			expect(result).to.be.equals(undefined);
		});

		it("Given locale and key arguments, locale resource file not available result from default file", () => {
			let result = message.get("unknown", "name");
			expect(result).to.be.equals("demoUser");
		});

		it("Given locale and key arguments, System user modify the value of a key", () => {

			let filepath = path.join(dir, "message_zh.json");
			let filedata = fs.readFileSync(filepath).toString();
			let json = JSON.parse(filedata);
			let temp = json.desc;
			json.desc = "update_" + new Date().getTime();
			fs.writeFileSync(filepath, JSON.stringify(json));
			let result = message.get("zh", "desc");
			expect(result).to.be.equals("test");
			json.desc = "test";
			fs.writeFileSync(filepath, JSON.stringify(json));
		});

	});

	describe("Test suite for get method, autoReload enabled ", () => {

		before(() => {
			message.init({
				prefix: "message",
				directory: dir,
				defaultKey: "default",
				defaultLocale: "en_US",
				autoReload: true,
				extension: ".json"
			});
		});

		it("Given Zero arguments", () => {
			let result = message.get();
			expect(result).to.be.equals("Default");
		});

		it("Given locale argument but no key", () => {
			let result = message.get("zh");
			expect(result).to.be.equals("Default");
		});

		it("Given locale and key arguments", () => {
			let result = message.get("en_US", "name");
			expect(result).to.be.equals("demoUser");
		});

		it("Given locale and key arguments, but key is not definined in resource", () => {
			let result = message.get("en_US", "unknown");
			expect(result).to.be.equals(undefined);
		});

		it("Given locale and key arguments, locale resource file not available result from default file", () => {
			let result = message.get("unknown", "name");
			expect(result).to.be.equals("demoUser");
		});

		it("Given locale and key arguments, System user modify the value of a key", () => {

			let filepath = path.join(dir, "message_zh.json");
			let filedata = fs.readFileSync(filepath).toString();
			let json = JSON.parse(filedata);
			let temp = json.desc;
			json.desc = "update_" + new Date().getTime();
			fs.writeFileSync(filepath, JSON.stringify(json));
			let result = message.get("zh", "desc");
			expect(result).to.be.equals("test");
			json.desc = "test";
			fs.writeFileSync(filepath, JSON.stringify(json));
		});
		it("Given locale and key arguments, locale resource file not available result from default file", () => {
			let result = message.get("unknown", "name");
			expect(result).to.be.equals("demoUser");
		});
	});
});