{"name": "tps_agent", "version": "3.0.9", "description": "node module which captures the App engine TPS stats into influxdb", "author": "<EMAIL>", "license": "ISC", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"commander": "^5.1.0", "common": "file:../../libs/common", "config-tree": "file:../../libs/config-tree", "flat": "^5.0.0", "influx": "^5.5.2", "log4js": "6.3.0", "md5": "^2.3.0", "mkdirp": "^1.0.4", "mysql": "^2.18.1", "oam": "file:../../libs/oam", "nyc": "15.1.0"}}