const influx = require("influx");
const os = require("os");
var client;
module.exports = {
  init: () => {
    client = getClient();
  },
  writeDataPoints: async (measurement, json, isArray = false) => {
    try {
      let cli = getClient();
      global.logger.trace("client object:", cli != null);
      if (cli != null) {
        global.logger.trace("client object obtained");
        await cli.ping(50);
        if (!isArray) {
          let datapoint = [{
            measurement: measurement,
            tags: { host: os.hostname() },
            fields: json
          }];
          await cli.writePoints(datapoint);
        } else {
          await cli.writePoints(json);
        }
      }
    } catch (e) {
      global.logger.error("resetting client", e);
      client = null;
    }
  }
};

function getClient() {
  if (client == null) {
    client = new influx.InfluxDB({
      host: global.config.influxDB.host,
      port: global.config.influxDB.port,
      database: global.config.influxDB.database,
      username: global.config.influxDB.username,
      password: global.config.influxDB.password
    });
  }
  return client;
}
