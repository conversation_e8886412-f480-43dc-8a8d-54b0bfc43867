"use strict";
/***
 *
 *  LEAP application platform -- entry point file.
 *  Initiates a web services to handle requests on apps.
 *
 *  Basic steps here:
 *  1.  Scan the apps directory and load available apps.
 *  2.  Start web service and be ready.
 *
 ***/

// Essential required modules
const args = require("commander");
const ConfigTree = require("config-tree");
const os = require("os");
const fs = require("fs");
const path = require("path");
const OAM = require("oam");
const common = require("common");
const influxCli = require("./influxdbcli");
const flatten = require("flat");

const INTERFACES = Object.freeze({
  QUERY_STRING: "QUERY_STRING",
  HTTP_JSON: "HTTP_JSON",
  CPS_XML: "CPS_XML",
  SMPP: "SMPP",
  WEBSOCKET: "WEBSOCKET",
  NEXMO: "NEXMO",
  SMPP_FLASH: "SMPP_FLASH",
  APP_PUSH: "APP_PUSH",
  USSD_PUSH: "USSD_PUSH"
});

/**
 * Module variable definitions here.
 * Note: The modules may be initialized *later*, after initBasic() succeeds.
 * This helps us to "require" the modules in a synchronous manner
 * without side-effects.
 **/
initBasic()
  .then(function () {
    console.warn("Finished with basic initialization.");
    console.warn("------------------------------------");
    console.warn();

    return initCall();
  })
  .catch(function (e) {
    console.error("error while starting LEAP platform...", e);
    OAM.raiseCriticalAlert("tps_agent")
      .then(() => {
        process.exit(1);
      })
      .catch(err => {
        console.error("Failed to Critical alert for TPS agent process", err);
        process.exit(1);
      });
  });

/**
 * Performs level-0 initialization, which include:
 * => reading / parsing command-line arguments
 * => Setting up global vars for other files / modules
 * => Reading configuration from config server
 **/
async function initBasic() {

  args
    .version(common.version)
    .option("-H, --host [host]", "Config server host -- defaults to", process.env.CONFIG_HOST || "127.0.0.1")
    .option("-p, --port [port]", "Config server port -- defaults to", process.env.CONFIG_PORT || 6380)
    .option("-n, --db [db]", "Config server db index -- defaults to", process.env.CONFIG_DB || 0)
    .option("-a, --password [password]", "config server password", process.env.CONFIG_AUTH || undefined)
    .parse(process.argv);
  global.args = args;
  let opts = {
    args: args.opts(),
    configTasks: [
      "getDBInfo",
      "getModuleInfoEx",
      "getGlobalInfo"
    ],
    keys2read: {
      getDBInfo: ["influxDB"],
      getModuleInfoEx: ["app_engine", "app_engine_production", "tps_agent"],
      getGlobalInfo: ["kpi", "oam"]
    }
  }

  const ConfigProxy = new ConfigTree(opts);
  return ConfigProxy.readConfig()
    .then(async () => {
      require("./logger").init();
      global.componentName = "tps_agent";
      influxCli.init();
      global.config.instance = global.config.app_engine_production;
      global.clusterCPUs = 1;
      global.cpus = os.cpus().length;
      if (global.config.instance.clusterCPUs > global.cpus) {
        global.clusterCPUs = global.cpus;
      } else {
        global.clusterCPUs = global.config.instance.clusterCPUs;
      }
    })
    .catch(e => {
      console.error("Failed to start LEAP Platform, Connection to:", args.host, args.port, "Failed", e);
      OAM.raiseCriticalAlert("tps_agent_configserver_conn")
        .then(() => {
          process.exit(1);
        });
    });
}

/**
 * Runs the initializer code for each of the worker's instance
 * (if running in cluster mode),
 * or runs this code, directly if running in non-cluster mode.
 **/
async function initCall() {
  try {
    console.warn("Config loaded successfully");
    await OAM.init(global.config.oam);
    OAM.emit("clearAlert", "tps_agent_configserver_conn");
    run();
    OAM.emit("clearAlert", "tps_agent");
  } catch (e) {
    console.error("Error while starting TPS Agent. ", e);
    OAM.raiseCriticalAlert("tps_agent")
      .then(() => {
        process.exit(1);
      });
  }
}


let heapstats;

let tps_cache = {};
let plugin_cache = {};
let app_cache = {};

let tps_rawdata = {};
let plugin_rawdata = {};
let app_rawdata = {};

let pecounter = 0;
let pecounterStr = [];

function readDataPoint() {
  tps_rawdata = {};

  let identifier = "engine_" + (process.env.NODE_NAME || os.hostname());
  if (global.config.kpi.enabled == true) {
    pecounter = 0;
    pecounterStr = [];
    for (let index = 1; index <= global.clusterCPUs; index++) {
      let nodeid = identifier + "_" + global.config.instance.port + "_" + index;
      let tpsFile = path.join(global.config.kpi.options.file, nodeid + ".tps");
      if (fs.existsSync(tpsFile)) {
        tps_rawdata["index_" + index] = JSON.parse(fs.readFileSync(tpsFile));
        let pc = tps_rawdata["index_" + index].pecounter || 0;
        pecounterStr.push(index + ":" + pc);
        pecounter += pc;
      }
      let memStatsFile = path.join(global.config.kpi.options.file, nodeid + ".stats");
      if (fs.existsSync(memStatsFile)) {
        heapstats = JSON.parse(fs.readFileSync(memStatsFile));
        if (heapstats.pid != null && heapstats.heapUsed > 0 && isRunning(heapstats.pid)) {
          heapstats = flatten(heapstats);
        } else {
          heapstats = null;
        }
      }
      let pluginStatsFile = path.join(global.config.kpi.options.file, nodeid + ".pstats");
      if (fs.existsSync(pluginStatsFile)) {
        plugin_rawdata["index_" + index] = JSON.parse(fs.readFileSync(pluginStatsFile));
      }
      let appStatsFile = path.join(global.config.kpi.options.file, nodeid + ".appstats");
      if (fs.existsSync(appStatsFile)) {
        app_rawdata["index_" + index] = JSON.parse(fs.readFileSync(appStatsFile));
      }
    }
  }
}

function cacheInitialDataPoints() {
  for (let index = 1; index <= global.clusterCPUs; index++) {
    if (tps_cache["index_" + index] == null) {
      tps_cache["index_" + index] = tps_rawdata["index_" + index];
    }
    if (plugin_cache["index_" + index] == null) {
      plugin_cache["index_" + index] = plugin_rawdata["index_" + index];
    }
    if (app_cache["index_" + index] == null) {
      app_cache["index_" + index] = app_rawdata["index_" + index];
    }
  }
}

function getAppSummary() {
  let data = {};
  for (let index = 1; index <= global.clusterCPUs; index++) {
    let key = "index_" + index;
    if (app_rawdata != null && app_rawdata[key] != null) {
      if (app_cache[key] == null) app_cache[key] = {};
      data[key] = getAppsSummaryData(app_rawdata[key], app_cache[key]);
    }
  }
  return data;
}

function getPluginStats() {
  let data = {};
  for (let index = 1; index <= global.clusterCPUs; index++) {
    let key = "index_" + index;
    if (plugin_rawdata != null && plugin_rawdata[key] != null) {
      if (plugin_cache[key] == null) plugin_cache[key] = {};
      data[key] = getPluginTPS(plugin_rawdata[key], plugin_cache[key]);
    }
  }
  return data;
}

function getNodeClusterDataPoints() {
  let data = {
    tps: {},
    app_tps_stats: {},
    apps: {},
    cats: {},
    inf: {}
  };
  for (let index = 1; index <= global.clusterCPUs; index++) {
    let key = "index_" + index;
    if (tps_rawdata != null && tps_rawdata[key] != null) {
      data.app_tps_stats[key] = getApplicationTPS(tps_rawdata[key].app_tps_stats, tps_cache[key].app_tps_stats);
      data.apps[key] = getApplicationStats(tps_rawdata[key].apps, tps_cache[key].apps);
      data.cats[key] = getCategoryStats(tps_rawdata[key].cat, tps_cache[key].cat);
      data.inf[key] = getInterfaceStats(tps_rawdata[key].inf, tps_cache[key].inf);
      data.tps[key] = tps_rawdata[key].tps - tps_cache[key].tps;
      tps_cache[key].tps = tps_rawdata[key].tps;
    }
  }
  return data;
}

function calcuteNodeTPS(data) {
  let nodeData = { tps: 0, app_tps_stats: {}, apps: { '0': { hits: 0, codes: { '0': 0 } } }, cats: { appModules: 0 }, inf: {} };

  Object.keys(data.app_tps_stats).forEach(index => {
    Object.keys(data.app_tps_stats[index]).forEach(appId => {
      if (nodeData.app_tps_stats[appId] == null) {
        nodeData.app_tps_stats[appId] = data.app_tps_stats[index][appId];
      } else {
        nodeData.app_tps_stats[appId] += data.app_tps_stats[index][appId];
      }
    });
  });

  Object.keys(data.apps).forEach(index => {
    Object.keys(data.cats[index]).forEach(cat => {
      if (nodeData.cats[cat] == null) {
        nodeData.cats[cat] = data.cats[index][cat];
      } else {
        nodeData.cats[cat] += data.cats[index][cat];
      }
    });

    Object.keys(data.inf[index]).forEach(interf => {
      if (nodeData.inf[interf] == null) {
        nodeData.inf[interf] = data.inf[index][interf];
      } else {
        nodeData.inf[interf] += data.inf[index][interf];
      }
    });
    nodeData.tps += data.tps[index];
    Object.keys(data.apps[index]).forEach(appId => {
      if (nodeData.apps[appId] == null) {
        nodeData.apps[appId] = data.apps[index][appId];
      } else {
        nodeData.apps[appId].hits += data.apps[index][appId].hits;
      }
    });
  });
  return nodeData;
}

function isRunning(pid) {
  try {
    return process.kill(pid, 0);
  }
  catch (e) {
    return e.code === 'EPERM'
  }
}

function run() {

  let data, nodeData, apps, adata, pdata;
  try {
    readDataPoint();
    cacheInitialDataPoints();
    data = getNodeClusterDataPoints();
    nodeData = calcuteNodeTPS(data);
    nodeData.iat = (new Date().getTime() / 1000).toFixed();
    if (nodeData.tps >= 0) {
      global.logger.info("IAT:" + nodeData.iat + "|TPS:" + nodeData.tps + "|INT:" + JSON.stringify(nodeData.inf) + "|AM:" + JSON.stringify(nodeData.app_tps_stats) + "|CM:" + JSON.stringify(nodeData.cats) + "|PEC:" + pecounter + "|PEB:" + pecounterStr.join(","));
      apps = nodeData.app_tps_stats;
      delete nodeData.app_tps_stats;
      nodeData.pecounter = pecounter;
      if (Object.keys(apps).length > 0) {
        influxCli.writeDataPoints("appstats", flatten(apps));
      }
      influxCli.writeDataPoints("tail", flatten(nodeData));
      adata = getAppSummary();
      Object.keys(adata).forEach(key => {
        influxCli.writeDataPoints("appsummary", adata[key], true);
      });

      pdata = getPluginStats();
      Object.keys(pdata).forEach(key => {
        influxCli.writeDataPoints("plugin_stats", pdata[key], true);
      });
    }
    if (heapstats != null)
      influxCli.writeDataPoints("heapstats", heapstats);
  } catch (e) {
    //console.error(e);
  } finally {
    data = null;
    nodeData = null;
    apps = null;
    pdata = null;
  }
  setTimeout(run, 1000);
}
process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);

process.on("uncaughtException", (e) => {
  console.error("Uncaught Expection", e);
});

process.on("unhandledRejection", (reason, p) => {
  console.error("Unhandled Expection", reason, p);
});

function shutdown() {
  console.error("Received kill signal. Initiating shutdown...");
  OAM.init(global.config.oam);
  OAM.raiseCriticalAlert("tps_agent")
    .then(() => {
      process.exit(1);
    }).catch(err => {
      console.error("Failed to Critical alert for TPS agent process", err);
      process.exit(1);
    });
}

function onlyUnique(value, index, self) {
  return self.indexOf(value) === index;
}

function getCategoryStats(catRawData, catCacheData) {
  let cats = {};
  let arr1 = Object.keys(catRawData);
  let arr2 = Object.keys(catCacheData);
  Array.prototype.push.apply(arr1, arr2);
  arr1 = arr1.filter(onlyUnique);
  arr1.forEach(category => {
    if (catRawData[category] != null && catCacheData[category] != null) {
      cats[category] = catRawData[category] - catCacheData[category];
      if (cats[category] < 0) cats[category] = 0;
      catCacheData[category] = catRawData[category];
    }
  });
  return cats;
}

function getInterfaceStats(infRawData, infCacheData) {
  let interfaces = {};
  Object.keys(INTERFACES).forEach(interf => {
    try {
      let req = 0, res = 0;
      if (infRawData.inf_req != null && infRawData.inf_req.hasOwnProperty(interf)) {
        if (infCacheData.inf_req == null) infCacheData.inf_req = {};
        req = infRawData.inf_req[interf] - infCacheData.inf_req[interf];
        infCacheData.inf_req[interf] = infRawData.inf_req[interf];
      }
      if (infRawData.inf_res != null && infRawData.inf_res.hasOwnProperty(interf)) {
        if (infCacheData.inf_res == null) infCacheData.inf_res = {};
        res = infRawData.inf_res[interf] - infCacheData.inf_res[interf];
        infCacheData.inf_res[interf] = infRawData.inf_res[interf];
      }
      interfaces[interf + "_REQ"] = req > 0 ? req : 0;
      interfaces[interf + "_RES"] = res > 0 ? res : 0;
    } catch (error) {
      console.error(error);
    }
  });
  return interfaces;
}

function findErrorCodeStats(c1, r1) {
  let codes = {};
  let arrc = Object.keys(c1);
  let arrr = Object.keys(r1);
  Array.prototype.push.apply(arrc, arrr);
  arrc = arrc.filter(onlyUnique);
  arrc.forEach(code => {
    code = String(code);
    r1[code] = checkDataType(r1[code]);
    c1[code] = checkDataType(c1[code]);
    codes[code] = r1[code] - c1[code];
    c1[code] = r1[code];
  });
  return codes;
}

function getAppsSummaryData(rData, cData) {
  let app_stats = [];
  let stats = {};
  let total = {};
  let arr1 = Object.keys(cData);
  let arr2 = Object.keys(rData);
  arr1 = arr1.concat(arr2);
  arr1 = arr1.filter(onlyUnique);
  arr1.forEach(id => {
    if (rData[id] == null) rData[id] = checkDataType(rData[id]);
    if (cData[id] == null) cData[id] = checkDataType(cData[id]);
    let response = rData[id] - cData[id];
    if (response > 0) {
      let str = id.split(";");
      let appId = str[0];
      if (stats[appId] == null) stats[appId] = {};
      if (stats[appId][str[1]] == null) stats[appId][str[1]] = 0;
      stats[appId][str[1]] += response;
      if (total[appId] == null) total[appId] = 0;
      total[appId] += response;
    }
    cData[id] = rData[id];
  });
  Object.keys(stats).forEach(appId => {
    Object.keys(stats[appId]).forEach(code => {
      app_stats.push({
        measurement: 'appsummary',
        tags: { host: os.hostname(), appId: appId, code: code },
        fields: { count: stats[appId][code], total: total[appId] }
      });
    });
  });
  return app_stats;
}


function getPluginTPS(rData, cData) {
  let plugin_stats = [];
  let stats = {};
  let total = {};
  let arr1 = Object.keys(cData);
  let arr2 = Object.keys(rData);
  arr1 = arr1.concat(arr2);
  arr1 = arr1.filter(onlyUnique);
  arr1.forEach(id => {
    if (rData[id] == null) rData[id] = checkDataType(rData[id]);
    if (cData[id] == null) cData[id] = checkDataType(cData[id]);
    let response = rData[id] - cData[id];
    if (response > 0) {
      let str = id.split(";");
      let key = str[0] + ";" + str[1] + ";" + str[2];
      if (stats[key] == null) stats[key] = {};
      if (stats[key][str[3]] == null) stats[key][str[3]] = 0;
      stats[key][str[3]] += response;
      if (total[key] == null) total[key] = 0;
      total[key] += response;
    }
    cData[id] = rData[id];
  });
  Object.keys(stats).forEach(stat => {
    let str = stat.split(";");
    Object.keys(stats[stat]).forEach(code => {
      plugin_stats.push({
        measurement: 'plugin_stats',
        tags: { host: os.hostname(), appId: str[0], mid: str[1], mname: str[2], code: code },
        fields: { count: stats[stat][code], total: total[stat] }
      });
    });
  });
  return plugin_stats;
}

function getApplicationTPS(appRawData, appCacheData) {
  let app_tps_stats = {};
  let arr1 = Object.keys(appCacheData);
  let arr2 = Object.keys(appRawData);
  arr1 = arr1.concat(arr2);
  arr1 = arr1.filter(onlyUnique);
  arr1.forEach(appId => {
    if (appRawData[appId] == null) appRawData[appId] = checkDataType(appRawData[appId]);
    if (appCacheData[appId] == null) appCacheData[appId] = checkDataType(appCacheData[appId]);
    app_tps_stats[appId] = appRawData[appId] - appCacheData[appId];
    appCacheData[appId] = appRawData[appId];
  });
  return app_tps_stats;
}

function getApplicationStats(appRawData, appCacheData) {
  let apps = {};
  let arr1 = Object.keys(appCacheData);
  let arr2 = Object.keys(appRawData);
  arr1 = arr1.concat(arr2);
  arr1 = arr1.filter(onlyUnique);
  arr1.forEach(appId => {
    if (appRawData[appId] != null && appCacheData[appId] != null) {
      apps[appId] = {};
      appRawData[appId].hits = checkDataType(appRawData[appId].hits);
      appCacheData[appId].hits = checkDataType(appCacheData[appId].hits);
      apps[appId].hits = appRawData[appId].hits - appCacheData[appId].hits;
      appCacheData[appId].hits = appRawData[appId].hits;
      apps[appId].codes = findErrorCodeStats(appCacheData[appId].codes, appRawData[appId].codes);
    }
  });
  return apps;
}

function checkDataType(val) {
  if (val == null || isNaN(val)) val = 0;
  return val;
}
