===============================================================================
                README for LEAP containerization 
===============================================================================

Installation:
---------------
Note: Disable all the existing monit processes.

1. Load the container images of leap by running 
    $ sh loadimg.sh
========================================================================
Note: Remove services which are not applicable for particular server from docker-compose.yml .
      Like for reporting server remove cdr_sweeper and node_logger.

Change leap-docker-compose-files/es.env :

In case of reporting server 1:
node.name=es01
discovery.seed_hosts=es02,es03

In case of reporting server 2:
node.name=es02
discovery.seed_hosts=es01,es03

In case of reporting server 3:
node.name=es03
discovery.seed_hosts=es01,es02

-----------------------------------------------------------------------------------------

2. Change permissions:
sudo chown -R 1000:0 leap-docker-compose-files/certs/elastic

3. Run the docker compose file.
docker-compose up -d

=========================================================================