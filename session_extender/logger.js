"use strict";
/**
 ** logger module that initializes log4js settings.
 **/

const log4js = require("log4js");
const path = require("path");
const fs = require("fs");
let logger;

// create the logs folder if not existing.
let logdir;
if (global.config.session_extender.log.logdir) {
  logdir = path.resolve(global.config.session_extender.log.logdir);
} else {
  logdir = path.join(__dirname, "/logs");
}
if (!fs.existsSync(logdir)) {
  fs.mkdirSync(logdir);
}



let logConfig = global.config.session_extender.log.log4js;

Object.keys(logConfig.appenders).forEach(appender => {
  try {
    if (logConfig.appenders[appender] && logConfig.appenders[appender].type != "console") {
      logConfig.appenders[appender].filename = path.join(global.config.session_extender.log.logdir, logConfig.appenders[appender].filename);
    }
  } catch (error) {
    global.logger.error(error);
  }
});

Object.keys(logConfig.categories).forEach(category => {
  try {
    logConfig.categories[category].appenders = [logConfig.categories[category].appender];
    delete logConfig.categories[category].appender;
  } catch (error) {
    //ignore
    global.logger.error(error);
  }
});

log4js.configure(logConfig);
if (process.env.PROD) {
  logger = log4js.getLogger("session_extender");
} else {
  logger = log4js.getLogger("console");
}
logger.level = logConfig.categories.default.level;

global.log4js = log4js;
global.logger = logger;

module.exports = logger;

if (logConfig.categories.pluginCDR != null) {
  global.pluginCDR = log4js.getLogger(logConfig.categories.pluginCDR.appenders[0]);
} else {
  global.pluginCDR = logger;
}
