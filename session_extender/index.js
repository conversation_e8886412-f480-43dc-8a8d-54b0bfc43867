"use strict";


const args = require("commander");
const cluster = require("cluster");
const ConfigTree = require("config-tree");
const OAM = require("oam");
const os = require("os");
const common = require("common");
const Redis = require('ioredis');
const axios = require("axios");


initBasic()
  .then(async () => {
    await OAM.init(global.config.oam);
    global.logger.warn("Config loaded successfully");
    global.componentName = "session_extender";
    OAM.emit("clearAlert", "sessionextender_configserver_conn");

    global.logger.warn("Finished with basic initialization.");
    global.logger.warn("------------------------------------");
    global.logger.warn();

    // Find out if we have to run in single-proc mode or cluster-mode.
    global.clusterSize = getClusterSize();
    if (global.clusterSize > 1) {
      return runInClusterMode();
    } else {
      return runInSingleMode();
    }
  })
  .catch(e => {
    console.error("error while starting plugnmanager file service...", e);
    OAM.raiseCriticalAlert("sessionextender_configserver_conn")
      .then(() => {
        process.exit(1);
      }).catch(err => {
        console.error("Failed to Critical alert for session extender process", err);
        process.exit(1);
      });
  });

async function initBasic() {

  args
    .version(common.version)
    .option("-H, --host [host]", "Config server host -- defaults to", process.env.CONFIG_HOST || "127.0.0.1")
    .option("-p, --port [port]", "Config server port -- defaults to", process.env.CONFIG_PORT || 6380)
    .option("-n, --db [db]", "Config server db index -- defaults to", process.env.CONFIG_DB || 0)
    .option("-a, --password [password]", "config server password", process.env.CONFIG_AUTH || undefined)
    .parse(process.argv);

  global.args = args.opts();
  let opts = {
    args: global.args,
    configTasks: [
      "getDBInfo",
      "getModuleInfoEx",
      "getGlobalInfo"
    ],
    keys2read: {
      getGlobalInfo: ["whiteboard", "timezone", "oam", "security"],
      getDBInfo: ["app_store"],
      getModuleInfoEx: ["session_extender", "api_gw", "app_engine", "app_engine_production"]
    }
  };
  const ConfigProxy = new ConfigTree(opts);

  return ConfigProxy.readConfig()
    .then(async () => {
      global.componentName = "session_extender";
      delete require.cache[require.resolve("./logger")];
      require("./logger");
      ConfigProxy.on("reload_config", async (pattern, channel, key) => {
        try {
          delete require.cache[require.resolve("./logger")];
          require("./logger");
        } catch (error) {
          global.logger.error(error);
        }
      });
    })
    .catch(e => {
      console.error("Failed to start session_extender , Connection to:", global.args.host, global.args.port, "Failed", e);
      OAM.raiseCriticalAlert("session_extender")
        .then(() => {
          process.exit(1);
        }).catch(err => {
          console.error("Failed to Critical alert for session extender process", err);
          process.exit(1);
        });
    });
}

async function initCall() {
  try {
    let url = `${global.config.app_engine_production.protocol}://${global.config.app_engine_production.host}:${global.config.app_engine_production.port}/session_extender`;

    var sessionStoreConfig = global.config && global.config.app_engine && global.config.app_engine.sessionstore;

    if (sessionStoreConfig == null) {
      sessionStoreConfig = {
        host: "127.0.0.1",
        port: 6379,
        db: 0,
        timeout: 60,
        compression_required: false
      };
    }

    if (sessionStoreConfig.compression_required === null) {
      sessionStoreConfig.compression_required = false;
    }

    const subscriber = new Redis({
      host: sessionStoreConfig.host,
      port: sessionStoreConfig.port,
      password: sessionStoreConfig.password,
      db: sessionStoreConfig.db
    });

    subscriber.config('set', 'notify-keyspace-events', 'Ex');
    subscriber.subscribe('__keyevent@0__:expired', (err, count) => {
      if (err) {
        console.error('Failed to subscribe:', err);
        return;
      }
      console.log(`Subscribed successfully. Listening for expiration events...`);
    });

    subscriber.on('message', async (channel, expiredKey) => {
      console.log("Request received for :" + expiredKey);
      let requestObj = {
        "key": expiredKey
      }

      const config = {
        headers: {
          'Content-Type': 'application/json',
          'channel': 'session_extender'
        },
        timeout: 10000
      };

      try {
        let resp = await axios.post(url, requestObj, config);
        console.log(resp.data);
      }
      catch (e) {
        console.log(e)
      }
    });
  } catch (error) {
    console.error("session_extender loading failed", error);
    OAM.raiseCriticalAlert("session_extender")
      .then(() => {
        process.exit(1);
      }).catch(err => {
        console.error("Failed to Critical alert for session extender process", err);
        process.exit(1);
      });
  }
}


function getClusterSize() {
  let configuredCPUs = global.config.session_extender && global.config.session_extender.clusterCPUs || 1;
  global.cpus = os.cpus().length;
  return Math.min(configuredCPUs, global.cpus);
}

function runInSingleMode() {
  global.logger.warn("Running the application in single-proc mode...");
  return initCall();
}

function runInClusterMode() {
  global.logger.warn("Running the application in cluster mode...");
  if (cluster.isMaster) {
    global.logger.warn("Master cluster setting up " + global.clusterSize + " workers...");
    for (let i = 0; i < global.clusterSize; i++) {
      cluster.fork();
    }
    cluster.on("online", worker => {
      global.logger.warn("Worker " + worker.process.pid + " is online");
    });
    cluster.on("exit", (worker, code, signal) => {
      global.logger.warn("Worker %s died with code: %s, and signal: %s ", worker.process.pid, code, signal);
      global.logger.warn("Starting a new worker");
      cluster.fork();
    });
  } else {
    initCall();
  }
}


process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);

process.on("unhandledRejection", error => {
  // Will print "unhandledRejection err is not defined"
  global.logger.error(error);
});

process.on("uncaughtException", (err) => {
  global.logger.error(err);
});

process.on("unhandledRejection", (reason, p) => {
  global.logger.error(reason, p);
});

function shutdown() {
  console.log("Received kill signal. Initiating shutdown...");
  OAM.raiseCriticalAlert("session_extender")
    .then(() => {
      process.exit(1);
    }).catch(err => {
      console.error("Failed to Critical alert for session extender process", err);
      process.exit(1);
    });
}
