/**
 *  LEAP - AppStore
 *
 * app_store is the NPM module which provides APIs for all CRUD operations on LEAP Apps
 *
 * Following APIs for API_GW
 *  1. listApps(req, res)
 *  2. createOrUpdateApp(app);
 *  3. deleteApp(appId|appName);
 *  4. findApp(appId|appName);
 *
 **/
const fs = require("fs");
const path = require("path");
var Sequelize;

const common = require("common");
const OAM = require("oam");

const oam_alert_oid = "app_store";

const DATE_FORMAT = "YYYY-MM-DD HH:mm:ss";
const moment = require("moment");

global.getTZFormatDateTime = (dt) => {
    return moment(dt)
        .tz(global.config.timezone)
        .format(DATE_FORMAT);
}

global.raiseAlert = function (e) {
    try {
        if (e.code == "ECONNREFUSED" || e.original.code == "ECONNREFUSED") {
            if (global.health) global.health.report.app_store = "KO";
            OAM.emit("criticalAlert", oam_alert_oid);
        }
    } catch (err) {
        //ignore
    }
}

global.clearAlert = function () {
    if (global.health) global.health.report.app_store = "OK";
    OAM.emit("clearAlert", oam_alert_oid);
}

const AppStore = require("./libs/AppStore");
const NotificationStore = require("./libs/NotificationStore");
const Templates = require("./libs/AppTemplateStore");
const Workflow = require("./libs/WorkflowStore");
const UssdServices = require("./libs/UssdServiceStore");
const MyDownloads = require("./libs/MyDownloads");
const HealthReport = require("./libs/HealthReport");
const AppVersions = require("./libs/AppVersions");

module.exports = {
    init: () => {
        console.log("Initializing the AppStore");
        if (global.config == null) {
            throw new Error("Configuration is not initialized");
        }

        if (global.config.app_store == null) {
            throw new Error("Cannot initialize app_store, unless db config variables are set.");
        }
        
        return new Promise((resolve) => {  // Remove 'async' here
            try {
                const basename = path.basename(module.filename);
                const dbDir = path.join(__dirname, "models");

                global.config.app_store.operatorsAliases = common.operator_aliases;
                global.logger.warn("Timezone:" + (global.config.app_store.timezone || "default")); // Added parentheses for clarity

                let Sequelize;
                switch (global.config.app_store.dialect) {
                    case "oracle":
                        Sequelize = require("sequelize-oracle");
                        global.jsonsupport = false;
                        break;
                    case "mysql":
                        Sequelize = require("sequelize");
                        global.jsonsupport = false;
                        break;
                    case "mariadb":
                        Sequelize = require("sequelize");
                        global.jsonsupport = true;
                        break;
                    default:
                        Sequelize = require("sequelize");
                        global.jsonsupport = true;
                }

                global.leapDB = new Sequelize(
                    global.config.app_store.database,
                    global.config.app_store.username,
                    global.config.app_store.password,
                    global.config.app_store
                );

                fs.readdirSync(dbDir)
                    .filter((file) => file.indexOf(".") !== 0 && file !== basename)
                    .forEach((file) => {
                        if (file.slice(-3) !== ".js") {
                            return;
                        }
                        const filepath = path.join(dbDir, file);
                        if (global.logger.isTraceEnabled()) {
                            global.logger.trace("Importing File: " + filepath);
                        }
                        global.leapDB["import"](filepath);
                    });

                global.leapDB.sync().then(async () => {
                    let result;
                    switch (global.config.app_store.dialect) {
                        case "oracle":
                            result = await global.leapDB.query("SELECT * from TAB");
                            break;
                        case "mysql":
                        case "mariadb":
                        default:
                            result = await global.leapDB.query("SELECT 1");
                    }
                    global.clearAlert();
                    resolve(result);
                }).catch(e => {
                    global.logger.error("Failed to sync AppStore", e);
                    global.raiseAlert(e);
                    resolve(null);
                });

            } catch (e) {
                global.logger.error("Failed to sync AppStore", e);
                global.raiseAlert(e);
                resolve(null);
            }
        });
    },
    ...AppStore,
    ...NotificationStore,
    ...Templates,
    ...Workflow,
    ...UssdServices,
    ...MyDownloads,
    ...HealthReport,
    ...AppVersions
};

