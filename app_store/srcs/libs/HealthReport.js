const KPI = require("app_kpi");
const utility = require("utility");

const DEFAULT_SORT_ORDER = "asc";
const KPI_KEY = KPI.KEYS.appstore;

const APPS_LIST_ATTRIBUTES = ["nid", "hostname", "mode", "cid", "report", "createdAt", "updatedAt"];
const APPS_INFO_ATTRIBUTES = ["nid", "hostname", "mode", "cid", "report", "createdAt", "updatedAt"];

module.exports = {
  getHealthCount: async (opts) => {
    let headers = null,
      startTime = new Date().getTime();
    try {
      let sequel_opts = {},
        page = 1,
        size = Number.MAX_SAFE_INTEGER;
      if (opts != null) {
        page = parseInt(opts.page, 10);
        size = parseInt(opts.size, 10);
        sequel_opts = applyFilters(opts, sequel_opts);
      }
      const total = await global.leapDB.models.EngineHealth.count(sequel_opts);
      size = size > total ? total : size;
      const lastPage = Math.ceil(total / size);
      headers = {
        totalRecords: total,
        lastPage: lastPage,
        prevPage: page > 1 ? page - 1 : page,
        nextPage: page < lastPage ? page + 1 : lastPage,
        pageSize: size
      };
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getHealthCount");
    } catch (e) {
      global.logger.error("Exception while retreiving health report count headers", e);
      global.raiseAlert(e);
    }
    return headers;
  },

  listHealthReports: async (opts) => {
    let reports = null,
      startTime = new Date().getTime();
    try {
      let sequel_opts = {
        attributes: APPS_LIST_ATTRIBUTES
      };
      if (opts != null) {
        sequel_opts = applyFilters(opts, sequel_opts);
      }
      // Converting the Sequelize object to Plain JSON Object
      reports = await global.leapDB.models.EngineHealth.findAll(sequel_opts);
      if (reports != null) {
        reports = JSON.parse(JSON.stringify(reports));
        global.clearAlert();
      }
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listHealthReports");
    } catch (e) {
      global.logger.error("Exception while retreiving reports", e);
      global.raiseAlert(e);
    }
    return reports;
  },

  createHealthReport: async (healthReport) => {
    let res;
    let startTime = new Date().getTime();
    try {
      if (healthReport.report != null) {
        healthReport.report = await utility.deflateJSON(healthReport.report, global.jsonsupport);
      }
      res = await global.leapDB.models.EngineHealth.create(healthReport);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "createHealthReport");
    } catch (e) {
      global.raiseAlert(e);
      throw e;
    }
    return res;
  },

  updateHealthReport: async (healthReport) => {
    let startTime = new Date().getTime(),
      result;
    try {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("NID:" + healthReport.nid + ", Updating report: ", healthReport.report);
      }

      if (healthReport.report != null) {
        healthReport.report = await utility.deflateJSON(healthReport.report, global.jsonsupport);
      }
      result = await global.leapDB.models.EngineHealth.update(healthReport, {
        where: {
          nid: healthReport.nid
        }
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "updateHealthReport");
    } catch (e) {
      global.logger.error("Exception while updating uHealthReport", e);
      global.raiseAlert(e);
    }
    return result;
  },

  findHealthReport: async (nid) => {
    let startTime = new Date().getTime(),
      healthReport = null;
    try {
      let filter = {
        attributes: APPS_INFO_ATTRIBUTES
      };
      filter = Object.assign(filter, {
        where: {
          nid: nid
        }
      });

      healthReport = await global.leapDB.models.EngineHealth.findOne(filter);
      if (healthReport != null) {
        healthReport = JSON.parse(JSON.stringify(healthReport));
        healthReport.report = await utility.unzipJSON(healthReport.report, global.jsonsupport);
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "findHealthReport");
    } catch (e) {
      global.logger.error("Failed to Retrieve HealthReport: ", nid, e);
      global.raiseAlert(e);
    }
    return healthReport;
  }
};

function getSortOptions(opts, sequel_opts) {
  if (opts.sortf) {
    let sortArray = [];
    if (opts.order) {
      sortArray.push(opts.sortf);
      sortArray.push(opts.order);
    } else {
      sortArray.push(opts.sortf);
      sortArray.push(DEFAULT_SORT_ORDER);
    }
    sequel_opts.order = [sortArray];
  }
  return sequel_opts;
}

function setDefaultOptions(opts, sequel_opts) {

  if (opts.hostname == null) {
    opts.hostname = {
      $like: "%"
    };
  } else {
    try {
      opts.hostname = {
        $in: opts.hostname.split(",")
      };
    } catch (e) { }
  }

  if (opts.cid == null) {
    opts.cid = {
      $like: "%"
    };
  }

  if (opts.nid == null) {
    opts.nid = {
      $like: "%"
    };
  } else {
    try {
      opts.nid = {
        $in: opts.nid.split(",")
      };
    } catch (e) { }
  }
  return sequel_opts;
}

function getPaginationOptions(opts, sequel_opts) {

  let pageno = opts.page ? parseInt(opts.page, 10) : 1;
  if (pageno <= 0) {
    pageno = 1;
  }
  sequel_opts.limit = opts.size ? parseInt(opts.size, 10) : 10;
  sequel_opts.offset = (pageno - 1) * sequel_opts.limit;
  return sequel_opts;
}

function getFilteringOptions(opts, sequel_opts) {

  let tokenFilter = [];
  if (opts.token && opts.token.trim().length > 0) {
    opts.token.split(",").forEach((item) => {
      let token = item.trim();
      if (token.length > 0) {
        tokenFilter.push({
          nid: {
            $like: "%" + token + "%"
          }
        });
        tokenFilter.push({
          hostname: {
            $like: "%" + token + "%"
          }
        });
      }
    });
  } else {
    tokenFilter.push({
      nid: {
        $like: "%"
      }
    });
    tokenFilter.push({
      hostname: {
        $like: "%"
      }
    });
  }
  sequel_opts.where = {
    $and: {
      cid: opts.cid,
      nid: opts.nid,
      $or: tokenFilter
    }
  };
  return sequel_opts;
}

function applyFilters(opts, sequel_opts) {
  sequel_opts = getSortOptions(opts, sequel_opts);
  sequel_opts = setDefaultOptions(opts, sequel_opts);
  sequel_opts = getPaginationOptions(opts, sequel_opts);
  sequel_opts = getFilteringOptions(opts, sequel_opts);
  return sequel_opts;
}
