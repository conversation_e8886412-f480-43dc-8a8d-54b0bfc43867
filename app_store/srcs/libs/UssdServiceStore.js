
const KPI = require("app_kpi");
const utility = require("utility");
const KPI_KEY = KPI.KEYS.appstore;

const USSD_SERVICE_LIST_ATTRIBUTES = ["id", "name", "desc", "shortcode", "status", "appId", "options", "createdBy", "modifiedBy"];
const USSD_SERVICE_INFO_ATTRIBUTES = ["id", "name", "desc", "shortcode", "status", "appId", "options", "createdBy", "modifiedBy", "createdAt", "updatedAt"];
const USSD_GATEWAY_LIST_ATTRIBUTES = ["id", "name", "type", "status", "ip", "port", "user", "pass", "enquiry_link_interval", "no_of_channels", "system_type", "interface_type", "version", "buffer_size", "parameters_order", "msg_delimiter", "shortcode_delimiter", "url", "callback_url", "createdBy", "modifiedBy"];
const USSD_GATEWAY_INFO_ATTRIBUTES = ["id", "name", "type", "status", "ip", "port", "user", "pass", "enquiry_link_interval", "no_of_channels", "system_type", "interface_type", "version", "buffer_size", "parameters_order", "msg_delimiter", "shortcode_delimiter", "url", "callback_url", "createdBy", "modifiedBy", "createdAt", "updatedAt"];

module.exports = {
  countUssdServices: async (opts = {}) => {
    let headers = null,
      startTime = new Date().getTime();
    try {
      let sequel_opts = {},
        page = 1,
        size = Number.MAX_SAFE_INTEGER;
      if (opts != null) {
        page = parseInt(opts.page, 10);
        size = parseInt(opts.size, 10);
        sequel_opts = applyShortcodeFilters(opts, sequel_opts);
      }
      const count = await global.leapDB.models.UssdService.count(sequel_opts);
      size = size > count ? count : size;
      const lastPage = Math.ceil(count / size);
      headers = {
        total: count,
        lastPage: lastPage,
        prevPage: page > 1 ? page - 1 : page,
        nextPage: page < lastPage ? page + 1 : lastPage,
        pageSize: size
      };
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "totalShortcodesCount");
    } catch (e) {
      global.logger.error("Exception while retreiving shortcodes headers", e);
      global.raiseAlert(e);
    }
    return headers;
  },

  listUssdServices: async (opts = {}) => {

    let ussdServices = null,
      startTime = new Date().getTime();
    try {
      let sequel_opts = {
        attributes: USSD_SERVICE_LIST_ATTRIBUTES
      };
      if (opts != null) {
        sequel_opts = applyShortcodeFilters(opts, sequel_opts);
      }
      // Converting the Sequelize object to Plain JSON Object
      ussdServices = await global.leapDB.models.UssdService.findAll(sequel_opts);
      if (ussdServices != null) {
        ussdServices = JSON.parse(JSON.stringify(ussdServices));
        global.clearAlert();
      }
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listUssdServices");
    } catch (e) {
      global.logger.error("Exception while retreiving UssdServices", e);
      global.raiseAlert(e);
    }
    return ussdServices;
  },

  createUssdService: async (ussdServiceInfo) => {
    let res;
    let startTime = new Date().getTime();
    try {
      ussdServiceInfo.createdAt = global.getTZFormatDateTime(ussdServiceInfo.createdAt);
      ussdServiceInfo.updatedAt = global.getTZFormatDateTime(ussdServiceInfo.updatedAt);
      ussdServiceInfo.options = await utility.deflateJSON(ussdServiceInfo.options, global.jsonsupport);
      res = await global.leapDB.models.UssdService.create(ussdServiceInfo);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "createUssdService");
    } catch (e) {
      global.raiseAlert(e);
      throw e;
    }
    return res;
  },

  findUssdService: async (ussdService) => {
    let startTime = new Date().getTime(),
      ussdServiceInfo = null;
    try {
      let filter = {
        attributes: USSD_SERVICE_INFO_ATTRIBUTES
      };
      filter = Object.assign(filter, {
        where: {
          $or: [{
            id: ussdService
          }, {
            name: ussdService
          }, {
            shortcode: ussdService
          }]
        }
      });
      ussdServiceInfo = await global.leapDB.models.UssdService.findOne(filter);
      if (ussdServiceInfo != null) {
        ussdServiceInfo = JSON.parse(JSON.stringify(ussdServiceInfo));
        ussdServiceInfo.createdAt = global.getTZFormatDateTime(ussdServiceInfo.createdAt);
        ussdServiceInfo.updatedAt = global.getTZFormatDateTime(ussdServiceInfo.updatedAt);
        ussdServiceInfo.options = await utility.unzipJSON(ussdServiceInfo.options, global.jsonsupport);
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "findUssdService");
    } catch (e) {
      global.logger.error("Failed to Retrieve UssdServiceInfo", e);
      global.raiseAlert(e);
    }
    return ussdServiceInfo;
  },

  updateUssdService: async (ussdServiceInfo) => {
    let startTime = new Date().getTime(),
      result;
    try {
      ussdServiceInfo.updatedAt = global.getTZFormatDateTime(ussdServiceInfo.updatedAt);
      ussdServiceInfo.options = await utility.deflateJSON(ussdServiceInfo.options, global.jsonsupport);
      result = await global.leapDB.models.UssdService.update(ussdServiceInfo, {
        where: {
          shortcode: ussdServiceInfo.shortcode
        }
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "updateUssdService");
    } catch (e) {
      global.logger.error("Exception while updating UssdService", e);
      global.raiseAlert(e);
      throw e;
    }
    return result;
  },

  deleteUssdService: async (shortcode, forceDelete = true) => {
    let startTime = new Date().getTime(),
      result = null;
    try {
      let filter = {
        force: forceDelete,
        where: {
          shortcode: shortcode
        }
      };

      result = await global.leapDB.models.UssdService.destroy(filter);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "deleteUssdService");
    } catch (e) {
      global.logger.error("Failed to delete UssdService", e);
      global.raiseAlert(e);
    }
    return (result != null && result > 0);
  },

  createBulkUssdServices: async (bulk) => {
    let res, startTime = new Date().getTime();
    try {
      res = await global.leapDB.models.UssdService.bulkCreate(bulk);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "createBulkUssdServices");
    } catch (e) {
      global.logger.error("Failed to createBulkUssdServices", e);
      global.raiseAlert(e);
    }
    return res;
  },

  countUssdGWAccounts: async (opts = {}) => {
    let headers = null,
      startTime = new Date().getTime();
    try {
      let sequel_opts = {},
        page = 1,
        size = Number.MAX_SAFE_INTEGER;
      if (opts != null) {
        page = parseInt(opts.page, 10);
        size = parseInt(opts.size, 10);
        sequel_opts = applyGatewayFilters(opts, sequel_opts);
      }
      const count = await global.leapDB.models.UssdGatewayInfo.count(sequel_opts);
      size = size > count ? count : size;
      const lastPage = Math.ceil(count / size);
      headers = {
        total: count,
        lastPage: lastPage,
        prevPage: page > 1 ? page - 1 : page,
        nextPage: page < lastPage ? page + 1 : lastPage,
        pageSize: size
      };
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "totalGatewaysCount");
    } catch (e) {
      global.logger.error("Exception while retreiving Gateway headers", e);
      global.raiseAlert(e);
    }
    return headers;
  },

  listUssdGatewayConfigs: async (opts = {}) => {

    let ussdGatewayConfigs = null,
      startTime = new Date().getTime();
    try {

      let sequel_opts = {
        attributes: USSD_GATEWAY_LIST_ATTRIBUTES
      };
      if (opts != null) {
        sequel_opts = applyGatewayFilters(opts, sequel_opts);
      }
      // Converting the Sequelize object to Plain JSON Object
      ussdGatewayConfigs = await global.leapDB.models.UssdGatewayInfo.findAll(sequel_opts);
      if (ussdGatewayConfigs != null) {
        ussdGatewayConfigs = JSON.parse(JSON.stringify(ussdGatewayConfigs));
        global.clearAlert();
      }
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listUssdGatewayConfigs");
    } catch (e) {
      global.logger.error("Exception while retreiving listUssdGatewayConfigs", e);
      global.raiseAlert(e);
    }
    return ussdGatewayConfigs;
  },

  createUssdGatewayConfig: async (gwConfiguration) => {
    let res;
    let startTime = new Date().getTime();
    try {
      gwConfiguration.createdAt = global.getTZFormatDateTime(gwConfiguration.createdAt);
      gwConfiguration.updatedAt = global.getTZFormatDateTime(gwConfiguration.updatedAt);
      res = await global.leapDB.models.UssdGatewayInfo.create(gwConfiguration);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "createUssdGatewayConfig");
    } catch (e) {
      global.raiseAlert(e);
      throw e;
    }
    return res;
  },

  findUssdGatewayConfig: async (idOrName) => {
    let startTime = new Date().getTime(),
      gwConfigInfo = null;
    try {
      let filter = {
        attributes: USSD_GATEWAY_INFO_ATTRIBUTES
      };
      filter = Object.assign(filter, {
        where: {
          $or: [{
            id: idOrName
          }, {
            name: idOrName
          }]
        }
      });

      gwConfigInfo = await global.leapDB.models.UssdGatewayInfo.findOne(filter);
      if (gwConfigInfo != null) {
        gwConfigInfo = JSON.parse(JSON.stringify(gwConfigInfo));
        gwConfigInfo.createdAt = global.getTZFormatDateTime(gwConfigInfo.createdAt);
        gwConfigInfo.updatedAt = global.getTZFormatDateTime(gwConfigInfo.updatedAt);
        if (typeof gwConfigInfo.options == "string") {
          gwConfigInfo.options = JSON.parse(gwConfigInfo.options);
        }
      }

      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "findUssdGatewayConfig");
    } catch (e) {
      global.logger.error("Failed to Retrieve UssdGatewayConfig", e);
      global.raiseAlert(e);
    }
    return gwConfigInfo;
  },

  updateUssdGatewayConfig: async (gwConfiguration) => {
    let startTime = new Date().getTime(),
      result;
    try {
      gwConfiguration.updatedAt = global.getTZFormatDateTime(gwConfiguration.updatedAt);
      result = await global.leapDB.models.UssdGatewayInfo.update(gwConfiguration, {
        where: {
          id: gwConfiguration.id
        }
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "updateUssdGatewayConfig");
    } catch (e) {
      global.logger.error("Exception while updating UssdGatewayConfig", e);
      global.raiseAlert(e);
    }
    return result;
  },

  deleteUssdGatewayConfig: async (idOrName, forceDelete = true) => {
    let startTime = new Date().getTime(),
      result = null;
    try {
      let filter = {
        force: forceDelete,
        where: {
          $or: [{
            id: idOrName
          }, {
            name: idOrName
          }]
        }
      };

      result = await global.leapDB.models.UssdGatewayInfo.destroy(filter);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "deleteUssdGatewayConfig");
    } catch (e) {
      global.logger.error("Failed to delete UssdGatewayConfig", e);
      global.raiseAlert(e);
    }
    return (result != null && result > 0);
  },

  createBulkUssdGatewayConfigs: async (bulk) => {
    let res, startTime = new Date().getTime();
    try {
      res = await global.leapDB.models.UssdGatewayInfo.bulkCreate(bulk);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "createBulkUssdGatewayConfigs");
    } catch (e) {
      global.logger.error("Failed to createBulkUssdGatewayConfigs", e);
      global.raiseAlert(e);
    }
    return res;
  },

  retrieve_params: async (appId) => {
    try {
      let res = await global.leapDB.models.AppsMaster.findOne({
        attributes: ["appData"],
        where: { id: appId }
      });
      return res.appData.modules[res.appData.startId].settings;
    }
    catch (e) {
      global.logger.error("Failed to retreive parameters", e);
    }
    return null;
  }

};

/**
 *  A helper function to extract sorting options
 *  from request object and construct an Object
 *  that could be used in sequelize query expression.
 *  @param {Object} req
 *  @param {JSONObject} opts
 **/
function getSortOptions(opts, sequel_opts) {
  if (opts.sortf) {
    let sortArray = [];
    if (opts.order) {
      sortArray.push(opts.sortf);
      sortArray.push(opts.order);
    } else {
      sortArray.push(opts.sortf);
    }
    sequel_opts.order = [sortArray];
  }
  return sequel_opts;
}

function getPaginationOptions(opts, sequel_opts) {

  let pageno = opts.page ? parseInt(opts.page, 10) : 1;
  if (pageno <= 0) {
    pageno = 1;
  }
  sequel_opts.limit = opts.size ? parseInt(opts.size, 10) : 10;
  sequel_opts.offset = (pageno - 1) * sequel_opts.limit;
  return sequel_opts;
}

function setDefaultOptions(opts, sequel_opts) {

  if (opts.status == null) {
    opts.status = {
      $like: "%"
    };
  } else {
    try {
      opts.status = {
        $in: opts.status.split(",")
      };
    } catch (e) { }
  }
  return sequel_opts;
}

function getShortCodeFilteringOptions(opts, sequel_opts) {

  let tokenFilter = [];
  if (opts.token && opts.token.trim().length > 0) {
    opts.token.split(",").forEach((item) => {
      let token = item.trim();
      if (token.length > 0) {
        tokenFilter.push({
          id: {
            $like: "%" + token + "%"
          }
        });
        tokenFilter.push({
          name: {
            $like: "%" + token + "%"
          }
        });
        tokenFilter.push({
          shortcode: {
            $like: "%" + token + "%"
          }
        });
      }
    });
  } else {
    tokenFilter.push({
      id: {
        $like: "%"
      }
    });
    tokenFilter.push({
      name: {
        $like: "%"
      }
    });
    tokenFilter.push({
      shortcode: {
        $like: "%"
      }
    });
  }
  sequel_opts.where = {
    $and: {
      status: opts.status,
      $or: tokenFilter
    }
  };
  return sequel_opts;
}

function getGatewayFilteringOptions(opts, sequel_opts) {

  let tokenFilter = [];
  if (opts.token && opts.token.trim().length > 0) {
    opts.token.split(",").forEach((item) => {
      let token = item.trim();
      if (token.length > 0) {
        tokenFilter.push({
          id: {
            $like: "%" + token + "%"
          }
        });
        tokenFilter.push({
          name: {
            $like: "%" + token + "%"
          }
        });
      }
    });
  } else {
    tokenFilter.push({
      id: {
        $like: "%"
      }
    });
    tokenFilter.push({
      name: {
        $like: "%"
      }
    });
  }
  sequel_opts.where = {
    $and: {
      status: opts.status,
      $or: tokenFilter
    }
  };
  return sequel_opts;
}

function applyShortcodeFilters(opts, sequel_opts) {
  sequel_opts = getSortOptions(opts, sequel_opts);
  sequel_opts = setDefaultOptions(opts, sequel_opts);
  sequel_opts = getPaginationOptions(opts, sequel_opts);
  sequel_opts = getShortCodeFilteringOptions(opts, sequel_opts);
  return sequel_opts;
}

function applyGatewayFilters(opts, sequel_opts) {
  sequel_opts = getSortOptions(opts, sequel_opts);
  sequel_opts = setDefaultOptions(opts, sequel_opts);
  sequel_opts = getPaginationOptions(opts, sequel_opts);
  sequel_opts = getGatewayFilteringOptions(opts, sequel_opts);
  return sequel_opts;
}