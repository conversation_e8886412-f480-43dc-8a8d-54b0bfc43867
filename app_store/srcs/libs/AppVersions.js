const KPI = require("app_kpi");
const utility = require("utility");

const DEFAULT_SORT_ORDER = "asc";

const KPI_KEY = KPI.KEYS.appstore;

const APP_VERSIONS_LIST_ATTRIBUTES = ["appid", "version", "comment", "owner", "createdBy", "createdAt", "type", "version_name"];
const APP_VERSION_ATTRIBUTES = ["appid", "version", "comment", "owner", "createdBy", "createdAt", "updatedAt", "appData", "type", "version_name"];

module.exports = {

  listAppVersions: async (appId) => {

    let appVersions = null,
      startTime = new Date().getTime();
    try {
      let filter = {
        attributes: APP_VERSIONS_LIST_ATTRIBUTES
      };
      filter = Object.assign(filter, {
        where: {
          $or: [{
            appid: appId
          }]
        }
      });
      // Converting the Sequelize object to Plain JSON Object
      appVersions = await global.leapDB.models.AppsSnapshots.findAll(filter);
      if (appVersions != null) {
        appVersions = JSON.parse(JSON.stringify(appVersions));
        global.clearAlert();
      }
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listAppVersions");
    } catch (e) {
      global.logger.error("Exception while retreiving app versions", e);
      global.raiseAlert(e);
    }
    return appVersions;
  },

  createAppSnapshot: async (appInfo) => {
    let res;
    let startTime = new Date().getTime();
    try {
      if (appInfo.appData != null) {
        appInfo.appData = await utility.deflateJSON(appInfo.appData, global.jsonsupport);
      }
      if (appInfo.historyObject != null) {
        appInfo.historyObject = await utility.deflateJSON(appInfo.historyObject, global.jsonsupport);
      }
      appInfo.createdAt = global.getTZFormatDateTime(appInfo.createdAt);
      appInfo.updatedAt = appInfo.createdAt;
      res = await global.leapDB.models.AppsSnapshots.create(appInfo);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "createAppSnapshot");
    } catch (e) {
      global.logger.error("Exception while creating app snapshot", e);
      global.raiseAlert(e);
    }
    return res;
  },

  updateAppVersion: async (appInfo) => {
    let startTime = new Date().getTime(),
      result;
    try {

      if (appInfo.appData != null) {
        appInfo.appData = await utility.deflateJSON(appInfo.appData, global.jsonsupport);
      }
      if (appInfo.historyObject != null) {
        appInfo.historyObject = await utility.deflateJSON(appInfo.historyObject, global.jsonsupport);
      }
      result = await global.leapDB.models.AppsSnapshots.update(appInfo, {
        where: {
          $and: {
            appid: appInfo.appid,
            version: appInfo.version
          }
        }
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "updateAppVersion");
    } catch (e) {
      global.logger.error("Exception while updating App version", e);
      global.raiseAlert(e);
    }
    return result;
  },

  deleteAppVersion: async (appid, forceDelete = true) => {
    let startTime = new Date().getTime(),
      result = null;
    try {
      let filter = {
        force: forceDelete,
        where: {
          $or: [{
            appid: appid
          }, {
            version: appid
          }]
        }
      };

      result = await global.leapDB.models.AppsSnapshots.destroy(filter);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "deleteAppVersion");
    } catch (e) {
      global.logger.error("Failed to delete App version", e);
      global.raiseAlert(e);
    }
    return (result != null && result > 0);
  },

  findAppVersion: async (appid, version) => {
    let startTime = new Date().getTime(),
      appVersionInfo = null;
    try {
      let filter = {
        attributes: APP_VERSION_ATTRIBUTES
      };
      filter = Object.assign(filter, {
        where: {
          $and: [{
            appid: appid
          }, {
            version: version
          }]
        }
      });

      appVersionInfo = await global.leapDB.models.AppsSnapshots.findOne(filter);
      if (appVersionInfo != null) {
        appVersionInfo = JSON.parse(JSON.stringify(appVersionInfo));
        appVersionInfo.appData = await utility.unzipJSON(appVersionInfo.appData, global.jsonsupport);
        appVersionInfo.historyObject = await utility.unzipJSON(appVersionInfo.historyObject, global.jsonsupport);
        appVersionInfo.createdAt = global.getTZFormatDateTime(appVersionInfo.createdAt);
        appVersionInfo.updatedAt = global.getTZFormatDateTime(appVersionInfo.updatedAt);
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "findAppVersion");
    } catch (e) {
      global.logger.error("Failed to Retrieve App Version details: ", appid, e);
      global.raiseAlert(e);
    }
    return appVersionInfo;
  },

  findOrCreateAppLock: async (appInfo) => {
    let res;
    let startTime = new Date().getTime();
    try {
      res = await global.leapDB.models.AppsLock.findOrCreate({
        where: {
          appId: appInfo.appId
        },
        defaults: appInfo
      });
      res = JSON.parse(JSON.stringify(res));
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "findOrCreateAppLock");
    } catch (e) {
      global.logger.error("Exception while findOrCreateAppLock", e);
      global.raiseAlert(e);
    }
    return res;
  },

  findAppLock: async (appId) => {
    let res;
    let startTime = new Date().getTime();
    try {
      res = await global.leapDB.models.AppsLock.findByPk(appId);
      res = JSON.parse(JSON.stringify(res));
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "findAppLock");
    } catch (e) {
      global.logger.error("Exception while findAppLock", e);
      global.raiseAlert(e);
    }
    return res;
  },

  unLockApp: async (appInfo) => {
    let res;
    let startTime = new Date().getTime();
    try {
      res = await global.leapDB.models.AppsLock.destroy({
        force: true,
        where: {
          appId: appInfo.appId,
          owner: appInfo.owner
        }
      });
      res = JSON.parse(JSON.stringify(res));
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "unLockApp");
    } catch (e) {
      global.logger.error("Exception while unLockApp", e);
      global.raiseAlert(e);
    }
    return res;
  },

  listAppLocks: async () => {
    let res;
    let startTime = new Date().getTime();
    try {
      res = await global.leapDB.models.AppsLock.findAll({
        where: {
          expiryTime: {
            $lt: Date.now()
          }
        }
      });
      res = JSON.parse(JSON.stringify(res));
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listAppLocks");
    } catch (e) {
      global.logger.error("Exception while listAppLocks", e);
      global.raiseAlert(e);
    }
    return res;
  },
  listAppLocksForUserDeletion: async (ownerId) => {
    let res;
    let startTime = new Date().getTime();
    try {
      res = await global.leapDB.models.AppsLock.findAll({
        where: {
          owner: ownerId
        }
      });
      res = JSON.parse(JSON.stringify(res));
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listAppLocksForUserDeletion");
    } catch (e) {
      global.logger.error("Exception while listAppLocksForUserDeletion", e);
      global.raiseAlert(e);
    }
    return res;
  }

};

function getSortOptions(opts, sequel_opts) {
  if (opts.sortf) {
    let sortArray = [];
    if (opts.order) {
      sortArray.push(opts.sortf);
      sortArray.push(opts.order);
    } else {
      sortArray.push(opts.sortf);
      sortArray.push(DEFAULT_SORT_ORDER);
    }
    sequel_opts.order = [sortArray];
  }
  return sequel_opts;
}

function setDefaultOptions(opts, sequel_opts) {

  if (opts.owner == null) {
    opts.owner = {
      $like: "%"
    };
  }
  return sequel_opts;
}

function getPaginationOptions(opts, sequel_opts) {

  let pageno = opts.page ? parseInt(opts.page, 10) : 1;
  if (pageno <= 0) {
    pageno = 1;
  }
  sequel_opts.limit = opts.size ? parseInt(opts.size, 10) : 10;
  sequel_opts.offset = (pageno - 1) * sequel_opts.limit;
  return sequel_opts;
}

function getFilteringOptions(opts, sequel_opts) {

  let tokenFilter = [];
  if (opts.token && opts.token.trim().length > 0) {
    opts.token.split(",").forEach((item) => {
      let token = item.trim();
      if (token.length > 0) {
        tokenFilter.push({
          appid: {
            $like: "%" + token + "%"
          }
        });
        tokenFilter.push({
          version: {
            $like: "%" + token + "%"
          }
        });
      }
    });
  } else {
    tokenFilter.push({
      appid: {
        $like: "%"
      }
    });
    tokenFilter.push({
      version: {
        $like: "%"
      }
    });
  }
  sequel_opts.where = {
    $and: {
      createdBy: opts.user,
      owner: opts.owner,
      $or: tokenFilter
    }
  };
  return sequel_opts;
}

function getDateRangeOptions(opts, sequel_opts) {
  if (opts.startTime && opts.endTime) {
    let startTime = new Date(Number(opts.startTime));
    let endTime = new Date(Number(opts.endTime));
    sequel_opts.where = Object.assign(sequel_opts.where.$and, {
      createdAt: {
        $between: [startTime, endTime]
      }
    });
  }
  if (opts.startTime && opts.endTime) {
    let startTime = new Date(Number(opts.startTime));
    let endTime = new Date(Number(opts.startTime));
    sequel_opts.where = Object.assign(sequel_opts.where.$and, {
      updatedAt: {
        $between: [startTime, endTime]
      }
    });
  }
  return sequel_opts;
}

function applyFilters(opts, sequel_opts) {
  sequel_opts = getSortOptions(opts, sequel_opts);
  sequel_opts = setDefaultOptions(opts, sequel_opts);
  sequel_opts = getPaginationOptions(opts, sequel_opts);
  sequel_opts = getFilteringOptions(opts, sequel_opts);
  sequel_opts = getDateRangeOptions(opts, sequel_opts);
  return sequel_opts;
}
