const KPI = require("app_kpi");
const KPI_KEY = KPI.KEYS.appstore;

module.exports = {
  assignApp: async (assignInfo) => {
    let startTime = new Date().getTime(),
      result;
    try {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("ID:" + assignInfo.id + ", Updating app owner to: " + assignInfo.assignTo);
      }
      let app = {
        modifiedBy: assignInfo.modifiedBy,
        updatedAt: global.getTZFormatDateTime(assignInfo.updatedAt)
      };
      if (assignInfo.assignTo != null) {
        app.owner = assignInfo.assignTo;
      }
      result = await global.leapDB.models.AppsMaster.update(app, {
        where: {
          id: assignInfo.id
        }
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "assignApp");
    } catch (e) {
      global.logger.error("Failed to Assign App", e);
      global.raiseAlert(e);
    }
    return result;
  },

  /*
   * Change the ownership of the APPs created
   * from sourceUserId to targetUserId
   */
  changeAppsOwner: async (sourceUserId, targetUserId) => {
    let startTime = new Date().getTime();
    try {

      /*
       * Find the entries corresponding to the sourceUserId in the apps_master table
       */
      let search_options = {
        where: {
          $or: [{
            createdBy: sourceUserId
          }, {
            modifiedBy: sourceUserId
          }]
        }
      };

      const rows = await global.leapDB.models.AppsMaster.findAll(search_options);

      /*
       * Create record for the app ownership change in App_history table
       */
      rows.forEach(
        (row) => {
          let invoker_user = {
            userID: sourceUserId
          };
          let insert_data = {
            appId: row.id,
            desc: row.desc,
            event: 0,
            remarks: invoker_user,
            performer: targetUserId
          };
          global.leapDB.models.AppsHistory.create(insert_data);
        });

      /*
       * Update the ownership of the app in App_master table to the invoker's id
       */
      let update_options = {
        where: {
          $or: [{
            createdBy: sourceUserId
          }, {
            modifiedBy: sourceUserId
          }]
        }
      };

      let data_to_update = {
        createdBy: targetUserId,
        modifiedBy: targetUserId
      };

      const rowCount = await global.leapDB.models.AppsMaster.update(data_to_update, update_options);
      if (global.logger.isTraceEnabled()) {
        if (rowCount > 0) {
          global.logger.trace("App_master updated ", rowCount, " rows successfully");
        } else {
          global.logger.trace("App_master didn't update since row wasn't found");
        }
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "changeAppsOwner");
    } catch (e) {
      global.logger.error("Exception in trying to update app_master table ", e);
      global.raiseAlert(e);
    }

  },

  routeApp: async (appId, targetStatus, scheduleTime, appHistoryData, eventsListData) => {
    let startTime = new Date().getTime();

    if (global.logger.isTraceEnabled()) {
      global.logger.trace("ID:" + appId + ", Updating app status: " + targetStatus);
    }
    let updateQuery = 'update apps_history set updatedAt=NOW() where id = (select a.id as id from(select id, createdAt from apps_history) as a where a.createdAt = (select MAX(b.createdAt) as max from(select appId, createdAt from apps_history) as b where b.appId ="' + appId + '"))';
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("App History update query IS:" + updateQuery);
    }
    await global.leapDB.query(updateQuery).spread((results, metadata) => {
      if (global.logger.isTraceEnabled()) {
        global.logger.trace("App History update query result: %s and metadat: %s:",
          JSON.stringify(results), JSON.stringify(metadata));
      }
    });
    return global.leapDB.transaction(function (t) {
      return global.leapDB.models.AppsMaster.update({
        status: targetStatus,
        scheduleTime: scheduleTime
      }, {
        transaction: t,
        where: {
          id: appId
        }
      }).then(function () {
        return global.leapDB.models.AppsHistory.create(
          appHistoryData, {
          transaction: t
        }).then(function () {
          return global.leapDB.models.AppsEvent.bulkCreate(eventsListData, {
            transaction: t
          });
        });
      });
    }).then(function (result) {
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "routeApp");
      return;
    }).catch(function (e) {
      global.logger.error("Failed to route the App", e);
      global.raiseAlert(e);
      throw new Error("Exception occured while making transaction:", e);
    });
  },

  appAgingDetails: async () => {
    let startTime = new Date().getTime();
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("App Aging details is Executing...");
    }
    try {
      let appAgingHistory = await global.leapDB.models.AppsHistory.findAll({
        attributes: ['appId', 'appName',
          'status',
          'createdAt', [global.leapDB.fn('datediff', global.leapDB.col('updatedAt'), global.leapDB.col('createdAt')), 'day']
        ]
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "EventsPerApp");
      return appAgingHistory;
    } catch (e) {
      global.logger.error("Failed to get eventsCountPerApp", e);
      global.raiseAlert(e);
      throw new Error("Exception occured while getting events per App", e);
    }
  }
};
