const KPI = require("app_kpi");
const KPI_KEY = KPI.KEYS.appstore;
const DOWNLOADS_LIST_ATTRIBUTES = ["id", "name", "fileName", "status", "fileType", "filepath", "fileBytes", "aid", "startDate", "endDate", "createdBy", "modifiedBy", "createdAt", "updatedAt"];

module.exports = {

  /**
   * @api findDownload(downloadId), MyDownloadInfo: Get My Download Information
   * @param {String} downloadId
   */
  findDownload: async (downloadId) => {
    let startTime = new Date().getTime(),
      downloadInfo = null;
    try {
      downloadInfo = await global.leapDB.models.MyDownloads.findByPk(downloadId);
      if (downloadInfo != null) {
        downloadInfo = JSON.parse(JSON.stringify(downloadInfo));
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "findDownload");
    } catch (e) {
      global.logger.error("Failed to Retrieve Download Info", e);
      global.raiseAlert(e);
    }
    return downloadInfo;
  },


  listDownload: async () => {

    let downloads = null,
      startTime = new Date().getTime();
    try {
      let sequel_opts = {
        attributes: DOWNLOADS_LIST_ATTRIBUTES
      };
      // Converting the Sequelize object to Plain JSON Object
      downloads = await global.leapDB.models.MyDownloads.findAll(sequel_opts);
      if (downloads != null) {
        downloads = JSON.parse(JSON.stringify(downloads));
        global.clearAlert();
      }
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "listDownload");
    } catch (e) {
      global.logger.error("Exception while retreiving downloads", e);
      global.raiseAlert(e);
    }
    return downloads;
  },

  checkBeforeAddDownload: async (aid, name, startDate, endDate) => {

    let startTime = new Date().getTime(), checkInfo = "No_Entry";
    let conditionCheck = {};
    if (name == "app") {
      conditionCheck = {
        where: {
          aid: aid,
          name: name
        }
      };
    } else {
      conditionCheck = {
        where: {
          startDate: startDate,
          endDate: endDate,
          name: name
        }
      };
    }
    try {
      let Rows = await global.leapDB.models.MyDownloads.findAndCountAll();
      if (Rows.count > 0) {
        checkInfo = await global.leapDB.models.MyDownloads.findAll(conditionCheck);
        if (checkInfo != null && checkInfo.length > 0) {
          global.clearAlert();
          KPI.emit(KPI_KEY, startTime, new Date().getTime(), "checkAppIdBeforeAddDownload");
          return "Duplicate";
        } else {
          return "No_Entry";
        }
      } else {
        return "No_Entry";
      }
    } catch (e) {
      global.logger.error(e);
      return "No_Entry";
    }
  },

  addDownload: (tnxId, name, status, fileName, fileType, filePath, fileBytes, aid, startDate, endDate, createBy, modifiedBy) => {
    let startTime = new Date().getTime();
    try {
      global.leapDB.models.MyDownloads.create({
        id: tnxId,
        name: name,
        status: status,
        fileName: fileName,
        fileType: fileType,
        filepath: filePath,
        fileBytes: fileBytes,
        aid: aid,
        startDate: startDate,
        endDate: endDate,
        createBy: createBy,
        modifiedBy: modifiedBy
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "addDownload");
    } catch (e) {
      global.logger.error("Not Able to add into the DataBase", e);
      return "Not_Registered";
    }
    return "registered";
  },

  updateDownloadStatus: async (tnxId, status = 2) => {
    let startTime = new Date().getTime();
    try {
      let search_options = {
        where: {
          id: tnxId
        }
      };
      let data_to_update = {
        status: status
      };
      global.logger.trace("UpdateDownloadStatus for TXNID:", tnxId, search_options, data_to_update);
      const rowCount = await global.leapDB.models.MyDownloads.update(data_to_update, search_options);
      if (global.logger.isTraceEnabled()) {
        if (rowCount > 0) {
          global.logger.trace("My_Downloads updated ", rowCount, " rows successfully");
        } else {
          global.logger.trace("My_Downloads didn't update since row wasn't found");
        }
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "updateDownloadStatus");
    } catch (e) {
      global.logger.error("Exception in trying to update my_downloads table ", e);
      global.raiseAlert(e);
    }
  }
};
