const KPI = require("app_kpi");
const KPI_KEY = KPI.KEYS.appstore;

const APP_EVENT_LIST_ATTRIBUTES = ["id", "appId", "appName", "action", "comment", "is_read", "performer", "createdAt", "updatedAt"];

module.exports = {
  getCountOfUnReadEvents: async (userId) => {
    let startTime = new Date().getTime();
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Getting un-read message count for user:" + userId);
    }
    try {
      let count = await global.leapDB.models.AppsEvent.findAndCountAll({
        where: {
          userId: userId,
          is_read: 0
        }
      }).then(result => {
        return result.count;
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getUnReadEventsCount");
      return count;
    } catch (e) {
      global.logger.error("Failed to get CountOfUnReadEvents", e);
      global.raiseAlert(e);
      throw new Error("Exception occured while get un read message count", e);
    }
  },

  getNotificationsCount: async (opts) => {
    let headers = null,
      startTime = new Date().getTime();
    try {
      let sequel_opts = {},
        page = parseInt(opts.page, 10),
        size = parseInt(opts.size, 10);
      if (opts != null) {
        sequel_opts = applyFiltersEvents(opts, sequel_opts);
      }
      const totalEvents = await global.leapDB.models.AppsEvent.count(sequel_opts);
      size = size > totalEvents ? totalEvents : size;
      const lastPage = Math.ceil(totalEvents / size);
      headers = {
        totalEvents: totalEvents,
        lastPage: lastPage,
        prevPage: page > 1 ? page - 1 : page,
        nextPage: page < lastPage ? page + 1 : lastPage,
        pageSize: size
      };
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getNotificationsCout");
    } catch (e) {
      global.logger.error("Failed to getNotificationsCount", e);
      global.raiseAlert(e);
    }
    return headers;
  },

  listEvents: async (opts) => {
    let eventsList = null,
      startTime = new Date().getTime();

    try {
      let sequel_opts = {
        attributes: APP_EVENT_LIST_ATTRIBUTES
      };

      if (opts != null) {
        sequel_opts = applyFiltersEvents(opts, sequel_opts);
      }
      eventsList = await global.leapDB.models.AppsEvent.findAll(sequel_opts);
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "ListEvents");
    } catch (e) {
      global.logger.error("Failed to list notifications", e);
      throw new Error("Exception occured while listing events", e);
    }
    return eventsList;
  },

  updateEvents: async (events, userID) => {
    let startTime = new Date().getTime(),
      flag = false;
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("ID:" + events + ", Updating event status for user:" + userID);
    }
    try {
      let status = await global.leapDB.models.AppsEvent.update({
        is_read: 1
      }, {
        where: {
          userId: userID,
          id: events
        }
      });
      if (status[0] > 0) flag = true;
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "updateEventStatus");
      return flag;
    } catch (e) {
      global.logger.error("Failed to update notification status", e);
      global.raiseAlert(e);
      throw new Error("Exception occured while updating the events status", e);
    }
  },

  deleteEvents: async (eventId, userID) => {
    let startTime = new Date().getTime();
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("ID:" + eventId + ", Delete events for user:" + userID);
    }
    try {
      let result = await global.leapDB.models.AppsEvent.destroy({
        where: {
          userId: userID,
          id: eventId
        }
      });

      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "DeleteEvents");
      global.logger.trace("Delete events Result:" + result);
      return (result != null && result > 0);
    } catch (e) {
      global.logger.error("Failed to list notifications", e);
      global.raiseAlert(e);
      throw new Error("Exception occured while get un read message count", e);
    }
  },

  eventsHistory: async (appId) => {
    let startTime = new Date().getTime();
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Events history for app:" + appId);
    }
    try {
      let eventsHistory = await global.leapDB.models.AppsHistory.findAll({
        where: {
          appId: appId
        },
        order: [["id", "desc"]]
      });
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "EventsHistory");
      return JSON.parse(JSON.stringify(eventsHistory));
    } catch (e) {
      global.logger.error("Failed to get eventsHistory", e);
      global.raiseAlert(e);
      throw new Error("Exception occured while listing events", e);
    }
  },

  eventsCountPerApp: async () => {
    let startTime = new Date().getTime();
    if (global.logger.isTraceEnabled()) {
      global.logger.trace("Events Count per app is Executing...");
    }
    try {
      let eventsHistory = await global.leapDB.models.AppsHistory.findAll({
        attributes: ['appId', [global.leapDB.fn('count', global.leapDB.col('appId')), 'count']],
        group: ['appId']
      });
      if (eventsHistory != null) {
        eventsHistory = JSON.parse(JSON.stringify(eventsHistory));
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "EventsPerApp");
      return eventsHistory;
    } catch (e) {
      global.logger.error("Failed to get eventsCountPerApp", e);
      global.raiseAlert(e);
      throw new Error("Exception occured while getting events per App", e);
    }
  }
};

/**
 *  A helper function to extract sorting options
 *  from request object and construct an Object
 *  that could be used in sequelize query expression.
 *  @param {Object} req
 *  @param {JSONObject} opts
 **/
function getSortOptions(opts, sequel_opts) {
  if (opts.sortf) {
    let sortArray = [];
    if (opts.order) {
      sortArray.push(opts.sortf);
      sortArray.push(opts.order);
    } else {
      sortArray.push(opts.sortf);
    }
    sequel_opts.order = [sortArray];
  }
  return sequel_opts;
}

function getPaginationOptions(opts, sequel_opts) {

  let pageno = opts.page ? parseInt(opts.page, 10) : 1;
  if (pageno <= 0) {
    pageno = 1;
  }
  sequel_opts.limit = opts.size ? parseInt(opts.size, 10) : 10;
  sequel_opts.offset = (pageno - 1) * sequel_opts.limit;
  return sequel_opts;
}

function applyFiltersEvents(opts, sequel_opts) {
  sequel_opts = getSortOptions(opts, sequel_opts);
  sequel_opts = getPaginationOptions(opts, sequel_opts);
  sequel_opts.where = {
    userId: opts.user
  };
  return sequel_opts;
}
