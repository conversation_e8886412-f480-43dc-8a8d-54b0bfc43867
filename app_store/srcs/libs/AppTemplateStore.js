const KPI = require("app_kpi");
const KPI_KEY = KPI.KEYS.appstore;
const utility = require("utility");

const APP_TEMPLATES_LIST_ATTRIBUTES = ["id", "name", "desc", "svg", "type"];
const APP_TEMPLATES_TYPE_LIST = ["type", "image", "description"];

module.exports = {
  /**
       * @api bulkCreateTemplate(templates) // bulkCreateTemplate: API for creating the bulk app templates in app_store database
       * @apiDescription This api allows the AppDeveloper to create the Applications
       * @param {JSONObject} templates
       * @returns {Boolean} // Return true on success database operation else false.
       */
  bulkCreateTemplate: async (templates) => {
    let res, startTime = new Date().getTime();
    try {
      for (var i = 0; i < templates.length; i++) {
        templates[i].appData = JSON.stringify(templates[i].appData);
      }
      console.log("Templates:", JSON.stringify(templates));
      res = await global.leapDB.models.AppsTemplate.bulkCreate(templates);
      console.log("Response:" + JSON.stringify(res))
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "bulkCreateTemplate");
    } catch (e) {
      console.log("Error:" + e);
      //Ignore errors
      //global.logger.error("Failed to create Templates", e);
      //global.raiseAlert(e);
    }
    return res;
  },

  /**
   * @api findAppTemplate(appId), AppTemplateInfo: Get Application Template Information
   * @param {String} appId
   * @apiDescription The scope of applications retrieved by this api.
   */
  findAppTemplate: async (templateId) => {
    let startTime = new Date().getTime(),
      app = null;
    try {
      app = await global.leapDB.models.AppsTemplate.findByPk(templateId);
      if (app != null) {
        app = JSON.parse(JSON.stringify(app));
      }
      if (app != null && app.appData != null && typeof app.appData == "string") {
        app.appData = JSON.parse(app.appData);
      }
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "findAppTemplate");
    } catch (e) {
      global.logger.error("Failed to Retrieve Template", e);
      global.raiseAlert(e);
    }
    return app;
  },

  getAppTemplates: async () => {
    let data, startTime = new Date().getTime();
    try {
      data = await global.leapDB.models.AppsTemplate.findAll({
        attributes: APP_TEMPLATES_LIST_ATTRIBUTES,
        order: [
          ['name', 'ASC']
        ]
      });
      // Converting the Sequelize object to Plain JSON Object
      data = data && JSON.parse(JSON.stringify(data)) || [];
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getAppTemplates");
    } catch (e) {
      global.logger.error("Failed to retreive Templates", e);
      global.raiseAlert(e);
    }
    return data;
  },

  getAppTemplatesType: async () => {
    let data, startTime = new Date().getTime();
    try {
      data = await global.leapDB.models.Industries.findAll({
        attributes: APP_TEMPLATES_TYPE_LIST
      });
      // Converting the Sequelize object to Plain JSON Object
      data = data && JSON.parse(JSON.stringify(data)) || [];
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getAppTemplatesType");
    } catch (e) {
      global.logger.error("Failed to retreive Templates", e);
      global.raiseAlert(e);
    }
    return data;
  },

  getAppTemplatesByType: async (type) => {
    let data, startTime = new Date().getTime();
    try {
      data = await global.leapDB.models.AppsTemplate.findAll({
        attributes: APP_TEMPLATES_LIST_ATTRIBUTES,
        where: {
          type: type // Add this line to filter by type
        },
        order: [
          ['name', 'ASC']
        ]
      });
      // Converting the Sequelize object to Plain JSON Object
      data = data && JSON.parse(JSON.stringify(data)) || [];
      global.clearAlert();
      KPI.emit(KPI_KEY, startTime, new Date().getTime(), "getAppTemplatesByType");
    } catch (e) {
      global.logger.error("Failed to retreive Templates", e);
      global.raiseAlert(e);
    }
    return data;
  },
};
