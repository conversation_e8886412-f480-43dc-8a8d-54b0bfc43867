module.exports = function (sequelize, DataTypes) {
  const AppsReport = sequelize.define("AppsReport", {
    timestamp: { type: DataTypes.DATE, allowNull: false },
    appId: { type: DataTypes.UUID, allowNull: false },
    appName: { type: DataTypes.STRING, allowNull: false },
    plugin: { type: DataTypes.STRING, allowNull: false },
    moduleId: { type: DataTypes.STRING, allowNull: false },
    errorCode: { type: DataTypes.STRING, allowNull: false },
    classification: { type: DataTypes.STRING, default: "Unknown" },
    requests: { type: DataTypes.INTEGER, allowNull: false }
  }, {
    paranoid: false,
    timestamps: false,
    individualHooks: true,
    tableName: "apps_report"
  });
  return AppsReport;
};
