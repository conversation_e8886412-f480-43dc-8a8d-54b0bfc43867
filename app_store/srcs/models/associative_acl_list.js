module.exports = function (sequelize, DataTypes) {
  let props = {
    aid: { type: DataTypes.UUID },
    menu_id: { type: DataTypes.STRING },
    menu_item_id: { type: DataTypes.STRING },
    list_id: { type: DataTypes.UUID }
  };
  const AssociativeAclList = sequelize.define("associative_acl_list", props, {
    paranoid: false,
    timestamps: false,
    individualHooks: true,
    tableName: "associative_acl_list"
  });
  return AssociativeAclList;
};
