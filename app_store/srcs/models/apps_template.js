module.exports = function (sequelize, DataTypes) {
  let props;
  const jsonsupport = global.jsonsupport || false;
  if (jsonsupport) {
    props = {
      id: { type: DataTypes.UUID, primaryKey: true, defaultValue: DataTypes.UUIDV1 },
      name: { type: DataTypes.STRING(32), allowNull: false, unique: true },
      desc: { type: DataTypes.STRING(128) },
      appData: { type: DataTypes.JSON, defaultValue: {} },
      type: { type: DataTypes.STRING(32) },
      image: { type: DataTypes.STRING(32) }
    };
  } else {
    props = {
      id: { type: DataTypes.UUID, primaryKey: true, defaultValue: DataTypes.UUIDV1 },
      name: { type: DataTypes.STRING(32), allowNull: false, unique: true },
      desc: { type: DataTypes.STRING(128) },
      appData: { type: DataTypes.TEXT("long") },
      type: { type: DataTypes.STRING(32) },
      image: { type: DataTypes.STRING(32) }
    };
  }
  return sequelize.define("AppsTemplate", props, {
    paranoid: true,
    timestamps: false,
    tableName: "apps_template"
  });
};
