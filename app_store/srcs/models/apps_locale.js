module.exports = function (sequelize, DataTypes) {
  let props;
  const jsonsupport = global.jsonsupport || false;
  if (jsonsupport) {
    props = {
      appId: { type: DataTypes.UUID, defaultValue: DataTypes.UUIDV1 },
      locale: { type: DataTypes.STRING(10), allowNull: false },
      translation: { type: DataTypes.JSON, defaultValue: {} },
      createdBy: { type: DataTypes.INTEGER, allowNull: false },
      modifiedBy: { type: DataTypes.INTEGER, allowNull: false }
    };
  } else {
    props = {
      appId: { type: DataTypes.UUID, defaultValue: DataTypes.UUIDV1 },
      locale: { type: DataTypes.STRING(10), allowNull: false },
      translation: { type: DataTypes.TEXT("long") },
      createdBy: { type: DataTypes.INTEGER, allowNull: false },
      modifiedBy: { type: DataTypes.INTEGER, allowNull: false }
    };
  }
  return sequelize.define("AppsLocale", props, {
    paranoid: true,
    timestamps: true,
    tableName: "apps_locale"
  });
};
