module.exports = function (sequelize, DataTypes) {
  let props = {
    id: { type: DataTypes.UUID, primaryKey: true, defaultValue: DataTypes.UUIDV1, allowNull: false },
    name: { type: DataTypes.STRING(64), defaultValue: "" },
    status: { type: DataTypes.INTEGER, allowNull: false, defaultValue: 0 },
    // PROGRESS-0, ABORTED-1, COMPLETE-2, EXPIRED-3, FAILED-4
    fileName: { type: DataTypes.TEXT, allowNull: false },
    fileType: { type: DataTypes.TEXT, allowNull: false },
    filepath: { type: DataTypes.TEXT, defaultValue: "" },
    fileBytes: { type: DataTypes.INTEGER, allowNull: true },
    aid: { type: DataTypes.INTEGER, defaultValue: -1, allowNull: true },
    startDate: { type: DataTypes.STRING(32), defaultValue: "", allowNull: true },
    endDate: { type: DataTypes.STRING(32), defaultValue: "", allowNull: true },
    createdBy: { type: DataTypes.TEXT, allowNull: true },
    modifiedBy: { type: DataTypes.TEXT, allowNull: true }
  };

  return sequelize.define("MyDownloads", props, {
    paranoid: true,
    timestamps: true,
    tableName: "my_downloads"
  });
};
