module.exports = function (sequelize, DataTypes) {
    return sequelize.define("DeletedUsers", {
        deletedUserId: { type: DataTypes.INTEGER },
        firstName: { type: DataTypes.STRING(32), allowNull: false },            // Human readable first name (mandatory)
        lastName: { type: DataTypes.STRING(32) }
    }, {
        paranoid: true,
        timestamps: true,
        tableName: "users_deleted"
    });
};
