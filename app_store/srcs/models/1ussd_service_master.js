module.exports = function (sequelize, DataTypes) {
  let props;
  const jsonsupport = global.jsonsupport || false;
  if (jsonsupport) {
    props = {
      id: { type: DataTypes.UUID, primaryKey: true, defaultValue: DataTypes.UUIDV1 },
      name: { type: DataTypes.STRING(32), allowNull: false, unique: true },
      desc: { type: DataTypes.STRING(128) },
      status: { type: DataTypes.INTEGER, defaultValue: 0 },
      shortcode: { type: DataTypes.STRING(32), allowNull: false, unique: true },
      appId: { type: DataTypes.UUID, allowNull: true, defaultValue: -1 },
      options: { type: DataTypes.JSON, defaultValue: {} },
      createdBy: { type: DataTypes.INTEGER, allowNull: false },
      modifiedBy: { type: DataTypes.INTEGER, allowNull: false }
    };
  } else {
    props = {
      id: { type: DataTypes.UUID, primaryKey: true, defaultValue: DataTypes.UUIDV1 },
      name: { type: DataTypes.STRING(32), allowNull: false, unique: true },
      desc: { type: DataTypes.STRING(128) },
      status: { type: DataTypes.INTEGER, defaultValue: 0 },
      shortcode: { type: DataTypes.STRING(32), allowNull: false, unique: true },
      appId: { type: DataTypes.UUID, allowNull: true, defaultValue: -1 },
      options: { type: DataTypes.TEXT("long") },
      createdBy: { type: DataTypes.INTEGER, allowNull: false },
      modifiedBy: { type: DataTypes.INTEGER, allowNull: false }
    };
  }
  const UssdService = sequelize.define("UssdService", props, {
    paranoid: true,
    timestamps: true,
    tableName: "ussd_service_info"
  });
  return UssdService;
};
