module.exports = function (sequelize, DataTypes) {
  let props;
  const jsonsupport = global.jsonsupport || false;
  if (jsonsupport) {
    props = {
      id: { type: DataTypes.UUID, primaryKey: true, defaultValue: DataTypes.UUIDV1 },
      name: { type: DataTypes.STRING(32), allowNull: false, unique: true },
      desc: { type: DataTypes.STRING(128) },
      status: { type: DataTypes.INTEGER, defaultValue: 0 },
      appData: { type: DataTypes.JSON, defaultValue: "{}" },
      owner: { type: DataTypes.INTEGER, allowNull: false },
      createdBy: { type: DataTypes.INTEGER, allowNull: false },
      modifiedBy: { type: DataTypes.INTEGER, allowNull: false },
      scheduleTime: { type: DataTypes.DATE },
      ngage_id: { type: DataTypes.STRING(32), allowNull: false, unique: true },
      alignment: { type: DataTypes.STRING(20), allowNull: true, unique: true },
      svg: { type: DataTypes.TEXT("long") },
      OTC: { type: DataTypes.STRING(20), allowNull: false, unique: true },
      MRC: { type: DataTypes.STRING(20), allowNull: false, unique: true },
      channels: { type: DataTypes.INTEGER, defaultValue: 0 },
      nodes: { type: DataTypes.INTEGER, defaultValue: 0 },
      freeNodeExec: { type: DataTypes.INTEGER, defaultValue: 0 },
      nodeExecCharge: { type: DataTypes.STRING(20), allowNull: false, unique: true },
    };
  } else {
    props = {
      id: { type: DataTypes.UUID, primaryKey: true, defaultValue: DataTypes.UUIDV1 },
      name: { type: DataTypes.STRING(32), allowNull: false, unique: true },
      desc: { type: DataTypes.STRING(128) },
      status: { type: DataTypes.INTEGER, defaultValue: 0 },
      appData: { type: DataTypes.TEXT("long") },
      owner: { type: DataTypes.INTEGER, allowNull: false },
      createdBy: { type: DataTypes.INTEGER, allowNull: false },
      modifiedBy: { type: DataTypes.INTEGER, allowNull: false },
      scheduleTime: { type: DataTypes.DATE },
      type: DataTypes.STRING(32), allowNull: false, unique: true,
      ngage_id: { type: DataTypes.STRING(32), allowNull: false, unique: true },
      alignment: { type: DataTypes.STRING(20), allowNull: true, unique: true },
      svg: { type: DataTypes.TEXT("long") },
      OTC: { type: DataTypes.STRING(20), allowNull: false, unique: true },
      MRC: { type: DataTypes.STRING(20), allowNull: false, unique: true },
      channels: { type: DataTypes.INTEGER, defaultValue: 0 },
      nodes: { type: DataTypes.INTEGER, defaultValue: 0 },
      freeNodeExec: { type: DataTypes.INTEGER, defaultValue: 0 },
      nodeExecCharge: { type: DataTypes.STRING(20), allowNull: false, unique: true },
    };
  }

  const AppsMaster = sequelize.define("AppsMaster", props, {
    paranoid: true,
    timestamps: true,
    tableName: "apps_master",
    classMethods: {
      associate: function (models) {
        sequelize.models.AppsHistory.belongsTo(AppsMaster, { foreignKey: "appId", onDelete: "cascade" });
        sequelize.models.AppsLocale.belongsTo(AppsMaster, { foreignKey: "appId", onDelete: "cascade" });
        sequelize.models.AppsLock.belongsTo(AppsMaster, { foreignKey: "appId", onDelete: "cascade" });
        sequelize.models.AppsSnapshots.belongsTo(AppsMaster, { foreignKey: "appId", onDelete: "cascade" });
      }
    }
  });
  
  return AppsMaster;
};
