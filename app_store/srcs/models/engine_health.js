module.exports = function (sequelize, DataTypes) {
    let props;
    const jsonsupport = global.jsonsupport || false;
    if (jsonsupport) {
        props = {
            nid: { type: DataTypes.STRING(50), primaryKey: true },
            hostname: { type: DataTypes.STRING(50) },
            cid: { type: DataTypes.INTEGER, defaultValue: 0 },
            mode: { type: DataTypes.STRING(50) },
            report: { type: DataTypes.JSON }
        };
    } else {
        props = {
            nid: { type: DataTypes.STRING(50), primaryKey: true },
            hostname: { type: DataTypes.STRING(50) },
            cid: { type: DataTypes.INTEGER, defaultValue: 0 },
            mode: { type: DataTypes.STRING(50) },
            report: { type: DataTypes.TEXT("long") }
        };
    }
    return sequelize.define("EngineHealth", props, {
        paranoid: true,
        timestamps: true,
        tableName: "engine_health"
    });
};
