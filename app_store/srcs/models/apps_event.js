"use strict";
module.exports = function (sequelize, DataTypes) {
    return sequelize.define("AppsEvent", {
        appId: { type: DataTypes.UUID, defaultValue: DataTypes.UUIDV1 },
        appName: { type: DataTypes.STRING(128) },
        userId: { type: DataTypes.STRING, allowNull: false },
        action: { type: DataTypes.STRING },
        comment: { type: DataTypes.TEXT },
        is_read: { type: DataTypes.CHAR(1), defaultValue: 0 },
        performer: { type: DataTypes.STRING, allowNull: false }
    }, {
        paranoid: true,
        timestamps: true,
        tableName: "apps_event"
    });
};
