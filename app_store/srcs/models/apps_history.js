module.exports = function (sequelize, DataTypes) {
  let props;
  const jsonsupport = global.jsonsupport || false;
  if (jsonsupport) {
    props = {
      appId: { type: DataTypes.UUID, defaultValue: DataTypes.UUIDV1 },
      appName: { type: DataTypes.STRING(128) },
      event: { type: DataTypes.INTEGER, defaultValue: 0 },
      status: { type: DataTypes.INTEGER, defaultValue: 0 },
      remarks: { type: DataTypes.JSON },
      performer: { type: DataTypes.STRING, allowNull: false }
    };
  } else {
    props = {
      appId: { type: DataTypes.UUID, defaultValue: DataTypes.UUIDV1 },
      appName: { type: DataTypes.STRING(128) },
      event: { type: DataTypes.INTEGER, defaultValue: 0 },
      status: { type: DataTypes.INTEGER, defaultValue: 0 },
      remarks: { type: DataTypes.TEXT("long") },
      performer: { type: DataTypes.STRING, allowNull: false }
    };
  }
  return sequelize.define("AppsHistory", props, {
    paranoid: true,
    timestamps: true,
    tableName: "apps_history"
  });
};
