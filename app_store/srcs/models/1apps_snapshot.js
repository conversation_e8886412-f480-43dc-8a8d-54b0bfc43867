module.exports = function (sequelize, DataTypes) {
  let props;
  const jsonsupport = global.jsonsupport || false;
  if (jsonsupport) {
    props = {
      appid: { type: DataTypes.CHAR(36), primaryKey: true },
      version: { type: DataTypes.STRING(10), allowNull: false, primaryKey: true },
      comment: { type: DataTypes.STRING(128) },
      appData: { type: DataTypes.JSON, defaultValue: "{}" },
      historyObject: { type: DataTypes.JSON, defaultValue: "{}" },
      owner: { type: DataTypes.INTEGER, allowNull: false },
      createdBy: { type: DataTypes.INTEGER, allowNull: false },
      type: { type: DataTypes.STRING(10) },
      version_name: { type: DataTypes.STRING(32) }
    };
  } else {
    props = {
      appid: { type: DataTypes.CHAR(36), primaryKey: true },
      version: { type: DataTypes.STRING(10), allowNull: false, primaryKey: true },
      comment: { type: DataTypes.STRING(128) },
      appData: { type: DataTypes.TEXT("long") },
      historyObject: { type: DataTypes.TEXT("long") },
      owner: { type: DataTypes.INTEGER, allowNull: false },
      createdBy: { type: DataTypes.INTEGER, allowNull: false },
      type: { type: DataTypes.STRING(10) },
      version_name: { type: DataTypes.STRING(32) },
    };
  }

  const AppsSnapshots = sequelize.define("AppsSnapshots", props, {
    paranoid: true,
    timestamps: true,
    tableName: "apps_snapshots"
  });
  return AppsSnapshots;
};
