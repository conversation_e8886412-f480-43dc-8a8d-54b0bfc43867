module.exports = function (sequelize, DataTypes) {
  const UssdGatewayInfo = sequelize.define("UssdGatewayInfo", {
    id: { type: DataTypes.UUID, primaryKey: true, defaultValue: DataTypes.UUIDV1 },
    name: { type: DataTypes.STRING(32), allowNull: false, unique: true },
    type: { type: DataTypes.INTEGER, defaultValue: 0, comment: "0-COMVIVA SMPP GW, 1-COMVIVA HTTP GW, 2-HP HTTP GW" },
    status: { type: DataTypes.INTEGER, defaultValue: 0, comment: "1-Enable, 0-Disable" },
    ip: { type: DataTypes.STRING(32), defaultValue: "localhost" },
    port: { type: DataTypes.INTEGER, defaultValue: 2775 },
    user: { type: DataTypes.STRING(10) },
    pass: { type: DataTypes.STRING(10) },
    enquiry_link_interval: { type: DataTypes.INTEGER, defaultValue: 10000 },
    no_of_channels: { type: DataTypes.INTEGER, defaultValue: 1 },
    system_type: { type: DataTypes.INTEGER, defaultValue: 0, comment: "0-USSD, 1-SMS" },
    interface_type: { type: DataTypes.INTEGER, defaultValue: 0, comment: "0-SMPP, 1-HTTP" },
    version: { type: DataTypes.INTEGER, defaultValue: 0, comment: "Ussd Gateway Version" },
    buffer_size: { type: DataTypes.INTEGER, defaultValue: 0, comment: "Ussd gateway request buffer size." },
    parameters_order: { type: DataTypes.STRING(120), defaultValue: "SERVICE_STRING||IMSI||MSISDN||MSC||MCC||MNC||LAC||CI" },
    msg_delimiter: { type: DataTypes.STRING(1), defaultValue: "#" },
    shortcode_delimiter: { type: DataTypes.STRING(1), defaultValue: "*" },
    url: { type: DataTypes.STRING(120), defaultValue: "smpp://localhost:2775" },
    callback_url: { type: DataTypes.STRING(120), defaultValue: "https://localhost:8220" },
    createdBy: { type: DataTypes.INTEGER, allowNull: false },
    modifiedBy: { type: DataTypes.INTEGER, allowNull: false }
  }, {
    paranoid: true,
    timestamps: true,
    tableName: "ussd_gateway_info"
  });
  return UssdGatewayInfo;
};
