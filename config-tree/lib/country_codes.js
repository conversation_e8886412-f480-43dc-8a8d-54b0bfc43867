var countries = {
   "1": {
      "name": "Canada,UnitedStates",
      "isocode": "CAN,USA",
      "length": "11"
   },
   "7": {
      "name": "Kazakhstan,Russia",
      "isocode": "KAZ,RUS",
      "length": "11"
   },
   "20": {
      "name": "Egypt",
      "isocode": "EGY",
      "length": "11"
   },
   "27": {
      "name": "SouthAfrica",
      "isocode": "ZAF",
      "length": "10"
   },
   "30": {
      "name": "Greece",
      "isocode": "GRC",
      "length": "10"
   },
   "31": {
      "name": "Netherlands",
      "isocode": "NLD",
      "length": "10"
   },
   "32": {
      "name": "Belgium",
      "isocode": "BEL",
      "length": "10"
   },
   "33": {
      "name": "France",
      "isocode": "FRA",
      "length": "10"
   },
   "34": {
      "name": "Spain",
      "isocode": "ESP",
      "length": "9"
   },
   "36": {
      "name": "Hungary",
      "isocode": "HUN",
      "length": "9"
   },
   "39": {
      "name": "Italy",
      "isocode": "ITA",
      "length": "10"
   },
   "40": {
      "name": "Romania",
      "isocode": "ROU",
      "length": "10"
   },
   "41": {
      "name": "Switzerland",
      "isocode": "CHE",
      "length": "10"
   },
   "43": {
      "name": "Austria",
      "isocode": "AUT",
      "length": "10"
   },
   "44": {
      "name": "UnitedKingdom",
      "isocode": "GBR",
      "length": "12"
   },
   "45": {
      "name": "Denmark",
      "isocode": "DNK",
      "length": "8"
   },
   "46": {
      "name": "Sweden",
      "isocode": "SWE",
      "length": "10"
   },
   "47": {
      "name": "Norway,SvalbardandJanMayen",
      "isocode": "NOR,SJM",
      "length": "8"
   },
   "48": {
      "name": "Poland",
      "isocode": "POL",
      "length": "9"
   },
   "49": {
      "name": "Germany",
      "isocode": "DEU",
      "length": "12"
   },
   "51": {
      "name": "Peru",
      "isocode": "PER",
      "length": "9"
   },
   "52": {
      "name": "Mexico",
      "isocode": "MEX",
      "length": "13"
   },
   "53": {
      "name": "Cuba",
      "isocode": "CUB",
      "length": "9"
   },
   "54": {
      "name": "Argentina",
      "isocode": "ARG",
      "length": "13"
   },
   "55": {
      "name": "Brazil",
      "isocode": "BRA",
      "length": "11"
   },
   "56": {
      "name": "Chile",
      "isocode": "CHL",
      "length": "10"
   },
   "57": {
      "name": "Colombia",
      "isocode": "COL",
      "length": "10"
   },
   "58": {
      "name": "Venezuela",
      "isocode": "VEN",
      "length": "11"
   },
   "60": {
      "name": "Malaysia",
      "isocode": "MYS",
      "length": "10"
   },
   "61": {
      "name": "Australia,ChristmasIsland,CocosIslands",
      "isocode": "AUS,CXR,CCK",
      "length": "10"
   },
   "62": {
      "name": "Indonesia",
      "isocode": "IDN",
      "length": "10"
   },
   "63": {
      "name": "Philippines",
      "isocode": "PHL",
      "length": "11"
   },
   "64": {
      "name": "NewZealand,Pitcairn",
      "isocode": "NZL,PCN",
      "length": "10"
   },
   "65": {
      "name": "Singapore",
      "isocode": "SGP",
      "length": "8"
   },
   "66": {
      "name": "Thailand",
      "isocode": "THA",
      "length": "10"
   },
   "81": {
      "name": "Japan",
      "isocode": "JPN",
      "length": "11"
   },
   "82": {
      "name": "SouthKorea",
      "isocode": "KOR",
      "length": "11"
   },
   "84": {
      "name": "Vietnam",
      "isocode": "VNM",
      "length": "10"
   },
   "86": {
      "name": "China",
      "isocode": "CHN",
      "length": "11"
   },
   "90": {
      "name": "Turkey",
      "isocode": "TUR",
      "length": "11"
   },
   "91": {
      "name": "India",
      "isocode": "IND",
      "length": "12"
   },
   "92": {
      "name": "Pakistan",
      "isocode": "PAK",
      "length": "12"
   },
   "93": {
      "name": "Afghanistan",
      "isocode": "AFG",
      "length": "10"
   },
   "94": {
      "name": "SriLanka",
      "isocode": "LKA",
      "length": "10"
   },
   "95": {
      "name": "Myanmar",
      "isocode": "MMR",
      "length": "9"
   },
   "98": {
      "name": "Iran",
      "isocode": "IRN",
      "length": "11"
   },
   "211": {
      "name": "SouthSudan",
      "isocode": "SSD",
      "length": "10"
   },
   "212": {
      "name": "Morocco,WesternSahara",
      "isocode": "MAR,ESH",
      "length": "10"
   },
   "213": {
      "name": "Algeria",
      "isocode": "DZA",
      "length": "10"
   },
   "216": {
      "name": "Tunisia",
      "isocode": "TUN",
      "length": "8"
   },
   "218": {
      "name": "Libya",
      "isocode": "LBY",
      "length": "10"
   },
   "220": {
      "name": "Gambia",
      "isocode": "GMB",
      "length": "7"
   },
   "221": {
      "name": "Senegal",
      "isocode": "SEN",
      "length": "9"
   },
   "222": {
      "name": "Mauritania",
      "isocode": "MRT",
      "length": "8"
   },
   "223": {
      "name": "Mali",
      "isocode": "MLI",
      "length": "8"
   },
   "224": {
      "name": "Guinea",
      "isocode": "GIN",
      "length": "9"
   },
   "225": {
      "name": "IvoryCoast",
      "isocode": "CIV",
      "length": "8"
   },
   "226": {
      "name": "BurkinaFaso",
      "isocode": "BFA",
      "length": "8"
   },
   "227": {
      "name": "Niger",
      "isocode": "NER",
      "length": "8"
   },
   "228": {
      "name": "Togo",
      "isocode": "TGO",
      "length": "8"
   },
   "229": {
      "name": "Benin",
      "isocode": "BEN",
      "length": "8"
   },
   "230": {
      "name": "Mauritius",
      "isocode": "MUS",
      "length": "8"
   },
   "231": {
      "name": "Liberia",
      "isocode": "LBR",
      "length": "10"
   },
   "232": {
      "name": "SierraLeone",
      "isocode": "SLE",
      "length": "9"
   },
   "233": {
      "name": "Ghana",
      "isocode": "GHA",
      "length": "10"
   },
   "234": {
      "name": "Nigeria",
      "isocode": "NGA",
      "length": "11"
   },
   "235": {
      "name": "Chad",
      "isocode": "TCD",
      "length": "8"
   },
   "236": {
      "name": "CentralAfricanRepublic",
      "isocode": "CAF",
      "length": "8"
   },
   "237": {
      "name": "Cameroon",
      "isocode": "CMR",
      "length": "9"
   },
   "238": {
      "name": "CapeVerde",
      "isocode": "CPV",
      "length": "7"
   },
   "239": {
      "name": "SaoTomeandPrincipe",
      "isocode": "STP",
      "length": "7"
   },
   "240": {
      "name": "EquatorialGuinea",
      "isocode": "GNQ",
      "length": "9"
   },
   "241": {
      "name": "Gabon",
      "isocode": "GAB",
      "length": "8"
   },
   "242": {
      "name": "RepublicoftheCongo",
      "isocode": "COG",
      "length": "9"
   },
   "243": {
      "name": "DemocraticRepublicoftheCongo",
      "isocode": "COD",
      "length": "10"
   },
   "244": {
      "name": "Angola",
      "isocode": "AGO",
      "length": "9"
   },
   "245": {
      "name": "GuineaBissau",
      "isocode": "GNB",
      "length": "9"
   },
   "246": {
      "name": "BritishIndianOceanTerritory",
      "isocode": "IOT",
      "length": "7"
   },
   "248": {
      "name": "Seychelles",
      "isocode": "SYC",
      "length": "7"
   },
   "249": {
      "name": "Sudan",
      "isocode": "SDN",
      "length": "10"
   },
   "250": {
      "name": "Rwanda",
      "isocode": "RWA",
      "length": "10"
   },
   "251": {
      "name": "Ethiopia",
      "isocode": "ETH",
      "length": "10"
   },
   "252": {
      "name": "Somalia",
      "isocode": "SOM",
      "length": "8"
   },
   "253": {
      "name": "Djibouti",
      "isocode": "DJI",
      "length": "8"
   },
   "254": {
      "name": "Kenya",
      "isocode": "KEN",
      "length": "9"
   },
   "255": {
      "name": "Tanzania",
      "isocode": "TZA",
      "length": "10"
   },
   "256": {
      "name": "Uganda",
      "isocode": "UGA",
      "length": "10"
   },
   "257": {
      "name": "Burundi",
      "isocode": "BDI",
      "length": "8"
   },
   "258": {
      "name": "Mozambique",
      "isocode": "MOZ",
      "length": "9"
   },
   "260": {
      "name": "Zambia",
      "isocode": "ZMB",
      "length": "10"
   },
   "261": {
      "name": "Madagascar",
      "isocode": "MDG",
      "length": "10"
   },
   "262": {
      "name": "Mayotte,Reunion",
      "isocode": "MYT,REU",
      "length": "10"
   },
   "263": {
      "name": "Zimbabwe",
      "isocode": "ZWE",
      "length": "10"
   },
   "264": {
      "name": "Namibia",
      "isocode": "NAM",
      "length": "10"
   },
   "265": {
      "name": "Malawi",
      "isocode": "MWI",
      "length": "10"
   },
   "266": {
      "name": "Lesotho",
      "isocode": "LSO",
      "length": "8"
   },
   "267": {
      "name": "Botswana",
      "isocode": "BWA",
      "length": "8"
   },
   "268": {
      "name": "Swaziland",
      "isocode": "SWZ",
      "length": "8"
   },
   "269": {
      "name": "Comoros",
      "isocode": "COM",
      "length": "7"
   },
   "290": {
      "name": "SaintHelena",
      "isocode": "SHN",
      "length": "4"
   },
   "291": {
      "name": "Eritrea",
      "isocode": "ERI",
      "length": "8"
   },
   "297": {
      "name": "Aruba",
      "isocode": "ABW",
      "length": "7"
   },
   "298": {
      "name": "FaroeIslands",
      "isocode": "FRO",
      "length": "6"
   },
   "299": {
      "name": "Greenland",
      "isocode": "GRL",
      "length": "6"
   },
   "350": {
      "name": "Gibraltar",
      "isocode": "GIB",
      "length": "8"
   },
   "351": {
      "name": "Portugal",
      "isocode": "PRT",
      "length": "9"
   },
   "352": {
      "name": "Luxembourg",
      "isocode": "LUX",
      "length": "9"
   },
   "353": {
      "name": "Ireland",
      "isocode": "IRL",
      "length": "10"
   },
   "354": {
      "name": "Iceland",
      "isocode": "ISL",
      "length": "7"
   },
   "355": {
      "name": "Albania",
      "isocode": "ALB",
      "length": "10"
   },
   "356": {
      "name": "Malta",
      "isocode": "MLT",
      "length": "8"
   },
   "357": {
      "name": "Cyprus",
      "isocode": "CYP",
      "length": "8"
   },
   "358": {
      "name": "Finland",
      "isocode": "FIN",
      "length": "10"
   },
   "359": {
      "name": "Bulgaria",
      "isocode": "BGR",
      "length": "9"
   },
   "370": {
      "name": "Lithuania",
      "isocode": "LTU",
      "length": "9"
   },
   "371": {
      "name": "Latvia",
      "isocode": "LVA",
      "length": "8"
   },
   "372": {
      "name": "Estonia",
      "isocode": "EST",
      "length": "8"
   },
   "373": {
      "name": "Moldova",
      "isocode": "MDA",
      "length": "9"
   },
   "374": {
      "name": "Armenia",
      "isocode": "ARM",
      "length": "9"
   },
   "375": {
      "name": "Belarus",
      "isocode": "BLR",
      "length": "11"
   },
   "376": {
      "name": "Andorra",
      "isocode": "AND",
      "length": "6"
   },
   "377": {
      "name": "Monaco",
      "isocode": "MCO",
      "length": "10"
   },
   "378": {
      "name": "SanMarino",
      "isocode": "SMR",
      "length": "8"
   },
   "380": {
      "name": "Ukraine",
      "isocode": "UKR",
      "length": "10"
   },
   "381": {
      "name": "Serbia",
      "isocode": "SRB",
      "length": "10"
   },
   "382": {
      "name": "Montenegro",
      "isocode": "MNE",
      "length": "9"
   },
   "385": {
      "name": "Croatia",
      "isocode": "HRV",
      "length": "10"
   },
   "386": {
      "name": "Slovenia",
      "isocode": "SVN",
      "length": "9"
   },
   "387": {
      "name": "BosniaandHerzegovina",
      "isocode": "BIH",
      "length": "9"
   },
   "389": {
      "name": "Macedonia",
      "isocode": "MKD",
      "length": "9"
   },
   "420": {
      "name": "CzechRepublic",
      "isocode": "CZE",
      "length": "9"
   },
   "421": {
      "name": "Slovakia",
      "isocode": "SVK",
      "length": "10"
   },
   "423": {
      "name": "Liechtenstein",
      "isocode": "LIE",
      "length": "9"
   },
   "500": {
      "name": "FalklandIslands",
      "isocode": "FLK",
      "length": "5"
   },
   "501": {
      "name": "Belize",
      "isocode": "BLZ",
      "length": "7"
   },
   "502": {
      "name": "Guatemala",
      "isocode": "GTM",
      "length": "8"
   },
   "503": {
      "name": "ElSalvador",
      "isocode": "SLV",
      "length": "8"
   },
   "504": {
      "name": "Honduras",
      "isocode": "HND",
      "length": "8"
   },
   "505": {
      "name": "Nicaragua",
      "isocode": "NIC",
      "length": "8"
   },
   "506": {
      "name": "CostaRica",
      "isocode": "CRI",
      "length": "8"
   },
   "507": {
      "name": "Panama",
      "isocode": "PAN",
      "length": "8"
   },
   "508": {
      "name": "SaintPierreandMiquelon",
      "isocode": "SPM",
      "length": "7"
   },
   "509": {
      "name": "Haiti",
      "isocode": "HTI",
      "length": "8"
   },
   "590": {
      "name": "SaintBarthelemy,SaintMartin",
      "isocode": "BLM,MAF",
      "length": "10"
   },
   "591": {
      "name": "Bolivia",
      "isocode": "BOL",
      "length": "8"
   },
   "592": {
      "name": "Guyana",
      "isocode": "GUY",
      "length": "7"
   },
   "593": {
      "name": "Ecuador",
      "isocode": "ECU",
      "length": "10"
   },
   "595": {
      "name": "Paraguay",
      "isocode": "PRY",
      "length": "10"
   },
   "597": {
      "name": "Suriname",
      "isocode": "SUR",
      "length": "7"
   },
   "598": {
      "name": "Uruguay",
      "isocode": "URY",
      "length": "9"
   },
   "599": {
      "name": "Curacao,NetherlandsAntilles",
      "isocode": "CUW,ANT",
      "length": "7"
   },
   "670": {
      "name": "EastTimor",
      "isocode": "TLS",
      "length": "8"
   },
   "672": {
      "name": "Antarctica",
      "isocode": "ATA",
      "length": "6"
   },
   "673": {
      "name": "Brunei",
      "isocode": "BRN",
      "length": "7"
   },
   "674": {
      "name": "Nauru",
      "isocode": "NRU",
      "length": "7"
   },
   "675": {
      "name": "PapuaNewGuinea",
      "isocode": "PNG",
      "length": "7"
   },
   "676": {
      "name": "Tonga",
      "isocode": "TON",
      "length": "7"
   },
   "677": {
      "name": "SolomonIslands",
      "isocode": "SLB",
      "length": "7"
   },
   "678": {
      "name": "Vanuatu",
      "isocode": "VUT",
      "length": "7"
   },
   "679": {
      "name": "Fiji",
      "isocode": "FJI",
      "length": "7"
   },
   "680": {
      "name": "Palau",
      "isocode": "PLW",
      "length": "7"
   },
   "681": {
      "name": "WallisandFutuna",
      "isocode": "WLF",
      "length": "6"
   },
   "682": {
      "name": "CookIslands",
      "isocode": "COK",
      "length": "5"
   },
   "683": {
      "name": "Niue",
      "isocode": "NIU",
      "length": "4"
   },
   "685": {
      "name": "Samoa",
      "isocode": "WSM",
      "length": "6"
   },
   "686": {
      "name": "Kiribati",
      "isocode": "KIR",
      "length": "8"
   },
   "687": {
      "name": "NewCaledonia",
      "isocode": "NCL",
      "length": "8"
   },
   "688": {
      "name": "Tuvalu",
      "isocode": "TUV",
      "length": "6"
   },
   "689": {
      "name": "FrenchPolynesia",
      "isocode": "PYF",
      "length": "8"
   },
   "690": {
      "name": "Tokelau",
      "isocode": "TKL",
      "length": "4"
   },
   "691": {
      "name": "Micronesia",
      "isocode": "FSM",
      "length": "7"
   },
   "692": {
      "name": "MarshallIslands",
      "isocode": "MHL",
      "length": "7"
   },
   "850": {
      "name": "NorthKorea",
      "isocode": "PRK",
      "length": "11"
   },
   "852": {
      "name": "HongKong",
      "isocode": "HKG",
      "length": "8"
   },
   "853": {
      "name": "Macao",
      "isocode": "MAC",
      "length": "8"
   },
   "855": {
      "name": "Cambodia",
      "isocode": "KHM",
      "length": "9"
   },
   "856": {
      "name": "Laos",
      "isocode": "LAO",
      "length": "11"
   },
   "880": {
      "name": "Bangladesh",
      "isocode": "BGD",
      "length": "11"
   },
   "886": {
      "name": "Taiwan",
      "isocode": "TWN",
      "length": "10"
   },
   "960": {
      "name": "Maldives",
      "isocode": "MDV",
      "length": "7"
   },
   "961": {
      "name": "Lebanon",
      "isocode": "LBN",
      "length": "8"
   },
   "962": {
      "name": "Jordan",
      "isocode": "JOR",
      "length": "10"
   },
   "963": {
      "name": "Syria",
      "isocode": "SYR",
      "length": "10"
   },
   "964": {
      "name": "Iraq",
      "isocode": "IRQ",
      "length": "11"
   },
   "965": {
      "name": "Kuwait",
      "isocode": "KWT",
      "length": "8"
   },
   "966": {
      "name": "SaudiArabia",
      "isocode": "SAU",
      "length": "10"
   },
   "967": {
      "name": "Yemen",
      "isocode": "YEM",
      "length": "10"
   },
   "968": {
      "name": "Oman",
      "isocode": "OMN",
      "length": "8"
   },
   "970": {
      "name": "Palestine",
      "isocode": "PSE",
      "length": "10"
   },
   "971": {
      "name": "UnitedArabEmirates",
      "isocode": "ARE",
      "length": "10"
   },
   "972": {
      "name": "Israel",
      "isocode": "ISR",
      "length": "10"
   },
   "973": {
      "name": "Bahrain",
      "isocode": "BHR",
      "length": "8"
   },
   "974": {
      "name": "Qatar",
      "isocode": "QAT",
      "length": "8"
   },
   "975": {
      "name": "Bhutan",
      "isocode": "BTN",
      "length": "8"
   },
   "976": {
      "name": "Mongolia",
      "isocode": "MNG",
      "length": "8"
   },
   "977": {
      "name": "Nepal",
      "isocode": "NPL",
      "length": "10"
   },
   "992": {
      "name": "Tajikistan",
      "isocode": "TJK",
      "length": "10"
   },
   "993": {
      "name": "Turkmenistan",
      "isocode": "TKM",
      "length": "9"
   },
   "994": {
      "name": "Azerbaijan",
      "isocode": "AZE",
      "length": "10"
   },
   "995": {
      "name": "Georgia",
      "isocode": "GEO",
      "length": "9"
   },
   "996": {
      "name": "Kyrgyzstan",
      "isocode": "KGZ",
      "length": "10"
   },
   "998": {
      "name": "Uzbekistan",
      "isocode": "UZB",
      "length": "10"
   },
   "1242": {
      "name": "Bahamas",
      "isocode": "BHS",
      "length": "10"
   },
   "1246": {
      "name": "Barbados",
      "isocode": "BRB",
      "length": "10"
   },
   "1264": {
      "name": "Anguilla",
      "isocode": "AIA",
      "length": "10"
   },
   "1268": {
      "name": "AntiguaandBarbuda",
      "isocode": "ATG",
      "length": "10"
   },
   "1284": {
      "name": "BritishVirginIslands",
      "isocode": "VGB",
      "length": "10"
   },
   "1340": {
      "name": "U.S.VirginIslands",
      "isocode": "VIR",
      "length": "10"
   },
   "1345": {
      "name": "CaymanIslands",
      "isocode": "CYM",
      "length": "10"
   },
   "1441": {
      "name": "Bermuda",
      "isocode": "BMU",
      "length": "10"
   },
   "1473": {
      "name": "Grenada",
      "isocode": "GRD",
      "length": "10"
   },
   "1649": {
      "name": "TurksandCaicosIslands",
      "isocode": "TCA",
      "length": "10"
   },
   "1664": {
      "name": "Montserrat",
      "isocode": "MSR",
      "length": "10"
   },
   "1670": {
      "name": "NorthernMarianaIslands",
      "isocode": "MNP",
      "length": "10"
   },
   "1671": {
      "name": "Guam",
      "isocode": "GUM",
      "length": "10"
   },
   "1684": {
      "name": "AmericanSamoa",
      "isocode": "ASM",
      "length": "10"
   },
   "1721": {
      "name": "SintMaarten",
      "isocode": "SXM",
      "length": "10"
   },
   "1758": {
      "name": "SaintLucia",
      "isocode": "LCA",
      "length": "10"
   },
   "1767": {
      "name": "Dominica",
      "isocode": "DMA",
      "length": "10"
   },
   "1784": {
      "name": "SaintVincentandtheGrenadines",
      "isocode": "VCT",
      "length": "10"
   },
   "1868": {
      "name": "TrinidadandTobago",
      "isocode": "TTO",
      "length": "10"
   },
   "1869": {
      "name": "SaintKittsandNevis",
      "isocode": "KNA",
      "length": "10"
   },
   "1876": {
      "name": "Jamaica",
      "isocode": "JAM",
      "length": "10"
   }
}
module.exports = countries;