/**
 *   ConfigProxy -- An NPM module that allows to
 *     read/write configuration settings stored
 *     in a redis configuration server.
 *     The configuration server is similar in analogy to windows registry.
 **/
const _ = require("underscore");
const BPromise = require("bluebird");
const MysqlManager = require("./mysql_man");
require("colors");
const channels = ["sms", "mms", "ussd", "email", "ip"];

const SETTINGS_KEY = "system:settings";
const COMPONENT_KEY = SETTINGS_KEY + ":components";
const PROTECTED_KEY = SETTINGS_KEY + ":protected";
const GLOBAL_KEY = SETTINGS_KEY + ":global";

const DATABASE_KEY = GLOBAL_KEY + ":databases";
const OAM_KEY = GLOBAL_KEY + ":oam";
const OAM_RESOURCE = GLOBAL_KEY + ":oamResources:oamOIDs";
const KPI_KEY = GLOBAL_KEY + ":kpi";
const INFLUX_KEY = GLOBAL_KEY + ":influxServer";
const GRAFANA_KEY = GLOBAL_KEY + ":grafanaServer";
const LIC_SER_KEY = GLOBAL_KEY + ":licenseServer";
const SLAB_KEY = GLOBAL_KEY + ":slab";
const REFUND_KEY = GLOBAL_KEY + ":refund";
const SMPP_KEY = GLOBAL_KEY + ":smppConfig";
const LOG_QUE_KEY = GLOBAL_KEY + ":logQueue";
const ELS_SER_KEY = GLOBAL_KEY + ":elasticsearch";
const PLUGINS = SETTINGS_KEY + ":plugins";
const JOB_STORE_KEY = GLOBAL_KEY + ":jobStore";
const KPI_COUNTERS_KEY = GLOBAL_KEY + ":kpiCounters";
const MO_SERVICES_KEY = GLOBAL_KEY + ":mocache";
const EVENT_SERVER_KEY = GLOBAL_KEY + ":eventBus";
const FSM_KEY = GLOBAL_KEY + ":fsm";

module.exports = {

  /**
   *  Retrieves and returns module info from the cache.
   *  If the cache is empty, it loads the info from config to cache.
   *  @param {String} moduleName
   *  @return {JSONObject} moduleInfo if found.
   **/
  getModuleInfo: async moduleName => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, COMPONENT_KEY + ":" + moduleName)
  },
  /**
   *  Retrieves and returns protected info from the cache.
   *  If the cache is empty, it loads the info from config to cache.
   **/
  getProtectedInfo: async keyName => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, PROTECTED_KEY + ":" + keyName)
  },

  /**
   *  Returns the info for a module (component) located at
   *  "system:settings;components:<moduleName>"
   *  Note: If a callback argument is provided, it will be called, else promise will be returned.
   *  @param {String} moduleName -- The name of the component for which to retrieve settings.
   *  @param {Function} cb -- Callback fn to be called, if retrieval finishes
   *                          (for supporting classic callbacks feature)
   **/
  getModuleInfoEx: (moduleName, cb) => {
    let promise = getSectionX(COMPONENT_KEY + ":" + moduleName)
      .then(restoreDataTypes.bind(null));
    if (cb) {
      promise.then(() => {
        return cb(null, promise.value());
      });
    } else return promise;
  },

  /**
     *  Returns the global rate card
     **/
  getGlobalRateCard: (cb) => {
    let promise = getSectionX(PROTECTED_KEY + ":globalRateCard");
    if (cb) {
      promise.then(() => {
        return cb(null, promise.value());
      });
    } else return promise;
  },

  /**
   *  Returns the database info.  All DB info located at
   *  "system:settings;global:databases:<dbname>"
   *  Note: If a callback argument is provided, it will be called, else promise will be returned.
   *  @param {String} dbName -- Name of the database whose settings are required.
   *  @param {Function} cb -- [optional] Callback fn to be called, which the operation finishes.
   **/
  getDBInfo: (dbName, cb) => {
    let promise = getSectionX(DATABASE_KEY + ":" + dbName)
      .then(restoreDataTypes.bind(null))
      .then(result => {
        if (result.dialectOptions && result.dialectOptions.connectTimeout) {
          result.dialectOptions = Number(result.dialectOptions);
        }
        return result;
      })
    if (cb) {
      promise.then(() => {
        return cb(null, promise.value());
      });
    } else return promise;

  },

  /**
   *  Returns a list of all matching module info from configuration registry.
   *  A module is an entity that resides under "system:settings:components"
   *  @param {String} name -- An optional param for wildcard matching
   *  @return {JSONObject} list of modules
   **/
  getModules: async name => {
    let conn = await MysqlManager.getConnection();
    let moduleKey = COMPONENT_KEY + ":" + name;
    return MysqlManager.keys(conn, moduleKey).then(function (results) {
      if (results.length === 0) return [];

      //TODO: Iterate the results and retrieve hashes for each.
      // ....
      let promises = results.map(function (entry) {
        return MysqlManager.hgetall(conn, entry)
          .then(function (result) {
            let obj = {};
            let key = entry.split(":")[3];
            obj[key] = result;
            return obj;
          });
      });
      return BPromise.all(promises);
    });

  },

  /**
   *  Updates the module Info if there is some change in configurations
   *  @param {String} moduleName
   *  @return {JSONObject} updated module info
   **/
  setModuleInfo: async (moduleName, moduleInfo) => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hmset(conn, COMPONENT_KEY + ":" + moduleName, moduleInfo);
  },

  /**
   *  Lists the base url to which the modules connect
   *  returns host:port/contextName
   **/
  getModuleBaseUrl: async moduleName => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, COMPONENT_KEY + ":" + moduleName)
      .then(result => {
        let url = result.protocol + ":\/\/" + result.host + ":" + +result.port + result.contextPath;
        return url;
      });
  },

  /**
   *  Retrieves all [or specific] global information
   *  Global info is typically stored at "system:settings;global" key
   *  You may pass a "key" argument, if you are interested
   *  in only knowing a particular key value from global space.
   **/
  getGlobalInfo: async (moduleName, cb) => {
    let conn = await MysqlManager.getConnection();
    if (!moduleName) {
      return MysqlManager.hgetall(conn, GLOBAL_KEY);
    } else {
      let json = await MysqlManager.hgetall(conn, GLOBAL_KEY);
      if (json[moduleName]) {
        return json[moduleName];
      } else {
        let promise = getSectionX(GLOBAL_KEY + ":" + moduleName)
          .then(restoreDataTypes.bind(null));
        if (cb) {
          promise.then(() => {
            return cb(null, promise.value());
          });
        } else return promise;
      }
    }
  },

  getLogQueue: async () => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, LOG_QUE_KEY);
  },

  getLicenseServer: async () => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, LIC_SER_KEY);
  },

  getElasticServer: async () => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, ELS_SER_KEY);
  },

  getGlobalByKey: async key => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, GLOBAL_KEY + ":" + key);
  },

  getComponentByKey: async key => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, COMPONENT_KEY + ":" + key);
  },

  getJobStore: async (jobstore, cb) => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, JOB_STORE_KEY).then(restoreDataTypes.bind(null));
  },

  getPaymentMethods: (module, cb) => {
    let promise = getSectionX(GLOBAL_KEY + ":" + module);
    if (cb) {
      promise.then(() => {
        return cb(null, promise.value());
      });
    } else return promise;
  },

  getSlabs: async slab => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, SLAB_KEY).then(restoreDataTypes.bind(null));
  },

  getKpiSweeper: (module, cb) => {
    let promise = getSectionX(GLOBAL_KEY + ":" + module);
    if (cb) {
      promise.then(() => {
        return cb(null, promise.value());
      });
    } else return promise;
  },

  getMoCache: async MoService => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, MO_SERVICES_KEY)
      .then(restoreDataTypes.bind(null));
  },

  getRefundInfo: async refundKey => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, REFUND_KEY)
      .then(restoreDataTypes.bind(null));
  },

  getKpiCountersStore: async jobstore => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, KPI_COUNTERS_KEY)
      .then(restoreDataTypes.bind(null));
  },

  getKpiConfig: async kpi => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, KPI_KEY)
      .then(restoreDataTypes.bind(null));
  },

  getSmppProxy: async smppConfig => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, SMPP_KEY)
      .then(restoreDataTypes.bind(null));
  },

  getEventServer: async () => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, EVENT_SERVER_KEY)
      .then(restoreDataTypes.bind(null));
  },

  /**
   * An extended version of getEventServer().
   * Iterates through channels list and returns an array of
   * hashes of eventBus settings for each channel.
   **/

  /**
   * This function will provide name of all the micro-service applet queues
   * which are present in the system
   **/
  getListOfAllApplets: async () => {
    let applet,
      finalJson = {},
      channels = ["sms", "mms", "ussd"],
      finalArray = [];
    let conn = await MysqlManager.getConnection();
    let promises = channels.map((ch) => {
      return MysqlManager.keys(conn, FSM_KEY + ":" + ch + "*")
        .then((keys) => {
          if (keys.length < 1) {
            finalArray = [];
            finalJson[ch] = []; // Update here to use the variable `ch` instead of the string `"ch"`
          } else {
            keys.forEach((item) => { // Changed to forEach
              let arr = item.split(":");
              applet = arr[6];
  
              if (applet !== "init") {
                finalArray.push(applet + "0");
                finalArray.push(applet + "1");
                finalArray.push(applet + "2");
              }
            });
            finalJson[ch] = require("uniq")(finalArray);
            finalArray = [];
          }
        });
    });
  
    return BPromise.all(promises)
      .then((res) => {
        return finalJson;
      });
  },
  

  /**
   * Reads and returns the fsm data for all message categories
   * @returns {Promise} a future object which when fulfilled
   *                    contains the fsm hash.
   **/
  getFSMKeys: async () => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.keys(conn, FSM_KEY + ":*");
  },

  getMsgRouterX: async key => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hmget(conn, GLOBAL_KEY + ":channels", key);
  },

  getPluginsInfo: async () => {
    let conn = await MysqlManager.getConnection(); // Changed to use MysqlManager.getConnection()
    return MysqlManager.hgetall(conn, PLUGINS);
  },

  getOAM: async oam => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, OAM_KEY)
      .then(restoreDataTypes.bind(null));
  },

  getOamOID: async oid => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, OAM_RESOURCE)
      .then(restoreDataTypes.bind(null));
  },
  getInfluxConfig: async kpi => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, INFLUX_KEY)
      .then(restoreDataTypes.bind(null));
  },

  getGrafanaConfig: async kpi => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.hgetall(conn, GRAFANA_KEY)
      .then(restoreDataTypes.bind(null));
  },

  /**
   *  Updates the quietTime module Info
   *  @param {String} moduleName
   *  @return {JSONObject} updated module info
   **/
  delQuietTimeInfo: async (ruleType, ruleName) => {
    let conn = await MysqlManager.getConnection();
    return MysqlManager.del(conn, COMPONENT_KEY + ":quietTime:" + ruleType + ":" + ruleName);
  }
};


/////////////////////////////////////////////////////////////////
// Internal helper functions
/////////////////////////////////////////////////////////////////

/**
 *   Reads entries for a given redis kay.
 *   Iterates all hashes and and sub-keys and
 *   accummulates their fields into a nested json object.
 **/
async function getSectionX(section) {
  let accumulator = {
    basePath: section,
    basePathCount: section.split(":").length,
    data: {}
  };

  // get all the matching paths under this section
  // i.e. "redis_cli keys *"
  let sectionWild = section + ":*";
  let conn = await MysqlManager.getConnection();
  return MysqlManager.keys(conn, sectionWild)
    .then((patterns) => {
      // iterate through all patterns, accummulating their hashes
      // wherever found.
      return BPromise.reduce(patterns, readPathStream, accumulator);
    })
    .then(() => { return MysqlManager.hgetall(conn, section) })
    .then((result) => { _.extend(accumulator.data, result); return accumulator.data; })
}

function readPathStream(accumulator, pathKey) {

  // Split the pathKey into components separated by colon (:)
  // and remove all components that are part of basepath.
  let pathItems = [];
  let pathPos = accumulator.basePath.length + 1;
  while ((pathPos = pathKey.indexOf(":", pathPos + 1)) !== -1) {
    pathItems.push(pathKey.substring(0, pathPos))
  }
  pathItems.push(pathKey);

  // For each path, accummulate hashes
  return BPromise.reduce(pathItems, readPathHash, accumulator)
    .then((result) => { return accumulator; });
}


function readPathHash(accumulator, path) {
  return MysqlManager.getConnection().then((conn) => {
    return MysqlManager.hgetall(conn, path)
      .then((result) => {
        if (result === null) return accumulator;
        let rhsPath = path.substring(accumulator.basePath.length + 1);
        let leafNode = createPath(accumulator.data, rhsPath);
        _.extend(leafNode, result);
        return accumulator;
      })
  })
}

function createPath(src, pathString) {

  let tmp = src;
  let pathArray = pathString.split(":");
  pathArray.map((entry) => {
    if (!tmp[entry]) tmp[entry] = {};
    tmp = tmp[entry];
  });

  return tmp;
}

/**
 * Converts stringified data (due to redis storage) back to original.
 *  -- number strings to numbers,
 *  -- "true" to true, "false" to false
 *
 **/
function restoreDataTypes(data) {
  for (let key in data) {
    if (data[key] === "true") data[key] = true;
    else if (data[key] === "false") data[key] = false;
    else if (typeof (data[key]) === "object") restoreDataTypes(data[key]);
    //TODO: convert number strings to numbers
  }
  return data;
}
