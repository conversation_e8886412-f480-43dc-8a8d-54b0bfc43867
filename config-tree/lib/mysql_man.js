const mysql = require("mysql2/promise");
var connectionObject = null;


module.exports = {
  getConnection: async function () {
    if (connectionObject == null) {
      let connection = await mysql.createConnection({
        host: process.env.LEAP_CONFIG_HOST_MYSQL,
        user: process.env.LEAP_CONFIG_USER_MYSQL,
        database: process.env.LEAP_CONFIG_DATABASE_MYSQL,
        password: process.env.LEAP_CONFIG_PASSWORD_MYSQL
      });
      connectionObject = connection;
      return connection;
    }
    else {
      return connectionObject;
    }
  },

  hgetall: async function (connection, hashKey) {
    let query = `select * from leapConfigurations where hashKey='${hashKey}'`;
    let queryResult = await connection.query(query);
    let actualQueryResult = queryResult[0];
    let resultJson = {};
    actualQueryResult.forEach((row, id) => {
      resultJson[row.subkey] = row.value;
    });
    return resultJson;
  },

  hmget: async function (connection, hashKey, subkey) {
    let query = `select * from leapConfigurations where hashKey='${hashKey}' and subkey='${subkey}'`;
    let queryResult = await connection.query(query);
    let actualQueryResult = queryResult[0];
    let resultJson = {};
    actualQueryResult.forEach((row, id) => {
      resultJson[row.subkey] = row.value;
    });
    return resultJson[subkey];
  },

  hmsetv2: async function (connection, hashKey, subkey, value) {
    let query = `INSERT INTO leapConfigurations VALUES ('${hashKey}','${subkey}', '${value}' ) ON DUPLICATE KEY UPDATE value='${value}'`;
    await connection.query(query);
    return 1;
  },

  hmset: async function (connection, hashKey, valueJson) {
    console.log("Came here to execute:" + hashKey);
    let keys = Object.keys(valueJson);
    await Promise.all(keys.map(async (key) => {
      let value = valueJson[key];
      let query = `INSERT INTO leapConfigurations VALUES ('${hashKey}','${key}', '${value}' ) ON DUPLICATE KEY UPDATE value='${value}'`;
      await connection.query(query);
      return 1;
    }));
  },

  keys: async function (connection, keyExpression) {
    let query;
    if (keyExpression[0] == '*' && keyExpression[keyExpression.length - 1] == "*") {
      keyExpression = keyExpression.slice(1, keyExpression.length - 1);
      query = `select hashKey from leapConfigurations where hashKey regexp '${keyExpression}'`;
    }
    else if (keyExpression[0] == '*') {
      keyExpression = keyExpression.slice(1, keyExpression.length) + "$";
      query = `select hashKey from leapConfigurations where hashKey regexp '${keyExpression}'`;
    }
    else if (keyExpression[keyExpression.length - 1] == "*") {
      keyExpression = "^" + keyExpression.slice(0, keyExpression.length - 1);
      query = `select hashKey from leapConfigurations where hashKey regexp '${keyExpression}'`;
    }
    else {
      query = `select hashKey from leapConfigurations where hashKey = '${keyExpression}'`;
    }
    let queryResult = await connection.query(query);
    let actualQueryResult = queryResult[0];
    let resultJson = {};
    let keys = []
    actualQueryResult.forEach((row, id) => {
      if (!(keys.includes(row.hashKey))) {
        keys.push(row.hashKey);
      }
    });
    return keys;
  },

  close: async function (connection) {
    await connection.close();
    return 1;
  },

  flushdb: async function (connection) {
    let query = `truncate table leapConfigurations`;
    let queryResult = await connection.query(query);
    return 1;
  },

  getReloadedConfigs: async function (connection) {
    let query = `select * from reloadedConfigs`;
    let queryResult = await connection.query(query);
    let actualQueryResult = queryResult[0];
    let resultJson = {};
    actualQueryResult.forEach((row, id) => {
      resultJson[row.subkey] = row.value;
    });
    return actualQueryResult;
  },

  flushReloadTable: async function (connection) {
    let query = `truncate table reloadedConfigs`;
    let queryResult = await connection.query(query);
    return 1;
  }
}