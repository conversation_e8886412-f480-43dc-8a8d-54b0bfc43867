/**
 *    Defines the FSM (Finite State Machine) tables for
 *    all message categories of ECP delivery system.
 *
 **/
module.exports = {
  // FSM for sms message processing.
  sms: {
    /* FSM routing for promo category message */
    "promo": {
      init: { success: "dndFilter", failure: "accRefunder" },
      dndFilter: { success: "blklistFilter", failure: "accRefunder" },
      blklistFilter: { success: "cntFilter", failure: "accRefunder" },
      cntFilter: { success: "cpUpdater", failure: "accRefunder" },
      cpUpdater: { success: "quietTimeFilter", failure: "accRefunder" },
      quietTimeFilter: { success: "smsChannel", failure: "accRefunder" },
      smsChannel: { success: "end", failure: "cpReverter" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "finalizer", submit_failure: "cpReverter" },
      cpReverter: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "drDispatcher", failure: "drDispatcher" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "promo.b.1": {
      init: { success: "dndFilter", failure: "aggregator" },
      dndFilter: { success: "blklistFilter", failure: "aggregator" },
      blklistFilter: { success: "cntFilter", failure: "aggregator" },
      cntFilter: { success: "jobExpander", failure: "aggregator" },
      jobExpander: { success: "cpUpdater", failure: "aggregator" },
      cpUpdater: { success: "quietTimeFilter", failure: "aggregator" },
      quietTimeFilter: { success: "smsChannel", failure: "aggregator" },
      smsChannel: { success: "end", failure: "cpReverter" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "aggregator", submit_failure: "cpReverter" },
      cpReverter: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "drDispatcher", failure: "drDispatcher" },
      drDispatcher: { success: "ftpJobUpdater", failure: "ftpJobUpdater" },
      ftpJobUpdater: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "promo.b.2": {
      init: { success: "dndFilter", failure: "aggregator" },
      dndFilter: { success: "blklistFilter", failure: "aggregator" },
      blklistFilter: { success: "jobExpander", failure: "aggregator" },
      jobExpander: { success: "cntFilter", failure: "aggregator" },
      cntFilter: { success: "cpUpdater", failure: "aggregator" },
      cpUpdater: { success: "quietTimeFilter", failure: "aggregator" },
      quietTimeFilter: { success: "smsChannel", failure: "aggregator" },
      smsChannel: { success: "end", failure: "cpReverter" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "aggregator", submit_failure: "cpReverter" },
      cpReverter: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "drDispatcher", failure: "drDispatcher" },
      drDispatcher: { success: "ftpJobUpdater", failure: "ftpJobUpdater" },
      ftpJobUpdater: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "otp": {
      init: { success: "smsChannel", failure: "accRefunder" },
      smsChannel: { success: "end", failure: "accRefunder" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "finalizer", submit_failure: "accRefunder" },
      accRefunder: { success: "drDispatcher", failure: "drDispatcher" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "txn": {
      init: { success: "smsChannel", failure: "accRefunder" },
      smsChannel: { success: "end", failure: "accRefunder" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "finalizer", submit_failure: "accRefunder" },
      accRefunder: { success: "drDispatcher", failure: "drDispatcher" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "content": {
      init: { success: "dndFilter", failure: "accRefunder" },
      dndFilter: { success: "blklistFilter", failure: "accRefunder" },
      blklistFilter: { success: "cntFilter", failure: "accRefunder" },
      cntFilter: { success: "subsCharger", failure: "accRefunder" },
      subsCharger: { success: "cpUpdater", failure: "accRefunder" },
      cpUpdater: { success: "smsChannel", failure: "accRefunder" },
      smsChannel: { success: "end", failure: "cpReverter" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "finalizer", submit_failure: "cpReverter" },
      cpReverter: { success: "accRefunder", failure: "finalizer" },
      accRefunder: { success: "subsRefunder", failure: "subsRefunder" },
      subsRefunder: { success: "drDispatcher", failure: "drDispatcher" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "content.b.1": {
      init: { success: "dndFilter", failure: "aggregator" },
      dndFilter: { success: "blklistFilter", failure: "aggregator" },
      blklistFilter: { success: "cntFilter", failure: "aggregator" },
      cntFilter: { success: "jobExpander", failure: "aggregator" },
      jobExpander: { success: "subsCharger", failure: "aggregator" },
      subsCharger: { success: "cpUpdater", failure: "aggregator" },// subsCharger === MT Billing Handler
      cpUpdater: { success: "smsChannel", failure: "aggregator" },
      smsChannel: { success: "end", failure: "cpReverter" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "aggregator", submit_failure: "cpReverter" },
      cpReverter: { success: "subsRefunder", failure: "subsRefunder" },
      subsRefunder: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "drDispatcher", failure: "drDispatcher" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "content.b.2": {
      init: { success: "dndFilter", failure: "aggregator" },
      dndFilter: { success: "blklistFilter", failure: "aggregator" },
      blklistFilter: { success: "jobExpander", failure: "aggregator" },
      jobExpander: { success: "cntFilter", failure: "aggregator" },
      cntFilter: { success: "subsCharger", failure: "aggregator" },
      subsCharger: { success: "cpUpdater", failure: "aggregator" },
      cpUpdater: { success: "smsChannel", failure: "aggregator" },
      smsChannel: { success: "end", failure: "cpReverter" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "aggregator", submit_failure: "cpReverter" },
      cpReverter: { success: "subsRefunder", failure: "subsRefunder" },
      subsRefunder: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "drDispatcher", failure: "drDispatcher" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "iod": {
      moTransRouter: { success: "moGateway", failure: "moGateway" },
      moGateway: { success: "moDBill", failure: "finalizer" },
      moDBill: { success: "moReqHandler", failure: "finalizer" },
      moReqHandler: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "subs": {
      moTransRouter: { success: "moGateway", failure: "moGateway" },
      moGateway: { success: "moDBill", failure: "finalizer" },
      moDBill: { success: "mosubsHandler", failure: "finalizer" },
      mosubsHandler: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "dnd": {
      moTransRouter: { success: "moGateway", failure: "moGateway" },
      moGateway: { success: "modndHandler", failure: "finalizer" },
      modndHandler: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "notification": {
      init: { success: "smsChannel", failure: "finalizer" },
      smsChannel: { success: "end", failure: "finalizer" },
      moTransRouter: { dr_inbox: "finalizer", submit_success: "finalizer", submit_failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "moresponse": {
      init: { success: "smsChannel", failure: "accRefunder" },
      smsChannel: { success: "end", failure: "accRefunder" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "finalizer", submit_failure: "accRefunder" },
      accRefunder: { success: "subsRefunder", failure: "subsRefunder" },
      subsRefunder: { success: "drDispatcher", failure: "drDispatcher" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    /* FSM routing for emergency category message */
    "emergency": {
      init: { success: "smsChannel", failure: "accRefunder" },
      smsChannel: { success: "end", failure: "accRefunder" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "finalizer", submit_failure: "accRefunder" },
      accRefunder: { success: "drDispatcher", failure: "drDispatcher" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "emergency.b.1": {
      init: { success: "jobExpander", failure: "aggregator" },
      jobExpander: { success: "smsChannel", failure: "aggregator" },
      smsChannel: { success: "end", failure: "aggregator" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "aggregator", submit_failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "drDispatcher", failure: "drDispatcher" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },

    "emergency.b.2": {
      init: { success: "jobExpander", failure: "aggregator" },
      jobExpander: { success: "smsChannel", failure: "aggregator" },
      smsChannel: { success: "end", failure: "aggregator" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "aggregator", submit_failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "drDispatcher", failure: "drDispatcher" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    }
  }, // End of SMS

  mms: {
    "promo": {
      init: { success: "dndFilter", failure: "accRefunder" },
      dndFilter: { success: "blklistFilter", failure: "accRefunder" },
      blklistFilter: { success: "cpUpdater", failure: "accRefunder" },
      cpUpdater: { success: "quietTimeFilter", failure: "accRefunder" },
      quietTimeFilter: { success: "mmsDispatcher", failure: "accRefunder" },
      mmsDispatcher: { success: "finalizer", failure: "cpReverter" },
      cpReverter: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "promo.b.1": {
      init: { success: "dndFilter", failure: "aggregator" },
      dndFilter: { success: "blklistFilter", failure: "aggregator" },
      blklistFilter: { success: "jobExpander", failure: "aggregator" },
      jobExpander: { success: "cpUpdater", failure: "aggregator" },
      cpUpdater: { success: "quietTimeFilter", failure: "aggregator" },
      quietTimeFilter: { success: "mmsDispatcher", failure: "aggregator" },
      mmsDispatcher: { success: "aggregator", failure: "cpReverter" },
      cpReverter: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "promo.b.2": {
      init: { success: "dndFilter", failure: "aggregator" },
      dndFilter: { success: "blklistFilter", failure: "aggregator" },
      blklistFilter: { success: "jobExpander", failure: "aggregator" },
      jobExpander: { success: "cpUpdater", failure: "aggregator" },
      cpUpdater: { success: "quietTimeFilter", failure: "aggregator" },
      quietTimeFilter: { success: "mmsDispatcher", failure: "aggregator" },
      mmsDispatcher: { success: "aggregator", failure: "cpReverter" },
      cpReverter: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "otp": {
      init: { success: "mmsDispatcher", failure: "accRefunder" },
      mmsDispatcher: { success: "finalizer", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "txn": {
      init: { success: "mmsDispatcher", failure: "accRefunder" },
      mmsDispatcher: { success: "finalizer", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "content": {
      init: { success: "dndFilter", failure: "accRefunder" },
      dndFilter: { success: "blklistFilter", failure: "accRefunder" },
      blklistFilter: { success: "cpUpdater", failure: "accRefunder" },
      cpUpdater: { success: "subsCharger", failure: "accRefunder" },
      subsCharger: { success: "mmsDispatcher", failure: "accRefunder" },
      mmsDispatcher: { success: "finalizer", failure: "cpReverter" },
      cpReverter: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "content.b.1": {
      init: { success: "dndFilter", failure: "accRefunder" },
      dndFilter: { success: "blklistFilter", failure: "accRefunder" },
      blklistFilter: { success: "jobExpander", failure: "accRefunder" },
      jobExpander: { success: "cpUpdater", failure: "aggregator" },
      cpUpdater: { success: "subsCharger", failure: "accRefunder" },
      subsCharger: { success: "mmsDispatcher", failure: "accRefunder" },
      mmsDispatcher: { success: "finalizer", failure: "cpReverter" },
      cpReverter: { success: "subsRefunder", failure: "subsRefunder" },
      subsRefunder: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "content.b.2": {
      init: { success: "dndFilter", failure: "accRefunder" },
      dndFilter: { success: "blklistFilter", failure: "accRefunder" },
      blklistFilter: { success: "jobExpander", failure: "accRefunder" },
      jobExpander: { success: "cpUpdater", failure: "aggregator" },
      cpUpdater: { success: "subsCharger", failure: "accRefunder" },
      subsCharger: { success: "mmsDispatcher", failure: "accRefunder" },
      mmsDispatcher: { success: "finalizer", failure: "cpReverter" },
      cpReverter: { success: "subsRefunder", failure: "subsRefunder" },
      subsRefunder: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "iod": {
      init: { success: "moGateway", failure: "moGateway" },
      moGateway: { success: "moDBill", failure: "finalizer" },
      moDBill: { success: "moReqHandler", failure: "finalizer" },
      moReqHandler: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "subs": {
      init: { success: "moGateway", failure: "moGateway" },
      moGateway: { success: "moDBill", failure: "finalizer" },
      moDBill: { success: "mosubsHandler", failure: "finalizer" },
      mosubsHandler: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "dnd": {
      init: { success: "moGateway", failure: "moGateway" },
      moGateway: { success: "modndHandler", failure: "finalizer" },
      modndHandler: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "notification": {
      init: { success: "mmsDispatcher", failure: "finalizer" },
      mmsDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "moresponse": {
      init: { success: "mmsDispatcher", failure: "finalizer" },
      mmsDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    }


  }, // END of MMS

  ussd: {
    "promo": {
      init: { success: "dndFilter", failure: "accRefunder" },
      dndFilter: { success: "blklistFilter", failure: "accRefunder" },
      blklistFilter: { success: "cntFilter", failure: "accRefunder" },
      cntFilter: { success: "cpUpdater", failure: "accRefunder" },
      cpUpdater: { success: "quietTimeFilter", failure: "accRefunder" },
      quietTimeFilter: { success: "ussdDispatcher", failure: "accRefunder" },
      ussdDispatcher: { success: "finalizer", failure: "cpReverter" },
      cpReverter: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "promo.b.1": {
      init: { success: "dndFilter", failure: "aggregator" },
      dndFilter: { success: "blklistFilter", failure: "aggregator" },
      blklistFilter: { success: "cntFilter", failure: "aggregator" },
      cntFilter: { success: "jobExpander", failure: "aggregator" },
      jobExpander: { success: "cpUpdater", failure: "aggregator" },
      cpUpdater: { success: "quietTimeFilter", failure: "aggregator" },
      quietTimeFilter: { success: "ussdDispatcher", failure: "aggregator" },
      ussdDispatcher: { success: "aggregator", failure: "cpReverter" },
      cpReverter: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "promo.b.2": {
      init: { success: "dndFilter", failure: "aggregator" },
      dndFilter: { success: "blklistFilter", failure: "aggregator" },
      blklistFilter: { success: "jobExpander", failure: "aggregator" },
      jobExpander: { success: "cntFilter", failure: "aggregator" },
      cntFilter: { success: "cpUpdater", failure: "aggregator" },
      cpUpdater: { success: "quietTimeFilter", failure: "aggregator" },
      quietTimeFilter: { success: "ussdDispatcher", failure: "aggregator" },
      ussdDispatcher: { success: "aggregator", failure: "cpReverter" },
      cpReverter: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "otp": {
      init: { success: "ussdDispatcher", failure: "accRefunder" },
      ussdDispatcher: { success: "finalizer", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "txn": {
      init: { success: "ussdDispatcher", failure: "accRefunder" },
      ussdDispatcher: { success: "finalizer", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "content": {
      init: { success: "dndFilter", failure: "accRefunder" },
      dndFilter: { success: "blklistFilter", failure: "accRefunder" },
      blklistFilter: { success: "cntFilter", failure: "accRefunder" },
      cntFilter: { success: "cpUpdater", failure: "accRefunder" },
      cpUpdater: { success: "subsCharger", failure: "accRefunder" },
      subsCharger: { success: "ussdDispatcher", failure: "accRefunder" },
      ussdDispatcher: { success: "finalizer", failure: "cpReverter" },
      cpReverter: { success: "subsRefunder", failure: "subsRefunder" },
      subsRefunder: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "content.b.1": {
      init: { success: "dndFilter", failure: "accRefunder" },
      dndFilter: { success: "blklistFilter", failure: "accRefunder" },
      blklistFilter: { success: "cntFilter", failure: "accRefunder" },
      cntFilter: { success: "jobExpander", failure: "accRefunder" },
      jobExpander: { success: "cpUpdater", failure: "aggregator" },
      cpUpdater: { success: "subsCharger", failure: "accRefunder" },
      subsCharger: { success: "ussdDispatcher", failure: "accRefunder" },
      ussdDispatcher: { success: "finalizer", failure: "cpReverter" },
      cpReverter: { success: "subsRefunder", failure: "subsRefunder" },
      subsRefunder: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "content.b.2": {
      init: { success: "dndFilter", failure: "accRefunder" },
      dndFilter: { success: "blklistFilter", failure: "accRefunder" },
      blklistFilter: { success: "jobExpander", failure: "accRefunder" },
      jobExpander: { success: "cntFilter", failure: "aggregator" },
      cntFilter: { success: "cpUpdater", failure: "aggregator" },
      cpUpdater: { success: "subsCharger", failure: "accRefunder" },
      subsCharger: { success: "ussdDispatcher", failure: "accRefunder" },
      ussdDispatcher: { success: "finalizer", failure: "cpReverter" },
      cpReverter: { success: "subsRefunder", failure: "subsRefunder" },
      subsRefunder: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "iod": {
      init: { success: "moGateway", failure: "moGateway" },
      moGateway: { success: "moDBill", failure: "finalizer" },
      moDBill: { success: "moReqHandler", failure: "finalizer" },
      moReqHandler: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "subs": {
      init: { success: "moGateway", failure: "moGateway" },
      moGateway: { success: "moDBill", failure: "finalizer" },
      moDBill: { success: "mosubsHandler", failure: "finalizer" },
      mosubsHandler: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "dnd": {
      init: { success: "moGateway", failure: "moGateway" },
      moGateway: { success: "modndHandler", failure: "finalizer" },
      modndHandler: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "notification": {
      init: { success: "ussdDispatcher", failure: "finalizer" },
      ussdDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "moresponse": {
      init: { success: "ussdDispatcher", failure: "finalizer" },
      ussdDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    }
  }, // END of USSD
  "email": {
    "promo": {
      init: { success: "dndFilter", failure: "accRefunder" },
      dndFilter: { success: "emailDispatcher", failure: "accRefunder" },
      emailDispatcher: { success: "finalizer", failure: "smsChannel" },
      smsChannel: { success: "end", failure: "accRefunder" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "finalizer", submit_failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "promo.b.1": {
      init: { success: "dndFilter", failure: "aggregator" },
      dndFilter: { success: "jobExpander", failure: "aggregator" },
      jobExpander: { success: "emailDispatcher", failure: "aggregator" },
      emailDispatcher: { success: "aggregator", failure: "smsChannel" },
      smsChannel: { success: "end", failure: "aggregator" },
      moTransRouter: { dr_inbox: "drDispatcher", submit_success: "aggregator", submit_failure: "aggregator" },
      aggregator: { success: "accRefunder", failure: "accRefunder" },
      accRefunder: { success: "finalizer", failure: "finalizer" },
      drDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "notification.b.1": {
      init: { success: "jobExpander", failure: "aggregator" },
      jobExpander: { success: "emailDispatcher", failure: "aggregator" },
      emailDispatcher: { success: "aggregator", failure: "aggregator" },
      aggregator: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    },
    "notification": {
      init: { success: "emailDispatcher", failure: "finalizer" },
      emailDispatcher: { success: "finalizer", failure: "finalizer" },
      finalizer: { success: "end", failure: "end" }
    }
  }
};
