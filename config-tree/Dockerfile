# Use Node.js 22 slim version as the base image
FROM node:22-bookworm-slim

# Set the working directory in the Docker container
WORKDIR /home/<USER>/app

# Copy all files in the current directory to the Docker container
COPY . .

# Change the owner of the app files to the node user
RUN chown -R node:node /home/<USER>/app

# Change permissions of the app directory to ensure all files are executable
RUN chmod 755 -R /home/<USER>/app

# Switch to the node user instead of root
USER node

# Set environment variables
ENV HOST=0.0.0.0 PORT=3000

# Expose the port that the application will run on
EXPOSE ${PORT}

# Set the working directory to the bin directory within the app directory
WORKDIR /home/<USER>/app/bin

# Run the Node.js command line tool with the specified options
CMD ["node", "config-cli.js", "-t", "1", "-F", "true", "-f", "/home/<USER>/app/bin/configs.json"]
