{"name": "config-tree", "version": "3.0.7", "description": "One and only awesome configuration manager for all config management needs!", "main": "./lib/ConfigProxy.js", "scripts": {"test": "mocha test"}, "keywords": ["config"], "publishConfig": {"registry": "http://cots.comviva.com/api/npm/Comviva-Npm-Repo/"}, "author": "<EMAIL>", "license": "ISC", "devDependencies": {"chai": "^4.2.0"}, "bin": {"config-tree": "./bin/config-cli.js"}, "preferGlobal": true, "dependencies": {"bluebird": "^3.7.2", "chai-as-promised": "^7.1.1", "colors": "^1.4.0", "commander": "^5.1.0", "ioredis": "^4.17.3", "md5": "^2.3.0", "mysql": "^2.18.1", "mysql2": "^2.3.3", "notation": "^1.3.6", "redis-dump": "^0.1.10", "underscore": "^1.10.2", "charenc": "0.0.2", "crypt": "0.0.2"}}