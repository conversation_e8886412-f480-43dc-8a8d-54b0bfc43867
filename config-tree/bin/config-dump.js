#!/usr/bin/env node
/**
 *  A simple utility to dump the configurations from Redis to JSON file
 **/

"use strict";
require("colors");
const commander = require("commander");
const dump = require("redis-dump");
const Notation = require('notation');
const fs = require("fs");
const path = require("path");

commander
  .version(require("../package.json").version)
  .option("-H, --host [host]", "Config server hostname", process.env.CONFIG_HOST || "127.0.0.1")
  .option("-p, --port [port]", "Config server port", process.env.CONFIG_PORT || 6380)
  .option("-n, --db [db]", "Config server Database to be selected", process.env.CONFIG_DB || 0)
  .option("-a, --auth [auth]", "Config server auth password", process.env.CONFIG_AUTH || undefined)
  .option("-f, --json [json]", "JSON filename to save the Output result", "console")
  .option("-q, --filter [filter]", "Query filter", "system:settings:*")
  .parse(process.argv);

let args = commander.opts();
Object.keys(args).forEach(key => {
  if (args[key] == null)
    delete args[key];
});
args.format = "json";
dump(args,
  (err, result) => {
    try {
      if (!err) {
        const notation = new Notation({ system: { settings: {} } });
        result = JSON.parse(result);
        Object.keys(result).forEach(key => {
          let arr = key.split(":");
          if (!isNaN(arr.slice(-1))) {
            result[key].value = { [arr.slice(-1)]: result[key].value };
            arr.splice(-1);
          }
          let value = vnormalize(result[key].value);
          notation.set(arr.join("."), value);
        });
        if (args.json != "console") {
          fs.writeFileSync(path.resolve(args.json), JSON.stringify(notation.value, null, 2));
        } else {
          console.log(JSON.stringify(notation.value, null, 2));
        }
      }
    } catch (error) {
      console.error(error);
    }
  });

function vnormalize(value) {
  let keys = Object.keys(value);
  let array = [];
  let isArray = true;
  for (let i = 0; i < keys.length; i++) {
    if (isNaN(keys[i])) {
      isArray = false;
      break;
    } else {
      array.push(value[keys[i]]);
    }
  }
  if (isArray) value = array;
  return value;
}