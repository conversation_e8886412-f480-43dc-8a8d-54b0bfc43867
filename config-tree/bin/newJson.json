{"system": {"settings": {"components": {"acl_manager": {"host": "*************", "port": "9012", "contextPath": "/ussd", "protocol": "https", "clusterCPUs": "1", "file_upload": {"minFreeDiskSpace": "1", "maxSessions": "5", "supportedExtentions": "csv,txt", "source": "/data/leap/files", "processed": "/data/leap/files/processed", "error": "/data/leap/files/error", "limits": {"fileSize": "2", "files": "10"}}, "log": {"cdr": "acl_manager_access", "cdrFormat": ":remote-addr :remote-user [:date[iso]] :username :method :url HTTP/:http-version :event :status :res[content-length] :response-time", "logdir": "/data/leap/logs", "log4js": {"appenders": {"acl_manager": {"type": "dateFile", "filename": "acl_manager.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "[%d{ISO8601}] [%p] %m"}}, "console": {"type": "console", "pattern": "[%d{ISO8601}] [%p] %m"}}, "categories": {"acl_manager": {"appender": "acl_manager", "level": "ERROR"}, "default": {"appender": "acl_manager", "level": "ERROR"}}}}, "queue": {"host": "redis-6384.ngstudio.svc.cluster.local", "port": "6384", "db": "0", "password": "leap123", "log_key": "LEAP_CDRQ", "limit": "100000"}}, "api_gw": {"host": "api-gw.ngstudio.svc.cluster.local", "port": "9001", "contextPath": "/leap_gw", "protocol": "https", "captureAppSnapshot": "true", "isCollaborativeAppDevelopmentEnabled": "true", "appsApi": {"defaultSortField": "name", "defaultPageSize": "50", "defaultSortOrder": "asc", "expressionAppName": "^[a-zA-Z0-9]{1,}[a-zA-Z0-9_ ]*$", "expressionAppStates": "^[0-9]{1,2}(,[0-9]{1,2})+$", "maxLengthAppName": "36", "maxLengthAppDesc": "128", "userActivationLink": "https://*************:9000/leap/verify-otp", "allowedSortFields": {"id": "id", "name": "name", "status": "status", "createdAt": "createdAt", "updatedAt": "updatedAt"}, "allowedSortOrder": {"asc": "asc", "desc": "desc"}, "configApis": {"/leap_gw/configapi/passwordstrength": "Get the regular expression to validate the Strength of Password", "/leap_gw/configapi/appstates": "Get App Status Code information", "/leap_gw/configapi/macros": "Get list of MACROS supported in platform", "/leap_gw/configapi/operators": "Get list of Operators supported in platform", "/leap_gw/configapi/languages": "Get list of Languges supported in platform", "/leap_gw/configapi/simulationinfo": "Get the App simulation settings.", "/leap_gw/configapi/userstatuses": "Gets User Status information", "/leap_gw/configapi/userActivationLink": "User activation callback URL"}, "passwordstrength": {"expression": "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})", "description": "The password string must contain at least 1 lowercase alphabetical character, must contain at least 1 uppercase alphabetical character, must contain at least 1 numeric character, must contain at least one special character, but we are escaping reserved RegEx characters to avoid conflict, The string must be 8 characters or longer"}, "simulationinfo": {"app_url": "http://*************:9004/app_engine/development/", "websocket_url": "ws://*************:9004"}}, "instances": [{"host": "*************", "port": "9001"}], "log": {"cdr": "apigw_access", "cdrFormat": "[:date[iso]] :transactionId :process :remote-addr :remote-user :username :method :url HTTP/:http-version :status :res[content-length] :response-time :referrer :event", "logdir": "/data/leap/logs/", "log4js": {"appenders": {"apigw": {"type": "dateFile", "filename": "api_gw.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "[%d{ISO8601}] [%p] %m"}}, "console": {"type": "console"}}, "categories": {"apigw": {"appender": "apigw", "level": "ERROR"}, "default": {"appender": "apigw", "level": "TRACE"}}}}, "myDownloads": {"downloadDirectory": "/data/leap/downloads"}, "sessionCache": {"host": "redis-6382.ngstudio.svc.cluster.local", "port": "6382", "password": "leap123", "sessionKey": "sessionJWT", "cleanupDuration": "1d"}}, "app_engine": {"defaultLanguage": "en", "userPreferenceStoreCheck": "false", "menuNavigationEnable": "false", "menuHelpEnable": "false", "sessionCleanKey": "clean-session", "commonMenuNavigation": "true", "app_cache": {"filter": {"development": "0,1,2,5,6,8", "staging": "0,1,2,5,6,8", "production": "0,1,2,5,6,8"}}, "cdrqueue": {"host": "redis-6384.ngstudio.svc.cluster.local", "port": "6384", "db": "0", "password": "leap123", "log_key": "LEAP_CDRQ", "limit": "100000", "fallback_cdr_path": "/data/leap/logs"}, "plugin_exec": {"thresholdValue": "200", "client": {"maxSockets": "20", "keepAlive": "true", "maxFreeSockets": "20"}}, "sessionstore": {"host": "redis-6381.ngstudio.svc.cluster.local", "port": "6381", "db": "0", "password": "leap123", "timeout": "600", "delta": "5", "cachePolicy": "redis", "compression_required": "false"}}, "app_engine_development": {"host": "*************", "port": "9002", "contextPath": "/app_engine/development", "protocol": "https", "clusterCPUs": "1", "instances": [{"host": "*************", "port": "9002"}], "interfaces": {"httpJSON": {"enabled": "false"}, "httpXML": {"enabled": "false", "contextPathSuffix": "xml"}, "smpp": {"enabled": "false", "port": "2776", "delay_between_submitsm": "5000", "accounts": {"leap": {"password": "leap123", "max_connections": "10", "status": "true", "enquire_link": "false", "enquire_link_interval": "60000"}}}}, "log": {"cdr": "app_engine_access", "cdrFormat": ":remote-addr :remote-user [:date[iso]] :username :method :url HTTP/:http-version :status :res[content-length] :response-time", "logdir": "/data/leap/logs/", "log4js": {"appenders": {"app_engine": {"type": "dateFile", "filename": "app_engine_development.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "%d{ISO8601}|%p|%m"}}, "console": {"type": "console", "pattern": "%d{ISO8601}|%p|%m"}, "pluginCDR": {"type": "dateFile", "filename": "plugin_dev_exec.cdr", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "%d{ISO8601}|%m"}}, "smpp_events": {"type": "dateFile", "filename": "smpp_events_development.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "%d{ISO8601}|%m"}}}, "categories": {"app_engine": {"appender": "app_engine", "level": "ERROR"}, "default": {"appender": "app_engine", "level": "TRACE"}, "pluginCDR": {"appender": "pluginCDR", "level": "TRACE"}, "smpp_events": {"appender": "smpp_events", "level": "ERROR"}}}}}, "app_engine_production": {"host": "ngagecpaas.com", "port": "9004", "contextPath": "/app_engine/production", "protocol": "http", "clusterCPUs": "1", "instances": [{"host": "*************", "port": "9004"}], "interfaces": {"httpJSON": {"enabled": "false"}, "httpXML": {"enabled": "false", "contextPathSuffix": "xml"}, "smpp": {"enabled": "false", "port": "2778", "delay_between_submitsm": "5000", "accounts": {"leap": {"password": "leap123", "max_connections": "10", "status": "true", "enquire_link": "false", "enquire_link_interval": "60000"}}}}, "log": {"cdr": "app_engine_access", "cdrFormat": ":remote-addr :remote-user [:date[iso]] :username :method :url HTTP/:http-version :status :res[content-length] :response-time", "logdir": "/data/leap/logs/", "log4js": {"appenders": {"app_engine": {"type": "dateFile", "filename": "app_engine.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "%d{ISO8601}|%p|%m"}}, "console": {"type": "console", "pattern": "%d{ISO8601}|%p|%m"}, "pluginCDR": {"type": "dateFile", "filename": "plugin_exec.cdr", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "%d{ISO8601}|%m"}}, "smpp_events": {"type": "dateFile", "filename": "smpp_events.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "%d{ISO8601}|%m"}}}, "categories": {"app_engine": {"appender": "app_engine", "level": "ERROR"}, "default": {"appender": "app_engine", "level": "TRACE"}, "pluginCDR": {"appender": "pluginCDR", "level": "TRACE"}, "smpp_events": {"appender": "smpp_events", "level": "ERROR"}}}}}, "app_engine_staging": {"host": "*************", "port": "9003", "contextPath": "/app_engine/staging", "protocol": "http", "clusterCPUs": "1", "instances": [{"host": "*************", "port": "9003"}], "interfaces": {"httpJSON": {"enabled": "false"}, "httpXML": {"enabled": "false", "contextPathSuffix": "xml"}, "smpp": {"enabled": "false", "port": "2777", "delay_between_submitsm": "5000", "accounts": {"leap": {"password": "leap123", "max_connections": "10", "status": "true", "enquire_link": "false", "enquire_link_interval": "60000"}}}}, "log": {"cdr": "app_engine_access", "cdrFormat": ":remote-addr :remote-user [:date[iso]] :username :method :url HTTP/:http-version :status :res[content-length] :response-time", "logdir": "/data/leap/logs/", "log4js": {"appenders": {"app_engine": {"type": "dateFile", "filename": "app_engine_staging.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "%d{ISO8601}|%p|%m"}}, "console": {"type": "console", "pattern": "%d{ISO8601}|%p|%m"}, "pluginCDR": {"type": "dateFile", "filename": "plugin_staging_exec.cdr", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "%d{ISO8601}|%m"}}, "smpp_events": {"type": "dateFile", "filename": "smpp_events_staging.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "%d{ISO8601}|%m"}}}, "categories": {"app_engine": {"appender": "app_engine", "level": "ERROR"}, "default": {"appender": "app_engine", "level": "TRACE"}, "pluginCDR": {"appender": "pluginCDR", "level": "OFF"}, "smpp_events": {"appender": "smpp_events", "level": "ERROR"}}}}}, "authServer": {"hrmsGetDetailsApi": "https://apps.ke.prod.esb.k8s.africa.airtel.itm/kong/api/employee-service/v1/employee", "host": "*************", "protocol": "http", "allowedEmailCount": "3", "maxPasswordsToBeStored": "5", "hrmsHeadersGetUserApi": "{\"headers\": {\"ASP-Consumer-Id\": \"KE_COMVIVA\",\"ASP-Locale\": \"en-US\",\"ASP-OPCO\": \"KE\",\"Content-Type\": \"application/json\",\"ASP-API-Key\": \"bb10806f-9d83-4185-a27e-973d7f0c0329\"}}", "allowedEmailDuration": "5m", "hrmsAuthenticationApi": "https://apps.mg.prod.esb.k8s.africa.airtel.itm/kong/api/user-service/v1/ldap/verify", "resetLink": "https://*************:9000/leap/verify-otp", "hlmsAuthorisation": "false", "db": "0", "activationLinkValidityPeriod": "3d", "otpValidityPeriod": "15m", "secretkey": "mysecretpassword", "hrmsHeaders": "{\"headers\": {\"ASP-Consumer-Id\": \"MG_COMVIVA\",\"ASP-Locale\": \"en-US\",\"ASP-OPCO\": \"MG\",\"Content-Type\": \"application/json\",\"ASP-API-Key\": \"7c9ffef4-e74a-47d5-8be4-782707154b26\",\"opco.code\": \"MG\"}}", "port": "9005", "refreshExpiryTime": "1h", "smtpSupport": "false", "jwtExpiryTime": "3h", "ctxPath": "/authenticator", "features": {"cache": {"enabled": "true", "options": {"cachePolicy": "redis", "redis": {"host": "*************", "port": "6380", "db": "0", "password": "leap123"}}}, "defaultRootUser": {"enabled": "true", "options": {"roleName": "root", "firstName": "root", "username": "root", "claims": "{ \"1.1\": { \"perms\": 6, \"scope\": 1 },\"*\": { \"perms\": 7, \"scope\": 2 } }", "email": "<EMAIL>", "password": "root123"}}, "email": {"enabled": "true", "options": {"host": "*************", "port": "25", "secure": "false", "authRequired": "true", "tlsRequired": "false", "sslRequired": "false", "sslPort": "587", "auth": {"user": "<EMAIL>", "pass": "oct@1234"}}}, "kpi": {"enabled": "false", "options": {"frequency": "1", "redis": {"host": "redis-6384.ngstudio.svc.cluster.local", "port": "6384", "db": "0", "password": "leap123"}}}, "ldap": {"enabled": "false", "options": {"url": "ldap://*************:389", "bindDn": "COMVIVA\\lakshmi.k", "bindCredentials": "sra@0218", "searchBase": "DC=comviva,DC=com", "filtername": "uid"}}, "lockOnInvalidLogins": {"enabled": "true", "options": {"maxInvalidLoginAttempts": "3", "userLockTime": "10"}}, "oam": {"enabled": "true", "options": {"oamServerIp": "*************", "oamServerPort": "4545", "appId": ".1", "oidMap": {"authServerStatus": [".*******.1.19338.126.5.1", "AuthServer started successfully", "AuthServer stopped"], "cacheConnectionStatus": [".*******.1.19338.126.5.2", "Redis connection established", "Redis connectivity down"], "ldapConnectionStatus": [".*******.1.19338.126.5.4", "ldap connection established", "ldap connectivity down"], "rdbmsConnectionStatus": [".*******.1.19338.126.5.5", "rdbms connection established", "rdbms connectivity down"], "smtpConnectionStatus": [".*******.1.19338.126.5.3", "smtp connection established", "smtp connectivity down"]}}}}, "instances": [{"host": "*************", "port": "9005"}], "log": {"cdr": "authServer_access", "cdrFormat": ":remote-addr :remote-user [:date[iso]] :username :method :url HTTP/:http-version :status :res[content-length] :response-time", "logdir": "/data/leap/logs/", "log4js": {"appenders": {"authServer": {"type": "dateFile", "filename": "authServer.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "[%d{ISO8601}] [%p] %m"}}, "console": {"type": "console"}}, "categories": {"authServer": {"appender": "authServer", "level": "ERROR"}, "default": {"appender": "authServer", "level": "TRACE"}}}}}, "cdrSweeper": {"clusterMode": "true", "clusterSize": "2", "transferMode": "1", "batchSize4ES": "200", "batchSize4FS": "4000", "esRetryInterval": "10000", "outputPath": "/data/leap/cdrStore/cdrs/", "cdrPollingTimeout": "5", "diskChkInterval": "10", "minFreeDiskSpace": "1", "recordMaxSize": "30000", "filePrefix": "LEAP", "log": {"logdir": "/data/leap/logs", "log4js": {"appenders": {"cdrSweeper": {"type": "dateFile", "filename": "cdrSweeper.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "[%d{ISO8601}] [%p] %m"}}, "console": {"type": "console", "pattern": "[%d{ISO8601}] [%p] %m"}}, "categories": {"cdrSweeper": {"appender": "cdrSweeper", "level": "ERROR"}, "default": {"appender": "cdrSweeper", "level": "ERROR"}}}}, "logging": {"appenders": {"file": {"filename": "/data/leap/logs//cdrSweeper.log"}}}}, "file_processor": {"minFreeDiskSpace": "1", "batchvalue": {"datavalue": "10", "msisdnvalue": "5"}, "file_paths": {"archieve": "true", "source": "/data/leap/files", "processed": "/data/leap/files/processed/", "error": "/data/leap/files/error/"}, "log": {"logdir": "/data/leap/logs/", "cdr": "FILE_PROCESSOR", "log4js": {"appenders": {"console": {"type": "console", "pattern": "[%d{ISO8601}] [%p] %m"}, "file_processor": {"type": "dateFile", "filename": "file_processor.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "[%d{ISO8601}] [%p] %m"}}}, "categories": {"default": {"appender": "file_processor", "level": "ERROR"}, "file_processor": {"appender": "file_processor", "level": "ERROR"}}}}, "queue": {"host": "redis-6384.ngstudio.svc.cluster.local", "port": "6384", "db": "0", "password": "leap123", "log_key": "LEAP_CDRQ", "limit": "100000"}, "retention_policy": {"enabled": "true", "period": "30"}, "retry_policy": {"enabled": "true", "maxRetry": "5", "retryInterval": "10000"}}, "grafanaServer": {"host": "*************", "port": "9008", "adminUser": "admin", "adminPassword": "admin", "instances": [{"host": "*************", "port": "9008"}]}, "nodeLogger": {"path": "/data/leap/cdrStore/cdrs", "proc": "/data/leap/cdrStore/proc", "backup": "/data/leap/cdrStore/backup", "corrupted": "/data/leap/cdrStore/corrupted", "intervalSize": "5000", "compressionInterval": "120000", "batch": "1000", "fileBatchSize": "1000", "retryTimeOut": "30000", "log": {"logdir": "/data/leap/logs/", "filename": "nodeLogger.log", "log4js": {"appenders": {"console": {"type": "console", "pattern": "[%d{ISO8601}] [%p] %m"}, "nodeLogger": {"type": "dateFile", "filename": "nodeLogger.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "[%d{ISO8601}] [%p] %m"}}}, "categories": {"default": {"appender": "node<PERSON>og<PERSON>", "level": "ERROR"}, "nodeLogger": {"appender": "node<PERSON>og<PERSON>", "level": "ERROR"}}}}}, "pm_fsync": {"host": "pm-fsync.ngstudio.svc.cluster.local", "port": "9006", "contextPath": "/pm_fsync", "protocol": "http", "clusterCPUs": "1", "allowedFileCount": "10", "publishPluginSyncEvents": "true", "pluginData": {"ngageBaseUrl": "https://dev2.ngagecpaas.com", "nodeData": {"appStart": {"enabled": "false"}, "appEnd": {"enabled": "false"}, "whatsapp": {"enabled": "false", "route": "/api/v1/whatsapp/text"}, "voice": {"enabled": "false", "route": "/api/v1/campaigns/voice/simplesubmit"}, "email": {"enabled": "false", "route": "/api/v1/email/send"}, "sms": {"enabled": "false", "route": "/api/v1/sms/send"}, "webhook": {"enabled": "false"}, "rcs": {"enabled": "false"}, "http": {"enabled": "false"}, "choice": {"enabled": "false"}, "repeat": {"enabled": "false"}, "ussd": {"enabled": "false"}, "messenger": {"enabled": "false"}, "instagram": {"enabled": "false"}, "line": {"enabled": "false"}, "telegram": {"enabled": "false"}, "wechat": {"enabled": "false"}, "keypress": {"enabled": "false"}, "hangup": {"enabled": "false"}, "jump": {"enabled": "false"}, "datetime": {"enabled": "false"}, "callforward": {"enabled": "false"}, "wait": {"enabled": "false"}, "addcontact": {"enabled": "false", "route": "/api/v1/contacts"}, "googlesheet": {"enabled": "false"}, "database": {"enabled": "false"}, "language": {"enabled": "false"}, "waitforresponse": {"enabled": "false"}, "workday": {"enabled": "false"}, "shopify": {"enabled": "false"}, "servicenow": {"enabled": "false"}, "openai": {"enabled": "false"}, "hubspot": {"enabled": "false"}, "zapier": {"enabled": "false"}, "salesforce": {"enabled": "false"}, "amazons3": {"enabled": "false"}, "amazonsns": {"enabled": "false"}, "adp": {"enabled": "false"}, "github": {"enabled": "false"}, "monday": {"enabled": "false"}, "quickbooks": {"enabled": "false"}, "stripe": {"enabled": "false"}, "slack": {"enabled": "false"}, "facebook": {"enabled": "false"}, "linkedin": {"enabled": "false"}, "zendesk": {"enabled": "false"}, "excel": {"enabled": "false"}, "mail": {"enabled": "false"}, "teams": {"enabled": "false"}, "onedrive": {"enabled": "false"}, "zoho": {"enabled": "false"}, "freshdesk": {"enabled": "false"}, "dialogflow": {"enabled": "false"}, "script": {"enabled": "false"}, "menu": {"enabled": "false"}, "network": {"enabled": "false"}, "cloud_computing": {"enabled": "false"}, "lte_signal": {"enabled": "false"}, "Satellite": {"enabled": "false"}, "wifi": {"enabled": "false"}, "intent": {"enabled": "false"}, "LLM": {"enabled": "false"}, "prompt": {"enabled": "false"}, "context": {"enabled": "false"}, "enquiry": {"enabled": "false"}}}, "instances": [{"host": "*************", "port": "9006"}], "log": {"cdr": "pm_fsync_access", "cdrFormat": ":remote-addr :remote-user [:date[iso]] :username :method :url HTTP/:http-version :event :status :res[content-length] :response-time", "logdir": "/data/leap/logs/", "log4js": {"appenders": {"console": {"type": "console"}, "pm_fsync": {"type": "dateFile", "filename": "pm_fsync.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "[%d{ISO8601}] [%p] %m"}}}, "categories": {"default": {"appender": "pm_fsync", "level": "TRACE"}, "pm_fsync": {"appender": "pm_fsync", "level": "ERROR"}}}}}, "session_extender": {"clusterCPUs": "1", "log": {"logdir": "/data/leap/logs/", "log4js": {"appenders": {"console": {"type": "console"}, "session_extender": {"type": "dateFile", "filename": "session_extender.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "[%d{ISO8601}] [%p] %m"}}}, "categories": {"default": {"appender": "session_extender", "level": "TRACE"}, "session_extender": {"appender": "session_extender", "level": "ERROR"}}}}}, "session_cleaner": {"outputPath": "/data/leap/cdrs/cdrs/", "log": {"logdir": "/data/leap/logs/", "log4js": {"appenders": {"console": {"type": "console", "pattern": "%d{ISO8601}|%m"}, "session_cleaner": {"type": "dateFile", "filename": "session_cleaner.log", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "%d{ISO8601}|%m"}}}, "categories": {"default": {"appender": "session_cleaner", "level": "ERROR"}, "session_cleaner": {"appender": "session_cleaner", "level": "ERROR"}}}}, "logging": {"appenders": {"file": {"filename": "/data/leap/logs//session_cleaner.log"}}}}, "tps_agent": {"log": {"logdir": "/data/leap/cdrs", "log4js": {"appenders": {"console": {"type": "console", "pattern": "%d{ISO8601}|%m"}, "tps_agent": {"type": "dateFile", "filename": "tps_agent.cdr", "pattern": ".yyyy-MM-dd_hh", "layout": {"type": "pattern", "pattern": "%d{ISO8601}|%m"}}}, "categories": {"default": {"appender": "tps_agent", "level": "INFO"}, "tps_agent": {"appender": "tps_agent", "level": "ERROR"}}}}}}, "global": {"country": "IN", "timezone": "Asia/Kolkata", "authSecret": "ThereAreNoGodDamnSecrets", "certificates": {"key": "/prd/leap/config/server.key", "cert": "/prd/leap/config/server.crt", "pem": "/prd/leap/config/server.pem"}, "databases": {"acl_store": {"username": "leap", "password": "leap123", "database": "leapDB", "host": "mysql.ngstudio.svc.cluster.local", "port": "3306", "dialect": "ma<PERSON>b", "logging": "false", "timezone": "Etc/GMT+05:30", "dialectOptions": {"useUTC": "false", "dateStrings": "true", "typeCast": "true"}, "pool": {"minConnections": "1", "maxIdleTime": "9"}}, "app_store": {"username": "leap", "password": "leap123", "database": "leapDB", "host": "mysql.ngstudio.svc.cluster.local", "port": "3306", "dialect": "mysql", "logging": "false", "timezone": "Etc/GMT+05:30", "generateUUID": "true", "driver_class_name": "com.mysql.jdbc.Driver", "dialectOptions": {"useUTC": "false", "dateStrings": "true", "typeCast": "true"}, "instances": [{"host": "mysql.ngstudio.svc.cluster.local", "port": "3306"}], "pool": {"minConnections": "1", "maxIdleTime": "9"}}, "authDB": {"username": "auth", "password": "auth123", "database": "authDB", "host": "*************", "port": "3306", "dialect": "mysql", "logging": "false", "timezone": "Etc/GMT+05:30", "dialectOptions": {"useUTC": "false", "dateStrings": "true", "typeCast": "true"}, "instances": [{"host": "*************", "port": "3306"}], "pool": {"minConnections": "1", "maxIdleTime": "9"}}, "influxDB": {"host": "*************", "port": "9009", "database": "leap_kpi", "username": "leap", "password": "leap123", "thresholdValue": "200", "dbSourceName": "leap_kpi", "instances": [{"host": "*************", "port": "9009"}]}, "reportServer": {"requestTimeout": "10000", "deadTimeout": "60000", "pingTimeout": "3000", "maxRetries": "3", "maxSockets": "100", "keepAlive": "true", "keepAliveInterval": "1000", "marketingIndex": "leap", "auditIndex": "au", "tempDir": "/data/leap/tmp", "archiveDir": "/data/leap/archive", "hosts": [{"host": "******************************************", "port": "9007"}]}}, "elasticsearch": {"hosts": "*****************************************"}, "kpi": {"enabled": "true", "policy": "file", "options": {"frequency": "1", "file": "/data/leap/kpi", "redis": {"host": "redis-6380.ngstudio.svc.cluster.local", "port": "6380", "db": "0", "password": "leap123"}}}, "oam": {"enabled": "true", "project": "LEAP", "options": {"oamServerIp": "*************", "oamServerPort": "4545", "appId": ".1", "oidPrefix": "*******.1.19338.126", "instances": [{"oamServerIp": "*************", "oamServerPort": "4545"}], "oidMap": {"apigw": ["1.1", "APIGW is up and running", "APIGW is down", "Warning Message", "Unknown Message"], "apigw_app_store_conn": ["1.3", "APIGW-AppStore DB connection established", "APIGW-AppStore DB connectivity is down", "Warning Message", "Unknown Message"], "apigw_authserver_conn": ["1.4", "APIGW-AuthServer connection established", "APIGW-AuthServer connectivity is down", "Warning Message", "Unknown Message"], "apigw_configserver_conn": ["1.2", "APIGW-Config Server connection established", "APIGW-Config Server connectivity is down", "Warning Message", "Unknown Message"], "apigw_login_sessionstore_conn": ["1.5", "APIGW-Login Session store connection established", "APIGW-Login Session store connectivity is down", "Warning Message", "Unknown Message"], "apigw_pmserver_conn": ["1.6", "APIGW - PMFSYNC - Server connection established", "APIGW - PMFSYNC - Server connectivity is down", "Warning Message", "Unknown Message"], "app_engine": ["2.1", "APP-ENGINE is up and running", "APP-ENGINE is down", "Warning Message", "Unknown Message"], "app_engine_app_store_conn": ["2.3", "AppExec-AppStore DB connection established", "AppExec-AppStore DB connectivity is down", "Warning Message", "Unknown Message"], "app_engine_cdrqueue_conn": ["2.6", "AppExec-logQueue connection established", "AppExec logQueue connectivity is down", "Warning Message", "Unknown Message"], "app_engine_configserver_conn": ["2.2", "AppExec-Config Server connection established", "AppExec-Config Server connectivity is down", "Warning Message", "Unknown Message"], "app_engine_pmserver_conn": ["2.5", "AppExec-PMFSYNC-Server connection established", "APIGW-PMFSYNC-Server connectivity is down", "Warning Message", "Unknown Message"], "app_engine_sessionstore_conn": ["2.4", "AppExec-Session store connection established", "AppExec-Session store connectivity is down", "Warning Message", "Unknown Message"], "app_engine_tpsqueue_conn": ["2.7", "AppExec-TPSQueue connection established", "AppExec-TPSQueue connectivity is down", "Warning Message", "Unknown Message"], "app_store": ["6.1", "App Store is up and running", "App Store is down", "Warning Message", "Unknown Message"], "authserver": ["5.1", "AUTH-SERV is up and running", "AUTH-SERV is down", "Warning Message", "Unknown Message"], "cdrSweeper": ["13.1", "cdrsweeper is up and running", "cdrsweeper is down", "Warning Message", "Unknown Message"], "cdr_queue": ["10.1", "CDR Queue is up and running", "CDR Queue is down", "Warning Message", "Unknown Message"], "diskspace_full": ["17.2", "LEAP CDR Disk space is OK", "LOW LEAP CDR Disk space, please clear some space", "Warning Message", "Unknown Message"], "duService": ["14.1", "duService is up and running", "duService is down", "Warning Message", "Unknown Message"], "elasticsearch_conn": ["17.1", "elasticsearch connection established", "elasticsearch connectivity is down", "Warning Message", "Unknown Message"], "gui": ["15.1", "LEAP Studio is up and running", "LEAP Studio is down", "Warning Message", "Unknown Message"], "login_session_store": ["8.1", "Login Session Store is up and running", "Login Session Store is down", "Warning Message", "Unknown Message"], "mockserver": ["7.1", "MOCK-SERV is up and running", "MOCK-SERV is down", "Warning Message", "Unknown Message"], "pm_acip_conn": ["4.8", "ACIP Module connection established", "ACIP Module connectivity is down", "Warning Message", "Unknown Message"], "pm_chl_http_conn": ["4.3", "Channel Module - HTTP connection established", "Channel Module - HTTP connectivity is down", "Warning Message", "Unknown Message"], "pm_chl_sms_conn": ["4.2", "Channel Module - SMS connection established", "Channel Module - SMS connectivity is down", "Warning Message", "Unknown Message"], "pm_db_mariadb_conn": ["4.6", "Database Module - mariadb connection established", "Database Module - mariadb connectivity is down", "Warning Message", "Unknown Message"], "pm_db_mysql_conn": ["4.4", "Database Module - MySQL connection established", "Database Module - MySQL connectivity is down", "Warning Message", "Unknown Message"], "pm_db_oracle_conn": ["4.5", "Database Module - Oracle connection established", "Database Module - Oracle connectivity is down", "Warning Message", "Unknown Message"], "pm_fsync": ["3.1", "PM-FSYNC is up and running", "PM-FSYNC is down", "Warning Message", "Unknown Message"], "pm_soap_conn": ["4.10", "SOAP Module connection established", "SOAP Module connectivity is down", "Warning Message", "Unknown Message"], "pm_ucip_conn": ["4.7", "UCIP Module connection established", "UCIP Module connectivity is down", "Warning Message", "Unknown Message"], "pm_vsip_conn": ["4.9", "VSIP Module connection established", "VSIP Module connectivity is down", "Warning Message", "Unknown Message"], "pmcli_eventq_conn": ["3.5", "PMCLI-Event Queue connection established", "PMCLI-Event Queue connectivity is down", "Warning Message", "Unknown Message"], "pmfsync_configserver_conn": ["3.2", "PMSERV-Config Server connection established", "PMSERV-Config Server connectivity is down", "Warning Message", "Unknown Message"], "pmfsync_eventq_conn": ["3.4", "PMSERV-Event Queue connection established", "PMSERV-Event Queue connectivity is down", "Warning Message", "Unknown Message"], "pmfsync_pmstore_conn": ["3.3", "PMSERV-PluginStore DB connection established", "PMSERV-PluginStore DB connectivity is down", "Warning Message", "Unknown Message"], "pmstore": ["4.1", "Plugin Store is up and running", "Plugin Store is down", "Warning Message", "Unknown Message"], "session_cleaner": ["16.1", "SESSION-CLEANER is up and running", "SESSION-CLEANER is down", "Warning Message", "Unknown Message"], "session_cleaner_cdrqueue_conn": ["16.4", "SESSION-CLEANER-logQueue connection established", "SESSION-CLEANER-logQueue connectivity is down", "Warning Message", "Unknown Message"], "session_cleaner_configserver_conn": ["16.2", "SESSION-CLEANER-Config Server connection established", "SESSION-CLEANER-Config Server connectivity is down", "Warning Message", "Unknown Message"], "session_cleaner_sessionstore_conn": ["16.3", "SESSION-CLEANER-Session store connection established", "SESSION-CLEANER-Session store connectivity is down", "Warning Message", "Unknown Message"], "tps_agent": ["12.1", "tpsAgent is up and running", "tpsAgent is down", "Warning Message", "Unknown Message"], "user_session_store": ["9.1", "User Session Store is up and running", "User Session Store is down", "Warning Message", "Unknown Message"], "whiteboard": ["11.1", "Event Queue is up and running", "Events Queue is down", "Warning Message", "Unknown Message"]}}}, "security": {"enabled": "false", "allowedRestMethods": ["GET", "POST", "OPTIONS"]}, "whiteboard": {"host": "redis-6383.ngstudio.svc.cluster.local", "port": "6383", "db": "0", "password": "leap123"}}}}}