#!/usr/bin/env node
/**
 *  A simple utility to upload configuration keys data to Redis
 **/

"use strict";
require("colors");
const commander = require("commander");
const ConfigProxy = require("../lib/ConfigProxy");

commander
  .version(require("../package.json").version)
  .option("-H, --host [host]", "Config server host -- defaults to", process.env.CONFIG_HOST || "127.0.0.1")
  .option("-p, --port [port]", "Config server port -- defaults to", process.env.CONFIG_PORT || 6380)
  .option("-n, --db [db]", "Config server db index -- defaults to", process.env.CONFIG_DB || 0)
  .option("-a, --password [password]", "config server password", process.env.CONFIG_AUTH || undefined)
  .option("-t, --type [type]", "Type of data to upload (0:all | 1:config | 2:fsm | 3:countries)", 0)
  .option("-F --flush [flush]", "Flush the configuration DB", null)
  .option("-f, --file [file]", "Configuration file that contains keys to be uploaded")
  .parse(process.argv);

let options = {
  args: commander.opts()
};

Object.keys(options.args).forEach(key => {
  if (options.args[key] == null)
    delete options.args[key];
});

let cfgProxy = new ConfigProxy(options);

if (commander.flush != null) {
  cfgProxy.flushDB().then(error => {
    if (error) {
      console.error("Failed to flush the Config-Tree".red, error);
      process.exit(1);
    }
    console.log("Successfully flushed the Config-Tree".green);
    loadConfiguration();
  });
} else {
  loadConfiguration();
}

function loadConfiguration() {
  cfgProxy.publish(commander.type, commander.file)
    .then(() => {
      console.log("Successfully Uploaded the Configuration template to Config Tree.".green);
      cfgProxy.close();
      process.exit(1);
    });
}
