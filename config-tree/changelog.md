
# Change Log
All notable changes to this project will be documented in this file.
 
This project adheres to [Semantic Versioning](http://semver.org/).

## [3.0.3] - 2020-08-18

### Fixed
 
- Corrected the "auth" property to redis-dump in place of "password" field.

## [3.0.2] - 2020-07-31
  
### Added
 
- CLI for dumping this configuration from redis to file.

To check help on configuration dump:
`cd ./config-tree/bin; node ./config-dump.js --help`

usage on configuration dump:

`cd ./config-tree/bin; node ./config-dump.js -H 127.0.0.1 -p 6379 -a password -f test.json`

## [3.0.1] - 2020-07-08
  
### Fixed
 
- Auto config reload global.config pattern registered.

## [3.0.0] - 2020-07-07
 
### Added
- MAJOR Implemented the Auto-Configuration reloader for global.config
- MINOR Code revamped
- MINOR implemented the APIs configproxy.readConfig() which emits the event "reload_config" caller can get events on what changes happened in config-tree.

### Changed

### Fixed
 
## [2.0.5] - 2020-06-10
 
### Added
   
### Changed
 
### Fixed

### Tags 
- [LEAP_v3.0.8_20200615_PVG.B1](http://blrgitlab.mahindracomviva.com/mbs/MBS-Generics/tags/LEAP_v3.0.8_20200615_PVG.B1)
