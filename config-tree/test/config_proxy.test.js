/**
 *  Test module for our config proxy
 **/

const expect = require("chai").expect;
const ConfigProxy = require("../lib/ConfigProxy");
const ConfigTasks = require("../lib/ConfigTasks");

const Configuration = {
  args: { "host": "127.0.0.1", "port": 6379, "db": 0 },
  configTasks: [
    "getDBInfo",
    "getModuleInfoEx",
    "getGlobalInfo"
  ],
  keys2read: {
    getDBInfo: ["app_store"],
    getModuleInfoEx: ["app_engine", "app_engine_producton", "pm_fsync", "acl_manager"],
    getGlobalInfo: ["whiteboard", "timezone", "kpi", "oam", "certificates", "security"]
  }
};


const Proxy = new ConfigProxy(Configuration);

describe('Test suite for config proxy', function () {

  before('Config proxy initialization', function (done) {
    console.log("Before hook: Initializing config proxy module...");
    Proxy.publish(1, require.resolve("./template.json")).then(function () {
      return done();
    }).catch(function (e) {
      return done(e);
    });
  });

  describe('Test suite for specific module retrieval', function () {
    it('Should retrieve the config info from cache if correct name is searched for', function (done) {
      ConfigTasks.getModuleInfoEx('authServer').then(function (info) {
        //console.log("moduleinfo",info);
        expect(info).to.be.not.null;
        return done();
      }).catch(function (e) { return done(e); });
    });

    it('Should not retrieve the config info from cache if wrong name is searched for', function (done) {
      ConfigTasks.getModuleInfoEx('XYZ').then(function (info) {
        expect(info).to.be.null;
        return done();
      }).catch(function (e) { return done(e); });
    });
  })

  describe('Test suite for all matching modules retrieval', function () {
    it('Should retrieve the list of modules from cache if correct string is searched for', function (done) {
      ConfigTasks.getModules('*Server').then(function (modules) {
        expect(modules).to.be.not.null;
        return done();
      }).catch(function (e) { return done(e); });
    });

    it('Should retrieve the list of all modules from cache if no string is specified', function (done) {
      ConfigTasks.getModules('*').then(function (modules) {
        expect(modules).to.be.not.null;
        return done();
      }).catch(function (e) { return done(e); });
    });

    it('Should not retrieve any list of modules from cache if wrong string is searched for', function (done) {
      ConfigTasks.getModules('*xyz').then(function (modules) {
        expect(modules).to.be.not.null;
        return done();
      }).catch(function (e) { return done(e); });
    });
  })

  describe('Test suite for setting some specific module info', function () {
    it('Should set some info for an existing module', function (done) {
      ConfigTasks.setModuleInfo('xyzServer', {
        "host": "************",
        "port": "8000",
        "baseurl": "http://************:8000/xyzserver"
      })
        .then(function (info) {
          expect(info).to.be.not.null;
          return ConfigTasks.getModuleInfo('xyzServer').then(function (information) {
            //console.log("moduleinfo",information);
            expect(information).to.be.not.null;
            return done();
          });
          //return done();
        }).catch(function (e) { return done(e); });
    });

    it('Should set info for a new module', function (done) {
      ConfigTasks.setModuleInfo('newServer', {
        "host": "************",
        "port": "3000",
        "baseurl": "http://************:3000/newserver"
      })
        .then(function (info) {
          expect(info).to.be.not.null;
          return ConfigTasks.getModuleInfo('newServer').then(function (information) {
            //console.log("moduleinfo",information);
            expect(information).to.be.not.null;
            return done();
          });
          //return done();
        }).catch(function (e) { return done(e); });
    });
  })

  describe('Test suite for getting the base url', function () {
    it('Should retrieve the baseurl of the searched module from cache', function (done) {
      ConfigTasks.getModuleBaseUrl('authServer').then(function (url) {
        expect(url).to.be.not.null;
        return done();
      }).catch(function (e) { return done(e); });
    });
  })

  describe('Test suite for global info retrieval', function () {
    it('Should retrieve the global info from cache', function (done) {
      ConfigTasks.getGlobalInfo().then(function (info) {
        //console.log("Info",info);
        expect(info).to.be.not.null;
        return done();
      }).catch(function (e) { return done(e); });
    });
  })

  describe('Test suite for jobStore info retrieval', function () {
    it('Should retrieve the jobStore info from cache', function (done) {
      ConfigTasks.getJobStore().then(function (info) {
        //console.log("Info",info);
        expect(info).to.be.not.null;
        return done();
      }).catch(function (e) { return done(e); });
    });
  })

  describe('Test suite for plugins info retrieval', function () {
    it('Should retrieve the plugins info from cache', function (done) {
      ConfigTasks.getPluginsInfo().then(function (info) {
        //console.log("Info",info);
        expect(info).to.be.not.null;
        return done();
      }).catch(function (e) { return done(e); });
    });
  })

  describe("Test suite for db info retrieval", function () {
    it('Should retrieve the database info for a given db', function (done) {
      ConfigTasks.getDBInfo('authDB').then(function (info) {
        expect(info).to.be.not.null;
        expect(info.pool).to.be.not.null;
        expect(info.pool.minConnections).to.be.not.null;
        //console.log("dbInfo:", info);
        return done();
      }).catch(function (e) { return done(e); });
    });
  });

});  //EOF: global describe
