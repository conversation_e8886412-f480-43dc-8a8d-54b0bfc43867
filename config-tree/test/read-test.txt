const ConfigProxy = require("../lib/ConfigProxy");

const opts = {
  args: { "host": "127.0.0.1", "port": 6380, "db": 0 },
  configTasks: [
    "getDBInfo",
    "getModuleInfoEx",
    "getGlobalInfo"
  ],
  keys2read: {
    getDBInfo: ["app_store"],
    getModuleInfoEx: ["app_engine", "app_engine_producton", "pm_fsync", "acl_manager"],
    getGlobalInfo: ["whiteboard", "timezone", "kpi", "oam", "certificates", "security"]
  }
};

const proxy = new ConfigProxy(opts);

proxy.readConfig()
  .then(async () => {
    await proxy.register();
    // console.log(JSON.stringify(global.config));
    proxy.on("reload_config", async (pattern, channel, key) => {
      console.log("CONFIG_CHANGE_EVENT", pattern, channel, key);
    });
  }).catch(e => {
    console.error(e);
    process.exit(1);
  });


