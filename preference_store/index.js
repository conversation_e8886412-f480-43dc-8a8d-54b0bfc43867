const common = require("common");
const RedisMan = common.redis_man;
const chunkSize = 10000;

module.exports = {
  init: () => {
    console.log("Initializing the Preference Store");
    RedisMan.init({
      key: "app_engine_preference_conn",
      config: global.config.app_engine.sessionstore,
      oid: "app_engine_preference_conn"
    });
  },

  set: async (msisdn, langCode) => {
    try {
      let connection = await RedisMan.getConnection("app_engine_preference_conn");
      msisdn = Number(msisdn);
      let coeffNumber = getCoeffNumber(msisdn);
      let num = (msisdn % chunkSize);
      let data = await connection.hget("user:preference:store:" + coeffNumber, num);
      if (data == null) {
        data = { lang: langCode || global.config.defaultLanguage };
      }
      if (typeof data == "string") {
        data = JSON.parse(data);
      }
      if (langCode) data.lang = langCode;
      await connection.hset("user:preference:store:" + coeffNumber, num, JSON.stringify(data));
      return true;
    } catch (e) {
      //ignore
      global.logger.error(e);
    }
    return false;
  },

  get: async (msisdn) => {
    try {
      let connection = await RedisMan.getConnection("app_engine_preference_conn");
      msisdn = Number(msisdn);
      let coeffNumber = getCoeffNumber(msisdn);
      let num = (msisdn % chunkSize);
      let data = await connection.hget("user:preference:store:" + coeffNumber, num);
      if (data == null) {
        data = { lang: global.config.defaultLanguage };
        await connection.hset("user:preference:store:" + coeffNumber, num, JSON.stringify(data));
      }
      if (typeof data == "string") {
        data = JSON.parse(data);
      }
      return data.lang;
    } catch (e) {
      //ignore
      global.logger.error(e);
    }
    return global.config.defaultLanguage;
  }
}

function getCoeffNumber(num) {
  return parseInt(num / chunkSize, 10);
}
