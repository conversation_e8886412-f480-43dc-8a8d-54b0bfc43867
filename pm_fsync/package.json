{"name": "pm_fsync", "version": "3.0.9", "description": "Plugin Manager File hosting service", "author": "<EMAIL>", "license": "ISC", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["pm_fsync"], "dependencies": {"ajv": "^6.12.3", "app_kpi": "file:../../libs/app_kpi", "app_macros": "file:../../libs/app_macros", "async": "^3.2.0", "axios": "^0.27.2", "bluebird": "^3.7.2", "body-parser": "^1.19.0", "commander": "^5.1.0", "common": "file:../../libs/common", "config-tree": "file:../../libs/config-tree", "date-and-time": "^0.13.1", "dir-to-json": "0.0.3", "events": "^3.1.0", "express": "^4.17.1", "file-stream-rotator": "^0.5.7", "iconv": "^3.0.0", "ioredis": "^4.17.3", "jsontoxml": "^1.0.1", "log4js": "6.3.0", "mariadb": "^2.4.1", "message": "file:../../libs/message", "mkdirp": "^1.0.4", "morgan": "^1.10.0", "multer": "^1.4.2", "mysql": "^2.18.1", "mysql2": "^2.3.3", "nodemailer": "^6.4.10", "nodemailer-smtp-transport": "^2.7.4", "oam": "file:../../libs/oam", "oracledb": "^5.0.1", "pg": "^8.2.1", "request": "^2.88.2", "request-promise": "^4.2.5", "rimraf": "^3.0.2", "sequelize": "^5.21.12", "sequelize-oracle": "file:../../extlibs/sequelize-oracle", "smpp": "file:../../extlibs/smpp", "soap": "^0.31.0", "ssh2": "^0.8.9", "telnet-client": "^1.4.4", "unescape": "^1.0.1", "utility": "file:../../libs/utility", "xml2js": "^0.4.23", "xml2json": "file:../../extlibs/xml2json", "xmlrpc": "file:../../extlibs/xmlrpc", "line-reader": "^0.4.0", "html2json": "^1.0.2", "nyc": "15.1.0"}, "directories": {"lib": "lib"}}