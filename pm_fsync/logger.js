"use strict";
/**
 ** logger module that initializes log4js settings.
 **/

const log4js = require("log4js");
const path = require("path");
const fs = require("fs");
const fileStreamRotator = require("file-stream-rotator");
const Morgan = require("morgan");
let logger;

// create the logs folder if not existing.
let logdir;
if (global.config.pm_fsync.log.logdir) {
    logdir = path.resolve(global.config.pm_fsync.log.logdir);
} else {
    logdir = path.join(__dirname, "/logs");
}
if (!fs.existsSync(logdir)) {
    fs.mkdirSync(logdir);
}

// set up CDR"s logging
let accessLogStream = fileStreamRotator.getStream({
    date_format: "YYYYMMDD",
    filename: path.join(global.config.pm_fsync.log.logdir, global.config.pm_fsync.log.cdr + "_%DATE%.cdr"),
    frequency: "daily",
    verbose: false
});

Morgan.token("username", req => {
    return req.invoker ? req.invoker.id : req.body.username;
});

Morgan.token("event", function (req, res) {
    try {
        if (req.originalUrl.split("/")[2] == "users") {
            return req.params.userId ? req.params.userId : null;
        } else if (req.originalUrl.split("/")[2] == "apps") {
            return req.params.appId ? req.params.appId : req.body.id;
        } else if (req.originalUrl.split("/")[2] == "workflow") {
            return req.params.appId ? req.params.appId : req.body.appId;
        } else if (req.originalUrl.split("/")[2] == "pm") {
            return req.params.pluginId ? req.params.pluginId : req.body.pluginId;
        }
    } catch (err) {
        logger.error("Error in logging event in access cdrs:", err);
    }
});

const morgan = new Morgan(global.config.pm_fsync.log.cdrFormat, { stream: accessLogStream });

// Setup log4js logging
// !! NOTE:- Ravindranath @18th December 2017
// There is a change in the way log4js requires appenders (changed in version 2.x)

let logConfig = global.config.pm_fsync.log.log4js;

Object.keys(logConfig.appenders).forEach(appender => {
    try {
        if (logConfig.appenders[appender] && logConfig.appenders[appender].type != "console") {
            logConfig.appenders[appender].filename = path.join(global.config.pm_fsync.log.logdir, logConfig.appenders[appender].filename);
        }
    } catch (error) {
        global.logger.error(error);
    }
});

Object.keys(logConfig.categories).forEach(category => {
    try {
        logConfig.categories[category].appenders = [logConfig.categories[category].appender];
        delete logConfig.categories[category].appender;
    } catch (error) {
        //ignore
        global.logger.error(error);
    }
});

log4js.configure(logConfig);
if (process.env.PROD) {
    logger = log4js.getLogger("pm_fsync");
} else {
    logger = log4js.getLogger("console");
}
logger.level = logConfig.categories.default.level;

logger.morgan = morgan;
global.log4js = log4js;
global.logger = logger;

module.exports = logger;
module.exports.morgan = morgan;

if (logConfig.categories.pluginCDR != null) {
    global.pluginCDR = log4js.getLogger(logConfig.categories.pluginCDR.appenders[0]);
} else {
    global.pluginCDR = logger;
}
