const typeTraverse = require('./typeTraverse');

let finalJSON = {},
    soapVersionJson = {};

module.exports = {
    startEngine: async (wsdlJSON) => {
        return new Promise((resolve, reject) => {
            global.logger.trace("Traversing wsdl file");
            jsonParser(wsdlJSON).then((res) => {
                resolve(res);
            }).catch((err) => {
                global.logger.error("Error while parsing wsdl: " + err);
                reject("Error while parsing wsdl: " + err);
            })
        });
    }
}

function jsonParser(wsdlJson) {
    return getDefinition(wsdlJson)
        .then(getServiceInfo)
        .catch((err) => {
            global.logger.error("Encountered error while parsing WSDL: " + err);
            return Promise.reject(err);
        });
}

function getDefinition(wsdlJson) {
    global.logger.trace("Getting all the definitions...");
    let definitions = wsdlJson.$;
    getSoapVersion(definitions);
    return Promise.resolve(wsdlJson);
}

async function getServiceInfo(wsdlJson) {
    global.logger.trace("Getting ServiceInfo...");
    finalJSON["serviceName"] = wsdlJson["service"][0].$.name;
    finalJSON["binding"] = [];
    for (let i = 0; i < wsdlJson["service"][0]["port"].length; i++) {
        let bindJson = await iterateServicePort(wsdlJson["service"][0]["port"][i], wsdlJson);
        finalJSON["binding"].push(bindJson);
    }
    return Promise.resolve(finalJSON);
}

function iterateServicePort(sPortJson, wsdlJson) {
    var bindingJSON = {};
    return new Promise((resolve, reject) => {
        global.logger.trace("Getting Port ServicePort info...");
        try {
            let addressKey;
            for (let key in sPortJson) {
                if (key.includes("address")) {
                    addressKey = key;
                    bindingJSON["soapVersion"] = soapVersionJson[key.includes(":") ? key.split(":")[0] : key];
                }
            }
            bindingJSON["bindingAddress"] = sPortJson[addressKey][0].$.location;
            bindingJSON["bindingName"] = delTag(sPortJson.$.binding);

            // Call the async function and handle its result separately
            iterateBinding(bindingJSON, wsdlJson)
                .then(bindingObject => resolve(bindingObject))
                .catch(error => reject(error));
        } catch (error) {
            reject(error);
        }
    });
}


function iterateBinding(bindingJSON, wsdlJson) {
    global.logger.trace("Iterating each binding...");
    return new Promise((resolve, reject) => {
        try {
            for (let i = 0; i < wsdlJson["binding"].length; i++) {
                if (wsdlJson["binding"][i].$.name === bindingJSON.bindingName) {
                    bindingJSON["type"] = delTag(wsdlJson["binding"][i].$.type);
                    bindingJSON["transport"] = wsdlJson["binding"][i]["binding"][0].$.transport;
                    bindingJSON["style"] = wsdlJson["binding"][i]["binding"][0].$.style;

                    // Handle the asynchronous call separately using .then() and .catch()
                    iteratePort(bindingJSON["type"], wsdlJson)
                        .then((operatorArr) => {
                            bindingJSON["operation"] = operatorArr;
                            resolve(bindingJSON);
                        })
                        .catch((error) => {
                            reject(error);
                        });

                    // Exit the loop once the binding is found and processed
                    break;
                }
            }
        } catch (error) {
            reject(error);
        }
    });
}


function iteratePort(type, wsdlJson) {
    global.logger.trace("Inside Binding Port...");
    return new Promise((resolve, reject) => {
        let opArr = [];
        try {
            for (let i = 0; i < wsdlJson["portType"].length; i++) {
                if (wsdlJson["portType"][i].$.name == type) {
                    // Collect the promises for each operation
                    let operations = wsdlJson["portType"][i]["operation"].map((operation) =>
                        getOperation(operation, wsdlJson)
                    );

                    // Use Promise.all to wait for all getOperation calls to complete
                    Promise.all(operations)
                        .then((results) => {
                            opArr = results;
                            resolve(opArr);
                        })
                        .catch((error) => {
                            reject(error);
                        });
                }
            }
        } catch (error) {
            reject(error);
        }
    });
}


function getOperation(operJson, wsdlJson) {
    global.logger.trace("Getting SOAP operation info:", operJson.$.name);
    return new Promise((resolve, reject) => {
        try {
            let operationJSON = {};
            //TODO => get soapJSON
            operationJSON["soapAction"] = "SOAP_Header";
            let promises = [];

            for (let key in operJson) {
                if (key === "$") {
                    operationJSON["name"] = operJson.$.name;
                } else if (key === "input" || key === "output" || key === "fault") {
                    // Collect promises for each iterateMessage call
                    promises.push(
                        iterateMessage(delTag(operJson[key][0].$.message), wsdlJson)
                            .then(result => {
                                operationJSON[key.includes(":") ? key.split(":")[1] : key] = result;
                            })
                    );
                }
            }

            // Wait for all promises to resolve
            Promise.all(promises)
                .then(() => {
                    resolve(operationJSON);
                })
                .catch(error => {
                    reject(error);
                });
        } catch (error) {
            reject(error);
        }
    });
}


function iterateMessage(messageName, wsdlJson) {
    return new Promise((resolve, reject) => {
        let messageJSON = {};
        try {
            // Create an array to hold promises for asynchronous operations
            let promises = [];

            for (let i = 0; i < wsdlJson["message"].length; i++) {
                if (wsdlJson["message"][i].$.name === messageName) {
                    promises.push(
                        getNameAndURL(wsdlJson["message"][i]["part"][0].$.element, wsdlJson)
                            .then(elementJSON => {
                                messageJSON["name"] = elementJSON.name;
                                messageJSON["ref"] = elementJSON.ref;
                                messageJSON["type"] = "object";
                                // Process child elements and add the promise to the array
                                return typeTraverse.elementProcess(elementJSON, wsdlJson)
                                    .then(child => {
                                        messageJSON["child"] = child;
                                    });
                            })
                    );
                }
            }

            // Wait for all promises to resolve
            Promise.all(promises)
                .then(() => {
                    resolve(messageJSON);
                })
                .catch(error => {
                    reject(error);
                });
        } catch (err) {
            global.logger.error(err);
            reject(err); // Reject the promise on synchronous errors
        }
    });
}


////////////////////////////////////////////////////////
///////Helper functions...
////////////////////////////////////////////////////////

/**
 * @description forms global json with <tag>:<soapVerison>
 * @param {definition json from wsdl} json
 */
function getSoapVersion(json) {
    let soapVerJson = {};
    for (let key in json) {
        if (json[key] === "http://schemas.xmlsoap.org/wsdl/soap/") {
            soapVerJson[key.includes(":") ? key.split(":")[1] : key] = "SOAPv11";
        } else if (json[key] === "http://schemas.xmlsoap.org/wsdl/soap12/") {
            soapVerJson[key.includes(":") ? key.split(":")[1] : key] = "SOAPv12";
        }
    }
    soapVersionJson = soapVerJson;
}

/*function for getting tags*/
function getDefTag(wsdlJson) {
    let tag = [];
    for (let key in wsdlJson.definitions.$) {
        if (wsdlJson.definitions.$[key] == "http://schemas.xmlsoap.org/wsdl/") {
            tag.push(key.split(":")[1]);
        }
    }
    return tag;
}

function delTag(string) {
    let str = (string.includes(":")) ? string.split(":")[1] : string;
    return str;
}

//Get url of the particular tag
async function getNameAndURL(string, json) {
    let retJson = {};
    retJson["name"] = string.includes(":") ? string.split(":")[1] : string;
    if (string.includes(":")) {
        for (let key in json.$) {
            if (key == ("xmlns:" + string.split(":")[0])) {
                retJson["ref"] = json.$[key];
                retJson["tag"] = string.split(":")[0];
                return retJson;
            }
        }
    } else {
        global.logger.warn("Message's tag is not present in WSDL");
    }
}
