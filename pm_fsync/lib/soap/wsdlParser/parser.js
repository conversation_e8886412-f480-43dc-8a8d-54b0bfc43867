const fs = require("fs"),
    xml2js = require("xml2js"),
    async = require("async"),
    wsdlTraverse = require("./wsdlTraverse"),
    createPlugin = require("../pluginGen/createPlugin"),
    checkPlugin = require("../pluginGen/checkPlugin");

const parseString = xml2js.parseString;

let dirJson = {},
    globalTagArr = [];

module.exports = {
    startEngine: async (wsdlPath, pluginPath) => {
        return new Promise((resolve, reject) => {
            main(wsdlPath, pluginPath).then((res) => {
                resolve(res);
            }).catch(err => {
                reject(err);
            });
        });
    }
}

/**
 * Scans the import directory
*/
function main(wsdlPath, pluginPath) {
    return new Promise((resolve, reject) => {
        global.logger.trace("Reading the import directory for WSDLs/XSDs");
        try {
            fs.readdir(wsdlPath, async (err, items) => {
                if (err) {
                    global.logger.error("Problem in reading the directory:" + wsdlPath, err);
                    return resolve({ pluginMsg: "readDirError", httpMsg: "internalServerError" });
                }
                if (items.length === 0) {
                    global.logger.warn("No files are present inside the import path. directory:" + wsdlPath);
                    return resolve({ pluginMsg: "emptyDir", httpMsg: "noContent" });
                }
                global.logger.trace("Total files found are " + items.length + " in directory:" + wsdlPath);
                let result,
                    checkflag;
                try {
                    result = await xmlIterator(items, wsdlPath);
                } catch (err) {
                    global.logger.error(err);
                    return resolve({ pluginMsg: "wsdlParsingErr", httpMsg: "internalServerError" });
                }
                fs.writeFileSync(wsdlPath + "/inter.json", JSON.stringify(result));
                global.logger.trace("Intermediate JSON is generated succesfully");
                try {
                    checkflag = await checkPlugin.checkPlugin(wsdlPath, pluginPath);
                } catch (err) {
                    global.logger.error(err);
                    return resolve({ pluginMsg: "pluginCheckErr", httpMsg: "internalServerError" });
                }
                if (checkflag == false) {
                    global.logger.info("Plugin already exists. Check for user's confirmation");
                    return resolve({ pluginMsg: "pluginExists", httpMsg: "resourceConflict" });
                } else {
                    let flag = await createPlugin.create(result, pluginPath, wsdlPath);
                    if (flag == true) {
                        global.logger.trace("Plugin created successfully. Deleting the WSDL upload directory...");
                        resolve({ pluginMsg: "pluginCreated", httpMsg: "resourceCreated" });
                    } else {
                        global.logger.error("Error while creating plugin");
                        resolve({ pluginMsg: "createPluginErr", httpMsg: "notImplemented" });
                    }
                }
            });
        } catch (err) {
            global.logger.error("Encountered error while parsing wsdl: ", err);
            resolve({ pluginMsg: "wsdlParsingErr", httpMsg: "internalServerError" });
        }
    });
}
/**
 *
 * @param {array} items
 * @description iterates through all files present inside the directory
 */
function xmlIterator(items, wsdlPath) {
    let wsdlCounter = 0;
    return new Promise((resolve, reject) => {
        async.everySeries(items, (filename, callback) => {
            if (filename.includes(".wsdl") || filename.includes(".xsd")) {
                if (filename.includes(".wsdl")) {
                    wsdlCounter++;
                }
                xmlToJson(filename, wsdlPath).then((resJSON) => {
                    dirJson[filename] = resJSON;
                    callback(null, !false);
                }).catch((err) => {
                    global.logger.error("Error while xml2json conversion", err);
                    callback(err, err);
                })
            } else {
                callback(null, !false);
            }
        }, async (err, result) => {
            if (wsdlCounter != 1) {
                global.logger.error("Count of uploaded WSDL:", wsdlCounter);
                reject("Uploaded WSDL count is not 1");
            } else if (result) {
                global.logger.trace("Converted all the available XMLs to json");
                let interJSON;
                try {
                    interJSON = await consolidateJSON(dirJson);
                } catch (err) {
                    global.logger.error(err);
                    reject(err);
                }
                resolve(interJSON);
            } else {
                global.logger.error('Error while XML to JSON conversion: ', err);
                reject(err);
            }
        });
    });
}


function consolidateJSON(dirJSON) {
    return new Promise((resolve, reject) => {
        global.logger.trace("Consolidating the json...");
        let promises = []; // Create an array to hold promises

        for (var key in dirJSON) {
            if (key.includes('wsdl')) {
                // Push the promise into the array
                promises.push(
                    getImportFromWsdl(dirJSON[key]["definitions"], dirJSON)
                        .then(wsdlJSON => {
                            // Process the wsdlJSON here if needed
                            return wsdlTraverse.startEngine(wsdlJSON);
                        })
                        .then(result => {
                            resolve(result); // Resolve with the final result
                        })
                        .catch(err => {
                            global.logger.error(err);
                            reject(err); // Reject if there is an error
                        })
                );
            }
        }

        // If no promises were added, resolve with null or an appropriate value
        if (promises.length === 0) {
            resolve(null); // Or handle this case as needed
        }
    });
}


/**
 *
 * @description gets dependency on xsd json and merges to single json with wsdl
 * @param {wsdl file in json} wsdl
 * @param {json having all the xml2js} dirJSON
 * @returns consolidated json having wsdl + xsd
 */
function getImportFromWsdl(wsdl, dirJSON) {
    return new Promise((resolve, reject) => {
        for (let i = 0; i < wsdl["types"][0]["schema"].length; i++) {
            if (wsdl["types"][0]["schema"][i]["import"]) {
                for (let j = 0; j < wsdl["types"][0]["schema"][i]["import"].length; j++) {
                    let importElement = wsdl["types"][0]["schema"][i]["import"][j].$;
                    if (importElement.schemaLocation) {
                        if (dirJSON.hasOwnProperty(importElement.schemaLocation)) {
                            global.logger.trace("XSD found in the path");
                            const importArr = [];
                            getKey(importElement.namespace, wsdl, importArr);
                            if (importArr.length == 0) {
                                global.logger.error("XSD definition is not present in WSDL");
                                reject("XSD definition is not present in WSDL:" + importElement.schemaLocation);
                            } else {
                                global.logger.trace("XSD definition found in WSDL. Adding with key");
                                wsdl[importArr[0].split(":")[1]] = dirJson[importElement.schemaLocation];
                                //TO-DO : Get import from XSD
                                //getImportFromXSD(wsdl, importArr[0].split(":")[1], dirJSON);
                            }
                        } else {
                            global.logger.error("XSD is not found:", importElement.schemaLocation);
                            reject("XSD not found in the path: " + importElement.schemaLocation);
                        }
                    }
                }
            } else {
                global.logger.warn("Nothing to import");
            }
        }
        resolve(wsdl);
    });
}

/**
 *
 * @description Recursive function which returns the key corresponding to value
 * @param {value to be search} value
 * @param {json} json
 * @returns key for the value
 */
function getKey(value, json, retKey) {
    let excludeTag = ["namespace", "targetNamespace", "location"];
    if (!retKey) retKey = [];
    function json_traversal(node, retKey) {
        if (Object.keys(node).length != 0) {
            for (var key in node) {
                if ((excludeTag.indexOf(key) < 0) && node[key] == value) {
                    retKey.push(key);
                    globalTagArr.push(key.split(":")[1] ? key.split(":")[1] : "");
                }
                if (typeof node[key] === "object" && Object.keys(node[key]).length > 0) {
                    json_traversal(node[key], retKey);
                }
            }
        }
    }
    if (Object.keys(json).length != 0) {
        return json_traversal(json, retKey);
    }
}

/**
 *
 * @param {string} xmlName
 * @description takes xml and gives back its json conversion
 */
function xmlToJson(xmlName, wsdlPath) {
    return new Promise((resolve, reject) => {
        fs.readFile(wsdlPath + "/" + xmlName, (err, data) => {
            parseString(data, { tagNameProcessors: [stripPrefix] }, (err, result) => {
                if (err) {
                    global.logger.error("Error while reading the XML:", xmlName);
                    reject(err);
                } else {
                    global.logger.trace("JSON conversion successful for XML:", xmlName);
                    resolve(result);
                }
            });
        });
    });
}

/**
 * @description strips the tag name prefix except xmlns and address
 * @param {tagName} str
 * @returns stripped prefix
 */
function stripPrefix(str) {
    let prefixMatch = new RegExp(/(?!xmlns)^.*:/);
    if (str.includes(":address")) {
        return str;
    } else {
        return str.replace(prefixMatch, '');
    }
}

