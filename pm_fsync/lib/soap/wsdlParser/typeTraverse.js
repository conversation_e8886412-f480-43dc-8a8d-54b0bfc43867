const primDataType = ["string", "normalizedString",
    "token", "date", "time", "dateTime", "duration",
    "decimal", "integer", "int", "boolean", "hexBinary", "anyURI",
    "long", "double", "float", "base64Binary", "anyType"];

module.exports = {
    elementProcess: async function (elementJSON, wsdlJson) {
        if (wsdlJson.hasOwnProperty(elementJSON.tag)) {
            return lookInsideXSD(elementJSON, wsdlJson);
        } else {
            return await lookInsideWSDL(elementJSON.name, wsdlJson);
        }
    }
}

async function lookInsideWSDL(elementName, wsdlJson) {
    let childArr = [];
    //look inside the wsdl
    for (let i = 0; i < wsdlJson.types[0].schema.length; i++) {
        for (let j = 0; j < wsdlJson.types[0].schema[i].element.length; j++) {
            if (elementName == wsdlJson.types[0].schema[i].element[j].$.name) {
                //if type present get type and search for this type
                //else continue to search is same block
                if (wsdlJson["types"][0].schema[i]["element"][j].$.type) {
                    childArr = await getFirstLevel(wsdlJson["types"][0].schema[i]["element"][j].$.type, wsdlJson);
                } else {
                    //continue search in same block
                    for (let k = 0; k < wsdlJson["types"][0].schema[i]["element"][j]["complexType"][0]["sequence"][0]["element"].length; k++) {
                        let element = wsdlJson["types"][0].schema[i]["element"][j]["complexType"][0]["sequence"][0]["element"][k];
                        let json = {};
                        json.name = element.$.name;
                        json.type = element.$.type.split(":")[1];
                        json.minBoundary = element.$.minOccurs;
                        json.maxBoundary = element.$.maxOccurs;
                        json.type = decideType(json.maxBoundary, json.type);
                        childArr.push(json);
                    }
                }
            }
        }
    }
    return childArr;
}

function getFirstLevel(type, wsdlJson) {
    let array = [];
    let typeJSON = getNameAndTag(type);
    if (wsdlJson.hasOwnProperty(typeJSON.tag)) {
        for (let i = 0; i < wsdlJson[typeJSON.tag].schema.complexType.length; i++) {
            if (typeJSON.name == wsdlJson[typeJSON.tag].schema.complexType[i].$.name) {
                for (let j = 0; j < wsdlJson[typeJSON.tag].schema.complexType[i].sequence[0].element.length; j++) {
                    let element = wsdlJson[typeJSON.tag].schema.complexType[i].sequence[0].element[j];
                    let tempJson = {};
                    tempJson.name = element.$.name;
                    tempJson.minBoundary = (element.$.minOccurs) ? (element.$.minOccurs) : "NA";
                    tempJson.maxBoundary = (element.$.maxOccurs) ? (element.$.maxOccurs) : "NA";
                    if (element.$.type) {
                        if (primDataType.includes(element.$.type.split(":")[1])) {
                            tempJson.type = element.$.type.split(":")[1];
                            tempJson.type = decideType(tempJson.maxBoundary, tempJson.type);
                            tempJson.child = "No";
                        } else {
                            tempJson.type = "object";
                            tempJson.type = decideType(tempJson.maxBoundary, tempJson.type);
                            tempJson.child = getSecondLevel(element.$.type, wsdlJson);
                        }
                    } else if (element.simpleType) {
                        for (let k = 0; k < element.simpleType.length; k++) {
                            if (element.simpleType[k].restriction) {
                                let json2 = handleRestriction(element.simpleType[k].restriction[0]);
                                tempJson = Object.assign(tempJson, json2);
                            }
                        }
                    }
                    array.push(tempJson);
                }
            }
        }
        return (array);
    } else {
        //For fault in SelfCare/CYN
        for (let i = 0; i < wsdlJson.types[0].schema.length; i++) {
            if (wsdlJson.types[0].schema[i].complexType) {
                for (let j = 0; j < wsdlJson.types[0].schema[i].complexType.length; j++) {
                    if (typeJSON.name == wsdlJson.types[0].schema[i].complexType[j].$.name) {
                        let tempBlock = wsdlJson.types[0].schema[i].complexType[j];
                        for (let k = 0; k < tempBlock.sequence[0].element.length; k++) {
                            let tempJSON = {};
                            tempJSON.name = tempBlock.sequence[0].element[k].$.name;
                            tempJSON.type = tempBlock.sequence[0].element[k].$.type.split(":")[1];
                            tempJSON.minBoundary = (tempBlock.sequence[0].element[k].$.minOccurs) ? (tempBlock.sequence[0].element[k].$.minOccurs) : "NA";
                            tempJSON.maxBoundary = (tempBlock.sequence[0].element[k].$.maxOccurs) ? (tempBlock.sequence[0].element[k].$.maxOccurs) : "NA";
                            tempJSON.type = decideType(tempJSON.maxBoundary, tempJSON.type);
                            array.push(tempJSON);
                        }
                    }
                }
            }
        }
    }
    return array;
}

function getSecondLevel(type, wsdlJson) {
    let array = [];
    let typeJSON = getNameAndTag(type);
    if (wsdlJson.hasOwnProperty(typeJSON.tag)) {
        for (let i = 0; i < wsdlJson[typeJSON.tag].schema.complexType.length; i++) {
            if (typeJSON.name == wsdlJson[typeJSON.tag].schema.complexType[i].$.name) {
                for (let j = 0; j < wsdlJson[typeJSON.tag].schema.complexType[i].sequence[0].element.length; j++) {
                    let element = wsdlJson[typeJSON.tag].schema.complexType[i].sequence[0].element[j];
                    let tempJson = {};
                    tempJson.name = element.$.name;
                    tempJson.minBoundary = (element.$.minOccurs) ? (element.$.minOccurs) : "NA";
                    tempJson.maxBoundary = (element.$.maxOccurs) ? (element.$.maxOccurs) : "NA";
                    if (element.$.type) {
                        if (primDataType.includes(element.$.type.split(":")[1])) {
                            tempJson.type = element.$.type.split(":")[1];
                            tempJson.type = decideType(tempJson.maxBoundary, tempJson.type);
                            tempJson.child = "No";
                        } else {
                            tempJson.type = "object";
                            tempJson.type = decideType(tempJson.maxBoundary, tempJson.type);
                            tempJson.child = getSecondLevel(element.$.type, wsdlJson);
                        }
                    } else if (element.simpleType) {
                        for (let k = 0; k < element.simpleType.length; k++) {
                            if (element.simpleType[k].restriction) {
                                let json2 = handleRestriction(element.simpleType[k].restriction[0]);
                                tempJson = Object.assign(tempJson, json2);
                            }
                        }
                    }
                    array.push(tempJson);
                }
            }
        }
        return array;
    }
}


async function lookInsideXSD(elementJSON, wsdlJson) {
    let childArr = [];
    global.logger.trace("Element Name::", elementJSON.name);
    for (let i = 0; i < wsdlJson[elementJSON.tag].schema.element.length; i++) {
        let tempElement = wsdlJson[elementJSON.tag].schema.element[i];
        if (elementJSON.name == tempElement.$.name) {
            //if type present get type and search for this type
            //else continue to search in same block
            if (tempElement.$.type) {
                global.logger.trace("Element Type::", tempElement.$.type);
                childArr = await getXSDFirstLevel(tempElement.$.type, wsdlJson[elementJSON.tag].schema.complexType);
            } else {
                //continue search in same block
            }
        }
    }
    return childArr;
}

function getXSDFirstLevel(type, wsdlComplexType) {
    let array = [];
    let typeJSON = getNameAndTag(type);
    global.logger.trace(typeJSON);
    for (let i = 0; i < wsdlComplexType.length; i++) {
        if (typeJSON.name == wsdlComplexType[i].$.name && (wsdlComplexType[i].sequence[0].element)) {
            wsdlComplexType[i].sequence[0].element.forEach(element => {
                let tempJson = {};
                tempJson.name = element.$.name;
                tempJson.minBoundary = (element.$.minOccurs) ? (element.$.minOccurs) : "NA";
                tempJson.maxBoundary = (element.$.maxOccurs) ? (element.$.maxOccurs) : "NA";
                if (element.$.type) {
                    if (primDataType.includes(element.$.type.split(":")[1])) {
                        tempJson.type = element.$.type.split(":")[1];
                        tempJson.type = decideType(tempJson.maxBoundary, tempJson.type);
                        tempJson.child = "No";
                    } else {
                        tempJson.type = "object";
                        tempJson.type = decideType(tempJson.maxBoundary, tempJson.type);
                        tempJson.child = getXSDFirstLevel(element.$.type, wsdlComplexType);
                    }
                    array.push(tempJson);
                }
            });
        }
    }
    return array;
}

function handleRestriction(restBlock) {
    var json = {};
    json["type"] = restBlock.$.base.split(":")[1];
    try {
        for (var key in restBlock) {
            if (key == "minInclusive") {
                json["minimum"] = restBlock[key][0].$.value;
            } else if (key == "maxInclusive") {
                json["maximum"] = restBlock[key][0].$.value;
            } else if (key == "minExclusive") {
                json["exclusiveMinimum"] = restBlock[key][0].$.value;
            } else if (key == "maxExclusive") {
                json["exclusiveMaximum"] = restBlock[key][0].$.value;
            } else if (key == "minLength") {
                json["minLength"] = restBlock[key][0].$.value;
            } else if (key == "maxLength") {
                json["maxLength"] = restBlock[key][0].$.value;
            } else if (key == "length") {
                json["minLength"] = restBlock[key][0].$.value;
                json["maxLength"] = restBlock[key][0].$.value;
            } else if (key == "totalDigits") {
                json["minimum"] = Math.pow(10, (restBlock[key][0].$.value - 1));
                json["maximum"] = (Math.pow(10, restBlock[key][0].$.value) - 1);
            } else if (key == "enumeration") {
                json["enum"] = [];
                for (var j = 0; j < restBlock[key].length; j++) {
                    json["enum"].push(restBlock[key][j].$.value);
                }
            }
        }
    } catch (err) {
        global.logger.error("Error in restrictionBlock");
    }
    json.child = "No";
    return json;
}

/**
 *
 * @param {input in form tag:name} string
 * @returns json having tag and name as key
 */
function getNameAndTag(string) {
    let retJson = {};
    retJson["name"] = string.includes(":") ? string.split(":")[1] : string;
    retJson["tag"] = string.includes(":") ? string.split(":")[0] : "";
    return retJson;
}

/**
 *
 * @param {maximum occurence of the variable} occurence
 * @param {dataType obtained from WSDL} type
 * @returns desired dataType
 */
function decideType(occurence, type) {
    if (occurence > 1 || occurence === "unbounded") {
        if (type === "object") {
            return "arrayOfObject";
        } else return ("array");
    } else {
        return type;
    }
}
