let template = require("./template"),
    moduleSchemaTemplate = require("./moduleSchemaTemplate"),
    fs = require("fs"),
    path = require("path"),
    util = require("util"),
    soapFaultTemplate = require("./soapFaultTemplate");

let finalSchema = {},
    moduleSchemaJSON = {};

finalSchema = Object.assign(finalSchema, template);
moduleSchemaJSON = Object.assign(moduleSchemaJSON, moduleSchemaTemplate);

module.exports = {
    create: async (jsonObj, pluginPath, wsdlPath) => {
        let serviceName = jsonObj.serviceName;
        let flag = true;
        try {
            for (let i = 0; i < jsonObj.binding.length; i++) {
                let faultSchema = Object.assign({}, soapFaultTemplate);
                let typeID = (parseInt(global.customPluginId, 10) + 1);
                let childTypeID = typeID;
                let dir = pluginPath;
                let soapVersion = jsonObj.binding[i].soapVersion;
                let fName = "SOAP_" + serviceName + "_" + jsonObj.binding[i].bindingName;
                dir = dir + path.sep + fName;
                if (!fs.existsSync(dir)) {
                    global.logger.trace("Creating 1st level SOAP directory:", fName);
                    fs.mkdirSync(dir);
                    let data = fs.readFileSync(__dirname + "/SOAPPlugin.js", "utf-8").toString();
                    let deplPath = path.join(pluginPath, fName, fName + "Plugin.js");
                    fs.writeFileSync(deplPath, data);
                    let schemaJSON = await setSoapModuleSchema(moduleSchemaJSON, typeID, fName);
                    fs.writeFileSync(dir + path.sep + fName + "Schema.json", JSON.stringify(schemaJSON));
                    await copyWSDL(dir, wsdlPath, fName);
                } else {
                    global.logger.trace("1st level SOAP directory already exists:", fName);
                }

                let bindingAddress = jsonObj.binding[i].bindingAddress;
                for (let j = 0; j < jsonObj.binding[i].operation.length; j++) {
                    let operationName = jsonObj.binding[i].operation[j].name;
                    let opDir = dir + path.sep + operationName;

                    if (!fs.existsSync(opDir)) {
                        global.logger.trace("Creating SOAP Operation Directory:", operationName);
                        fs.mkdirSync(opDir);
                    } else {
                        global.logger.trace("Operation directory already exists:", operationName);
                    }

                    childTypeID = Number((childTypeID + 0.01).toFixed(2));

                    await setDefaultSettings(childTypeID, bindingAddress, jsonObj.binding[i].operation[j]);

                    //For input tab
                    let result2 = await headHoncho(jsonObj.binding[i].operation[j].input, "process");
                    finalSchema.properties.process.properties = {};
                    finalSchema.properties.process.required = [];
                    finalSchema.properties.process.properties[result2.title] = result2;
                    finalSchema.properties.process.required.push(result2.req);

                    //For output tab
                    let result3 = await headHoncho(jsonObj.binding[i].operation[j].output, "response");
                    finalSchema.properties.response.properties = {};
                    finalSchema.properties.response.required = [];
                    finalSchema.properties.response.properties = result3.properties;

                    //For fault tab
                    let result4;
                    let faultJSON = {};
                    if (jsonObj.binding[i].operation[j].fault) {
                        result4 = headHoncho(jsonObj.binding[i].operation[j].fault, "response");
                        if (soapVersion === "SOAPv11") {
                            faultJSON = Object.assign({}, faultSchema.soapv11);
                            faultJSON.soapFault.properties.detail.properties[result4.title] = result4;
                        } else if (soapVersion === "SOAPv12") {
                            faultJSON = Object.assign({}, faultSchema.soapv12);
                            faultJSON.soapFault.properties.Detail.properties[result4.title] = result4;
                        }
                    }
                    finalSchema.properties.response.properties =
                        Object.assign(finalSchema.properties.response.properties || {}, faultJSON);

                    //Writing plugin schema file
                    fs.writeFileSync(opDir + path.sep + operationName + "Schema.json", JSON.stringify(finalSchema));

                    //Writing plugin exec file
                    let templatePath = path.join(__dirname, "pluginExecTemplate.js");
                    let data = fs.readFileSync(templatePath, "utf-8").toString();
                    data = data.replace(".SOAPPlugin.", "../" + fName + "Plugin");
                    data = data.replace(".operationName.", operationName);
                    data = data.replace(".operationSchemaJSON.", "./" + operationName + "Schema.json");
                    data = fs.writeFileSync(opDir + path.sep + operationName + "Plugin.js", data);
                }
                global.customPluginId = typeID;
            }
            global.logger.trace("All SOAP plugins successfully generated for service:", serviceName);
        } catch (err) {
            global.logger.error("Error while generating plugin:", err);
            flag = flag && false;
        }
        return flag;
    }
};

/**
 * @description it converts the interJSON to schemaJSON which gets deployed
 * @param {accepts json from interjson} json
 * @returns schemaJSON
 */
function headHoncho(json, tabName) {
    if (!json) return {};
    let retObj = {
        title: json.name,
        type: json.type
    }, i, resObj;
    try {
        switch (json.type) {
            case "object":
                retObj = Object.assign(retObj, {
                    properties: {},
                    required: []
                });
                if (json.minBoundary != 0 && (tabName === "process")) {
                    retObj.req = json.name;
                }
                break;
            case "arrayOfObject":
                retObj = Object.assign(retObj, {
                    type: "array",
                    hint: "ArrayofObjects",
                    minItem: "",
                    items: {
                        hint: "grouped",
                        type: "object",
                        properties: {},
                        required: []
                    }
                });
                retObj.minItem = Number(json.minBoundary);
                if (json.maxBoundary && json.maxBoundary !== "unbounded") {
                    retObj.maxItem = json.maxBoundary;
                }
                if (json.minBoundary != 0 && (tabName === "process")) {
                    retObj.req = json.name;
                }
                break;
            case "string":
                retObj = Object.assign(retObj, {});
                if (json.minLength) {
                    retObj.minLength = json.minLength;
                }
                if (json.maxLength) {
                    retObj.maxLength = json.maxLength;
                }
                if (json.minBoundary != 0 && (tabName === "process")) {
                    retObj.req = json.name;
                }
                break;
            case "integer": case "int": case "long": case "float": case "double": case "decimal":
                retObj = Object.assign(retObj, {});
                retObj.type = "integer";
                if (json.minimum) {
                    retObj.minimum = json.minimum;
                }
                if (json.maximum) {
                    retObj.maximum = json.maximum;
                }
                if (json.exclusiveMinimum) {
                    retObj.exclusiveMinimum = json.exclusiveMinimum;
                }
                if (json.exclusiveMaximum) {
                    retObj.exclusiveMaximum = json.exclusiveMaximum;
                }
                if (json.minBoundary != 0 && (tabName === "process")) {
                    retObj.req = json.name;
                }
                break;
            case "date": case "time": case "dateTime":
                retObj = Object.assign(retObj, {
                    type: "string",
                    format: json.type,
                    minLength: 1
                });
                if (json.minLength) {
                    retObj.minLength = json.minLength;
                }
                if (json.maxLength) {
                    retObj.maxLength = json.maxLength;
                }
                if (json.minBoundary != 0 && (tabName === "process")) {
                    retObj.req = json.name;
                }
                break;
            case "array":
                retObj = Object.assign(retObj, {});
                retObj.type = "array";
                retObj.minItem = Number(json.minBoundary);
                if (json.maxBoundary && json.maxBoundary !== "unbounded") {
                    retObj.maxItem = json.maxBoundary;
                }
                break;
        }
        if (json.child != null && util.isArray(json.child)) {
            if (retObj.hint === "ArrayofObjects") {
                delete retObj.items.type;
                for (i = 0; i < json.child.length; i++) {
                    resObj = headHoncho(json.child[i]);
                    if (resObj.req) {
                        retObj.items.required.push(resObj.req);
                    }
                    retObj.items.properties[json.child[i].name] = resObj;
                }
            } else if (retObj.type === "object") {
                for (i = 0; i < json.child.length; i++) {
                    resObj = headHoncho(json.child[i]);
                    if (resObj.req) {
                        retObj.required.push(resObj.req);
                    }
                    retObj.properties[json.child[i].name] = resObj;
                }
            }
        }
    } catch (error) {
        global.logger.error("Error while traversing interJSON", error);
    }
    return retObj;
}

/**
 * @description sets up default settings for the plugin
 * @param {typeID} typeID
 * @param {Endpoint URL} bindingAddress
 * @param {operationJSON} opJson
 */
function setDefaultSettings(typeID, bindingAddress, opJson) {
    return new Promise((resolve, reject) => {
        finalSchema.typeId = typeID.toString();
        finalSchema.name = opJson.name;
        finalSchema.title = opJson.name;
        finalSchema.properties.name.title = opJson.name;
        finalSchema.properties.settings.properties["Endpoint URL"].default = bindingAddress;
        finalSchema.properties.settings.properties["SOAP Header"].default = opJson.soapAction;
        resolve("Success");
    });
}

/**
 * @description fills up moduleSchema
 * @param {schema for module} moduleSchemaJSON
 * @param {typeID for module} typeID
 * @param {SOAP_servicename_bindingname} fName
 */
function setSoapModuleSchema(moduleSchemaJSON, typeID, fName) {
    return new Promise((resolve, reject) => {
        try {
            moduleSchemaJSON.typeId = typeID.toString();
            moduleSchemaJSON.name = fName;
            moduleSchemaJSON.title = fName;
            resolve(moduleSchemaJSON);
        } catch (err) {
            reject(err);
        }
    });
}

/**
 *
 * @param {pluginPath} pluginPath
 * @param {sourcePath} wsdlPath
 * @param {SOAP_servicename_bindingname} fName
 * @description it copies the wsdl and xsd from import directory to
 * plugin deployed directory
 */
function copyWSDL(pluginPath, wsdlPath, fName) {
    return new Promise((resolve, reject) => {
        try {
            let files = fs.readdirSync(wsdlPath);
            for (let i in files) {
                if (files[i].includes(".wsdl") || files[i].includes(".xsd")) {
                    fs.copyFileSync(wsdlPath + path.sep + files[i], pluginPath + path.sep + files[i])
                }
            }
        } catch (err) {
            reject(err);
        }
        resolve("Done");
    });
}
