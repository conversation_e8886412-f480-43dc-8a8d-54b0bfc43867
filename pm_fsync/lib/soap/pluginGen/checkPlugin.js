const fs = require("fs");

module.exports = {
    checkPlugin: (wsdlPath, pluginPath) => {
        let intermediateJson = JSON.parse(getJSON(wsdlPath));
        let serviceName = intermediateJson.serviceName;
        let flag = true;
        for (let i = 0; i < intermediateJson.binding.length; i++) {
            let dir = pluginPath;
            let fName = "SOAP_" + serviceName + "_" + intermediateJson.binding[i].bindingName;
            dir = dir + "/" + fName;
            if (!fs.existsSync(dir)) {
                global.logger.info("Plugin doesn't exist:", fName);
            } else {
                global.logger.info("Plugin already exists:", fName);
                flag = false;
            }
        }
        return flag;
    }
};

function getJSON(wsdlPath) {
    let files = fs.readdirSync(wsdlPath);
    for (let i in files) {
        if (files[i].includes("inter")) {
            let json = fs.readFileSync(wsdlPath + "/" + files[i], "utf-8");
            return json;
        }
    }
}
