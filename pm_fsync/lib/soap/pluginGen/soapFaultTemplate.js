module.exports = {
    "soapv11": {
        "soapFault": {
            "title": "SOAPFault",
            "type": "object",
            "properties": {
                "faultcode": {
                    "title": "FaultCode",
                    "type": "string"
                },
                "faultstring": {
                    "title": "FaultString",
                    "type": "object",
                    "properties": {
                        "attributes": {
                            "title": "attribues",
                            "type": "object",
                            "properties": {
                                "xml:lang": {
                                    "title": "xml:lang",
                                    "type": "string"
                                }
                            }
                        },
                        "$value": {
                            "title": "Value",
                            "type": "string"
                        }
                    }
                },
                "faultactor": {
                    "title": "FaultActor",
                    "type": "string"
                },
                "detail": {
                    "title": "Detail",
                    "type": "object",
                    "properties": {}
                }
            }
        }
    },
    "soapv12": {
        "soapFault": {
            "title": "SOAPFault",
            "type": "object",
            "properties": {
                "code": {
                    "title": "Code",
                    "type": "object",
                    "properties": {
                        "Value": {
                            "title": "Value",
                            "type": "string"
                        },
                        "Subcode": {
                            "title": "Subcode",
                            "type": "object",
                            "properties": {
                                "Value": {
                                    "title": "Value",
                                    "type": "string"
                                },
                                "Subcode": {
                                    "title": "Subcode",
                                    "type": "string"
                                }
                            }
                        }
                    }
                },
                "Reason": {
                    "title": "Reason",
                    "type": "array",
                    "hint": "ArrayofObjects",
                    "minItem": 1,
                    "items": {
                        "hint": "grouped",
                        "type": "object",
                        "properties": {
                            "Text": {
                                "title": "Text",
                                "type": "object",
                                "properties": {
                                    "attributes": {
                                        "title": "attribues",
                                        "type": "object",
                                        "properties": {
                                            "xml:lang": {
                                                "title": "xml:lang",
                                                "type": "string"
                                            }
                                        }
                                    },
                                    "$value": {
                                        "title": "$value",
                                        "type": "string"
                                    }
                                }
                            }
                        }
                    }
                },
                "Node": {
                    "title": "Node",
                    "type": "string"
                },
                "Role": {
                    "title": "Role",
                    "type": "string"
                },
                "Detail": {
                    "title": "Detail",
                    "type": "object",
                    "properties": {}
                }
            }
        }
    }
};
