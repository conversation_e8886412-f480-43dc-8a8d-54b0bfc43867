"use strict";
/**
 *  SOAP operation Plugin
 *
 **/
const SOAPPlugin = require(".SOAPPlugin."),
    soap = require("soap"),
    message = require("message"),
    error_codes = message.error_codes;

let schema;
const timeOut = 10000;

class OperationPlugin extends SOAPPlugin {
    init() {
        //TODO: Need to implement
    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof OperationPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof OperationPlugin
     */
    validate(module) {
        loadSchemaFile();
        if (module.settings.Authentication === "No Auth") {
            module.settings.basic_username = "noUser";
            module.settings.basic_password = "noPass";
        }
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof OperationPlugin
     */
    exec(moduleContext, options) {
        if (options && options.mode === "testConn") {
            return testConn(moduleContext);
        }
        moduleContext.operationName = ".operationName.";
        return super.exec(moduleContext);
    }

    close() {
        //TODO: Need to implement
    }
}
module.exports = OperationPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require(".operationSchemaJSON.");
    schema.category = "soap";
    return schema;
}

function testConn(moduleContext) {
    return new Promise((resolve, reject) => {
        try {
            if (!moduleContext.settings && !moduleContext.settings["Endpoint URL"]) {
                reject(message.getResponseJson(moduleContext.language, error_codes.settingsNotFound));
            }
            let options = {};
            options["endpoint"] = moduleContext.settings["Endpoint URL"];
            options["wsdl_options"] = { timeout: timeOut };
            let wsdlPath = (moduleContext.settings["Endpoint URL"] + "?wsdl").toString();
            soap.createClient(wsdlPath, options, function (err, client) {
                if (err) {
                    if (err.code === "ESOCKETTIMEDOUT") {
                        logger.error("Error while creating client:", err.code);
                        reject(message.getResponseJson(moduleContext.language, error_codes.ESOCKETTIMEDOUT));
                    } else {
                        logger.error("Error while creatig client: ResourceNotFound/ConnectionRefused");
                        reject(message.getResponseJson(moduleContext.language, error_codes.resourceNotFound));
                    }
                } else {
                    logger.trace("SOAP test connection successful");
                    resolve(message.getResponseJson(moduleContext.language, error_codes.success, 0));
                }
            });
        } catch (err) {
            logger.error("Error while testing SOAP connection:", err);
            reject(message.getResponseJson(moduleContext.language, error_codes.soapTestErr));
        }
    });
}
