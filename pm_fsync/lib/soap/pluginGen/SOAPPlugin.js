"use strict";
/**
 * SOAPPlugin - Simple Object Access Protocol Plugin
 **/

const LeapPlugin = require("../LeapPlugin"),
    message = require("message"),
    error_codes = message.error_codes,
    soap = require("soap"),
    fs = require("fs"),
    path = require("path");

const timeOut = 5000;
/**
 * @class SOAPPlugin
 */
class SOAPPlugin extends LeapPlugin {

    init() {
    }

    getMetaDataInfo(moduleData) {
        return super.getMetaDataInfo(moduleData);
    }

    /**
     * plugin validate method
     *
     * @param {any} moduleData
     * @param {any} schemaData
     * @returns
     * @memberof SOAPPlugin
     * @returns module validation result object
     */
    validate(moduleData, schemaData) {
        return super.validate(moduleData, schemaData);
    }

    /**
     * exec method
     * @param {any} pluginName
     * @param {any} moduleContext
     * @memberof SOAPPlugin
     * @returns result object
     */
    exec(moduleContext) {
        return new Promise((resolve, reject) => {
            let options = {};
            try {
                options["endpoint"] = moduleContext.settings["Endpoint URL"];
                options["wsdl_options"] = { timeout: timeOut };
                let wsdlName = findWSDL();
                let wsdlPath = path.join(__dirname, wsdlName);
                soap.createClient(wsdlPath, options, (err, client) => {
                    if (err) {
                        if (err.code === "ESOCKETTIMEDOUT") {
                            global.logger.error("Error while creating client:", err.code);
                            reject(message.getResponseJson(moduleContext.language, error_codes.ESOCKETTIMEDOUT));
                        } else {
                            global.logger.error("Error while creatig client: ResourceNotFound/ConnectionRefused");
                            reject(message.getResponseJson(moduleContext.language, error_codes.resourceNotFound));
                        }
                    } else {
                        if (global.logger.isTraceEnabled()) {
                            global.logger.trace("SOAP client created successfully");
                        }
                        //Setting security
                        if (moduleContext.settings.Authentication === "BasicAuthSecurity") {
                            if (global.logger.isTraceEnabled()) {
                                global.logger.trace("Setting up basic security");
                            }
                            client.setSecurity(new soap.BasicAuthSecurity(
                                moduleContext.settings.basic_username,
                                moduleContext.settings.basic_password));
                        } else if (moduleContext.settings.Authentication === "WSSecurity") {
                            if (global.logger.isTraceEnabled()) {
                                global.logger.trace("Setting up WS security");
                            }
                            let options = {
                                hasNonce: true,
                                actor: "actor"
                            };
                            let wsSecurity = new soap.WSSecurity(
                                moduleContext.settings.ws_username,
                                moduleContext.settings.ws_password, options)
                            client.setSecurity(wsSecurity);
                        } else if (moduleContext.settings.Authentication === "SOAPHeaders") {
                            if (global.logger.isTraceEnabled()) {
                                global.logger.trace("Setting up credentials in SOAP Headers");
                            }
                            client.addSoapHeader("UserName", moduleContext.settings.username);
                            client.addSoapHeader("Password", moduleContext.settings.password);
                        }
                        //method name
                        let method = client[moduleContext.operationName];
                        if (global.logger.isTraceEnabled()) {
                            global.logger.trace("Firing up the SOAP request buffer");
                        }
                        method(moduleContext.process, (err, result, envelope) => {
                            if (err) {
                                global.logger.error("Error while getting the response");
                                if (err.root) {
                                    let retJSON = message.getResponseJson(moduleContext.language, error_codes.soapFaultCode);
                                    retJSON.soapFault = err.root.Envelope.Body.Fault;
                                    resolve(retJSON);
                                } else {
                                    resolve(message.getResponseJson(moduleContext.language, error_codes.soapExecErr));
                                }
                            } else {
                                if (global.logger.isInfoEnabled()) {
                                    global.logger.info("Got the response successfully");
                                }
                                resolve({
                                    code: error_codes.success,
                                    msg: result
                                });
                            }
                        }, { timeout: timeOut });
                    }
                });
            } catch (err) {
                global.logger.error("Encountered error in SOAP exec function:", err);
                reject(message.getResponseJson(moduleContext.language, error_codes.soapExecErr));
            }
        });
    }

    close() {
        //TODO: Need to implement
    }
}

//////////////////////////////////////////////////
///////Internal Function
/////////////////////////////////////////////////
/**
 * @description Finds and return wsdl from present directory
 * @returns wsdl filename
 */
function findWSDL() {
    let files = fs.readdirSync(__dirname);
    for (let i in files) {
        if (files[i].includes(".wsdl")) return files[i];
    }
}

module.exports = SOAPPlugin;
