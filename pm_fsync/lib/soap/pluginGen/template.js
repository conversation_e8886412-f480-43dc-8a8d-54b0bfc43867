module.exports = {
    "typeId": "",
    "name": "",
    "title": "",
    "type": "object",
    "required": ["name", "coordinates", "settings", "process", "output"],
    "properties": {
        "name": {
            "description": "Name of the module",
            "title": "",
            "type": "string",
            "minLength": 1
        },
        "coordinates": {
            "description": "Coordinates Of the Module",
            "title": "Coordinates",
            "type": "object",
            "properties": {
                "id": {
                    "description": "Coordinate ID",
                    "title": "Coordinate ID",
                    "type": "string",
                    "minLength": 1
                },
                "type": {
                    "description": "Coordinate type",
                    "title": "Coordinate Type",
                    "type": "string",
                    "minLength": 1
                },
                "ports": {
                    "description": "Coordinate ports",
                    "title": "Coordinate ports",
                    "type": "array",
                    "minItems": 1
                },
                "nodedata": {
                    "description": "Node data",
                    "title": "Node data",
                    "type": "object",
                    "properties": {
                        "title": {
                            "description": "The title of node",
                            "title": "Module title",
                            "type": "string"
                        },
                        "name": {
                            "description": "The name of node",
                            "title": "Module Name",
                            "type": "string"
                        },
                        "id": {
                            "description": "The Id of node",
                            "title": "Module ID",
                            "type": "string"
                        }
                    }
                }
            }
        },
        "settings": {
            "description": "Setting properties Of the Module",
            "title": "Settings",
            "type": "object",
            "required": [
                "Endpoint URL",
                "SOAP Header"
            ],
            "properties": {
                "Endpoint URL": {
                    "description": "Endpoint URL of the SOAP service",
                    "title": "Endpoint URL",
                    "type": "string",
                    "default": "",
                    "minLength": 1
                },
                "Authentication": {
                    "title": "Authentication",
                    "type": "object",
                    "hint": "choiceType",
                    "oneOf": [
                        {
                            "title": "No Auth",
                            "type": "null"
                        },
                        {
                            "title": "BasicAuthSecurity",
                            "type": "object",
                            "required": [
                                "basic_username",
                                "basic_password"
                            ],
                            "properties": {
                                "basic_username": {
                                    "title": "Username",
                                    "type": "string"
                                },
                                "basic_password": {
                                    "title": "Password",
                                    "hint": "password",
                                    "type": "string"
                                }
                            }
                        },
                        {
                            "title": "WSSecurity",
                            "type": "object",
                            "required": [
                                "ws_username",
                                "ws_password"
                            ],
                            "properties": {
                                "ws_username": {
                                    "title": "Username",
                                    "type": "string"
                                },
                                "ws_password": {
                                    "title": "Password",
                                    "hint": "password",
                                    "type": "string"
                                }
                            }
                        },
                        {
                            "title": "SOAPHeaders",
                            "type": "object",
                            "required": [
                                "username",
                                "password"
                            ],
                            "properties": {
                                "username": {
                                    "title": "Username",
                                    "type": "string"
                                },
                                "password": {
                                    "title": "Password",
                                    "hint": "password",
                                    "type": "string"
                                }
                            }
                        }
                    ]
                },
                "SOAP Header": {
                    "description": "SOAP Header",
                    "title": "SOAP Header",
                    "type": "string",
                    "default": "WSDL",
                    "minLength": 1,
                    "readOnly": true
                }
            }
        },
        "input": {
            "description": "Input parameters",
            "title": "Input",
            "type": "object"
        },
        "process": {
            "description": "Setting properties of the Module",
            "title": "Process",
            "type": "object",
            "required": [],
            "properties": {}
        },
        "response": {
            "description": "Success Response of the Module",
            "title": "Response",
            "type": "object",
            "required": [],
            "properties": {}
        },
        "output": {
            "description": "The output params",
            "type": "object"
        }
    }
};
