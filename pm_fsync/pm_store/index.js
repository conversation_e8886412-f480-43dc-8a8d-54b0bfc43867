/**
 *  LEAP - PluginStore
 *
 * pmstore is the NPM module which provides APIs for all CRUD operations on LEAP Plugins
 *
 * Following APIs for API_GW
 *  1. listPlugins(req, res)
 *  2. createOrUpdatePlugin(plugin);
 *  3. deletePlugin(pluginId|pluginName);
 *  4. findPlugin(pluginId|pluginName);
 *
 **/
const fs = require("fs");
const path = require("path");
var Sequelize;

const common = require("common");
const OAM = require("oam");
const utility = require("utility");

const DEFAULT_SORT_ORDER = "asc";

const oam_alert_oid = "pmstore";

module.exports = {

  init: () => {
    return new Promise((resolve, reject) => {
      const basename = path.basename(module.filename);
      const dbDir = path.join(__dirname, "models");

      global.config.app_store.operatorsAliases = common.operator_aliases;
      global.logger.warn("Timezone:" + (global.config.app_store.timezone || "default"));

      let Sequelize;
      switch (global.config.app_store.dialect) {
        case "oracle":
          Sequelize = require("sequelize-oracle");
          global.jsonsupport = false;
          break;
        case "mysql":
          Sequelize = require("sequelize");
          global.jsonsupport = false;
          break;
        case "mariadb":
          Sequelize = require("sequelize");
          global.jsonsupport = true;
          break;
        default:
          Sequelize = require("sequelize");
          global.jsonsupport = true;
      }

      global.leapDB = new Sequelize(
        global.config.app_store.database,
        global.config.app_store.username,
        global.config.app_store.password,
        global.config.app_store
      );

      fs.readdirSync(dbDir)
        .filter((file) => {
          return file.indexOf(".") !== 0 && file !== basename;
        })
        .forEach((file) => {
          if (file.slice(-3) !== ".js") {
            return;
          }
          let filepath = path.join(dbDir, file);
          if (global.logger.isTraceEnabled()) {
            global.logger.trace("Importing File: " + filepath);
          }
          global.leapDB["import"](filepath);
        });

      global.leapDB.sync()
        .then(async () => {
          let result;
          switch (global.config.app_store.dialect) {
            case "oracle":
              result = await global.leapDB.query("SELECT * from TAB");
              break;
            case "mysql":
            case "mariadb":
            default:
              result = await global.leapDB.query("SELECT 1");
          }
          OAM.emit("clearAlert", oam_alert_oid);
          resolve(result);
        })
        .catch((e) => {
          global.logger.error(e);
          OAM.emit("criticalAlert", oam_alert_oid);
          reject(e);
        });
    });
  },

  count: async (opts) => {
    let headers = { totalPlugins: 0 };

    try {
      let sequel_opts = {},
        page = 1,
        size = Number.MAX_SAFE_INTEGER;
      if (opts != null) {
        page = parseInt(opts.page, 10), size = parseInt(opts.size, 10);
        sequel_opts = applyFilters(opts, sequel_opts);
      }
      const totalPlugins = await global.leapDB.models.PluginsMaster.count(sequel_opts);
      size = size > totalPlugins ? totalPlugins : size;
      const lastPage = Math.ceil(totalPlugins / size);
      headers = {
        totalPlugins,
        lastPage,
        prevPage: page > 1 ? page - 1 : page,
        nextPage: page < lastPage ? page + 1 : lastPage,
        pageSize: size
      };
    } catch (e) {
      global.logger.error("Exception while retreiving plugins headers", e);
      OAM.emit("criticalAlert", oam_alert_oid);
    }
    return headers;
  },

  listPlugins: async (opts) => {
    let plugins = null;
    try {
      let sequel_opts = {
        attributes: ["id", "category", "name", "description", "type", "status"]
      };

      if (opts != null) {
        sequel_opts = applyFilters(opts, sequel_opts);
      }
      // Converting the Sequelize object to Plain JSON Object
      if (global.logger.isTraceEnabled()) { global.logger.trace("PluginStore Request params|" + JSON.stringify(sequel_opts)); }
      let list = await global.leapDB.models.PluginsMaster.findAll(sequel_opts);
      plugins = JSON.parse(JSON.stringify(list));

    } catch (e) {
      global.logger.error("Exception while retreiving plugins", e);
      OAM.emit("criticalAlert", oam_alert_oid);
    }
    return plugins;
  },

  listPluginCategories: async () => {

    let plugins = null;
    try {
      let sequel_opts = {
        attributes: ["category"],
        distinct: true
      };
      // Converting the Sequelize object to Plain JSON Object
      plugins = await global.leapDB.models.PluginsMaster.findAll(sequel_opts);
    } catch (e) {
      global.logger.error("Exception while retreiving plugins", e);
      OAM.emit("criticalAlert", oam_alert_oid);
    }
    return plugins;
  },

  createPlugin: async (pluginInfo) => {
    let res = null;
    try {
      pluginInfo.settings = await utility.deflateJSON(pluginInfo.settings, global.jsonsupport);
      res = await global.leapDB.models.PluginsMaster.create(pluginInfo);
    } catch (e) {
      global.logger.error("Exception while creating plugins", e);
      OAM.emit("criticalAlert", oam_alert_oid);
    }
    return (res != null);
  },

  bulkCreatePlugin: async (bulk) => {
    let res = null;
    try {
      let updatedBulk = [];
      bulk.forEach(async item => {
        item.settings = await utility.deflateJSON(item.settings, global.jsonsupport);
        updatedBulk.push(item);
      });
      res = await global.leapDB.models.PluginsMaster.bulkCreate(updatedBulk);
    } catch (e) {
      global.logger.error("bulkCreatePlugin failed", e);
      OAM.emit("criticalAlert", oam_alert_oid);
    }
    return (res != null && res.length == bulk.length);
  },

  updatePlugin: async (pluginInfo) => {
    let result;
    try {
      let modify = Object.assign({}, pluginInfo.settings);
      let modifyLog = Object.assign({}, modify, {
        dev: Object.assign({}, modify.dev, { password: "xxxxxxx" }), // NOSONAR : This is just a sample value
        prod: Object.assign({}, modify.prod, { password: "xxxxxxx" }) // NOSONAR : This is just a sample value
      });

      if (global.logger.isTraceEnabled()) {
        global.logger.trace("Name:" + pluginInfo.id + ", Updating plugin: " + JSON.stringify(modifyLog));
      }

      pluginInfo.settings = await utility.deflateJSON(pluginInfo.settings, global.jsonsupport);
      result = await global.leapDB.models.PluginsMaster.update(pluginInfo, {
        where: {
          id: pluginInfo.id
        }
      });

    } catch (e) {
      global.logger.error(e);
      OAM.emit("criticalAlert", oam_alert_oid);
    }
    return result;
  },

  deletePlugin: async (pluginId) => {
    let result = 0;
    try {
      let filter = {
        force: true,
        where: {
          $and: {
            id: pluginId,
            type: {
              $ne: 0
            }
          }
        }
      };
      result = await global.leapDB.models.PluginsMaster.destroy(filter);

    } catch (e) {
      global.logger.error(e);
      OAM.emit("criticalAlert", oam_alert_oid);
    }
    return (result > 0);
  },

  findPlugin: async (pluginId) => {
    let plugin = null;
    try {
      let filter = {
        attributes: ["id", "category", "name", "description", "status", "type", "settings", "path"],
        where: {
          id: pluginId
        }
      };

      plugin = await global.leapDB.models.PluginsMaster.findOne(filter);
      if (plugin != null) {
        plugin = JSON.parse(JSON.stringify(plugin));
        plugin.settings = await utility.unzipJSON(plugin.settings, global.jsonsupport);
      }
    } catch (e) {
      global.logger.error(e);
      OAM.emit("criticalAlert", oam_alert_oid);
    }
    return plugin;
  },

  allPlugins: async () => {
    let plugins = [];
    try {
      let plugins1 = await global.leapDB.models.PluginsMaster.findAll({
        attributes: ["id", "category", "name", "status", "type", "settings"]
      });
      if (plugins1 != null) {
        plugins1 = JSON.parse(JSON.stringify(plugins1));
        for (let i = 0; i < plugins1.length; i++) {
          let plugin = plugins1[i];
          plugin.settings = await utility.unzipJSON(plugin.settings, global.jsonsupport);
          plugins.push(plugin);
        }
      }
    } catch (e) {
      global.logger.error(e);
      OAM.emit("criticalAlert", oam_alert_oid);
    }
    return plugins;
  }
};

/**
 *  A helper function to extract sorting options
 *  from request object and construct an Object
 *  that could be used in sequelize query expression.
 *  @param {Object} req
 *  @param {JSONObject} opts
 **/
function getSortOptions(opts, sequel_opts) {
  if (opts.sortf) {
    let sortArray = [];
    if (opts.order) {
      sortArray.push(opts.sortf);
      sortArray.push(opts.order);
    } else {
      sortArray.push(opts.sortf);
      sortArray.push(DEFAULT_SORT_ORDER);
    }
    sequel_opts.order = [sortArray];
  }
  return sequel_opts;
}

function getPaginationOptions(opts, sequel_opts) {

  let pageno = opts.page ? parseInt(opts.page, 10) : 1;
  if (pageno <= 0) {
    pageno = 1;
  }
  sequel_opts.limit = opts.size ? parseInt(opts.size, 10) : 10;
  sequel_opts.offset = (pageno - 1) * sequel_opts.limit;
  return sequel_opts;
}

function getFilteringOptions(opts, sequel_opts) {

  let tokenFilter = [];
  if (opts.token && opts.token.trim().length > 0) {
    opts.token.split(",").forEach((item) => {
      let token = item.trim();
      if (token.length > 0) {
        tokenFilter.push({
          id: {
            $like: "%" + token + "%"
          }
        });
        tokenFilter.push({
          category: {
            $like: "%" + token + "%"
          }
        });
        tokenFilter.push({
          name: {
            $like: "%" + token + "%"
          }
        });
        tokenFilter.push({
          description: {
            $like: "%" + token + "%"
          }
        });
      }
    });
  } else {
    tokenFilter.push({
      id: {
        $like: "%"
      }
    });
    tokenFilter.push({
      name: {
        $like: "%"
      }
    });
    tokenFilter.push({
      description: {
        $like: "%"
      }
    });
  }
  if (opts.status == null) {
    opts.status = {
      $like: "%"
    };
  } else {
    try {
      opts.status = {
        $in: opts.status.split(",")
      };
    } catch (e) {
      global.logger.error(e);
    }
  }

  if (opts.category == null) {
    opts.category = {
      $like: "%"
    };
  } else {
    try {
      let arr = [];
      opts.category.split(",").forEach(item => {
        arr.push(item);
      });
      opts.category = {
        $in: arr
      };
    } catch (e) {
      global.logger.error(e);
    }
  }

  if (opts.type == null) {
    opts.type = {
      $like: "%"
    };
  } else {
    try {
      let arr = [];
      opts.type.split(",").forEach(item => {
        arr.push(Number(item));
      });
      opts.type = {
        $in: arr
      };
    } catch (e) {
      global.logger.error(e);
    }
  }
  sequel_opts.where = {
    $and: {
      type: opts.type,
      status: opts.status,
      category: opts.category,
      $or: tokenFilter
    }
  };
  return sequel_opts;
}

function applyFilters(opts, sequel_opts) {
  sequel_opts = getSortOptions(opts, sequel_opts);
  sequel_opts = getPaginationOptions(opts, sequel_opts);
  sequel_opts = getFilteringOptions(opts, sequel_opts);
  return sequel_opts;
}
