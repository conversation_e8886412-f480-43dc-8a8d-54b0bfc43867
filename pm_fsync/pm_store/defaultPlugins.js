module.exports = [
  {
    id: 0,
    name: "appModules",
    description: "App Modules",
    settings: {}
  },
  {
    id: 1,
    name: "channelModules",
    description: "Channel Modules",
    settings: {}
  },
  {
    id: 2,
    name: "databases",
    description: "Database Modules",
    settings: {}
  },
  {
    id: 3,
    name: "ucip",
    description: "UCIP",
    settings: {
      dev: {
        host: "127.0.0.1",
        port: 2144,
        path: "/mock/ucip",
        username: "test",
        password: "test",
        "User-Agent": "test"
      },
      prod: {
        host: "127.0.0.1",
        port: 2144,
        path: "/ucip",
        username: "leap",
        password: "leap123",
        "User-Agent": "prod"
      }
    }
  },
  {
    id: 4,
    name: "acip",
    description: "ACIP",
    settings: {
      dev: {
        host: "127.0.0.1",
        port: 2144,
        path: "/mock/acip",
        username: "test",
        password: "test",
        "User-Agent": "test"
      },
      prod: {
        host: "127.0.0.1",
        port: 2144,
        path: "/acip",
        username: "leap",
        password: "leap123",
        "User-Agent": "prod"
      }
    }
  },
  {
    id: 5,
    name: "miscellaneous",
    description: "Miscellaneous",
    settings: {}
  },
  {
    id: 6,
    name: "vcip",
    description: "VCIP",
    settings: {
      dev: {
        host: "127.0.0.1",
        port: 2144,
        path: "/mock/vsip",
        username: "test",
        password: "test",
        "User-Agent": "test"
      },
      prod: {
        host: "127.0.0.1",
        port: 2144,
        path: "/vsip",
        username: "leap",
        password: "leap123",
        "User-Agent": "prod"
      }
    }
  }
];
