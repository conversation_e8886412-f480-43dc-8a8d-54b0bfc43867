module.exports = function (sequelize, DataTypes) {
  let props;
  const jsonsupport = global.jsonsupport || false;
  if (jsonsupport) {
    props = {
      id: { type: DataTypes.STRING(4), primaryKey: true },
      category: { type: DataTypes.STRING(60), allowNull: false },
      name: { type: DataTypes.STRING(60), allowNull: false },
      description: { type: DataTypes.TEXT },
      type: { type: DataTypes.INTEGER, defaultValue: 0 },
      status: { type: DataTypes.INTEGER, defaultValue: 1 },
      settings: { type: DataTypes.JSON, defaultValue: {} },
      path: { type: DataTypes.TEXT }
    }
  }
  else {
    props = {
      id: { type: DataTypes.STRING(4), primaryKey: true },
      category: { type: DataTypes.STRING(60), allowNull: false },
      name: { type: DataTypes.STRING(60), allowNull: false },
      description: { type: DataTypes.TEXT },
      type: { type: DataTypes.INTEGER, defaultValue: 0 },
      status: { type: DataTypes.INTEGER, defaultValue: 1 },
      settings: { type: DataTypes.TEXT("long") },
      path: { type: DataTypes.TEXT }
    };
  }
  const PluginsMaster = sequelize.define("PluginsMaster", props, {
    paranoid: true,
    timestamps: true,
    tableName: "plugins_master"
  });
  return PluginsMaster;
};
