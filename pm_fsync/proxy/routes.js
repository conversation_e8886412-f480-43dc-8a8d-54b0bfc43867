"use strict";

const PluginProxy = require("./services");
const Router = require("express").Router();
const PluginExec = require("./exec");

Router.get("/", PluginProxy.getPluginDirectoryStructure);
Router.get("/fsbuffer", PluginProxy.getPluginFileBuffer);

Router.get("/plugins", PluginProxy.listPluginStats);
Router.get("/plugins/categories", PluginProxy.listPluginCategories);
Router.patch("/plugins/:pluginId", PluginProxy.changePluginStatus);
Router.delete("/plugins/:pluginId", PluginProxy.deletePlugin);

Router.get("/settings/:mode", PluginProxy.getModeSettings);
Router.get("/status", PluginProxy.getPlginStatus);
Router.get("/plugins/settings/:pluginId", PluginProxy.retrievePluginSettings);
Router.post("/plugins/settings/:pluginId", PluginProxy.updatePluginSettings);
Router.post("/plugins/execute/:pluginName", PluginExec.execute);

module.exports = Router;
