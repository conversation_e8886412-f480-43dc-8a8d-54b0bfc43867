/**
 *  <PERSON><PERSON>nis for LEAP - PluginManager
 *  PluginsManager entry point
 *  <AUTHOR>
 **/

const fs = require("fs");
const dirToJson = require("dir-to-json");
const path = require("path");
const utility = require("utility");
const PluginStore = require("../pm_store/index");
const common = require("common");
const message = require("message");
const OAM = require("oam");
const Ajv = require("ajv");
const error = "error";
const warn = "warn";

const HTTP = common.http_codes;

const oam_alert_oid = "pmfsync_pmstore_conn";

const error_codes = message.error_codes;

const whiteboard = common.whiteboard;

const logger = global.logger || require("log4js").getLogger();
const DEFAULT_PAGESIZE = 10;
const BUILT_IN_PLUGINS = 8;
global.customPluginId = BUILT_IN_PLUGINS + 1;
const plugin_types = {
  0: "Built-In",
  1: "Custom"
};

const plugin_states = {
  0: "Inactive",
  1: "Active"
};

module.exports = {

  init: async () => {
    try {
      global.fileData = {};
      let basedir = path.join(__dirname, "../plugins");
      // Calling load plugins function.
      logger.warn("Starting plugin scanner");
      await scanDirectories(basedir);
      logger.warn("Completed plugin scanner");
    } catch (error) {
      logger.error(error);
    }
  },

  getPluginStates: (req, res) => {
    res.json(plugin_states);
  },

  getPluginTypes: (req, res) => {
    res.json(plugin_types);
  },

  getPluginDirectoryStructure: (req, res) => {
    try {
      if (logger.isTraceEnabled()) {
        logger.trace("getPluginDirectoryStructure qs:" + JSON.stringify(req.query));
      }
      res.json(global.pluginsDirInfo);
    } catch (error) {
      logger.error(error);
      res.json({});
    }
  },

  getPluginFileBuffer: (req, res) => {
    try {
      if (logger.isTraceEnabled()) {
        logger.trace("getPluginFileBuffer qs:" + JSON.stringify(req.query), ", Present:", global.fileData.hasOwnProperty(req.query.file));
      }
      res.json(global.fileData[req.query.file]);
    } catch (error) {
      logger.error(error);
      res.json({});
    }
  },

  listPluginCategories: async (req, res) => {
    try {
      if (logger.isTraceEnabled()) {
        logger.trace("getCategories qs:" + JSON.stringify(req.query));
      }

      let list = await PluginStore.listPluginCategories({});
      let cats = {};
      list.forEach(item => {
        if (req.query && req.query.size) {
          item.category = item.category.slice(0, req.query.size);
        }
        cats[item.category] = 0;
      });
      res.json(Object.keys(cats));
    } catch (error) {
      logger.error(error);
      OAM.emit("criticalAlert", oam_alert_oid);
      res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.pluginStoreError));
    }
  },

  listPluginStats: async (req, res) => {
    try {
      if (logger.isTraceEnabled()) {
        logger.trace("listPlugins qs:" + JSON.stringify(req.query));
      }
      let opts = prepareListQuery(req);
      const headers = await PluginStore.count(Object.assign({}, opts));
      if (opts.page > headers.lastPage) {
        return res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.pluginPageDoesNotExists));
      }
      const response = preparePaginationHeaders(req, res, opts, headers);
      response.data.pluginList = await getListPlugins(opts);
      res.status(HTTP.ok.code).json(response);
    } catch (error) {
      logger.error(error);
      OAM.emit("criticalAlert", oam_alert_oid);
      res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.pluginStoreError));
    }
  },

  deletePlugin: async (req, res) => {
    try {
      if (logger.isTraceEnabled()) {
        logger.trace("deletePlugin pluginId:" + req.params.pluginId);
      }
      if (isNaN(Number(req.params.pluginId))) {
        return res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.invalidPluginId));
      }
      if (Number(req.params.pluginId) < BUILT_IN_PLUGINS) {
        return res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.builtInPlugins));
      }
      let plugin = await PluginStore.findPlugin(req.params.pluginId);


      if (plugin != null) {
        let relativePath = plugin.path;
        let absolutePath = path.join(__dirname, "../plugins", relativePath);
        let dir = path.dirname(absolutePath);
        utility.rmdir(dir);
        global.fileData = {};
        let basedir = path.join(__dirname, "../plugins");
        // Calling load plugins function.
        logger.warn("Starting plugin scanner");
        await scanDirectories(basedir);

        plugin = await PluginStore.deletePlugin(req.params.pluginId);
        if (plugin != null) {
          whiteboard.publish("delete_plugin", relativePath);
          res.json(message.getResponseJson(req.locale, error_codes.pluginDeleteSuccess, 0));
        } else {
          res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.pluginDeleteFailed));
        }
      } else {
        res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.pluginDeleteFailed));
      }

    } catch (e) {
      logger.error(e);
      OAM.emit("criticalAlert", oam_alert_oid);
      res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.pluginStoreError));
    }
  },

  getModeSettings: async (req, res) => {
    try {
      if (logger.isTraceEnabled()) {
        logger.trace("getProductionSettings ");
      }
      let settings = await PluginStore.allPlugins();
      let result = {};
      if (settings != null) {
        settings.forEach(ps => {
          if (ps.settings != null) {
            if (ps.settings.hasOwnProperty(req.params.mode)) {
              result[String(ps.id)] = ps.settings[req.params.mode];
            } else {
              result[String(ps.id)] = {};
            }
          }
        });
        res.json(result);
      } else {
        res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.pluginInfoNotFound));
      }
    } catch (error) {
      logger.error(error);
      OAM.emit("criticalAlert", oam_alert_oid);
      res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.pluginStoreError));
    }
  },

  getPlginStatus: async (req, res) => {
    try {
      if (logger.isTraceEnabled()) {
        logger.trace("getPlginStatus ");
      }
      let settings = await PluginStore.allPlugins();
      let result = {};
      if (settings != null) {
        settings.forEach(ps => {
          result[String(ps.id)] = (ps.status == 1);
        });
        res.json(result);
      } else {
        res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.pluginInfoNotFound));
      }
    } catch (error) {
      logger.error(error);
      OAM.emit("criticalAlert", oam_alert_oid);
      res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.pluginStoreError));
    }
  },

  retrievePluginSettings: async (req, res) => {
    try {
      if (logger.isTraceEnabled()) {
        logger.trace("getPluginSettings pluginId:" + req.params.pluginId);
      }
      let plugin = await PluginStore.findPlugin(req.params.pluginId);
      if (plugin == null) {
        let groupPluginId = parseInt(req.params.pluginId, 10);
        plugin = await PluginStore.findPlugin(groupPluginId);
      }
      if (plugin != null) {
        let settings = getSettings(plugin, global.plugin_settings_mapping[req.params.pluginId]);
        res.json(settings);
      } else {
        res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.pluginInfoNotFound));
      }
    } catch (error) {
      logger.error(error);
      OAM.emit("criticalAlert", oam_alert_oid);
      res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.pluginStoreError));
    }
  },

  updatePluginSettings: async (req, res) => {
    try {
      if (isEmpty(req.body)) {
        return res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.settingsPayloadRequired));
      }
      let plugin = await PluginStore.findPlugin(req.params.pluginId);
      if (plugin == null) {
        let groupPluginId = parseInt(req.params.pluginId, 10);
        plugin = await PluginStore.findPlugin(groupPluginId);
      }
      if (plugin != null) {
        let settings = getSettings(plugin, req.params.pluginId);
        let pluginValidationRes = pluginSettingsValidation(req.body, settings);

        if (!pluginValidationRes.isValid) {
          return res.status(HTTP.badRequest.code).json({ code: error_codes.settingsUpdateFailed, msg: pluginValidationRes.errors });
        }

        let pluginInfo = { id: req.params.pluginId, settings: req.body };
        let pluginUpdateRes = await PluginStore.updatePlugin(pluginInfo);
        if (pluginUpdateRes != null) {
          return res.json(message.getResponseJson(req.locale, error_codes.settingsUpdatedSuccessfully, 0));
        } else {
          return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.settingsUpdateFailed));
        }
      } else {
        return res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, error_codes.pluginInfoNotFound));
      }
    } catch (error) {
      logger.error(error);
      OAM.emit("criticalAlert", oam_alert_oid);
      res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.pluginStoreError));
    }
  },

  changePluginStatus: async (req, res) => {
    try {
      if (isEmpty(req.body)) {
        return res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.pluginPayloadRequired));
      }
      let status = Number(req.body.status);
      if (isNaN(status)) {
        return res.status(HTTP.badRequest.code).json(message.getResponseJson(req.locale, error_codes.invalidPluginStatus));
      }
      let pluginInfo = { id: req.params.pluginId, status: status };
      let plugin = await PluginStore.updatePlugin(pluginInfo);
      if (plugin && plugin[0] == 1) {
        let code = (status > 0) ? error_codes.pluginActivatedSuccessfully : error_codes.pluginDeactivatedSuccessfully;
        res.json(message.getResponseJson(req.locale, code, 0));
      } else {
        let code = (status > 0) ? error_codes.pluginActivationFailed : error_codes.pluginDeactivationFailed;
        res.status(HTTP.resourceNotFound.code).json(message.getResponseJson(req.locale, code));
      }
    } catch (error) {
      logger.error(error);
      res.status(HTTP.badGateway.code).json(message.getResponseJson(req.locale, error_codes.pluginStoreError));
      OAM.emit("criticalAlert", oam_alert_oid);
    }
  }
};

async function scanDirectories(basedir) {
  logger.warn("Scanning Plugins");
  global.pluginsDirInfo = await dirToJson(basedir);
  await readfile(basedir, global.pluginsDirInfo);
  await scanPlugins();
  let list = await PluginStore.listPlugins();
  if (list) {
    for (const plugin of Object.keys(global.plugin_settings)) {
      let val = Object.assign({}, global.plugin_settings[plugin]);
      val.settings = {};
      let item = list.filter(i => String(val.id).includes(i.id));
      if (item.length === 0 && Number(val.id) > 0) {
        await PluginStore.createPlugin(val);
      }
    }
  }
}


let rescann = 0;
async function scanPlugins() {
  try {
    logger.warn("Scanning Plugins");
    let scanfile = "../plugins/pluginsScanner";
    if (fs.existsSync(path.resolve(scanfile))) {
      delete require.cache[require.resolve(scanfile)];
    }
    let scanner = require(scanfile);
    let result = await scanner.pluginsInfo();
    global.pluginsInfo = result;
    logger.warn("Plugins scan completed");

    if (global.pluginsInfo && global.pluginsInfo.pluginsList) {
      let results = {};
      global.pluginsInfo.pluginsList.forEach(item => {
        results[item.category.id] = item.category;
        logger.trace(JSON.stringify(item.category));
      });
    }
    if (result.pluginId > 0) {
      global.customPluginId = result.pluginId;
    } else {
      global.customPluginId = BUILT_IN_PLUGINS + 1;
    }
    rescann = 0;
  } catch (error) {
    if (rescann < 5) {
      rescann++;
      setTimeout(scanPlugins, 1000);
    }
  }
}

async function readfile(basedir, tree) {

  try {
    if (!tree) return;
    if (tree.type == "directory") {
      if (logger.isTraceEnabled()) {
        logger.trace("Scanning: " + tree.path);
      }
      tree.children
        .forEach(child => {
          readfile(basedir, child);
        });
    } else if (tree.type == "file") {
      let filename = path.join(basedir, tree.path);
      if (fs.existsSync(filename)) {
        tree.cksum = await utility.checksum(filename);
        global.fileData[tree.path] = fs.readFileSync(filename);
        fs.watch(filename, { encoding: "buffer" }, async (eventType) => {
          logger.warn("File modified " + filename + ", EventType: " + eventType);
          tree.cksum = await utility.checksum(filename);
          global.fileData[tree.path] = fs.readFileSync(filename);
          if (logger.isInfoEnabled()) {
            logger.info("New Checksum: " + tree.cksum + ", tree.path:" + tree.path);
          }
          if (global.config.pm_fsync.publishPluginSyncEvents) {
            whiteboard.publish("resync_plugin", tree.path);
          }
        });
      }
    }
  } catch (error) {
    logger.error(error);
  }
}

function prepareListQuery(req) {
  let opts = {};
  opts.token = req.query.token || null;
  opts.category = req.query.category || null;
  opts.page = req.query.page || 1;
  opts.size = req.query.size || DEFAULT_PAGESIZE;
  if (!isNaN(Number(req.query.type))) {
    opts.type = Number(req.query.type);
  } else {
    opts.type = req.query.type;
  }
  if (req.query.sortf) {
    opts.sortf = req.query.sortf;
    opts.order = req.query.order;
  }
  opts.status = req.query.status || null;
  if (logger.isTraceEnabled()) { logger.trace("PluginStore Request params|" + JSON.stringify(opts)); }
  return opts;
}

function preparePaginationHeaders(req, res, opts, headers) {

  res.set("totalPlugins", headers.totalPlugins);
  res.set("pageSize", opts.size);
  if (headers.totalPlugins > 0) {
    let header = "/plugins?token=" + opts.token + "&size=" + headers.pageSize;
    if (req.query.status) {
      header += "&status=" + req.query.status
    }
    header += "&page=";
    res.set("firstPage", header + 1);
    res.set("prevPage", header + headers.prevPage);
    res.set("currPage", header + opts.page);
    res.set("nextPage", header + headers.nextPage);
    res.set("lastPage", header + headers.lastPage);
  }
  let response = {};
  response.data = {};
  response.data.pageSize = headers.pageSize;
  response.data.totalPlugins = headers.totalPlugins;
  response.data.totalPages = headers.lastPage;
  response.data.currentPage = parseInt(opts.page, 10);
  return response;
}

function getType(type) {
  if (plugin_types.hasOwnProperty(type)) return plugin_types[type];
  return "Unknown";
}

function getState(state) {
  if (plugin_states.hasOwnProperty(state)) return plugin_states[state];
  return "Unknown";
}

async function getListPlugins(opts) {
  let plugins = [];
  try {
    let list = await PluginStore.listPlugins(opts);
    list.forEach((item) => {
      let plugin = Object.assign({}, item);
      plugin.typeDesc = getType(item.type);
      plugin.statusDesc = getState(item.status);
      plugins.push(plugin);
    });
  } catch (error) {
    logger.error(error);
  }
  return plugins;
}


function fillSettings(schema, data) {

  if (isEmpty(schema)) {
    schema = {};
  }
  if (isEmpty(data)) {
    return schema;
  }

  if (typeof data == "string") {
    try {
      data = JSON.parse(data);
    } catch (error) {
      //ignore
      logger.error(error);
    }
  }
  let serviceLog = Object.assign({}, data,
    {
      dev: Object.assign({}, data.dev, { password: "xxxxxxx" }), // NOSONAR : This is just a sample value
      prod: Object.assign({}, data.prod, { password: "xxxxxxx" }) // NOSONAR : This is just a sample value
    });
  if (serviceLog.password) {
    serviceLog.password = "xxxxxxxxx"; // NOSONAR : This is just a sample value
    delete serviceLog.dev;
    delete serviceLog.prod;
  }
  logger.error("FILL SETTINGS", typeof data, JSON.stringify(serviceLog));
  let keys = Object.keys(data);
  for (let i = 0; i < keys.length; i++) {
    let key = keys[i];
    logger.error(key, data[key]);
    if (schema && schema.properties && schema.properties[key]) {
      if (typeof data[key] == "object") {
        schema.properties[key] = fillSettings(JSON.parse(JSON.stringify(schema.properties[key])), data[key]);
      } else {
        schema.properties[key].default = data[key];
      }
      let copy = {};
      Object.keys(data).forEach((key) => {
        copy[key] = data[key];
      })
      if (key == "prod" || key == "dev") {
        copy[key].password = "xxxxxxxxx"; // NOSONAR : This is just a sample value
      }
      logger.error(key, "::", copy[key], JSON.stringify(schema.properties[key]));
    }

  }
  return schema;
}

function pluginSettingsValidation(pluginData, PluginSchema) {
  let ajv = new Ajv({
    allErrors: true
  });
  let resObj = {
    "isValid": false,
    "errors": []
  };
  ajv.addFormat("Numeric", {
    type: "integer",
    validate: () => false
  });
  ajv.addFormat("Numeric", {
    type: "String",
    validate: () => false
  });

  let validate = ajv.compile(PluginSchema);
  let valid = validate(pluginData);
  // Check is module valid
  if (valid) {
    resObj.isValid = true;
  } else {
    validate.errors.forEach(e => {
      let errorInfo = {
        "parameter": "",
        "path": "",
        "severity": "",
        "msg": ""
      };

      if (e) {
        // Check is mandotory paramater missing
        if (e.keyword === "required") {
          errorInfo.parameter = e.params.missingProperty;
          errorInfo.severity = error;
          errorInfo.path = e.dataPath.slice(1);
        } else {
          let arrayOfPath = e.dataPath.split(".");
          let path = "";
          for (let i = 1; i < arrayOfPath.length - 1; i++) {
            path = path + arrayOfPath[i] + ".";
          }
          errorInfo.path = path.slice(0, -1);
          errorInfo.parameter = arrayOfPath[arrayOfPath.length - 1];
          errorInfo.severity = warn;
        }
        errorInfo.msg = e.message;
        resObj.errors.push(errorInfo);
      }
    });
  }
  return resObj;
}

function getSettings(plugin, pluginId) {
  try {
    let schema, settings = {};
    if (global.plugin_settings.hasOwnProperty(plugin.category)) {
      schema = global.plugin_settings[plugin.category].settings;
    } else if (global.plugin_settings.hasOwnProperty(plugin.name)) {
      schema = global.plugin_settings[plugin.name].settings;
    } else {
      let file = path.join(__dirname, "../plugins", plugin.path);
      if (fs.existsSync(file)) {
        schema = require(file);
        global.plugin_settings[plugin.name] = schema;
      }
    }
    let testButtonRequired = (plugin.category == "databases" || plugin.category == "channelModules");
    let url;
    switch (plugin.category) {
      case "databases":
        url = "/plugins/database/" + plugin.name + "/exec?mode=testConn";
        break;
      case "channelModules":
        url = "/plugins/channelModules/" + plugin.name + "/exec?mode=testConn";
        break;
      case "soap":
        url = "/plugins/soap/" + plugin.name + "/exec?mode=testConn";
        break;
      default:
        url = null;
    }
    let opts = {
      typeId: pluginId,
      pluginName: plugin.name,
      category: plugin.category,
      testRequired: testButtonRequired,
      url: url,
      required: ["dev", "prod"],
      properties: schema
    }
    settings = fillSettings(opts, plugin.settings);
    return settings;
  }
  catch (error) {
    throw new Error("Exception occured while gettign the plugins settings", error);
  }
}

function isEmpty(data) {
  return data == null || Object.keys(data).length == 0;
}
