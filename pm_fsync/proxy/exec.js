/**
 *  <PERSON><PERSON>nis for LEAP - PluginManager Execution
 *  PluginsManager Execution entry point
 *  <AUTHOR>
 **/

const message = require("message");
const error_codes = message.error_codes;

module.exports = {
    init: async () => {
        console.log("Initializing the Plugin Manager");
        // Calling load plugins function.
        global.logger.warn("Loading plugins...");
        try {
            if (!global.pluginsInfo) {
                global.pluginsInfo = {};
            }
            global.logger.warn("plugins loaded successfully...");
            return true;
        } catch (e) {
            global.logger.error("Error occured while loading the plugins:", e);
            return false;
        }
    },

    /**
     * initialize
     *
     * @param {any} contextData
     * @returns response coming from external interfaces
     */
    initialize: async (contextData) => {
        global.logger.trace("Module initialize request for plugin:" + contextData.pluginName);
        //Check wether the requested plugin exist, if not return plugin not found error
        if (!global.pluginsInfo.pluginsMap.hasOwnProperty(contextData.pluginName)) {
            global.logger.trace("The requested plugin does not exist:" + contextData.pluginName);
            return null;
        }
        return await global.pluginsInfo.pluginsMap[contextData.pluginName].init(contextData);
    },

    /**
     * execute
     *
     * @param {any} contextData
     * @returns response coming from external interfaces
     */
    execute: async (req, res) => {

        let pluginName = req.params.pluginName;
        try {

            let contextData = req.body;

            if (global.logger.isTraceEnabled()) {
                global.logger.trace("Module execute request for plugin:" + pluginName);
            }
            //Check wether the requested plugin exist, if not return plugin not found error
            if (!global.pluginsInfo.pluginsMap.hasOwnProperty(pluginName)) {
                if (global.logger.isTraceEnabled()) {
                    global.logger.trace("The requested plugin does not exist:" + pluginName);
                }
                return message.getResponseJson(null, error_codes.pluginNotFound);
            }
            const Timeout = new Promise(resolve => {
                setTimeout(() => {
                    resolve(message.getResponseJson(null, error_codes.pluginExecTimeOut));
                }, contextData.settings.timeout || 60000);
            });
            let plugin = global.pluginsInfo.pluginsMap[pluginName];
            res.json(await Promise.race([plugin.exec(contextData), Timeout]));
        } catch (e) {
            global.logger.error("Exception occurred while executing plugin:" + pluginName, e);
            let err = message.getResponseJson(null, error_codes.pluginInternalError);
            if (e.code != null) {
                err.msg = e.msg + ", Original Error Code:" + e.code;
            }
            if (e.msg != null) {
                err.msg = err.msg + " Trace:" + e.msg;
            }
            res.json(err);
        }
    },

    convertJson2Schema: async (pluginID, json) => {
        let schema = await global.pluginsInfo.pluginsMap[pluginID].getMetaDataInfo();
        return fillData(schema.properties.process, json);
    }
};

/**
 *find Category
 *@param {string} category
 *Check wether the requested category exists or not.
 */
function findCategory(response, category) {
    return (response.filter(item => item.category.type === category).length > 0);
}

function fillData(prop, json) {
    switch (prop.type) {
        case "object":
            let keys = Object.keys(prop.properties);
            for (let i = 0; i < keys.length; i++) {
                let property = keys[i];
                prop.properties[property] = fillData(prop.properties[property], json[property]);
            }
            break;
        case "array":
            let properties = [];

            for (let i = 0; i < json.length; i++) {
                let iprops = JSON.parse(JSON.stringify(prop.items));
                properties.push(fillData(iprops, json[i]));
            }
            prop.items.properties = properties;
            break;
        case "number":
        case "integer":
        case "boolean":
        case "string":
        default:
            if (json != null) {
                prop.value = json;
            }
    }
    return prop;
}
