{"typeId": "5.1", "name": "OnNetOffNet", "title": "OnNetOffNet Check", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["minlength", "prefix", "countrycode"], "properties": {"minlength": {"description": "Minimum length of Subscriber Number(MSISDN)", "title": "Minimum length of Subscriber Number(MSISDN)", "type": "integer", "default": 10}, "prefix": {"description": "Array of MSISDN Prefixes", "title": "Onnet Subscriber Number Prefixes", "type": "array", "minItems": 0, "default": ["98", "97-99"]}, "countrycode": {"description": "Array of country codes", "title": "Onnet Country codes", "type": "array", "minItems": 0, "default": ["91"]}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["subscriberNumber"], "properties": {"subscriberNumber": {"description": "Number which is required for validatig whether number is ONNET or OFFNET", "title": "Subscriber Number", "type": "string"}}}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"responseCode": {"description": "parameter is sent back after a message has been processed and indicates success or failure of the message ", "title": "Response Code", "type": "string"}, "message": {"description": "Response message", "title": "Response Message", "type": "string"}}}, "output": {"description": "The output params", "type": "object"}}}