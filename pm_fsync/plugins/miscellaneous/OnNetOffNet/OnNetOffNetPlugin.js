"use strict";
/**
*  MiscellaneousModule Plugins - OnNetOffNet Plugin
*
*  <AUTHOR>
**/

const MiscellaneousPlugin = require("../miscellaneousPlugin");

const ONNET = "1";
const OFFNET = "2";
const MINUS = "-";

let schema;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./OnNetOffNetSchema.json");
    return schema;
}

class OnNetOffNetPlugin extends MiscellaneousPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof OnNetOffNetPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof OnNetOffNetPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof OnNetOffNetPlugin
     */
    exec(moduleContext) {

        try {
            let subscriberNumber = moduleContext.process.subscriberNumber ||
                moduleContext.process.MSISDN ||
                moduleContext.query.MSISDN;

            subscriberNumber = String(subscriberNumber);

            if (subscriberNumber) {
                let minLength = moduleContext.settings.minlength || 0;
                let prefix = moduleContext.settings.prefix || [];
                let msisdnLength = subscriberNumber.length;
                if (msisdnLength < minLength) {
                    return Promise.resolve({
                        code: -1,
                        msg: { message: "MSISDN is not meeting minimum length criteria" }
                    });
                } else if (msisdnLength === minLength) {
                    for (let i = 0; i < prefix.length; i++) {
                        if (prefix[i].includes(MINUS)) {

                            let prefixRange = prefix[i].split(MINUS);
                            if (msisdnRangeChecker(prefixRange, subscriberNumber)) {
                                return Promise.resolve({
                                    code: ONNET,
                                    msg: { message: "ONNET number" }
                                });
                            }
                        } else if (subscriberNumber.startsWith(prefix[i])) {
                            return Promise.resolve({
                                code: ONNET,
                                msg: { message: "ONNET number" }
                            });
                        }
                    }
                } else if (msisdnLength > minLength) {
                    let countryCode = moduleContext.settings.countrycode || [];
                    for (let i = 0; i < countryCode.length; i++) {
                        if (subscriberNumber.startsWith(countryCode[i])) {
                            let cclen = countryCode[i].length;
                            if (msisdnLength === (minLength + cclen)) {
                                for (let j = 0; j < prefix.length; j++) {
                                    let tmpSub = subscriberNumber.substring(cclen);
                                    if (prefix[j].includes(MINUS)) {
                                        let prefixRange = prefix[j].split(MINUS);
                                        let tmp = Number(tmpSub.substring(0, prefixRange[0].length).trim());
                                        if (tmp > Number(prefixRange[0]) - 1 && tmp < Number(prefixRange[1]) + 1) {
                                            return Promise.resolve({
                                                code: ONNET,
                                                msg: { message: "ONNET number" }
                                            });
                                        }
                                    } else if (tmpSub.startsWith(prefix[j])) {
                                        return Promise.resolve({
                                            code: ONNET,
                                            msg: { message: "ONNET number" }
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
                return Promise.resolve({
                    code: OFFNET,
                    msg: { message: "OFFNET number" }
                });
            }
        } catch (error) {
            global.logger.error(error);
        }
        return Promise.resolve({
            code: -1,
            msg: { message: "Invalid subscriber number" }
        });

    }

    close() {

    }
}

module.exports = OnNetOffNetPlugin;


function msisdnRangeChecker(rangeArray, subscriberNumber) {
    let subscriberNumberInt = Number(subscriberNumber.substring(0, rangeArray[0].length()).trim());
    return subscriberNumberInt > (Number(rangeArray[0]) - 1) && subscriberNumberInt < (Number(rangeArray[1]) + 1);
}