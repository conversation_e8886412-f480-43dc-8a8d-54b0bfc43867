{"typeId": "5.3", "name": "CSVOperationsPlugin", "title": "CSV Operations", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["opType"], "properties": {"opType": {"description": "List Operation Type", "title": "List Operation Type -Allowed operations EXISTS, ADD, DELETE", "type": "string", "default": "EXISTS"}, "delimeter": {"description": "Delimeter", "title": "List Delimeter", "type": "string", "default": ","}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["item", "list"], "properties": {"list": {"description": "List", "title": "List", "type": "string"}, "item": {"description": "Which is required for validating whether item is present in the list, or Add entry to list, or Delete from the list", "title": "List Entry", "type": "string"}}}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"responseCode": {"description": "parameter is sent back after a message has been processed and indicates success or failure of the message ", "title": "Response Code", "type": "string"}, "list": {"description": "Updated List", "title": "Updated List", "type": "string"}}}, "output": {"description": "The output params", "type": "object"}}}