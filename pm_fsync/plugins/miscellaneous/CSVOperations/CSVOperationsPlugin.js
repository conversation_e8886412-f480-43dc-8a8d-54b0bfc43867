"use strict";
/**
*  MiscellaneousModule Plugins - CSVOperationsPlugin Plugin
*
*  <AUTHOR>
**/

const MiscellaneousPlugin = require("../miscellaneousPlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./CSVOperationsSchema.json");
    return schema;
}

class CSVOperationsPlugin extends MiscellaneousPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof CSVOperationsPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof CSVOperationsPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof CSVOperationsPlugin
     */
    exec(moduleContext) {
        try {
            let delimeter = moduleContext.settings.delimeter || ",",
                opType = moduleContext.settings.opType || "", flag = -1, message = "";

            if (moduleContext.process.list) {
                moduleContext.process.item = String(moduleContext.process.item);
                switch (opType.toLocaleUpperCase()) {
                    case "EXISTS":
                        let list = moduleContext.process.list.split(delimeter);
                        flag = (list.includes(moduleContext.process.item)) ? 0 : 1;
                        if (flag > 0) {
                            message = moduleContext.process.item + " exists in list";
                        } else {
                            message = moduleContext.process.item + " does not exists in list";
                        }
                        break;
                    case "ADD":
                        if (moduleContext.process.list.split(delimeter).includes(moduleContext.process.item)) {
                            flag = 2;
                            message = moduleContext.process.item + " alreay exists in list";
                        } else {
                            moduleContext.process.list = moduleContext.process.list + "," + moduleContext.process.item;
                            flag = 0;
                            message = moduleContext.process.item + " Added successfully to list";
                        }
                        break;
                    case "DELETE":
                        flag = 2;
                        message = moduleContext.process.item + " does not exists in list";

                        if (moduleContext.process.list.includes(moduleContext.process.item) || moduleContext.process.list.includes(moduleContext.process.item.slice(4))) {
                            let array = moduleContext.process.list.split(delimeter);
                            let index = array.indexOf(moduleContext.process.item);

                            if (index > -1) {
                                array.splice(index, 1);
                                flag = 0;
                                message = moduleContext.process.item + " Deleted successfully from list";
                            }
                            moduleContext.process.list = array.join(delimeter);
                        }
                        break;
                }

            }
            return Promise.resolve({
                code: error_codes.success,
                msg: { responseCode: flag, opType, list: moduleContext.process.list, message }
            });
        } catch (error) {
            global.logger.error(error);
        }
        return Promise.resolve({
            code: error_codes.pluginInternalError
        });
    }

    close() {

    }
}

module.exports = CSVOperationsPlugin;
