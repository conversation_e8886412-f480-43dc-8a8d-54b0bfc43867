"use strict";
/**
*  MiscellaneousModule Plugins - Dbill Plugin
*
*  <AUTHOR>
**/

const MiscellaneousPlugin = require("../miscellaneousPlugin");
const jsontoxml = require("jsontoxml");
const parser = require("xml2json");
const message = require("message");
const error_codes = message.error_codes;
const http = require("http");

let schema;

const CDRWriter = require("../../pluginCDRWriter");
const keepAliveAgent = new http.Agent({
  maxSockets: 10,
  keepAlive: true,
  maxFreeSockets: 10
});

const NS_PER_SEC = 1e9;

class DBILLPlugin extends MiscellaneousPlugin {

  init() {

  }

  /**
   * method getMetaDataInfo
   * @returns
   * @memberof DBILLPlugin
   */
  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  /**
   * method validate
   * @param {any} module
   * @returns
   * @memberof DBILLPlugin
   */
  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  /**
   * method exec
   * @param {any} context
   * @returns
   * @memberof DBILLPlugin
   */

  async exec(context) {
    let startTime = process.hrtime(), diff;
    let diagnostics = {
      tid: context.process.txnId,
      MSISDN: context.process.MSISDN
    };

    // Use a standard Promise without async
    return new Promise((resolve) => {
      try {
        let time = process.hrtime();
        diagnostics.url = context.settings.url;
        let reqJSON = context.process;

        // Clean up ocsRequest object
        Object.keys(reqJSON.ocsRequest).forEach(function (key) {
          if (!reqJSON.ocsRequest[key]) {
            delete reqJSON.ocsRequest[key];
          }
        });

        // Handle contentId if it exists
        if (reqJSON.ocsRequest.contentId) {
          let contentId = reqJSON.ocsRequest.contentId.toString();
          if (contentId.length == 9) {
            reqJSON.ocsRequest.contentId = "242" + contentId;
          }
        }

        diagnostics.ocsRequest = reqJSON.ocsRequest;
        let buffer = jsontoxml(JSON.stringify({ ocsRequest: reqJSON.ocsRequest }));
        let req = {
          url: context.settings.url,
          method: "POST",
          headers: {
            "Content-Type": "text/xml"
          },
          timeout: context.settings.timeout || context.settings.connectionTimeOut || 5000,
          body: buffer,
          agent: keepAliveAgent
        };

        diff = process.hrtime(time);
        diagnostics.serializerTime = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
        time = process.hrtime();

        // Awaiting the httpCall
        super.httpCall(req).then(dbillResponse => {
          let response;
          diagnostics.elapsedTime = dbillResponse.diagnostics.elapsedTime;
          diagnostics.responseStartTime = dbillResponse.diagnostics.responseStartTime;
          diagnostics.timingStart = dbillResponse.diagnostics.timingStart;
          diagnostics.timings = dbillResponse.diagnostics.timings;
          diagnostics.timingPhases = dbillResponse.diagnostics.timingPhases;
          diff = process.hrtime(time);
          diagnostics.httpRTT = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();

          time = process.hrtime();
          if (dbillResponse.code == 200) {
            try {
              dbillResponse.body = dbillResponse.body.split("&").join("&#38;");
              dbillResponse.body = JSON.parse(parser.toJson(dbillResponse.body));
              response = dbillResponse.body;
            } catch (error) {
              global.logger.error(error);
              response = dbillResponse.body;
            }
          } else {
            response = message.getResponseJson(context.language, dbillResponse.code);
          }

          diff = process.hrtime(time);
          diagnostics.deserializerTime = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
          diff = process.hrtime(startTime);
          diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
          diagnostics.applicationResponse = response;

          resolve({
            code: error_codes.success,
            msg: response,
            diagnostics
          });

          CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, diagnostics.pluginResponse, dbillResponse.code, diagnostics);
        }).catch(error => {
          global.logger.error(error);
          diff = process.hrtime(startTime);
          diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
          resolve({
            code: error_codes.pluginInternalError,
            msg: error
          });
          CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, diagnostics.pluginResponse, error_codes.pluginInternalError, diagnostics);
        });
      } catch (error) {
        global.logger.error(error);
        diff = process.hrtime(startTime);
        diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
        resolve({
          code: error_codes.pluginInternalError,
          msg: error
        });
        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, diagnostics.pluginResponse, error_codes.pluginInternalError, diagnostics);
      }
    });
  }


  close() {
  }
}

module.exports = DBILLPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./dbillSchema.json");
  schema.category = "dbill";
  return schema;
}
