{"typeId": "5.5", "name": "dbill", "title": "dbill", "description": "Dbill module", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["method", "url"], "properties": {"method": {"description": "HTTP Request Method", "title": "HTTP Request Method", "enum": ["GET", "POST"]}, "url": {"description": "URL of the connecting server", "title": "URL", "type": "string", "minLength": 1}, "connectionTimeOut": {"description": "Time out to connecting server or receive response from server", "title": "Connection and Read TimeOut", "default": 5000, "type": "integer"}, "maxRetry": {"description": "The Max number of retries to connect to the DBILL", "title": "maxRetry", "default": 0, "type": "integer"}, "retryInterval": {"description": "The time interval betwwen retries to connect to the DBILL in millis", "title": "retryInterval", "default": 3000, "type": "integer"}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "properties": {"ocsRequest": {"description": "Setting properties Of the Module", "title": "ocsRequest", "type": "object", "required": ["serviceNode", "sequenceNo", "requestType", "cpcgFlag", "callingParty", "subscrFlag", "startTime", "servicId", "serviceType", "bundleType", "asyncFlag", "startTime", "contentId", "firstName", "lastName", "password", "bearerId", "amount", "offerCategory", "offerId", "segement", "serviceClass"], "properties": {"serviceNode": {"description": "From where request is coming to dBIll", "title": "serviceNode", "type": "string", "minLength": 1}, "sequenceNo": {"description": "Unique Transaction ID originated from Service Requester Node", "title": "sequenceNo", "type": "string", "minLength": 1}, "requestType": {"description": "Dbill request for the service", "title": "requestType", "type": "string", "minLength": 1}, "companyName": {"description": "Company Name", "title": "CompanyName", "type": "string"}, "emailAddress": {"description": "email Address", "title": "E-mail Address", "type": "string"}, "subsChargingFlag": {"description": "subsChargingFlag", "title": "Subscriber Charging Flag", "type": "string"}, "subsChargeAmount": {"description": "subsChargeAmount", "title": "Subscriber Charge Amount", "type": "string"}, "regionId": {"description": "Username", "title": "Region Id", "type": "string"}, "origVLR": {"description": "origVLR", "title": "Origin VLR", "type": "string"}, "cpcgFlag": {"description": "Dbill flag for this request", "title": "cpcgFlag", "type": "string", "minLength": 1}, "callingParty": {"description": "A-party MSISDN Number", "title": "callingParty", "type": "string", "minLength": 1}, "calledParty": {"description": "B-party number and default as -1", "title": "calledParty", "type": "string", "default": "-1", "minLength": 1}, "planId": {"description": "Plan ID configured at Dbill end", "title": "planId", "type": "string"}, "subscrFlag": {"description": "Dbill request subscription flag", "title": "subscrFlag", "type": "string", "default": "S"}, "startTime": {"description": "Event originated time. Its format is system current millisecond.", "title": "startTime", "type": "string", "minLength": 1}, "serviceId": {"description": "serviceId configured at dBILL end and coming in request from external node", "title": "serviceId", "type": "string", "minLength": 1}, "serviceType": {"description": "Type of service like CRBT, MCA, SE etc", "title": "serviceType", "type": "string", "minLength": 1}, "flag": {"description": "Fixed value as 3B (3rd party bundle subscription flag)", "title": "flag", "type": "string", "default": "3B", "minLength": 1}, "bundleType": {"description": "Dbill request bundle type(B = Bundle N = Single/Independent)", "title": "bundleType", "enum": ["B", "N"]}, "asyncFlag": {"description": "Dbill request mode(N = Sync Mode Y = Async Mode)", "title": "asyncFlag", "type": "string"}, "usageFlag": {"description": "Dbill request usage flag(N = Validity Based Y = Unit Based)", "title": "usageFlag", "enum": ["Y", "N"]}, "renFlag": {"description": "Dbill request mode(N = Sync Mode Y = Async Mode)", "title": "renFlag", "type": "string"}, "subsTypeFlag": {"description": "Subscriber Type Flag", "title": "subsTypeFlag", "type": "string"}, "contentId": {"description": "B party number for 3rd party bundle subscription else default value as -1", "title": "Content ID", "type": "string", "default": "-1", "minLength": 1}, "firstName": {"description": "First name for 3rd party bundle subscription and default value as any string", "title": "firstName", "type": "string", "minLength": 1}, "lastName": {"description": "Last name for 3rd party bundle subscription and default value as any string", "title": "lastName", "type": "string", "minLength": 1}, "password": {"description": "Password for 3rd party bundle subscription", "title": "password", "type": "string", "minLength": 1}, "oldPassword": {"description": "old password", "title": "oldPassword", "type": "string", "minLength": 1}, "chargingFlag": {"description": "Dbill request Charging mode (Y=Charging required N= Charging not required)", "title": "chargingFlag", "type": "string"}, "offerCategory": {"description": "offerCategory information.", "title": "offerCategory", "type": "string"}, "languageId": {"description": "Not in use", "title": "languageId", "type": "string"}, "usage": {"description": "Unit usages by the subscriber.", "title": "Usage", "type": "string"}, "bearerId": {"description": "Bearer configured at dBill end.", "title": "bearerId", "type": "string", "minLength": 1}, "reqSource": {"description": "Source of request.", "title": "reqSource", "type": "string"}, "crInfo": {"description": "Circle information.", "title": "crInfo", "type": "string"}, "promoId": {"description": "promo id.", "title": "promoId", "type": "string"}, "callDuration": {"description": "callDuration information.", "title": "callDuration", "type": "string"}, "amount": {"description": "Amount information.", "title": "amount", "type": "string"}, "subsThresold": {"description": "subsThresold information.", "title": "subsThres<PERSON>", "type": "string"}, "offerId": {"description": "OfferID information.", "title": "offerId", "type": "string"}, "segement": {"description": "Segment information.", "title": "segement", "type": "string"}, "serviceClass": {"description": "SeviceClass information.", "title": "serviceClass", "type": "string"}, "OptionalParameter1": {"description": "Optional Parameters used for service specific", "title": "OptionalParameter1", "type": "string"}, "OptionalParameter2": {"description": "Optional Parameters used for service specific", "title": "OptionalParameter2", "type": "string"}, "OptionalParameter3": {"description": "Optional Parameters used for service specific", "title": "OptionalParameter3", "type": "string"}, "OptionalParameter4": {"description": "Optional Parameters used for service specific", "title": "OptionalParameter4", "type": "string"}, "OptionalParameter5": {"description": "Optional Parameters used for service specific", "title": "OptionalParameter5", "type": "string"}, "OptionalParameter9": {"description": "Optional Parameters used for service specific", "title": "OptionalParameter9", "type": "string"}}}}}, "output": {"description": "The output params", "type": "object"}}}