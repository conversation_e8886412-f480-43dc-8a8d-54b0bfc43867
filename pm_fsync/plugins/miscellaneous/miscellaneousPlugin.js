"use strict";
/**
 *  MiscellaneousModule Plugins
 *
 *  <AUTHOR>
 **/
const LeapPlugin = require("../LeapPlugin");
const message = require("message");
const error_codes = message.error_codes;

/**
 * @class channelPlugin
 */
class MiscellaneousPlugin extends LeapPlugin {

    init() {

    }

    getMetaDataInfo(moduleData) {
        moduleData.category = "misc";
        return super.getMetaDataInfo(moduleData);
    }

    /**
     * plugin validate method
     *
     * @param {any} moduleData
     * @param {any} schemaData
     * @returns
     * @memberof channelPlugin
     * @returns module validation result object
     */
    validate(moduleData, schemaData) {
        return [];
    }

    /**
     * exec method
     * @param {any} moduleContext
     * @memberof channelPlugin
     * @returns result object
     */
    exec(moduleContext) {
        return message.getResponseJson(moduleContext.language, error_codes.success);
    }

    close() {

    }

    httpCall(req) {
        return super.httpCall(req);
    }
}

module.exports = MiscellaneousPlugin;