"use strict";
/**
*  MiscellaneousModule Plugins - Async Plugin
*  Async functions are imported from the codebase and used.
**/

const MiscellaneousPlugin = require("../miscellaneousPlugin");
const message = require("message");
const error_codes = message.error_codes;
const codeBase = require("./codeBase");

let schema;

const CDRWriter = require("../../pluginCDRWriter");


const NS_PER_SEC = 1e9;

class ASYNCPlugin extends MiscellaneousPlugin {

  init() {

  }

  /**
   * method getMetaDataInfo
   * @returns
   * @memberof DBILLPlugin
   */
  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  /**
   * method validate
   * @param {any} module
   * @returns
   * @memberof DBILLPlugin
   */
  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  /**
   * method exec
   * @param {any} context
   * @returns
   * @memberof DBILLPlugin
   */

  async exec(context) {
    let startTime = process.hrtime(), diff;
    let diagnostics = {
      tid: context.process.txnId,
      MSISDN: context.process.MSISDN
    };

    // Use a standard Promise without async
    return new Promise((resolve) => {
      try {
        let time = process.hrtime();
        let reqJSON = context.process;
        let functionCall = reqJSON.function;
        let parameters = reqJSON.parameters;
        diff = process.hrtime(time);
        diagnostics.serializerTime = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
        time = process.hrtime();
        let response;

        // Function call handling
        let functionPromise;
        if (functionCall === 'encryptPGPRequest') {
          let value = parameters[0];
          let publicKey = parameters[1];
          let privateKey = parameters[2];
          let passPhrase = parameters[3];
          functionPromise = codeBase.encryptPGPRequest(value, publicKey, privateKey, passPhrase);
        } else if (functionCall === 'decryptPGPRequest') {
          let value = parameters[0];
          let publicKey = parameters[1];
          let privateKey = parameters[2];
          let passPhrase = parameters[3];
          functionPromise = codeBase.decryptPGPRequest(value, publicKey, privateKey, passPhrase);
        } else if (functionCall === 'sleepFunction') {
          let sleepTime = parameters[0];
          functionPromise = codeBase.sleepFunction(sleepTime);
        } else {
          resolve({
            code: error_codes.pluginInternalError,
            msg: "No function found"
          });
          return; // Early exit if no function found
        }

        // Handle the result of the function call
        functionPromise.then(result => {
          response = result;
          diff = process.hrtime(time);

          time = process.hrtime();
          diff = process.hrtime(time);
          diagnostics.deserializerTime = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
          diff = process.hrtime(startTime);
          diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
          diagnostics.applicationResponse = response;

          resolve({
            code: error_codes.success,
            msg: response,
            diagnostics
          });
          CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, diagnostics.pluginResponse, error_codes.success, diagnostics);
        }).catch(error => {
          global.logger.error(error);
          diff = process.hrtime(startTime);
          diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
          resolve({
            code: error_codes.pluginInternalError,
            msg: error
          });
          CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, diagnostics.pluginResponse, error_codes.pluginInternalError, diagnostics);
        });
      } catch (error) {
        global.logger.error(error);
        diff = process.hrtime(startTime);
        diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
        resolve({
          code: error_codes.pluginInternalError,
          msg: error
        });
        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, diagnostics.pluginResponse, error_codes.pluginInternalError, diagnostics);
      }
    });
  }


  close() {
  }
}

module.exports = ASYNCPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./asyncSchema.json");
  schema.category = "async";
  return schema;
}
