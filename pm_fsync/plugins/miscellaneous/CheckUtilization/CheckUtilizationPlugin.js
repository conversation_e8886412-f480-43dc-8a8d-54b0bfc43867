"use strict";
/**
 *  MiscellaneousModule Plugins - CheckUtilization Plugin
 *
 *  <AUTHOR>
 **/

const RedisStore = require("ioredis");
const MiscellaneousPlugin = require("../miscellaneousPlugin");
const DEFAULT_KEY = "CheckUtilizationKey";

let schema;
let redisPool = {};

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./CheckUtilizationSchema.json");
    return schema;
}

class CheckUtilizationPlugin extends MiscellaneousPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof CheckUtilizationPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof CheckUtilizationPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof CheckUtilizationPlugin
     */
    async exec(moduleContext) {

        let _utilization = -1;
        try {
            let utilizationLimit = moduleContext.settings.utilizationLimit;
            if (utilizationLimit > 0) {
                let redisKey = getRedisPropKey(moduleContext);
                let connection = await getConnection(redisKey);
                let key = getKey(moduleContext);
                if (moduleContext.process.CMD == "INR") {
                    connection.incr(key);
                    let todayEnd;
                    if (moduleContext.process.TYPE == "DAY") {
                        todayEnd = new Date().setHours(23, 59, 59, 999);
                        connection.expireat(key, parseInt(todayEnd / 1000, 10));
                    }
                    else if (moduleContext.process.TYPE == "HOUR") {
                        connection.expire(key, 3600);
                    }
                    else if (moduleContext.process.TYPE == "MINUTE") {
                        connection.expire(key, 60);
                    }
                }
                let _utilization = await connection.get(key);
                return Promise.resolve({ code: 0, msg: { utilization: _utilization } });
            }
        } catch (error) {
            global.logger.error(error);
        }
        return Promise.resolve({ code: 0, msg: { utilization: _utilization } });
    }

    close() {

    }
}

module.exports = CheckUtilizationPlugin;

function getKey(moduleContext) {
    let keyprefix = moduleContext.settings.keyprefix || "UC";
    return keyprefix + "_" + moduleContext.process.subscriberNumber + "_" + moduleContext.process.appId;
}

function getRedisPropKey(moduleContext) {
    if (moduleContext.settings.host === "localhost") {
        moduleContext.settings.host = "127.0.0.1";
    }

    let key = moduleContext.settings.host + "_" + moduleContext.settings.port + "_" + moduleContext.settings.database;

    if (!redisPool[key]) {

        redisPool[key] = {
            config: moduleContext.settings,
            redis: null,    // initial value, when no connection is yet attempted.
            status: 0       // status of connection.
        };
    }
    return key;
}

function getConnection(key) {
    if (!key) {
        key = DEFAULT_KEY;
    }
    return new Promise((resolve, reject) => {
        const conn = redisPool[key];
        if (conn && conn.redis != null && conn.status == 1) {
            resolve(conn.redis);
        } else {
            conn.redis = RedisStore.createClient(conn.config);
            conn.redis.on("ready", () => {
                conn.status = 1;
                return resolve(conn.redis);
            });
            conn.redis.on("error", (e) => {
                conn.redis = null;
                conn.status = 0;
                return reject(e);
            });
        }
    });
}
