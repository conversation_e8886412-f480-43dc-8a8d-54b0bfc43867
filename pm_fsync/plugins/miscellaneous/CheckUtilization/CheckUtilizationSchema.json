{"typeId": "5.2", "name": "CheckUtilization", "title": "Utilization Check", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["host", "port", "db"], "properties": {"host": {"description": "The hostname of the redis database", "title": "<PERSON><PERSON>", "default": "localhost", "type": "string"}, "port": {"description": "The port number of the redis database", "title": "Redis Cache Port", "default": 6379, "type": "integer"}, "db": {"description": "Database Index", "title": "Redis Database Index", "type": "integer", "default": 0}, "user": {"description": "User of redis account", "title": "User Name", "type": "string"}, "password": {"description": "Password of redis account", "title": "Password", "type": "string"}, "utilizationLimit": {"description": "Service Utilization Limit, 0 for disabling", "title": "Service Utilization Limit, 0 for disabling", "type": "integer", "default": 0}, "keyprefix": {"description": "Service Utilization Redis Key Prefix", "title": "Service Utilization Redis Key Prefix", "type": "string"}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["subscriberNumber", "appId"], "properties": {"appId": {"description": "AppId", "title": "AppId", "type": "string"}, "subscriberNumber": {"description": "Number which is required for validatig whether number is ONNET or OFFNET", "title": "Subscriber Number", "type": "string"}, "CMD": {"description": "Command to execute", "title": "Command", "enum": ["INR", "GET"]}, "TYPE": {"description": "Utilization rotation Type", "title": "Usage TYPE", "enum": ["DAY", "HOUR", "MINUTE"]}}}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"responseCode": {"description": "parameter is sent back after a message has been processed and indicates success or failure of the message ", "title": "Response Code", "type": "string"}, "utilization": {"description": "Current utilization", "title": "Current utilization", "type": "integer"}}}, "output": {"description": "The output params", "type": "object"}}}