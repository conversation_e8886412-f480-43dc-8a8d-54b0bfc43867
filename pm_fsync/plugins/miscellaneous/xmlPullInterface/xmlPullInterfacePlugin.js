"use strict";
/**
*  MiscellaneousModule Plugins - XML-PULL Interface Plugin
*
*  <AUTHOR>
**/

const MiscellaneousPlugin = require("../miscellaneousPlugin");
const parser = require("xml2json");
const DecodeXML = require("unescape");
const message = require("message");
const error_codes = message.error_codes;
const http = require("http");


const CDRWriter = require("../../pluginCDRWriter");
const keepAliveAgent = new http.Agent({
    maxSockets: 10,
    keepAlive: true,
    maxFreeSockets: 10,
    timeout: 30000
});

const NS_PER_SEC = 1e9;

let schema;

/**
 * @class xmlPullInterfacePlugin
 */
class xmlPullInterfacePlugin extends MiscellaneousPlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof xmlPullInterfacePlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof xmlPullInterfacePlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} context
     * @returns
     * @memberof xmlPullInterfacePlugin
     */

    exec(context) {
        let startTime = process.hrtime(), diff;
        let diagnostics = {
            tid: context.process.transactionId,
            sid: context.process.sessionId,
            MSISDN: context.process.msisdn,
            NR: context.process.newRequest,
            SI: context.process.subsInput
        };

        return new Promise((resolve) => { // Removed async
            let time = process.hrtime();
            let timeout = context.settings.timeout || 30000;
            diagnostics.url = context.settings.url;
            diagnostics.timeout = timeout;

            getXmlBuffer(context.process)
                .then(async (body) => { // Handle async inside the then
                    diagnostics.serializerTime = Number((process.hrtime(time)[0] * NS_PER_SEC + process.hrtime(time)[1]) / 1000000).toFixed();
                    time = process.hrtime();

                    let req = {
                        url: context.settings.url,
                        method: "POST",
                        headers: {
                            "Content-Type": "text/xml"
                        },
                        timeout,
                        body,
                        agent: keepAliveAgent
                    };

                    let cisResponse = await super.httpCall(req);
                    let response;
                    diagnostics.elapsedTime = cisResponse.diagnostics.elapsedTime;
                    diagnostics.responseStartTime = cisResponse.diagnostics.responseStartTime;
                    diagnostics.timingStart = cisResponse.diagnostics.timingStart;
                    diagnostics.timings = cisResponse.diagnostics.timings;
                    diagnostics.timingPhases = cisResponse.diagnostics.timingPhases;
                    diagnostics.httpRTT = Number((process.hrtime(time)[0] * NS_PER_SEC + process.hrtime(time)[1]) / 1000000).toFixed();

                    time = process.hrtime();
                    if (cisResponse.code == 200) {
                        try {
                            cisResponse.body = cisResponse.body.split("&").join("&#38;");
                            cisResponse.body = JSON.parse(parser.toJson(cisResponse.body));
                            response = cisResponse.body.response;
                            response.applicationResponse = DecodeXML(response.applicationResponse);
                        } catch (error) {
                            response = cisResponse.body;
                        }
                    } else {
                        response = message.getResponseJson(context.language, cisResponse.code);
                    }

                    diagnostics.deserializerTime = Number((process.hrtime(time)[0] * NS_PER_SEC + process.hrtime(time)[1]) / 1000000).toFixed();
                    diff = process.hrtime(startTime);
                    diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
                    diagnostics.applicationResponse = response.applicationResponse;

                    let code;
                    if (cisResponse.code == 200) {
                        if (response.applicationResponse != null && response.applicationResponse.trim().length > 0) {
                            code = 200;
                            resolve({
                                code: error_codes.success,
                                msg: response,
                                diagnostics
                            });
                        } else {
                            code = error_codes.pluginEmptyResponse;
                            resolve({
                                code: error_codes.success,
                                msg: {
                                    freeflowState: "FB",
                                    applicationResponse: "Sorry! System is busy. please try again later."
                                },
                                diagnostics
                            });
                        }
                    } else {
                        code = cisResponse.code;
                        resolve({
                            code: cisResponse.code,
                            msg: cisResponse.body,
                            diagnostics
                        });
                    }
                    CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, diagnostics.pluginResponse, code, diagnostics);
                })
                .catch(error => { // Handle errors from getXmlBuffer
                    diff = process.hrtime(startTime);
                    diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
                    resolve({
                        code: error_codes.pluginInternalError,
                        msg: error
                    });
                    CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, diagnostics.pluginResponse, error_codes.pluginInternalError, diagnostics);
                });
        });
    }


    close() {

    }
}
module.exports = xmlPullInterfacePlugin;

/* loads the app start schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./xmlPullInterfaceSchema.json");
    schema.category = "miscellaneous";
    return schema;
}

function getTag(header, data) {
    let tag = "<" + header + ">" + data + "</" + header + ">";
    return tag;
}

function getXmlBuffer(data) {
    let schema = loadSchemaFile();
    return new Promise(resolve => {
        let message = [];
        let keys = Object.keys(data);
        for (let i = 0; i < keys.length; i++) {
            try {
                let key = keys[i];
                let value = data[key];
                if (!schema.properties.process.required.includes(key) && value == null) {
                    key = null;
                } else if (value == null) {
                    value = "";
                }
                switch (key) {
                    case "cellId":
                    case "dateFormat":
                    case "gsmMapUserSpecVal":
                    case "hlr":
                    case "vlr":
                    case "imsi":
                    case "lac":
                    case "language":
                    case "mcc":
                    case "mnc":
                    case "msc":
                    case "msisdn":
                    case "newRequest":
                    case "nodeIdentifier":
                    case "region":
                    case "freeflow":
                    case "sessionId":
                    case "transactionId":
                        message.push(getTag(key, value)); break;
                    case "subsInput":
                        message.push(getTag("subscriberInput", value)); break;
                }
            } catch (e) {
                global.logger.error(e);
            }
        }
        resolve("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><request type=\"pull\">" + message.join("") + "</request>");
    });
}
