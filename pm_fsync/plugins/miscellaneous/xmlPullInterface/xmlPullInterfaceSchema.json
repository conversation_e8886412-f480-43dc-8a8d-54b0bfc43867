{"typeId": "5.4", "name": "xmlPullInterface", "title": "XML PULL Interface", "description": "HTTP Channel functions as a request– response protocol in the client– server computing mode", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["url"], "properties": {"url": {"description": "HTTP URL", "type": "string", "title": "HTTP URL"}, "AMCMain": {"description": "App Menu Code - Main Menu", "type": "string", "title": "App Menu Code - Main Menu"}, "AMCBack": {"description": "App Menu Code - Previous Page", "type": "string", "title": "App Menu Code - Previous Page"}, "AMCContinue": {"description": "App Menu Code - Continue Processing", "type": "string", "title": "App Menu Code - Continue Processing"}, "timeout": {"description": "The time interval defines the Request timeout in millis", "title": "Request Timeout in ms", "default": 10000, "type": "integer"}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["sessionId", "transactionId", "newRequest", "msisdn", "freeflow", "subsInput"], "properties": {"sessionId": {"type": "string", "title": "Session ID"}, "transactionId": {"type": "string", "title": "Transaction ID"}, "newRequest": {"type": "string", "title": "Is New Request"}, "msisdn": {"type": "string", "title": "MSISDN"}, "freeflow": {"type": "string", "title": "Free Flow Mode"}, "subsInput": {"type": "string", "title": "Subscriber Input"}, "cellId": {"type": "string", "title": "Cell ID"}, "gsmMapUserSpecVal": {"type": "string", "title": "GSM map User Specification"}, "hlr": {"type": "string", "title": "HLR"}, "vlr": {"type": "string", "title": "VLR"}, "imsi": {"type": "string", "title": "IMSI"}, "lac": {"type": "string", "title": "LAC"}, "language": {"type": "string", "title": "Language"}, "mcc": {"type": "string", "title": "MCC"}, "mnc": {"type": "string", "title": "MNC"}, "msc": {"type": "string", "title": "MSC"}, "nodeIdentifier": {"type": "string", "title": "Node Identifier"}, "region": {"type": "string", "title": "Region"}}}, "output": {"description": "The output params", "type": "object"}}}