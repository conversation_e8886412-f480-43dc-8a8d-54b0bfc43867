const LeapPlugin = require("./LeapPlugin");
const xmlrpc = require("xmlrpc");
const message = require("message");
const CDRWriter = require("./pluginCDRWriter");

const error_codes = message.error_codes;
const booleanArr = ["compositeDedicatedAccountFlag ", "accountActivatedFlag", "allowCropOfCompositeDedicatedAccounts", "accountActivationFlag", "activationStatusFlag", "requestPamInformationFlag", "promotionNotificationFlag", "firstIVRCallSetFlag", "accountActivationFlag", "requestRefillAccountBeforeFlag", "requestRefillAccountAfterFlag", "requestRefillDetailsFlag", "requestSubDedicatedAccountDetailsFlag"];

const NS_PER_SEC = 1e9;

class xmlRPC extends LeapPlugin {

  init() {

  }

  getMetaDataInfo(moduleData) {
    return super.getMetaDataInfo(moduleData);
  }

  /**
   * plugin validate method
   *
   * @param {any} moduleData
   * @param {any} schemaData
   * @returns
   * @memberof UCIPPlugin
   * @returns module validation result object
   */
  validate(moduleData, schemaData) {
    return super.validate(moduleData, schemaData);
  }

  prepare(schema, data) {
    return prepareSchema(schema, data);
  }

  /**
   * exec method
   * @param {any} pluginName
   * @param {any} context
   * @returns result object
   */
  exec(context) {
    let startTime = process.hrtime(), reqBody, diff, pdiagnosys, timeout;
    return new Promise(resolve => {
      try {
        timeout = context.settings.timeout || 60000;
        // Set the HTTP request headers
        let clientOptions = {
          host: context.settings.host,
          port: context.settings.port,
          path: context.settings.path,
          headers: {
            "Content-Type": "text/xml",
            "User-Agent": context.settings["User-Agent"] || "LEAP Platform",
            "Connection": "keep-alive",
            "Accept": "text/html, image/gif, image/jpeg, ; q=.2, /*; q=.2",
            "cache-control": "no-cache",
            "Authorization": "Basic " + Buffer.from(context.settings.username + ":" + context.settings.password).toString("base64"),
            "accept-encoding": "gzip, deflate"
          },
          timeout,
          basic_auth: {
            user: context.settings.username,
            pass: context.settings.password
          }
        };
        if (global.logger.isTraceEnabled()) {
          global.logger.trace("Client pluginName:" + context.pluginName + ",Opts:" + JSON.stringify(clientOptions) + ", Resquest Params:" + JSON.stringify(context.process));
        }

        reqBody = context.process;
        if (reqBody.dedicatedAccountUpdateInformation != null) {
          for (let i = 0; i < reqBody.dedicatedAccountUpdateInformation.length; i++) {
            delete reqBody.dedicatedAccountUpdateInformation[i].process;
            if (!reqBody.dedicatedAccountUpdateInformation[i].dedicatedAccountID || reqBody.dedicatedAccountUpdateInformation[i].dedicatedAccountID == "NA") {
              delete reqBody.dedicatedAccountUpdateInformation[i];
            }
            else {
              reqBody.dedicatedAccountUpdateInformation[i].dedicatedAccountID = parseInt(reqBody.dedicatedAccountUpdateInformation[i].dedicatedAccountID, 10);
              let adjustmentAmountRelative = reqBody.dedicatedAccountUpdateInformation[i].adjustmentAmountRelative;
              if (adjustmentAmountRelative != null && adjustmentAmountRelative) {
                adjustmentAmountRelative = adjustmentAmountRelative.toString();
                adjustmentAmountRelative = "+" + adjustmentAmountRelative;
                if (String(adjustmentAmountRelative).substr(0, 1) == "+") {
                  adjustmentAmountRelative = adjustmentAmountRelative.substr(1);
                  reqBody.dedicatedAccountUpdateInformation[i].adjustmentAmountRelative = String(adjustmentAmountRelative);
                }
              } else {
                delete reqBody.dedicatedAccountUpdateInformation[i].adjustmentAmountRelative;
              }

              let dedicatedAccountValueNew = reqBody.dedicatedAccountUpdateInformation[i].dedicatedAccountValueNew;
              if (dedicatedAccountValueNew != null && dedicatedAccountValueNew.length > 0) {
                dedicatedAccountValueNew = dedicatedAccountValueNew.toString();
                dedicatedAccountValueNew = "+" + dedicatedAccountValueNew;
                if (String(dedicatedAccountValueNew).substr(0, 1) == "+") {
                  dedicatedAccountValueNew = dedicatedAccountValueNew.substr(1);
                  reqBody.dedicatedAccountUpdateInformation[i].dedicatedAccountValueNew = dedicatedAccountValueNew;
                }
              } else {
                delete reqBody.dedicatedAccountUpdateInformation[i].dedicatedAccountValueNew;
              }
              let dedicatedexpiryDate = reqBody.dedicatedAccountUpdateInformation[i].expiryDate;
              if (typeof (dedicatedexpiryDate) == "string") {
                if (dedicatedexpiryDate != null && dedicatedexpiryDate != "") {
                  reqBody.dedicatedAccountUpdateInformation[i].expiryDate = new Date(Number(dedicatedexpiryDate));
                } else {
                  delete reqBody.dedicatedAccountUpdateInformation[i].expiryDate;
                }
              }
            }
          }
          let deidicatedFileter = reqBody.dedicatedAccountUpdateInformation.filter(function (el) {
            return el != null;
          });
          reqBody.dedicatedAccountUpdateInformation = deidicatedFileter;
          if (reqBody.dedicatedAccountUpdateInformation[0] == null) {
            delete reqBody.dedicatedAccountUpdateInformation;
          }
        }

        if (reqBody.communityInformationCurrent != null) {
          for (let i = 0; i < reqBody.communityInformationCurrent.length; i++) {
            if (reqBody.communityInformationCurrent[i] != null) {
              delete reqBody.communityInformationCurrent[i].process;
              if (reqBody.communityInformationCurrent[i].communityID == "NA") {
                delete reqBody.communityInformationCurrent[i];
              } else {
                reqBody.communityInformationCurrent[i].communityID = parseInt(reqBody.communityInformationCurrent[i].communityID, 10);
              }
            }
            else {
              delete reqBody.communityInformationCurrent[i];
            }
          }
          let filteredArray = reqBody.communityInformationCurrent.filter(el => {
            return el != null;
          });
          reqBody.communityInformationCurrent = filteredArray;
        };

        if (reqBody.communityInformationNew != null) {
          for (let i = 0; i < reqBody.communityInformationNew.length; i++) {
            if (reqBody.communityInformationNew[i] != null) {
              delete reqBody.communityInformationNew[i].process;
              if (reqBody.communityInformationNew[i].communityID == "NA") {
                delete reqBody.communityInformationNew[i];
              }
              else {
                reqBody.communityInformationNew[i].communityID = parseInt(reqBody.communityInformationNew[i].communityID, 10);
              }
            } else {
              delete reqBody.communityInformationNew[i]
            }
          }
          let filteredArray = reqBody.communityInformationNew.filter(el => {
            return el != null;
          });
          reqBody.communityInformationNew = filteredArray;
        };
        if (reqBody.usageCounterUpdateInformation != null) {
          for (let i = 0; i < reqBody.usageCounterUpdateInformation.length; i++) {
            if (reqBody.usageCounterUpdateInformation[i] != null) {
              delete reqBody.usageCounterUpdateInformation[i].process;
              if (reqBody.usageCounterUpdateInformation[i].usageCounterID == "NA") {
                delete reqBody.usageCounterUpdateInformation[i];
              }
              else {
                reqBody.usageCounterUpdateInformation[i].usageCounterID = parseInt(reqBody.usageCounterUpdateInformation[i].usageCounterID, 10);
                let adjustmentUsageCounterMonetaryValueRelative = reqBody.usageCounterUpdateInformation[i].adjustmentUsageCounterMonetaryValueRelative;
                if (adjustmentUsageCounterMonetaryValueRelative != null && adjustmentUsageCounterMonetaryValueRelative) {
                  adjustmentUsageCounterMonetaryValueRelative = adjustmentUsageCounterMonetaryValueRelative.toString();
                  adjustmentUsageCounterMonetaryValueRelative = "+" + adjustmentUsageCounterMonetaryValueRelative;
                  if (String(adjustmentUsageCounterMonetaryValueRelative).substr(0, 1) == "+") {
                    adjustmentUsageCounterMonetaryValueRelative = adjustmentUsageCounterMonetaryValueRelative.substr(1);
                    reqBody.usageCounterUpdateInformation[i].adjustmentUsageCounterMonetaryValueRelative = String(adjustmentUsageCounterMonetaryValueRelative);
                  }
                } else {
                  delete reqBody.usageCounterUpdateInformation[i].adjustmentUsageCounterMonetaryValueRelative;
                }
              }
            }
            else {
              delete reqBody.usageCounterUpdateInformation[i];
            }
          }
          let filteredArray = reqBody.usageCounterUpdateInformation.filter(el => {
            return el != null;
          });
          reqBody.usageCounterUpdateInformation = filteredArray;
        };
        if (reqBody.usageThresholdUpdateInformation != null) {
          for (let i = 0; i < reqBody.usageThresholdUpdateInformation.length; i++) {
            if (reqBody.usageThresholdUpdateInformation[i] != null) {
              delete reqBody.usageThresholdUpdateInformation[i].process;
              if (reqBody.usageThresholdUpdateInformation[i].usageThresholdID == "NA") {
                delete reqBody.usageThresholdUpdateInformation[i];
              }
              else {
                reqBody.usageThresholdUpdateInformation[i].usageThresholdID = parseInt(reqBody.usageThresholdUpdateInformation[i].usageThresholdID, 10);

                reqBody.usageThresholdUpdateInformation[i].usageThresholdValueNew = reqBody.usageThresholdUpdateInformation[i].usageThresholdValueNew.toString();
              }
            } else {
              delete reqBody.usageThresholdUpdateInformation[i]
            }
          }
        };
        if (reqBody.pamInformationList != null) {
          for (let i = 0; i < reqBody.pamInformationList.length; i++) {
            if (reqBody.pamInformationList[i] != null) {
              delete reqBody.pamInformationList[i].process;
              if (reqBody.pamInformationList[i] == "NA" || reqBody.pamInformationList[i]) {
                let currentPamPeriod = reqBody.pamInformationList[i].currentPamPeriod;
                let deferredToDate = reqBody.pamInformationList[i].deferredToDate;
                let lastEvaluationDate = reqBody.pamInformationList[i].lastEvaluationDate;
                if (!currentPamPeriod) {
                  delete reqBody.pamInformationList[i].currentPamPeriod;
                }
                if (!deferredToDate) {
                  delete reqBody.pamInformationList[i].deferredToDate;
                }
                if (!lastEvaluationDate) {
                  delete reqBody.pamInformationList[i].lastEvaluationDate;
                }
                reqBody.pamInformationList[i].pamServiceID = parseInt(reqBody.pamInformationList[i].pamServiceID, 10);
                reqBody.pamInformationList[i].pamClassID = parseInt(reqBody.pamInformationList[i].pamClassID, 10);
                reqBody.pamInformationList[i].scheduleID = parseInt(reqBody.pamInformationList[i].scheduleID, 10);
              }
            } else {
              delete reqBody.pamInformationList[i]
            }
          }
        }
        if (reqBody.attributeUpdateInformationList != null) {
          for (let i = 0; i < reqBody.attributeUpdateInformationList.length; i++) {
            if (reqBody.attributeUpdateInformationList[i] != null) {
              delete reqBody.attributeUpdateInformationList[i].process;
            } else {
              delete reqBody.attributeUpdateInformationList[i]
            }
          }
        }

        if (reqBody.accumulatorUpdateInformation != null) {
          for (let i = 0; i < reqBody.accumulatorUpdateInformation.length; i++) {
            if (reqBody.accumulatorUpdateInformation[i] != null) {
              delete reqBody.accumulatorUpdateInformation[i].process;
              if (reqBody.accumulatorUpdateInformation[i].accumulatorID == "NA") {
                delete reqBody.accumulatorUpdateInformation[i];
              }
              else {
                reqBody.accumulatorUpdateInformation[i].accumulatorID = parseInt(reqBody.accumulatorUpdateInformation[i].accumulatorID, 10);
                let accumulatorValueRelative = reqBody.accumulatorUpdateInformation[i].accumulatorValueRelative;
                if (accumulatorValueRelative != null && accumulatorValueRelative) {
                  accumulatorValueRelative = accumulatorValueRelative.toString();
                  accumulatorValueRelative = "+" + accumulatorValueRelative;
                  if (String(accumulatorValueRelative).substr(0, 1) == "+") {
                    accumulatorValueRelative = accumulatorValueRelative.substr(1);
                    reqBody.accumulatorUpdateInformation[i].accumulatorValueRelative = String(accumulatorValueRelative);
                  }
                } else {
                  delete reqBody.accumulatorUpdateInformation[i].accumulatorValueRelative;
                }
              }
            }
            else {
              delete reqBody.accumulatorUpdateInformation[i];
            }
          }
          let filteredArray = reqBody.accumulatorUpdateInformation.filter(el => {
            return el != null;
          });
          reqBody.accumulatorUpdateInformation = filteredArray;
        };

        if (global.logger.isTraceEnabled()) {
          global.logger.trace("Final Client Resquest Params:" + JSON.stringify(reqBody));
        }
        const xmlrpcCli = xmlrpc.createClient(clientOptions);
        xmlrpcCli.methodCall(context.pluginName, [reqBody], (error, response, diagnosys) => {
          pdiagnosys = diagnosys;
          pdiagnosys.timeout = timeout;
          pdiagnosys.req = reqBody;
          diff = process.hrtime(startTime);
          let code;
          if (error) {
            code = error.code;
            if (global.logger.isTraceEnabled()) {
              global.logger.trace(JSON.stringify(error));
              global.logger.trace("Client Response Params: Code:", code, ", Message:", error.msg);
            }
            resolve(error);
          } else {
            if (global.logger.isTraceEnabled()) {
              global.logger.trace("Client Response Params:" + JSON.stringify(response));
            }
            code = response.responseCode;
            resolve({
              code: error_codes.success,
              msg: response
            });
          }

          if (global.pluginCDR && global.pluginCDR.isTraceEnabled()) {
            pdiagnosys.res = response;
          }
          pdiagnosys.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
          CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, pdiagnosys.pluginResponse, code, pdiagnosys);
        });
      } catch (error) {
        diff = process.hrtime(startTime);
        pdiagnosys.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
        global.logger.error(error);
        resolve({
          code: error_codes.pluginInternalError,
          msg: error
        });
        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, pdiagnosys.pluginResponse, error_codes.pluginInternalError, pdiagnosys);
      }
    });
  }
}
//preparing schema plugin request
function prepareSchema(schema, data) {
  let process = {};
  if (data == null) {
    data = {};
  }
  try {
    if (schema != null && schema.properties != null) {
      Object.keys(schema.properties).forEach(prop => {
        let props = schema.properties[prop];
        if (!data.hasOwnProperty(prop)) {
          if (schema.required != undefined) {
            if (schema.required.includes(prop) && props.default != null) {
              process[prop] = props.default;
            }
          }
        } else if (booleanArr.includes(prop)) {
          process[prop] = (data[prop] == "true");
        } else if (prop == "originTimeStamp") {
          process[prop] = new Date(Number(data[prop]));
        } else if (props.format == "Numeric") {
          process[prop] = Number(data[prop]);
        } else {
          if (schema.required != undefined) {
            if (schema.required.hasOwnProperty(prop) && data[prop] == null) {
              data[prop] = props.default || "";
            }
          }
          switch (props.type) {
            case undefined: {
              if (props.enum) {
                process[prop] = String(data[prop]);
              }
            }
              break;
            case "string": {
              if (data && data.hasOwnProperty(prop)) {
                if (props.format == "date-time") {
                  if (data[prop] != "" && data[prop] != null) {
                    process[prop] = new Date(data[prop]);
                  }
                } else if (props.format == "Numeric") {
                  process[prop] = Number(data[prop]);
                } else if (data[prop] != "") {
                  process[prop] = String(data[prop]);
                }
              } else if (props.default != null) {
                process[prop] = String(props.default);
              }
            }
              break;
            case "integer":
            case "number": {
              if (data && data.hasOwnProperty(prop)) {
                process[prop] = Number(data[prop]);
              } else if (props.default != null) {
                process[prop] = Number(props.default);
              }
            }
              break;
            case "boolean": {
              if (data && data.hasOwnProperty(prop)) {
                process[prop] = Boolean(data[prop]);
              } else if (props.default != null) {
                process[prop] = Boolean(props.default);
              }
            }
              break;
            case "object": {
              if (Array.isArray(data[prop])) {
                process[prop] = data[prop];
              } else {
                process[prop] = prepareSchema(props, data[prop]);
              }
            }
              break;
            case "array": {
              if (Array.isArray(data[prop])) {
                process[prop] = data[prop];
              } else {
                let arr = [];
                try {
                  if (data[prop] != null) {
                    Object.keys(data[prop]).forEach(key => {
                      arr.push(prepareSchema(props.items, data[prop][key]));
                    });
                  }
                } catch (error) {
                  global.logger.error(error);
                }
                process[prop] = arr;
              }
            }
              break;
            default:
              process[prop] = data[prop];
          }
          if (process[prop] == null || process[prop] == "undefined") {
            delete process[prop];
          }
        }
      });
    }
  } catch (e) {
    global.logger.error(e);
  }
  return process;
}
module.exports = xmlRPC;
