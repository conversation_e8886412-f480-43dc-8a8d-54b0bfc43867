const LeapPlugin = require("./LeapPlugin");
const message = require("message");
const jsontoxml = require("jsontoxml");
const parser = require("xml2json");
const request = require("request");
const error_codes = message.error_codes;
const CDRWriter = require("./pluginCDRWriter");

class mobiquity extends LeapPlugin {

  init() {

  }

  getMetaDataInfo(moduleData) {
    return super.getMetaDataInfo(moduleData);
  }

  /**
   * plugin validate method
   *
   * @param {any} moduleData
   * @param {any} schemaData
   * @returns
   * @memberof mobiquity
   * @returns module validation result object
   */
  validate(moduleData, schemaData) {
    return super.validate(moduleData, schemaData);
  }

  prepare(schema, data) {
    let process = {};
    if (data == null) {
      data = {};
    }
    try {
      if (schema != null && schema.properties != null) {
        Object.keys(schema.properties).forEach(prop => {
          let props = schema.properties[prop];
          if (data == null || !data.hasOwnProperty(prop)) {
            if (schema.required.includes(prop) && props.default != null) {
              process[prop] = props.default;
            }
          } else {
            if (schema.required.hasOwnProperty(prop) && data[prop] == null) {
              data[prop] = props.default || "";
            }
            switch (props.type) {
              case undefined: {
                if (props.enum) {
                  process[prop] = String(data[prop]);
                }
              }
                break;
              case "string": {
                if (data && data.hasOwnProperty(prop)) {
                  process[prop] = String(data[prop]);
                }
                else if (props.default != null) {
                  process[prop] = String(props.default);
                }
              }
                break;
              case "object": {
                process[prop] = this.prepare(props.properties, data[prop]);
              }
                break;
              default:
                process[prop] = data[prop];
            }
            if (process[prop] == null || process[prop] == "undefined") {
              delete process[prop];
            }
          }
        });
      }
    } catch (e) {
      global.logger.error(e);
    }
    return process;
  }

  /**
   * exec method
   * @param {any} pluginName
   * @param {any} moduleContext
   * @returns result object
   */
  exec(moduleContext) {
    return new Promise((resolve, reject) => {
      try {
        if (moduleContext.process == null) {
          return reject(message.getResponseJson(moduleContext.language, error_codes.queryNotFound));
        }
        let req = {
          headers: {
            "Content-Type": "text/xml",
            "Connection": "close"
          },
          url: moduleContext.settings.url,
          method: moduleContext.settings.method,
          timeout: moduleContext.settings.connectionTimeOut || 3000,
          rejectUnauthorized: false
        };
        let reqJOSN = moduleContext.process;
        delete reqJOSN.COMMAND.process;
        if (reqJOSN.COMMAND.PIN != null) {
          let PIN = reqJOSN.COMMAND.PIN.toString();
          let newPIN;
          if (PIN.length == 4) {
            newPIN = PIN;
          }
          else if (PIN.length == 3) {
            newPIN = "0" + PIN;
          }
          else if (PIN.length == 2) {
            newPIN = "00" + PIN;
          } else if (PIN.length == 1) {
            newPIN = "000" + PIN;
          }
          else {
            newPIN = "0000";
          }
          reqJOSN.COMMAND.PIN = newPIN;
        }
        req.body = jsontoxml(JSON.stringify(reqJOSN));

        let reqtime = Date.now();
        request(req, (error, response, body) => {
        if (error) {
                        var errorcode = error_codes.pluginInternalError;
                        if (error.connect == true)
                                errorcode =error_codes.ECONNREFUSED ;
                        else if (error.connect == false)
                                errorcode = error_codes.ESOCKETTIMEDOUT;

                        global.logger.error("Exception occured while making request to Mobiquity", error);
                        let msgObj = message.getResponseJson(moduleContext.language, errorcode);

            CDRWriter.emit("MOBIQUITY_CDR", moduleContext.appId, moduleContext.mid, moduleContext.pluginName, Date.now() - reqtime, error_codes.pluginInternalError, reqJOSN, error);

            resolve({
              code: error_codes.success,
              msg: msgObj
            });
          } else {
            if (response.statusCode == 200) {
              let json;
              try {
                json = JSON.parse(parser.toJson(body));
              } catch (error) {
                global.logger.error("Error occured while parsing the response", error);
                json = body;
              }

              CDRWriter.emit("MOBIQUITY_CDR", moduleContext.appId, moduleContext.mid, moduleContext.pluginName, Date.now() - reqtime, json.COMMAND.TXNSTATUS, reqJOSN, json);

              return resolve({
                code: error_codes.success,
                msg: json
              });
            } else {
              return reject(message.getResponseJson(moduleContext.language, response.statusCode));
            }
          }
        });
      } catch (error) {
        global.logger.error('Error occured while making request to Mobiquity', error);
        return reject(error);
      }
    });
  }
}
module.exports = mobiquity;
