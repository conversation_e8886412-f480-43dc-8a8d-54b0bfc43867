/**
 *  PlunisScanner for LEAP - PluginManager
 *  <AUTHOR>
 **/

const fs = require("fs");
const dirToJson = require("dir-to-json");
const path = require("path");
const schemaFileSuffix = "Schema.json";
const commonPropertiesFileSuffix = "CommonProperties.json";
const pluginFileSuffix = "Plugin.js";
let pluginId = -1;
const DEFAULT_PLUGINS = 7;

module.exports = {
    pluginsInfo: async (dir) => {
        if (!dir) {
            dir = path.join(__dirname, "../");
        }

        let pluginsInfo = await getPluginsList(dir);
        pluginsInfo.pluginId = pluginId;
        global.logger.warn("Palette Sorting");

        // Sort the pluginsList
        pluginsInfo.pluginsList.sort(sortPlugins);

        // Use forEach to sort modules in each plugin
        pluginsInfo.pluginsList.forEach((item) => {
            // Ensure item.modules exists before sorting
            if (item.modules && Array.isArray(item.modules)) {
                item.modules.sort(sortPlugins);
            }
        });

        global.logger.warn("Palette Sorting Completed");
        return pluginsInfo;
    }
};


/**
 * Get PluginsList
 *
 * @param {any} done
 * Scann the plugins directory, Lists plugins and creates instance for plugins
 */
async function getPluginsList(dir) {

    global.plugin_settings = {};
    global.plugin_settings_mapping = {};
    let pluginsInfo = {
        pluginsList: [],
        pluginsMap: {}
    };

    const plunisDir = path.join(dir, "/plugins");

    //Scanns plugins directory and gives JSON object
    let dirTree = await dirToJson(plunisDir);
    dirTree.children.forEach(category => {
        let categoryInfo = {
            category: {
                type: "",
                name: "",
                id: ""
            },
            modules: []
        };
        //Check wether the category type is directory or not
        if (category.type == "directory") {
            let parentPath = path.join(plunisDir, category.name, "/");
            let fileName = parentPath + category.name + schemaFileSuffix;

            // Read the category name from schema file
            try {
                categoryInfo.category.type = category.name;
                let categoryObj = JSON.parse(fs.readFileSync(fileName));
                categoryInfo.category.name = categoryObj.title;
                categoryInfo.category.id = categoryObj.typeId || categoryObj.typeid;
                categoryInfo.category.description = categoryObj.description;
                categoryInfo.category.expand = categoryObj.expand;
            } catch (err) {
                global.logger.error("Exception occured while, Reading schema for category:" + category.name);
            }
            category.children.forEach(children => {
                if (children.type == "directory") {
                    let moduleInfo = {
                        category: "",
                        id: "",
                        type: "",
                        name: "",
                        description: ""
                    };
                    let moduleType = children.name;
                    let modulePath = path.join(parentPath, moduleType, moduleType);
                    try {
                        let schemaFileObj = JSON.parse(fs.readFileSync(modulePath + schemaFileSuffix));
                        moduleInfo.category = category.name;
                        moduleInfo.name = schemaFileObj.title;
                        moduleInfo.description = schemaFileObj.description;

                        let id;
                        if (schemaFileObj.typeId != null) {
                            id = schemaFileObj.typeId;
                        } else {
                            id = schemaFileObj.id;
                        }
                        if (Number(id) > pluginId) {
                            pluginId = id;
                        }
                        pluginId = Math.ceil(pluginId);
                        let type = 0;
                        if (Number(id) >= DEFAULT_PLUGINS) {
                            type = 1;
                        }

                        let mapping = moduleType;
                        if (categoryInfo.category.expand) {
                            global.plugin_settings[moduleType] = {
                                id: id,
                                category: categoryInfo.category.type,
                                name: moduleType,
                                description: moduleInfo.description || "",
                                type: type,
                                path: category.name + "/" + moduleType + "/" + moduleType + schemaFileSuffix,
                                settings: {
                                    dev: schemaFileObj.properties.settings,
                                    prod: schemaFileObj.properties.settings
                                }
                            };
                            mapping = moduleType;
                        } else if (!global.plugin_settings.hasOwnProperty(categoryInfo.category.type)) {
                            if (!categoryInfo.category.type.startsWith("SOAP")) {
                                let commonPropertiesFiles = JSON.parse(fs.readFileSync(parentPath + categoryInfo.category.type + commonPropertiesFileSuffix));
                                schemaFileObj.properties = Object.assign(schemaFileObj.properties, commonPropertiesFiles);
                            }
                            global.plugin_settings[categoryInfo.category.type] = {
                                id: Number(categoryInfo.category.id),
                                category: categoryInfo.category.type,
                                name: "All",
                                description: categoryInfo.category.description || "",
                                type: type,
                                path: category.name + "/" + category.name + schemaFileSuffix,
                                settings: {
                                    dev: schemaFileObj.properties.settings,
                                    prod: schemaFileObj.properties.settings
                                }
                            };

                            mapping = categoryInfo.category.type;
                        } else {
                            mapping = categoryInfo.category.type;
                        }

                        global.plugin_settings_mapping[String(id)] = mapping;
                        moduleInfo.id = id.toString();
                        moduleInfo.type = moduleType;
                        categoryInfo.modules.push(moduleInfo);
                        global.logger.trace("Reading schema for plugin:" + moduleType + " is Done...");
                    } catch (err) {
                        global.logger.error("Exception occured while reading schema for plugin:" + moduleType, err);
                    }

                    //Create instance for plugins
                    try {
                        let pluginFileName = modulePath + pluginFileSuffix;
                        global.logger.trace("Creating instance for plugin:" + moduleType + " is Started...");
                        pluginFileName = path.resolve(pluginFileName);
                        delete require.cache[pluginFileName];
                        let ModuleClass = require(pluginFileName);
                        pluginsInfo.pluginsMap[moduleType] = new ModuleClass();
                        global.logger.trace("Creating instance for plugin:" + moduleType + " is Done...");
                    } catch (err) {
                        global.logger.error("Exception occured while creating instance for plugin: %s", moduleType, err);
                    }
                }
            });
            if (categoryInfo.category.name.length > 0) {
                pluginsInfo.pluginsList.push(categoryInfo);
            }
        }
    });
    return pluginsInfo;
}

/**
 *Sort Floating Array
 *@param {string} Array Items
 *Returns the sorted array.
 */
function sortPlugins(a, b) {
    let aArr, bArr, i = 0;
    try {
        if (a.category != null && a.category.id) {
            a.id = a.category.id;
            b.id = b.category.id;
            aArr = a.category.id.split(".");
            bArr = b.category.id.split(".");
        } else {
            aArr = a.id.split(".");
            bArr = b.id.split(".");
        }
        for (i = 0; i < aArr.length; i++) {
            // convert substring into Number if it is a Number
            if (aArr[i] == null || bArr[i] == null) { return 1; } // b lower index
            let aVal = Number(aArr[i]);
            let bVal = Number(bArr[i]);
            if (aVal == bVal) { continue; }
            return aVal - bVal; // for strings, works only if length == 1
        }
        return aArr[0] == bArr[0] ? 0 : -1; // if b undefined, then both numbers are equal, otherwise a is shorter
    } catch (error) {
        global.logger.error("Exception occurred while sorting:", error);
        return 0;
    }
}
