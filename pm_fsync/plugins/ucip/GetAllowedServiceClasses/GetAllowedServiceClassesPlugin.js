/**
 *  UCIP GetAllowedServiceClasses Plugin
 *
 *  <AUTHOR>
 **/
const UCIPPlugin = require('../ucipPlugin');

let schema;

class GetAllowedServiceClassesPlugin extends UCIPPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof GetAllowedServiceClassesPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof GetAllowedServiceClassesPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof GetAllowedServiceClassesPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = GetAllowedServiceClassesPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require('./GetAllowedServiceClassesSchema.json');
    return schema;
}
