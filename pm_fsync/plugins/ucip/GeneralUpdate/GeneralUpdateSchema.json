{"typeId": "3.1", "name": "GeneralUpdate", "title": "General Update", "description": "The message GeneralUpdate is used by external system to adjust offers, account balances, accumulators, service class and more in a single transaction", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated", "title": "Origin Operator ID", "type": "string"}, "transactionCurrency": {"description": "parameter contains an ID to point out what currency is used for the transaction", "title": "Transaction Currency", "type": "string"}, "adjustmentAmountRelative": {"description": "parameter contains the amount of the adjustment(positive or negative) to be applied to the account", "title": "Adjustment Amount Relative", "type": "string"}, "mainAccountValueNew": {"description": "parameter contains a value to assign to a main account", "title": "Main Account Value New", "type": "string"}, "transactionType": {"description": "parameter is used to specify the operation in more detail", "title": "Transaction Type", "enum": ["EUC", "PIN", "TT", "GSM"]}, "transactionCode": {"description": "parameter is used to specify the operation in more detail ", "title": "Transaction Code", "enum": ["SCC", "FAF", "CBE", "ADJ", "TC", "TV", "REBATE", "DEBIT", "DEDUCTIONGSM", "DEDUCTIONPERIOD"]}, "serviceClassAction": {"description": "parameter is used to indicate which action to take when updating the service class", "title": "Service Class Action", "enum": ["Set", "SetOriginal", "SetTemporary", "DeleteTemporary"]}, "serviceClassCurrent": {"description": "The serviceClassCurrent parameter contains the service class currently used by the subscriber. This might be a temporary Service Class, which is controlled by a temporary Service Class expiry date (separate parameter).", "title": "Service Class Current", "type": "integer"}, "serviceClassNew": {"description": "parameter contains the new service class for the refill", "title": "Service Class New", "type": "string"}, "serviceClassTemporary": {"description": "parameter contains the service class to be used by the subscriber", "title": "Service Class Temporary", "type": "string"}, "serviceClassTemporaryExpiryDate": {"description": "parameter contains the expiry date of a temporary service class of an account", "title": "Service Class Temporary Expiry Date", "type": "string", "format": "date-time"}, "serviceClassTemporaryNew": {"description": "parameter contains the new temporary service class when changing an already assigned temporary service class", "title": "Service Class Temporary New", "type": "string"}, "serviceClassTemporaryNewExpiryDate": {"description": "parameter contains the new temporary service class expiry date when changing an already assigned expiry date", "title": "Service Class Temporary New Expiry Date", "type": "string", "format": "date-time"}, "accountGroupID": {"description": "parameter contains the Account Group identity for the account", "title": "Account Group ID", "type": "string"}, "accumulatorUpdateInformation": {"description": "The AccumulatorUpdateInformation is enclosed in a <struct> of its own.Structs are placed in an <array>", "title": "Accumulator Update Information", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"accumulatorID": {"description": "parameter contains the accumulator identity", "title": "Accumulator ID", "type": "string"}, "accumulatorValueRelative": {"description": "parameter contains an accumulator value used for a relative update", "title": "Accumulator Value Relative", "type": "string"}, "accumulatorValueAbsolute": {"description": "parameter contains an accumulator value used for an absolute update", "title": "Accumulator Value Absolute", "type": "string"}, "accumulatorStartDate": {"description": "parameter indicates the date on which the accumulator was last reset", "title": "Accumulator Start Date", "type": "string", "format": "date-time"}}}}}}}}