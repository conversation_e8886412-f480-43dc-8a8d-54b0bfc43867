{"typeId": "3", "name": "ucip", "title": "UCIP v5 Modules", "description": "UCIP(User Communication Interface Protocol) is intended for user self services such as Adjustments, Account Refill, and Account Enquiries and to extract account details. UCIP is an IP-based protocol used for integration towards the AIR server from the external application. UCIP is an XML over HTTP based protocol, which makes it easy to integrate with a central integration point within a network. The protocol supports both session as well as event based clients. A UCIP request is sent to one of the AIR servers within the network and for redundancy purposes it is required to have N+1 AIR system in the network.", "expand": false}