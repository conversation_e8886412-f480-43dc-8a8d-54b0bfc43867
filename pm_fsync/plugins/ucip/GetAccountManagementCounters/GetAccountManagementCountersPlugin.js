/**
 *  UCIP GetAccountManagementCounters Plugin
 *
 *  <AUTHOR>
 **/

const UCIPPlugin = require('../ucipPlugin');

let schema;

class GetAccountManagementCountersPlugin extends UCIPPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof GetAccountManagementCountersPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof GetAccountManagementCountersPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof GetAccountManagementCountersPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = GetAccountManagementCountersPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require('./GetAccountManagementCountersSchema.json');
    return schema;
}