/**
 *  UCIP GetOffers Plugin
 *
 *  <AUTHOR>
 **/
const UCIPPlugin = require('../ucipPlugin');

let schema;

class GetOffersPlugin extends UCIPPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof GetOffersPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof GetOffersPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof GetOffersPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = GetOffersPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require('./GetOffersSchema.json');
    return schema;
}
