{"typeId": "3.20", "name": "UpdateUsageThresholdsAndCounters", "title": "Update Usage Thresholds And Counters", "description": "The message UpdateUsageThresholdsAndCounters is used to personalize a usage threshold for a subscriber by setting a value other than the default value, either an individual value for a subscriber or an individual value for a provider shared by all consumers", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated", "title": "Origin Operator ID", "type": "string"}, "transactionCurrency": {"description": "parameter contains an ID to point out what currency is used for the transaction", "title": "Transaction Currency", "type": "string"}, "usageCounterUpdateInformation": {"description": "specifies usage counter data", "title": "Usage Counter Update Information", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"usageCounterID": {"description": "parameter identifies the ID of a usage counter", "title": "usageCounterID", "type": "string"}, "usageCounterValueNew": {"description": "parameter contains the updated value of a non-monetary usage counter", "title": "Usage Counter Value New", "type": "string", "format": "Numeric"}, "adjustmentUsageCounterValueRelative": {"description": "parameter contains the adjustment value of a non-monetary usage counter", "title": "Adjustment Usage Counter Value Relative", "type": "string", "format": "Numeric"}, "usageCounterMonetaryValueNew": {"description": "parameters contains the updated value of a monetary usage counter", "title": "Usage Counter Monetary Value New", "type": "string", "format": "Numeric"}, "adjustmentUsageCounterMonetaryValueRelative": {"description": "parameter indicates the adjustment value of a monetary usage counter", "title": "Adjustment Usage Counter Monetary Value Relative", "type": "string"}, "associatedPartyID": {"description": "parameter contains the subscriber identity of the consumer or provider", "title": "Associated Party ID", "type": "string", "format": "Numeric"}, "productID": {"description": "parameter contains the identity of a product", "title": "Product ID", "type": "string"}, "externalproductID": {"description": "parameter contains the identity of a product", "title": "External Product ID", "type": "string", "format": "Numeric"}}}}, "usageThresholdUpdateInformation": {"description": "element is enclosed in a <struct> of its own. Structs are placed in an <array>", "title": "Usage Threshold Update Information", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"usageThresholdID": {"description": "parameter identifies the ID of a usage threshold", "title": "Usage Threshold ID", "type": "string"}, "usageThresholdValueNew": {"description": "parameter contains the new value of a non-monetary usage threshold", "title": "Usage Threshold Value New", "type": "string", "format": "Numeric"}, "usageThresholdMonetaryValueNew": {"description": "parameter contains theu pdated value of a monetary usage threshold", "title": "usage Threshold Monetary Value New", "type": "string"}, "associatedPartyID": {"description": "parameter contains the subscriber identity of the consumer or provider", "title": "Associated Party ID", "type": "string", "format": "Numeric"}}}}, "backdatedToDateTime": {"description": "parameter signals that the request is backdated", "title": "back dated To Date Time", "type": "string", "format": "date-time"}, "backdatedToStartOfBillCycleInstance": {"description": "parameter signals that the request is backdated ", "title": "back dated To Start Of BillCycle Instance", "type": "string", "format": "Numeric"}}}}}