{"typeId": "3.16", "name": "UpdateFaFList", "title": "Update FaF List", "description": "The message UpdateFaFList is used to update the Family and Friends list for either the account or subscriber", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string"}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string"}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string"}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated", "title": "Origin Operator ID", "type": "string"}, "fafAction": {"description": "parameter contains the requested action for changing the Family and Friends list", "title": "faf Action", "enum": ["ADD", "SET", "DELETE"]}, "fafInformation": {"description": "contains information about family and friends function", "title": "fafInformation", "type": "object", "properties": {"fafNumber": {"description": "parameter contains a Family and Friends number", "title": "faf Number", "type": "string"}, "owner": {"description": "parameter is used to indicate if the data is attached to the account or subscriber", "title": "owner", "type": "string"}, "expiryDate": {"description": "parameter contains the expiry date for a dedicated account", "title": "Expiry Date", "type": "string", "format": "date-time"}, "expiryDateRelative": {"description": "parameter is used to make a relative adjustment to an expiry date ", "title": "Expiry Date Relative", "type": "integer"}, "fafIndicator": {"description": "parameter is used for differentiated rating for traffic events to and from numbers in the Family and Friends(FaF) number list", "title": "fafIndicator", "type": "integer"}, "offerID": {"description": "parameter contains the identity of an offer", "title": "OfferID", "type": "integer"}, "startDate": {"description": "parameter contains the date when a dedicated account, FaF entry or offer will be considered as active", "title": "Start Date", "type": "string", "format": "date-time"}, "startDateRelative": {"description": "parameter is used to make a relative adjustment to the current start date", "title": "Start Date Relative", "type": "string"}, "exactMatch": {"description": "parameter is used to make a relative adjustment to the current start date", "title": "Exact Match", "enum": [false, true]}}}, "selectedOption": {"description": "parameter contains the value of the selected option", "title": "Selected Option", "type": "integer"}, "chargingRequestInformation": {"description": "parameter contains request information for a charged end user communication event", "title": "Charging Request Information", "type": "object", "properties": {"chargingType": {"description": "parameter contains information how the request is to be charged and which mechanism to use.", "title": "Charging type", "type": "integer"}, "chargingIndicator": {"description": "parameter contains an indicator for rating differentiation", "title": "Charging indicator", "type": "string"}, "reservationCorrelationID": {"description": "parameter contains the id needed to correlate a reservation", "title": "Reservation correlation id", "type": "string"}}}}}}}