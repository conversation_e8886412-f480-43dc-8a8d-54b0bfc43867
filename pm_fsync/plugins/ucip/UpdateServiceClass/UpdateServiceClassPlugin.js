/**
 *  UCIP UpdateServiceClass Plugin
 *
 *  <AUTHOR>
 **/
;
const UCIPPlugin = require('../ucipPlugin');

let schema;

class UpdateServiceClassPlugin extends UCIPPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof UpdateServiceClassPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof UpdateServiceClassPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof UpdateServiceClassPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = UpdateServiceClassPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require('./UpdateServiceClassSchema.json');
    return schema;
}
