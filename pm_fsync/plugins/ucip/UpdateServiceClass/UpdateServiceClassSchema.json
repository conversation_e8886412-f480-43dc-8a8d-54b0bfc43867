{"typeId": "3.18", "name": "UpdateServiceClass", "title": "Update Service Class", "description": "This message UpdateServiceClass is used to update the service class (SC) for the subscriber", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string"}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string"}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string"}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated", "title": "Origin Operator ID", "type": "string"}, "serviceClassAction": {"description": "parameter is used to indicate which action to take when updating the service class", "title": "Service Class Action", "enum": ["Set", "SetOriginal", "SetTemporary", "DeleteTemporary"]}, "serviceClassCurrent": {"description": "The serviceClassCurrent parameter contains the service class currently used by the subscriber. This might be a temporary Service Class, which is controlled by a temporary Service Class expiry date (separate parameter).", "title": "Service Class Current", "type": "integer"}, "serviceClassNew": {"description": "parameter contains the new service class for the refill", "title": "Service Class New", "type": "integer"}, "serviceClassTemporary": {"description": "parameter contains the service class to be used by the subscriber", "title": "Service Class Temporary", "type": "integer"}, "serviceClassTemporaryExpiryDate": {"description": "parameter contains the expiry date of a temporary service class of an account", "title": "Service Class Temporary Expiry Date", "type": "string", "format": "date-time"}, "serviceClassTemporaryNew": {"description": "parameter contains the new temporary service class when changing an already assigned temporary service class", "title": "Service Class Temporary New", "type": "string"}, "serviceClassTemporaryNewExpiryDate": {"description": "parameter contains the new temporary service class expiry date when changing an already assigned expiry date", "title": "Service Class Temporary New Expiry Date", "type": "string", "format": "date-time"}, "serviceClassValidationFlag": {"description": "parameter is used to indicate if validation regarding if a service class update is made within the range of allowed service classes shall be performed or not", "title": "Service Class Validation Flag", "type": "boolean", "default": false, "enum": [false, true]}, "externalData1": {"description": "These parameters are used as a spare parameter for  customizations to include in data records.", "title": "External Data1", "type": "string"}, "externalData2": {"description": "These parameters are used as a spare parameter for  customizations to include in data records.", "title": "External Data2", "type": "string"}}}}}