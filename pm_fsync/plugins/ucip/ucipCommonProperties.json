{"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["host", "port", "path"], "properties": {"host": {"description": "The hostname of the UCIP", "title": "Host", "default": "127.0.0.1", "type": "string", "format": "ipv4"}, "port": {"description": "The port number of the UCIP", "title": "Port", "default": 3306, "type": "integer", "minimum": 1024, "maximum": 65535}, "timeout": {"description": "The time interval defines the Request timeout in millis", "title": "Request Timeout in ms", "default": 10000, "type": "integer"}, "path": {"description": "Path of the UCIP client", "title": "Path", "type": "string", "minLength": 1}, "username": {"description": "User Name to connect UCIP client", "title": "User name", "type": "string", "minLength": 1}, "password": {"description": "Password to connect UCIP client", "title": "Password", "hint": "password", "type": "string", "minLength": 1}, "Content-Type": {"description": "Content Type to send in request header", "title": "Content type", "default": "text/xml", "enum": ["text/xml", "application/json", "text/json"], "minLength": 1}, "Accept-Charset": {"description": "Content Type to send in request header", "title": "Accept charset", "default": "UTF-8", "enum": ["US-ASCII", "UTF-8", "UTF-32", "UTF-64"], "minLength": 1}, "User-Agent": {"description": "The extrenal user agent to send in the HTTP request header", "title": "User agent", "type": "string", "minLength": 1}, "maxRetry": {"description": "The Max number of retries to connect to the IN", "title": "maxRetry", "default": 1, "type": "integer"}, "retryInterval": {"description": "The time interval betwwen retries to connect to the IN in millis", "title": "retryInterval", "default": 3000, "type": "integer"}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"responseCode": {"description": "parameter is sent back after a message has been processed and indicates success or failure of the message ", "title": "Response Code", "type": "string"}, "originTransactionID": {"description": "origin trans id", "title": "Origin Transaction ID", "type": "string"}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated ", "title": "Origin Operator ID", "type": "string"}, "currency1": {"description": "parameters contains the account value for the subscriber's master account", "title": "Currency 1", "type": "string"}, "currency2": {"description": "parameters contains the account value for the subscriber's second account", "title": "Currency2", "type": "string"}, "accountValue1": {"description": "parameters contains the account value for the subscriber's master account", "title": "Account Value1", "type": "string"}, "accountValue2": {"description": "parameters contains the account value for the subscriber's second account", "title": "Account Value2", "type": "string"}, "accountFlagsAfter": {"description": "contains life cycle state flags of the account, indicating the actual status of the account after", "title": "Account Flags After", "type": "object", "properties": {"activationStatusFlag": {"description": "This parameter is used to indicate if an account is activated or not", "title": "Account Flags After", "enum": ["0", "1"]}, "negativeBarringStatusFlag": {"description": "This parameter is used to indicate if an account is barred due to negative balance or not ", "title": "ANegative Barring Status Flag", "enum": ["0", "1"]}, "supervisionPeriodWarningActiveFlag": {"description": "This parameter is used to indicate if the supervision period date expiration warning is active or not", "title": "Supervision Period Warning Active Flag", "enum": ["0", "1"]}, "supervisionPeriodExpiryFlag": {"description": "This parameter is used to indicate if the supervision period date has expired or not.", "title": "Supervision Period Expiry Flag", "enum": ["0", "1"]}, "serviceFeePeriodExpiryFlag": {"description": "This parameter is used to indicate if the service fee period date has expired or not.", "title": "Service Fee Period Expiry Flag", "enum": ["0", "1"]}}}, "accountFlagsbefore": {"description": "parameter contains life cycle state flags of the account, indicating the actual status of the account before ", "title": "Account Flags After", "type": "object", "properties": {"activationStatusFlag": {"description": "This parameter is used to indicate if an account is activated or not", "title": "Account Flags After", "enum": ["0", "1"]}, "negativeBarringStatusFlag": {"description": "This parameter is used to indicate if an account is barred due to negative balance or not ", "title": "ANegative Barring Status Flag", "enum": ["0", "1"]}, "supervisionPeriodWarningActiveFlag": {"description": "This parameter is used to indicate if the supervision period date expiration warning is active or not", "title": "Supervision Period Warning Active Flag", "enum": ["0", "1"]}, "supervisionPeriodExpiryFlag": {"description": "This parameter is used to indicate if the supervision period date has expired or not.", "title": "Supervision Period Expiry Flag", "enum": ["0", "1"]}, "serviceFeePeriodExpiryFlag": {"description": "This parameter is used to indicate if the service fee period date has expired or not.", "title": "Service Fee Period Expiry Flag", "enum": ["0", "1"]}}}, "dedicatedAccountChangeInformation": {"description": "contains information of changes done to balances and dates for dedicated accounts ", "title": "Dedicated Account Change Information", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"dedicatedAccountID": {"description": "parameter contains the identity of the dedicated account in order to be able to distinguish between the various dedicated accounts in an array of dedicated accounts", "title": "Dedicated Account ID", "type": "integer"}, "productID": {"description": "parameter contains the identity of a product", "title": "Product ID", "type": "integer"}, "externalproductID": {"description": "parameter contains the identity of a product", "title": "External Product ID", "type": "integer"}, "startDate": {"description": "parameter contains the start date for a dedicated account", "title": "Start Date", "type": "string", "format": "date-time"}, "expiryDate": {"description": "parameter contains the end date for a dedicated account", "title": "Expiry Date", "type": "string", "format": "date-time"}, "pamServiceID": {"description": "parameter specifies the id of the periodic account management service", "title": "PAM Service ID", "type": "string"}, "offerID": {"description": "The offerID parameter contains the identity of an offer", "title": "Offer ID", "type": "integer"}, "dedicatedAccountValue1": {"description": "indicates that the balance is in the first currency to be announced", "title": "Dedicated Account Value1", "type": "integer"}, "dedicatedAccountValue2": {"description": "indicates that the balance is in the second currency to be announced", "title": "Dedicated Account Value2", "type": "integer", "format": "Numeric"}, "dedicatedAccountRealMoneyFlag": {"description": "If the dedicated account is used to hold money that the subscriber have received through the purchase", "title": "Dedicated Acccount Real Money Flag", "type": "string"}, "closestExpiryDate": {"description": "parameter contains the date when the next sub dedicated account(s) will expire", "title": "Closest Expiry Date", "type": "string", "format": "date-time"}, "closestExpiryValue1": {"description": "indicates the balance of the first currency to be announced", "title": "Closest Expiry Value1", "type": "string"}, "closestExpiryValue2": {"description": "indicates the balance of the second currency to be announced", "title": "Closest Expiry Value2", "type": "string"}, "closestAccessibleDate": {"description": "parameter contains the date when the next sub dedicated account(s) will be accessible", "title": "Closest Accessible Date", "type": "string", "format": "date-time"}, "closestAccessibleValue1": {"description": "indicates the balance of the first currency to be announced", "title": "Closest Accessible Value1", "type": "string"}, "closestAccessibleValue2": {"description": "indicates the balance of the second currency to be announced", "title": "Closest Accessible value2", "type": "string"}, "dedicatedAccountActiveValue1": {"description": "indicates that the balance is in the first currency to be announced", "title": "Dedicated Account Active Value1", "type": "string"}, "dedicatedAccountActiveValue2": {"description": "indicates that the balance is in the second currency to be announced", "title": "Dedicated Account Active Value2", "type": "string"}, "dedicatedAccountUnitType": {"description": "parameter contains the unit of the dedicated account values ", "title": "Dedicated Account Unit Type", "enum": ["0", "1", "2", "3", "4", "5", "6"]}, "subDedicatedAccountChangeInformation": {"description": "contains information about the changes made to a sub dedicated account", "title": "Sub Dedicated Account Change Information", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"changedAmount1": {"description": "indicates a changed amount in the first currency to be announced ", "title": "Changed Amount1", "type": "string"}, "changedAmount2": {"description": "indicates a changed amount in the second currency to be announced ", "title": "Changed Amount2", "type": "string"}, "dedicatedAccountValue1": {"description": "indicates that the balance is in the first currency to be announced", "title": "Dedicated Account Value1", "type": "integer"}, "dedicatedAccountValue2": {"description": "indicates that the balance is in the second currency to be announced", "title": "Dedicated Account Value2", "type": "integer", "format": "Numeric"}, "changedExpiryDate": {"description": "parameter contains the number of days the expiry date for a dedicated account has been changed as a result of the operation ", "title": "Changed Expiry Date", "type": "integer"}, "newExpiryDate": {"description": "parameter contains the new expiry date for a dedicated account", "title": "new Expiry Date", "type": "string", "format": "date-time"}, "clearedExpiryDate": {"description": "parameter contains the previous expiry date for a cleared dedicated account", "title": "cleared Expiry Date", "type": "string", "format": "date-time"}, "changeStartDate": {"description": "parameter contains the number of days the start date for a dedicated account has been changed as a result of the operation", "title": "Change Start Date", "type": "string", "format": "date-time"}, "newStartDate": {"description": "parameter contains the new start date for a dedicated account", "title": "new Start Date", "type": "string", "format": "date-time"}, "clearedStartDate": {"description": "parameter contains the previous start date for a cleared dedicated account", "title": "clearedStartDate", "type": "string", "format": "date-time"}}}}}}}}}, "output": {"description": "The output params", "type": "object"}}