"use strict";
/**
 *  UCIP Plugins
 *
 *  <AUTHOR>
 **/
const XML_RPC = require("../xmlrpcImplClass");
const CommonProperties = require("./ucipCommonProperties.json");
const utility = require("utility");

/**
 * @class UCIPPlugin
 */
class UCIPPlugin extends XML_RPC {

    init() {

    }

    getMetaDataInfo(contextData) {
        contextData = setProperties(contextData);
        return super.getMetaDataInfo(contextData);
    }

    /**
     * plugin validate method
     *
     * @param {any} contextData
     * @param {any} schemaData
     * @returns
     * @memberof UCIPPlugin
     * @returns module validation result object
     */
    validate(contextData, schemaData) {
        schemaData = setProperties(schemaData);
        return super.validate(contextData, schemaData);
    }

    /**
     * exec method
     * @param {any} contextData
     * @memberof UCIPPlugin
     * @returns result object
     */
    exec(contextData) {
        return super.exec(contextData);
    }

    prepare(schema, moduleContext) {
        moduleContext.process = super.prepare(schema, moduleContext.process);
        if (moduleContext.pluginName != null) {
            if (moduleContext.process.originTransactionID == null || isNaN(moduleContext.process.originTransactionID)) {
                moduleContext.process.originTransactionID = String(utility.getUniqueTxnId());
            }
        }
        return moduleContext;
    }

    close() {

    }
}

module.exports = UCIPPlugin;

function setProperties(contextData) {
    contextData.category = "ucip";
    contextData.version = "v5.0";
    contextData.type = "object";
    contextData.required = [
        "name",
        "coordinates",
        "settings",
        "process",
        "output"
    ];
    Object.assign(contextData.properties, CommonProperties);
    return contextData;
}
