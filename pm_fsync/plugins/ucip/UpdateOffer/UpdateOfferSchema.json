{"typeId": "3.17", "name": "UpdateOffer", "title": "Update Offer", "description": "The UpdateOffer message will assign a new offer or update an existing offer to an account", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated ", "title": "Origin Operator ID", "type": "string"}, "offerID": {"description": "The offerID parameter contains the identity of an offer", "title": "Offer ID", "type": "integer"}, "startDate": {"description": "The startDate parameter defines the date when a dedicated account, FaF entry or offer will be considered as active. The parameter may also be used to define start date for other entities depending on the context where it is used.", "title": "Start Date", "type": "string", "format": "date-time"}, "startDateTime": {"description": "parameter contains the expiry date and time", "title": "Start Date", "type": "string", "format": "date-time"}, "startDateTimeRelative": {"description": "The startDateRelative parameter is used to make a relative adjustment to the current start date. The adjustment can be positive or negative. It is expressed in number of days.", "title": "Start Date Relative", "type": "integer"}, "expiryDate": {"description": "The expiryDate parameter contains the expiry date for a dedicated account", "title": "Expiry Date", "type": "string", "format": "date-time"}, "expiryDateTime": {"description": "parameter contains the expiry date and time", "title": "Expiry Date Time", "type": "string", "format": "date-time"}, "expiryDateTimeRelative": {"description": "parameter is used to make a relative adjustment to an expiry date ", "title": "Expiry Date Time Relative", "type": "integer"}, "offerType": {"description": "parameter identifies the offer type", "title": "offer Type", "enum": ["0", "1", "2", "3", "4", "5", "6", "7"], "format": "Numeric"}}}}}