{"typeId": "3.14", "name": "UpdateBalanceAndDate", "title": "Update Balance And Date", "description": "The message UpdateBalanceAndDate is used by external system to adjust balances, start dates and expiry dates on the main account and the dedicated accounts", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "default": "EXT", "type": "string", "minLength": 1}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "transactionCurrency": {"description": "parameter contains an ID to point out what currency is used for the transaction", "title": "Transaction Currency", "type": "string"}, "adjustmentAmountRelative": {"description": "parameter contains the amount of the adjustment(positive or negative) to be applied to the account", "title": "Adjustment Amount Relative", "type": "string"}, "transactionType": {"description": "parameter is used to specify the operation in more detail", "title": "Transaction Type", "type": "string"}, "transactionCode": {"description": "parameter is used to specify the operation in more detail", "title": "Transaction Code", "type": "string"}, "dedicatedAccountUpdateInformation": {"description": "parameter contains a value to assign to a main account", "title": "Dedicated AccountUpdate Information", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"dedicatedAccountID": {"description": "The dedicatedAccountID parameter contains the identity of the dedicated account in order to be able to distinguish between the various dedicated accounts in an array of dedicated accounts.", "title": "Dedicated Account ID", "type": "integer"}, "adjustmentAmountRelative": {"description": "The adjustmentAmountRelative parameter defines the amount of the adjustment (positive or negative) to be applied to the account.", "title": "Adjustment Amount Relative", "type": "string"}, "dedicatedAccountValueNew": {"description": "The dedicatedAccountValueNew parameter defines the amount of the adjustment (positive or negative)", "title": "dedicatedAccountValueNew", "type": "string"}, "expiryDate": {"description": "The expiryDate parameter contains the expiry date for a dedicated account", "title": "Expiry Date", "type": "string", "format": "date-time"}, "dedicatedAccountUnitType": {"description": "The adjustmentDateRelative parameter is used to make a relative adjustment to the current expiry date. The adjustment can be positive or negative. It is expressed in number of days.", "title": "dedicatedAccountUnitType", "type": "integer"}}}}, "externalData1": {"description": "These parameters are used as a spare parameter for  customizations to include in data records.", "title": "External Data1", "type": "string"}, "externalData2": {"description": "These parameters are used as a spare parameter for  customizations to include in data records.", "title": "External Data2", "type": "string"}}}}}