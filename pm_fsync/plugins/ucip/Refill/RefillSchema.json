{"typeId": "3.12", "name": "Refill", "title": "Refill", "description": "The message Refill is used to apply a refill from an administrative system to a prepaid account associated with a specific subscriber identity", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber", "transactionAmount", "transactionCurrency", "refillProfileID"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "default": "EXT", "type": "string", "minLength": 1}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "externalData1": {"description": "These parameters are used as a spare parameter for  customizations to include in data records.", "title": "External Data1", "type": "string"}, "externalData2": {"description": "These parameters are used as a spare parameter for  customizations to include in data records.", "title": "External Data2", "type": "string"}, "externalData3": {"description": "These parameters are used as a spare parameter for  customizations to include in data records.", "title": "External Data3", "type": "string"}, "externalData4": {"description": "These parameters are used as a spare parameter for  customizations to include in data records.", "title": "External Data4", "type": "string"}, "transactionAmount": {"description": "The transactionAmount parameter specifies the nominal value of the refill. A transaction parameter includes data regarding a requested change.", "title": "Transaction Amount", "type": "string"}, "transactionCurrency": {"description": "The transactionCurrency parameter contains an ID to point out what currency is used for the transaction. A transaction parameter includes data regarding a requested change.", "title": "Transaction Currency", "type": "string"}, "refillProfileID": {"description": "parameter contains a refill profile that will be converted into a segmentation identity by the AIR server", "title": "Refill Profile ID", "type": "string"}, "voucherActivationCode": {"description": "parameter contains a refill profile that will be converted into a segmentation identity by the AIR server", "title": "voucher Activation Code", "type": "string"}}}}}