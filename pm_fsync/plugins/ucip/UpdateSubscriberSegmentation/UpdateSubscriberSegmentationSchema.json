{"typeId": "3.19", "name": "UpdateSubscriberSegmentation", "title": "Update Subscriber Segmentation", "description": "The message UpdateSubscriberSegmentation is used in order set or update the accountGroupID and serviceOffering parameters which are used for subscriber segmentation", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string"}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string"}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string"}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated", "title": "Origin Operator ID", "type": "string"}, "accountGroupID": {"description": "parameter contains the Account Group identity for the account", "title": "Account Group ID", "type": "string"}, "serviceOfferings": {"description": "parameter contains the values of the service offerings defined on an account", "title": "Service Offerings", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"serviceOfferingID": {"description": "parameter contains the identity of a current service offering defined on an account", "title": "Service Offering ID", "type": "string"}, "serviceOfferingActiveFlag": {"description": "indicates if a specific service offering pointed out by the serviceOfferingID parameter is active or not", "title": "Service Offering Active Flag", "default": false, "enum": [false, true]}}}}}}}}