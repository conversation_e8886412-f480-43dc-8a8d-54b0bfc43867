/**
 *  UCIP GetUsageThresholdsAndCounters Plugin
 *
 *  <AUTHOR>
 **/
const UCIPPlugin = require('../ucipPlugin');

let schema;

class GetUsageThresholdsAndCountersPlugin extends UCIPPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof GetUsageThresholdsAndCountersPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof GetUsageThresholdsAndCountersPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof GetUsageThresholdsAndCountersPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = GetUsageThresholdsAndCountersPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require('./GetUsageThresholdsAndCountersSchema.json');
    return schema;
}
