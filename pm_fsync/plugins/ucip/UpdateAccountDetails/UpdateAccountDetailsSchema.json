{"typeId": "3.13", "name": "UpdateAccountDetails", "title": "Update Account Details", "description": "The message UpdateAccountDetails is used to update the account information", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated ", "title": "Origin Operator ID", "type": "string"}, "accountPrepaidEmptyLimitAction": {"description": "parameter contains the requested action for changing the Account Prepaid Empty Limit", "title": "Account Prepaid Empty Limit Action", "type": "string"}, "externalData1": {"description": "These parameters are used as a spare parameter for  customizations to include in data records.", "title": "External Data1", "type": "string"}, "externalData2": {"description": "These parameters are used as a spare parameter for  customizations to include in data records.", "title": "External Data2", "type": "string"}, "languageIDNew": {"description": "parameter contains the subscriber's new preferred language", "title": "Language ID New", "type": "integer"}, "ussdEndOfCallNotificationID": {"description": "parameter identifies which decision tree to use, when selecting the appropriate USSD text string for the End of Call Notification message to the subscriber", "title": "USSD EndOf Call Notification ID", "type": "string"}, "accountHomeRegion": {"description": "parameter contains the home region for the account", "title": "Account Home Region", "type": "string"}, "pinCodeOriginal": {"description": "parameter contains the pin code the subscriber currently has assigned to the account", "title": "PIN Code Original", "type": "string"}, "pinCode": {"description": "parameter contains the pin code for the subscriber", "title": "PIN Code", "type": "string"}, "languageIDCurrent": {"description": "Curent lang ID", "type": ["string", "integer"]}, "accountTimeZone": {"description": "This parameter contains the accountsTimeZone", "title": "Account Time Zone", "type": "string"}, "transactionCurrency": {"description": "parameter contains an ID to point out what currency is used for the transaction", "title": "Transaction Currency", "type": "string"}, "AccountPrepaidEmptyLimit": {"description": "is used to set a lowest allowed balance on an account", "title": "Account Prepaid Empty Limit", "type": "string"}}}}}