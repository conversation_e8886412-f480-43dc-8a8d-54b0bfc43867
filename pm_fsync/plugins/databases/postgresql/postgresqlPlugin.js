/**
 *  Databases PostgreSQL Plugin
 *
 *  <AUTHOR>
 **/
const pg = require("pg");
const DatabasesPlugin = require("../databasesPlugin");
const message = require("message");
const error_codes = message.error_codes;
const AllowedCommands = require("../allowedCommands");
const BPromise = require("bluebird");
const CDRWriter = require("../../pluginCDRWriter");
const logger = global.logger || require("log4js").getLogger();
let schema;
let poolManager = {};
const NS_PER_SEC = 1e9;

/**
 * @class PostgreSQLPlugin
 */
class PostgreSQLPlugin extends DatabasesPlugin {

  init() {

  }

  /**
   * method getMetaDataInfo
   * @returns
   * @memberof PostgreSQLPlugin
   */
  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  /**
   * method validate
   * @param {any} module
   * @returns
   * @memberof PostgreSQLPlugin
   */
  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  /**
   * method exec
   * @param {any} moduleContext
   * @returns
   * @memberof PostgreSQLPlugin
   */
  async exec(moduleContext, options) {
    if (options && options.mode === "testConn") {
      return await testConn(moduleContext);
    }
    if (options && options.mode === "testQuery") {
      return await testMode(moduleContext, options);
    }

    return await execMode(moduleContext);
  }

  close() {

  }
}

module.exports = PostgreSQLPlugin;

/* loads the PostgreSQL schema file (once and only once) and returns its schema */
function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./postgresqlSchema.json");
  return schema;
}

function testConn(moduleContext) {
  return new BPromise((resolve, reject) => {
    try {
      if (!moduleContext.settings) {
        return resolve(message.getResponseJson(moduleContext.language, error_codes.settingsNotFound));
      }
      let pool = new pg.Pool(moduleContext.settings);
      pool.connect((err, connect) => {
        if (err) {
          return resolve({
            code: err.errno,
            result: err.sqlMessage
          });
        }
        connect.end();
        return resolve(message.getResponseJson(moduleContext.language, error_codes.success));
      });
    } catch (error) {
      return reject(error);
    }
  });
}

function testMode(moduleContext, options) {
  return new BPromise((resolve, reject) => {
    try {
      if (!moduleContext.settings) {
        return resolve(message.getResponseJson(moduleContext.language, error_codes.settingsNotFound));
      }

      if (!moduleContext.process.query) {
        return resolve(message.getResponseJson(moduleContext.language, error_codes.queryNotFound));
      }

      let pool = new pg.Pool(moduleContext.settings);
      pool.connect(async (err, connect) => {
        if (err) {
          return resolve({
            code: err.errno,
            result: err.sqlMessage
          });
        }
        let result = await executeMultipleQueries(connect, moduleContext, options);
        connect.end();
        return resolve(result);
      });
    } catch (error) {
      return reject(error);
    }
  });
}

async function execMode(moduleContext, options) {
  try {
    if (!moduleContext.settings) {
      return message.getResponseJson(moduleContext.language, error_codes.settingsNotFound);
    }
    if (moduleContext.process == null || moduleContext.process.query == null) {
      return message.getResponseJson(moduleContext.language, error_codes.queryNotFound);
    }
    let startTime = process.hrtime(), diff;

       let diagnostics = {
      tid: moduleContext.process.txnId,
      MSISDN: moduleContext.process.MSISDN
    };
    let poolConfig = Object.assign(moduleContext.settings, moduleContext.advancedSettings);
    let connection = await getConnection(poolConfig);
    let result = await executeMultipleQueries(connection, moduleContext, options);
    let reqJson = moduleContext.process;
       diagnostics.reqest = reqJson;
        diff = process.hrtime(startTime);
       diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
      diagnostics.Info = moduleContext.settings;
      CDRWriter.emit("EXEC_CDR",moduleContext.appId, moduleContext.mid,moduleContext.pluginName, diagnostics.pluginResponse,result.code ,diagnostics );
    connection.release();
    return result;
  } catch (error) {
    global.logger.error(error);
     let startTime = process.hrtime(), diff;
     let diagnostics = {
     };
     diff = process.hrtime(startTime);
     diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
     let e_msg = message.getResponseJson(moduleContext.language, error_codes.pluginInternalError);
      CDRWriter.emit("EXEC_CDR",moduleContext.appId, moduleContext.mid,moduleContext.pluginName, error_codes.pluginInternalError,diagnostics.pluginResponse,e_msg );
    return e_msg;
  }
}

function getConnection(settings) {
  return new BPromise((resolve, reject) => {
    try {
      let host = settings.host;
      if (settings.host === "localhost") {
        host = "127.0.0.1";
      }
      //pool key is the combination of host,port and database name
      let poolKey = host + "_" + settings.port + "_" + settings.database;
      //Check whether the connection pool exists or not for pool key if not create new pool
      if (!poolManager.hasOwnProperty(poolKey)) {
        settings.statement_timeout = settings.statement_timeout || 30000;
        settings.query_timeout = settings.query_timeout || 30000;
        settings.connectionTimeoutMillis = settings.connectionTimeoutMillis || 30000;
        settings.idleTimeoutMillis = settings.idleTimeoutMillis || 30000;
        settings.max = 100;
        poolManager[poolKey] = new pg.Pool(settings);
      }
      poolManager[poolKey].connect()
        .then((con) => {
          return resolve(con);
        }).catch(err => {
         global.logger.error("Checking DB Connection :" + ":::::" + JSON.stringify(settings));
         global.logger.error("Checking DB Connection :" + ":::::" + err.errno + "::::" + err.sqlMessage);
          return reject({
            code: err.errno,
            result: err.sqlMessage
          });
        });
    } catch (error) {
      return reject(error);
    }
  });
}

function executeMultipleQueries(connection, moduleContext, options) {
  let sqlQueries = moduleContext.process.query;
  return new BPromise(async (resolve, reject) => {
    try {
      let finalResult = {
        code: error_codes.success,
        resultsets: {}
      };
      if (sqlQueries.match(/PROCEDURE/i) || sqlQueries.match(/DECLARE/i) || sqlQueries.match(/BEGIN/i)) {
        try {
          finalResult.resultsets["result1"] = await executeQuery(connection, sqlQueries);
        } catch (error) {
          finalResult.resultsets["result1"] = error;
        }
      }
      else {
        //The SQL statements will always ends with ";" while splitting it will give empty Query so removing the last ";"
        if (sqlQueries.substr(sqlQueries.length - 1) === ";") {
          sqlQueries = sqlQueries.slice(0, -1);
        }
        let index = 1;
        //Splitting the multiple SQL statements into single and executing
        BPromise.reduce(sqlQueries.split(";"), async (_finalResult1, sqlQuery) => {
          let flag = false;
          AllowedCommands.forEach(expression => {
            let expr = new RegExp(expression);
            if (sqlQuery.match(expr)) {
              flag = true;
              return;
            }
          });
          let key = "result" + index;
          index++;
          if (!flag) {
            finalResult.resultsets[key] = message.getResponseJson(moduleContext.language, error_codes.notAllowed);
          } else {
            if (sqlQuery.match(/select/i) && !sqlQuery.match(/LIMIT/i)) {
              let opts = setOptions(options);
              sqlQuery = sqlQuery + " LIMIT " + opts.pageLimit + " OFFSET " + opts.offset;
            }
            try {
              finalResult.resultsets[key] = await executeQuery(connection, sqlQuery);
            } catch (error) {
              finalResult.resultsets[key] = error;
            }
            return finalResult.resultsets[key];
          }
        }, finalResult)
          .then(() => {
            return resolve(finalResult);
          });
      }
    } catch (error) {
      reject(error);
    }
  });
}

function executeQuery(connection, sqlQuery) {
  return new BPromise((resolve, reject) => {
    try {
      connection.query(sqlQuery, (err, result) => {
        if (err) {
         global.logger.error("Checking DB execution :" + " ::::::" + err.errno + ":::::::" + err.sqlMessage);
         global.logger.error("Checking DB execution :" + "::::::" + sqlQuery);
          return resolve({
            code: err.errno,
            rows: err.sqlMessage
          });
        }
        let res = {
          code: error_codes.success,
          rows: JSON.parse(JSON.stringify(result)).rows
        };
        return resolve(res);
      });

    } catch (error) {
      return reject(error);
    }
  });
}

function setOptions(options) {
  if (!options) {
    options = {};
  }
  let pageLimit = options.pagesize || 10;
  let pageNumber = options.pageno && options.pageno - 1 || 0;
  let offset = pageNumber * pageLimit;
  options.pageLimit = pageLimit;
  options.offset = offset;
  return options;
}
