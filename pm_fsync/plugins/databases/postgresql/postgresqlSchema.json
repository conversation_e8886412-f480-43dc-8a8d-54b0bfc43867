{"typeId": "2.4", "name": "postgresql", "title": "Postgre SQL", "category": "database", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["host", "port", "user", "password", "database"], "properties": {"host": {"description": "The hostname of the database", "title": "Host", "default": "127.0.0.1", "type": "string", "format": "ipv4"}, "port": {"description": "The port number of the database", "title": "Port", "default": 5432, "type": "integer", "minimum": 1024, "maximum": 65535}, "database": {"description": "Name of the database", "title": "Database Name", "type": "string"}, "user": {"description": "The MySQL user to authenticate as", "title": "User Name", "type": "string", "minLength": 1}, "password": {"description": "The password of that MySQL user", "title": "Password", "hint": "password", "type": "string", "minLength": 1}, "keepAlive": {"description": "keepAlive ", "title": "KeepAlive", "default": false, "hint": "checkBox", "type": "boolean"}, "statement_timeout": {"description": "The statement_timeout value is the maximum amount of time a query can run before Postgre terminates it", "title": "Statement TimeOut(ms)", "default": 10000, "hint": "input", "type": "integer", "minimum": 1000, "maximum": 36000}, "query_timeout": {"description": "The query_timeout value is the maximum amount of time a query can run", "title": "Query TimeOut(ms)", "default": 10000, "hint": "input", "type": "integer", "minimum": 1000, "maximum": 36000}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "properties": {"query": {"description": "SQL query editor", "title": "Query Edit", "hint": "queryEditor", "type": "string"}}}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"responseCode": {"description": "parameter is sent back after a message has been processed and indicates success or failure of the message ", "title": "Response Code", "type": "string"}, "resultsets": {"description": "Resultsets of Query", "title": "Resultsets of Query", "type": "object", "properties": {"rows": {"title": "result rows", "type": "array"}}}}}, "output": {"description": "The output params", "type": "object"}}}