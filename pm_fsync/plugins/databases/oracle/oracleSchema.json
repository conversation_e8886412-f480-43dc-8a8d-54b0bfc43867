{"typeId": "2.2", "name": "oracle", "title": "Oracle DB", "category": "database", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["user", "password", "connectString", "<PERSON><PERSON><PERSON><PERSON>"], "properties": {"user": {"description": "The database user name", "title": "User Name", "type": "string", "minLength": 1}, "password": {"description": "The password of the database user", "title": "Password", "hint": "password", "type": "string", "minLength": 1}, "connectString": {"description": "The Oracle database instance to connect to", "title": "Connect String", "type": "string", "minLength": 1}, "bindParam": {"description": "Pass bind parameter to DB request", "title": "Bind Parma", "type": "string", "default": "true"}}}, "advancedSettings": {"description": "Oracle databse advanced settings", "title": "Advanced Settings", "type": "object", "properties": {"autoCommit": {"description": "If this property is true, then the transaction in the current connection is automatically committed at the end of statement execution", "title": "Auto Commit", "default": false, "hint": "checkBox", "type": "boolean"}, "poolTimeout": {"description": "The number of seconds after which idle connections may be terminated", "title": "Pool TimeOut", "default": 60, "hint": "input", "type": "integer"}, "poolMax": {"description": "The maximum number of connections to which a connection pool can grow", "title": "Pool Max", "default": 10, "type": "integer"}, "poolMin": {"description": "The minimum number of connections a connection pool maintains", "title": "Pool Min", "default": 1, "type": "integer"}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "properties": {"query": {"description": "SQL query editor", "title": "Query Edit", "hint": "queryEditor", "type": "string"}}}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"responseCode": {"description": "parameter is sent back after a message has been processed and indicates success or failure of the message ", "title": "Response Code", "type": "string"}, "resultsets": {"description": "Resultsets of Query", "title": "Resultsets of Query", "type": "object", "properties": {"rows": {"title": "result rows", "type": "array"}}}}}, "output": {"description": "The output params", "type": "object"}}}