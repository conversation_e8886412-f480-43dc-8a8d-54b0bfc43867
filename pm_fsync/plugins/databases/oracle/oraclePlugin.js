/**
 *  Databases oracle Plugin
 *
 *  <AUTHOR>
 **/

const oracledb = require("oracledb");
const DatabasesPlugin = require("../databasesPlugin");
const message = require("message");
const error_codes = message.error_codes;
const AllowedCommands = require("../allowedCommands");
oracledb.outFormat = oracledb.OBJECT;
const BPromise = require("bluebird");
const CDRWriter = require("../../pluginCDRWriter");
const logger = global.logger || require("log4js").getLogger();
const NS_PER_SEC = 1e9;

let schema;

/**
 * @class OraclePlugin
 */
class OraclePlugin extends DatabasesPlugin {
    init() {

    }

    /**
    * method getMetaDataInfo
    * @returns
    * @memberof OraclePlugin
    */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof OraclePlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof OraclePlugin
     */
    async exec(moduleContext, options) {
        if (options) {
            switch (options.mode) {
                case "testConn":
                    return await testConn(moduleContext);
                case "testQuery":
                    return await testMode(moduleContext, options);
                default:
                    return await execMode(moduleContext, options);
            }
        } else {
            return await execMode(moduleContext);
        }
    }

    close() {

    }
}

module.exports = OraclePlugin;

/* loads the oracle schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./oracleSchema.json");
    return schema;
}

function testConn(moduleContext) {
    return new BPromise((resolve, reject) => {
        try {
            if (!moduleContext.settings) {
                return reject(message.getResponseJson(moduleContext.language, error_codes.settingsNotFound));
            }
            oracledb.getConnection(moduleContext.settings,
                function (err, connection) {
                    if (err) {
                        return reject({
                            code: err.errorNum,
                            msg: err.message
                        });
                    }
                    connection.close();
                    return resolve(message.getResponseJson(moduleContext.language, error_codes.success));
                });
        } catch (error) {
            return reject(error)
        }
    });
}

function testMode(moduleContext, options) {
    return new BPromise((resolve, reject) => {
        try {
            if (!moduleContext.settings) {
                return reject(message.getResponseJson(moduleContext.language, error_codes.settingsNotFound));
            }

            if (!moduleContext.process.query) {
                return reject(message.getResponseJson(moduleContext.language, error_codes.queryNotFound));
            }
            oracledb.getConnection(moduleContext.settings, function (err, connection) {

                if (err) {
                    return reject({
                        code: err.errorNum,
                        result: err.message
                    });
                }
                executeMultipleQueries(connection, moduleContext, options).then(result => {
                    connection.close();
                    return resolve(result);
                }).catch(err => {
                    return reject(err)
                });
            });
        } catch (error) {
            return reject(error);
        }
    });
}

async function execMode(moduleContext, options) {
    let connection; // Declare connection outside the try-catch block
    try {
        if (!moduleContext.settings) {
            return message.getResponseJson(moduleContext.language, error_codes.settingsNotFound);
        }
        if (moduleContext.process == null || moduleContext.process.query == null) {
            return message.getResponseJson(moduleContext.language, error_codes.queryNotFound);
        }
        let startTime = process.hrtime(), diff;
        let diagnostics = {
            tid: moduleContext.process.txnId,
            MSISDN: moduleContext.process.MSISDN
        };
        let poolConfig = Object.assign(moduleContext.settings, moduleContext.advancedSettings);
        connection = await getConnection(poolConfig);
        let result = await executeMultipleQueries(connection, moduleContext, options);
        let reqJson = moduleContext.process;
        diagnostics.reqest = reqJson;
        diff = process.hrtime(startTime);
        diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
        diagnostics.Info = moduleContext.settings;
        CDRWriter.emit("EXEC_CDR", moduleContext.appId, moduleContext.mid, moduleContext.pluginName, diagnostics.pluginResponse, result.code, diagnostics);
        connection.release();
        return result;
    } catch (error) {
        global.logger.error(error);
        let startTime = process.hrtime(), diff;
        let diagnostics = {};

        diff = process.hrtime(startTime);
        diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
        let e_msg = error.message || error.result || message.getResponseJson(moduleContext.language, error_codes.pluginInternalError);
        CDRWriter.emit("EXEC_CDR", moduleContext.appId, moduleContext.mid, moduleContext.pluginName, error_codes.pluginInternalError, diagnostics.pluginResponse, e_msg);

        // Check if connection exists before releasing
        if (connection) {
            connection.release();
        }
        return e_msg;
    }
}


function getConnection(settings) {
    return new BPromise(async (resolve, reject) => {
        try {
            let poolAlias = settings.user + settings.connectString;
            try {
                let pool = await oracledb.getPool(poolAlias);
                pool.getConnection((err, connection) => {
                    if (err) {
                        global.logger.error("Checking DB Connection :" + ":::::" + JSON.stringify(settings));
                        global.logger.error("Checking DB Connection :" + ":::::" + err.errorNum + "::::" + err.message);
                        return reject({
                            code: err.errorNum,
                            result: err.message
                        });
                    } else {
                        connection.callTimeout = settings.timeout || 30000;
                        return resolve(connection);
                    }
                });
            } catch (error) {
                global.logger.error("Oracle DB getPool", error);
                settings.poolAlias = poolAlias;
                settings.poolMax = 50;
                settings.poolMin = 0;
                settings.poolTimeout = 0;
                settings.poolIncrement = 1;
                settings.queueRequests = true;
                /**
                 * The number of milliseconds after which connection requests waiting in the connection request queue are terminated. If queueTimeout is 0, then queued connection requests are never terminated.
                 * The default value is 60000.
                 */
                settings.queueTimeout = 5000;
                oracledb.createPool(settings, (err, pool) => {
                    if (err) {
                        return reject({
                            code: err.errorNum,
                            result: err.message
                        });
                    }
                    pool.getConnection((err, connection) => {
                        if (err) {
                            return reject({
                                code: err.errorNum,
                                result: err.message
                            });
                        } else {
                            connection.callTimeout = 10000;
                            return resolve(connection);
                        }
                    });
                });
            }
        } catch (error) {
            return reject(error);
        }
    });
}

function executeMultipleQueries(connection, moduleContext, options) {
    return new BPromise(async (resolve, reject) => {
        try {
            let sqlQueries = moduleContext.process.query;
            let finalResult = {
                code: error_codes.success,
                resultsets: {}
            };
            //STORED PROCEDURE
            if (sqlQueries.match(/\bDECLARE\b/i) ||
                sqlQueries.match(/\bBEGIN\b/i) && sqlQueries.match(/\bEND\b/i)) {
                try {
                    let bindVars = {};
                    let variables = sqlQueries.substring(sqlQueries.indexOf('\n') + 1, sqlQueries.indexOf(sqlQueries.match(/\bBEGIN\b/i)[0]));
                    variables = variables.replace(/\r\n/g, "");
                    variables = variables.replace(/\n/g, "");
                    if (variables.substr(variables.length - 1) === ";") {
                        variables = variables.slice(0, -1);
                    }
                    variables = variables.split(";");
                    variables.forEach(Element => {
                        let vars = Element.split(' ');
                        if (moduleContext.settings.bindParam != "false") {
                            bindVars[vars[0]] = { dir: oracledb.BIND_OUT, type: oracledb[vars[0]] };
                        }
                    });
                    let sqlQuery = sqlQueries.substr(sqlQueries.indexOf(sqlQueries.match(/\bBEGIN\b/i)[0]));
                    const result = await connection.execute(
                        sqlQuery,
                        bindVars);
                    finalResult.resultsets["result1"] = {
                        code: error_codes.success,
                        rows: result.outBinds
                    };
                    logger.trace("Response: ", finalResult);
                    return resolve(finalResult);
                }
                catch (err) {
                    logger.error(err);
                    return reject(err);
                }
            }
            //END OF STORED PROCEDURE
            else {
                //The SQL statements will always ends with ";" while splitting it will give empty Query so removing the last ";"
                if (sqlQueries.substr(sqlQueries.length - 1) === ";") {
                    sqlQueries = sqlQueries.slice(0, -1);
                }
                let index = 1;
                //Splitting the multiple SQL statements into single and executing
                let arrayOfQueries = sqlQueries.split(";");
                BPromise.reduce(arrayOfQueries, function (finalResult1, sqlQuery) {
                    let flag = false;
                    if (sqlQuery != null && sqlQuery.trim().length == 0) return;
                    AllowedCommands.forEach(expression => {
                        let expr = new RegExp(expression);
                        if (sqlQuery.match(expr)) {
                            flag = true;
                            return;
                        }
                    });
                    let key = "result" + (index++);
                    if (!flag) {
                        finalResult.resultsets[key] = message.getResponseJson(moduleContext.language, error_codes.notAllowed);
                    } else {
                        return executeQuery(connection, sqlQuery).then(result => {
                            return finalResult.resultsets[key] = result;
                        }).catch(err => {
                            return finalResult.resultsets[key] = err;
                        });
                    }
                }, finalResult).then(function (finalResult1) {
                    return resolve(finalResult);
                });
            }
        } catch (error) {
            reject(error);
        }
    });
}

function executeQuery(connection, sqlQuery) {
    return new BPromise((resolve, reject) => {
        connection.execute(sqlQuery, function (err, result) {
            if (err) {
                global.logger.error("Checking DB execution :" + " ::::::" + err.errorNum + ":::::::" + err.message);
                global.logger.error("Checking DB execution :" + "::::::" + sqlQuery);

                return reject({
                    code: err.errorNum,
                    rows: err.message
                });
            }
            return resolve({
                code: error_codes.success,
                rows: result.rows
            });
        });
    });
}
