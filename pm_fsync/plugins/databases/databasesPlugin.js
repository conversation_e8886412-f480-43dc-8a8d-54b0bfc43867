/**
 *  Databases Plugins
 *
 *  <AUTHOR>
 **/
const LeapPlugin = require("../LeapPlugin");
const message = require("message");
const error_codes = message.error_codes;
let allowedCommands;
/**
 * @class databasesPlugin
 */
class DatabasesPlugin extends LeapPlugin {
    init() {

    }

    getMetaDataInfo(moduleData) {
        moduleData.category = "database";
        return super.getMetaDataInfo(moduleData);
    }

    /**
     * plugin validate method
     *
     * @param {any} moduleData
     * @param {any} schemaData
     * @returns
     * @memberof databasesPlugin
     * @returns module validation result object
     */
    validate(moduleData, schemaData) {
        return super.validate(moduleData, schemaData);
    }

    /**
     * exec method
     * @param {any} moduleContext
     * @memberof databasesPlugin
     * @returns result object
     */
    exec(moduleContext) {
        return message.getResponseJson(moduleContext.language, error_codes.success);
    }

    close() {

    }
}

module.exports = DatabasesPlugin;
module.exports.loadAllowedCommands = function () {
    if (allowedCommands) {
        return allowedCommands;
    }
    allowedCommands = require("./allowedCommands");
    return allowedCommands;
}
