/**
 *  Databases mysql Plugin
 *
 *  <AUTHOR>
 **/
const mysql = require("mysql2");
const DatabasesPlugin = require("../databasesPlugin");
const message = require("message");
const error_codes = message.error_codes;
const AllowedCommands = require("../allowedCommands");
const CDRWriter = require("../../pluginCDRWriter");

const BPromise = require("bluebird");
const logger = global.logger || require("log4js").getLogger();
let schema;
let poolManager = {};
const NS_PER_SEC = 1e9;

/**
 * @class MysqlPlugin
 */
class MysqlPlugin extends DatabasesPlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof MysqlPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof MysqlPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof MysqlPlugin
     */
    async exec(moduleContext, options) {


        if (options && options.mode === "testConn") {
            return await testConn(moduleContext);
        }
        if (options && options.mode === "testQuery") {
            return await testMode(moduleContext, options);
        }
        return await execMode(moduleContext);
    }

    close() {

    }
}

module.exports = MysqlPlugin;

/* loads the mysql schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./mysqlSchema.json");
    return schema;
}

function testConn(moduleContext) {
    return new BPromise((resolve, reject) => {
        try {
            if (!moduleContext.settings) {
                return resolve(message.getResponseJson(moduleContext.language, error_codes.settingsNotFound));
            }
            let connection = mysql.createConnection(moduleContext.settings);
            connection.connect(err => {
                if (err) {
                    return resolve({
                        code: err.errno,
                        result: err.sqlMessage
                    });
                }
                connection.end();
                return resolve(message.getResponseJson(moduleContext.language, error_codes.success));
            });
        } catch (error) {
            return reject(error);
        }
    });
}

function testMode(moduleContext, options) {
    return new BPromise((resolve, reject) => {
        try {
            if (!moduleContext.settings) {
                return resolve(message.getResponseJson(moduleContext.language, error_codes.settingsNotFound));
            }

            if (!moduleContext.process.query) {
                return resolve(message.getResponseJson(moduleContext.language, error_codes.queryNotFound));
            }

            let connection = mysql.createConnection(moduleContext.settings);
            connection.connect(async (err) => {
                if (err) {
                    return resolve({
                        code: err.errno,
                        result: err.sqlMessage
                    });
                }
                let result = await executeMultipleQueries(connection, moduleContext, options);
                connection.end();
                return resolve(result);
            });
        } catch (error) {
            return reject(error);
        }
    });
}

async function execMode(moduleContext, options) {
    try {

        if (!moduleContext.settings) {
            return message.getResponseJson(moduleContext.language, error_codes.settingsNotFound);
        }

        if (!moduleContext.process.query) {
            return message.getResponseJson(moduleContext.language, error_codes.queryNotFound);
        }
        let poolConfig = Object.assign(moduleContext.settings, moduleContext.advancedSettings);
        if (global.logger.isTraceEnabled()) {
            global.logger.trace("Pool: ", poolConfig);
        }
        let connection = await getConnection(poolConfig, moduleContext);
        let result = await executeMultipleQueries(connection, moduleContext, options);
        connection.release();
        return result;
    } catch (error) {
        let e_msg = message.getResponseJson(moduleContext.language, error_codes.pluginInternalError);
        return e_msg;
    }
}

function getConnection(settings, moduleContext) {
    return new BPromise((resolve, reject) => {
        let pre_query = new Date().getTime(), resTime, post_query;
        try {
            let host = settings.host;
            if (settings.host === "localhost") {
                host = "127.0.0.1";
            }
            //pool key is the combination of host,port and database name
            let poolKey = host + "_" + settings.port + "_" + settings.database;
            //Check whether the connection pool exists or not for pool key if not create new pool
            if (!poolManager.hasOwnProperty(poolKey)) {
                settings.connectTimeout = moduleContext.settings.connectTimeout || 10000;
                settings.acquireTimeout = moduleContext.settings.acquireTimeout || 10000;
                if (global.logger.isTraceEnabled()) {
                    global.logger.trace("poolkey", poolKey, " settings ", settings);
                }
                poolManager[poolKey] = mysql.createPool(settings);
            }
            poolManager[poolKey].getConnection(function (err, connection) {
                if (err) {
                    global.logger.error("Checking DB Connection :" + ":::::" + JSON.stringify(settings));
                    global.logger.error("Checking DB Connection :" + ":::::" + err.errorno + "::::" + err.sqlMessage);
                    post_query = new Date().getTime();
                    resTime = (post_query - pre_query);
                    CDRWriter.emit("EXEC_CDR", moduleContext.appId, moduleContext.mid, moduleContext.pluginName, resTime, error_codes.pluginInternalError, err.code);
                    return reject({
                        code: err.errorno,
                        result: err.sqlMessage
                    });
                } else {
                    return resolve(connection);
                }
            });
        } catch (error) {
            post_query = new Date().getTime();
            resTime = (post_query - pre_query);
            CDRWriter.emit("EXEC_CDR", moduleContext.appId, moduleContext.mid, moduleContext.pluginName, resTime, error_codes.pluginInternalError, error.message);
            return reject(error);
        }
    });
}

function executeMultipleQueries(connection, moduleContext, options) {
    let sqlQueries = moduleContext.process.query;
    return new BPromise((resolve, reject) => {
        try {
            let finalResult = {
                code: error_codes.success,
                resultsets: {}
            };
            //The SQL statements will always ends with ";" while splitting it will give empty Query so removing the last ";"
            if (sqlQueries.substr(sqlQueries.length - 1) === ";") {
                sqlQueries = sqlQueries.slice(0, -1);
            }
            let index = 1;
            //Splitting the multiple SQL statements into single and executing
            BPromise.reduce(sqlQueries.split(";"), async (finalResult1, sqlQuery) => {
                let flag = false;
                AllowedCommands.forEach(expression => {
                    let expr = new RegExp(expression);
                    if (sqlQuery.match(expr)) {
                        flag = true;
                        return;
                    }
                });
                let key = "result" + index;
                index++;

                if (!flag) {
                    finalResult.resultsets[key] = message.getResponseJson(moduleContext.language, error_codes.notAllowed);
                } else {
                    if (sqlQuery.match(/select/i) && !sqlQuery.match(/LIMIT/i)) {
                        let opts = setOptions(options);
                        sqlQuery = sqlQuery + " LIMIT " + opts.pageLimit + " OFFSET " + opts.offset;
                    }

                    try {
                        finalResult.resultsets[key] = await executeQuery(connection, sqlQuery, moduleContext);
                    } catch (error) {
                        global.logger.info(error);
                        finalResult.resultsets[key] = error;
                    }
                    return finalResult.resultsets[key];
                }
            }, finalResult)
                .then((finalResult1) => {
                    return resolve(finalResult);
                });
        } catch (error) {
            reject(error);
        }
    });
}

function executeQuery(connection, sqlQuery, moduleContext) {
    return new BPromise((resolve, reject) => {
        let pre_query = new Date().getTime(), resTime, post_query;
        try {
            connection.query({ sql: sqlQuery, timeout: moduleContext.settings.queryTimeout || 10000 }, (err, result) => {
                if (err) {
                    global.logger.error("Checking DB execution :" + " ::::::" + err.errorno + ":::::::" + err.code + ":::::::" + err.sqlMessage);
                    global.logger.error("Checking DB execution :" + "::::::" + sqlQuery);
                    let post_query = new Date().getTime();
                    let resTime = (post_query - pre_query);
                    if (err.code == "PROTOCOL_SEQUENCE_TIMEOUT") {
                        err.errorno = error_codes.pluginExecTimeOut;
                    }
                    CDRWriter.emit("EXEC_CDR", moduleContext.appId, moduleContext.mid, moduleContext.pluginName, resTime, err.errorno, sqlQuery, err.code);

                    return reject({
                        code: err.errorno,
                        rows: err.code
                    });
                }
                post_query = new Date().getTime();
                resTime = (post_query - pre_query);
                CDRWriter.emit("EXEC_CDR", moduleContext.appId, moduleContext.mid, moduleContext.pluginName, resTime, error_codes.success, sqlQuery, JSON.stringify(result));
                return resolve({
                    code: error_codes.success,
                    rows: JSON.parse(JSON.stringify(result))
                });
            });

        } catch (error) {
            global.logger.error("Error 292:", error.message);
            post_query = new Date().getTime();
            resTime = (post_query - pre_query);
            CDRWriter.emit("EXEC_CDR", moduleContext.appId, moduleContext.mid, moduleContext.pluginName, resTime, error_codes.pluginInternalError, error.message);
            return reject(error);
        }
    });
}


function setOptions(options) {
    if (!options) {
        options = {};
    }
    let pageLimit = options.pagesize || 10;
    let pageNumber = options.pageno && options.pageno - 1 || 0;
    let offset = pageNumber * pageLimit;
    options.pageLimit = pageLimit;
    options.offset = offset;
    return options;
}
