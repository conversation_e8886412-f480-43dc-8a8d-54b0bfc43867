{"typeId": "6.12", "name": "PurgeVouchers", "title": "Purge Vouchers", "description": "The message PurgeVouchers is used to schedule a purge voucher task", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["expiryDate", "offset", "state"], "properties": {"expiryDate": {"description": "parameter is used to identify the last date when the voucher will be usable in the system", "title": "Expiry Date", "type": "string", "minLength": 1}, "offset": {"description": "parameter is used to indicate a date in the past, by specifying the offset, in days, from the current date", "title": "offset", "type": "string", "minLength": 1}, "state": {"description": "parameter is used to represent the state of a voucher, as it currently is", "title": "state", "type": "string", "minLength": 1}, "schedulation": {"description": "The scheduled record is a <struct> of its own", "title": "Schedulation", "type": "object", "properties": {"executionTime": {"description": "parameter is used to define the time when a task was run or should be run", "title": "Execution Time", "type": "string", "format": "date-time"}, "recurrence": {"description": "parameter is, in combination with the recurrenceValue parameter, used to define how often a scheduled task should be executed", "title": "Recurrence", "enum": ["daily", "weekly", "monthly"]}, "recurrenceValue": {"description": "This parameter defines the interval of the recurrence", "title": "Recurrence Value", "type": "string", "format": "Numeric"}}}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}, "purgeVouchers": {"description": "parameter is used to specify whether the vouchers should actually be purged by the PurgeVouchers scheduled task", "title": "Purge Vouchers", "enum": ["0", "1"]}, "outputVAC": {"description": "parameter is used to specify whether the Voucher Activation Code should be included in the report generated by a scheduled task", "title": "Output VAC", "enum": ["0", "1"]}}}}}