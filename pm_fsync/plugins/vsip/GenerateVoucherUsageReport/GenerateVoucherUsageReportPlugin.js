"use strict";
/**
 *  VSIP GenerateVoucherUsageReport Plugin
 *
 *  <AUTHOR>
 **/
const VSIPPlugin = require("../vsipPlugin");

let schema;

class GenerateVoucherUsageReportPlugin extends VSIPPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof GenerateVoucherUsageReportPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof GenerateVoucherUsageReportPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof GenerateVoucherUsageReportPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = GenerateVoucherUsageReportPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./GenerateVoucherUsageReportSchema.json");
    return schema;
}