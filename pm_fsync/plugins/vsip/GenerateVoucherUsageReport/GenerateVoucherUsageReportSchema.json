{"typeId": "6.21", "name": "GenerateVoucherUsageReport", "title": "Generate Voucher Usage Report", "description": "The GenerateVoucherUsageReport message is used to schedule a report file of all vouchers that was used within a specified time frame", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "properties": {"fromTime": {"description": "The fromTime parameter is used to determine which vouchers was used in a specific time frame", "title": "From Time", "type": "string", "format": "date-time"}, "toTime": {"description": "parameter is used to determine which vouchers was used in a specific time frame", "title": "To Time", "type": "string", "format": "date-time"}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}, "schedulation": {"description": "The scheduled record is a <struct> of its own", "title": "Schedulation", "type": "object", "properties": {"executionTime": {"description": "parameter is used to define the time when a task was run or should be run", "title": "Execution Time", "type": "string", "format": "date-time"}, "recurrence": {"description": "parameter is, in combination with the recurrenceValue parameter, used to define how often a scheduled task should be executed", "title": "Recurrence", "enum": ["daily", "weekly", "monthly"]}, "recurrenceValue": {"description": "This parameter defines the interval of the recurrence", "title": "Recurrence Value", "type": "string", "format": "Numeric"}}}}}}}