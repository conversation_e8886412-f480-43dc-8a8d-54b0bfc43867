{"typeId": "6.19", "name": "GenerateVoucherDistributionReport", "title": "Generate Voucher Distribution Report", "description": "The GenerateVoucherDistributionReport message is used to create a voucher distribution report file either for a batch or for all vouchers in the database", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["batchId"], "properties": {"batchId": {"description": "parameter indicates what batch a voucher belongs to", "title": "Batch Id", "type": "string", "minLength": 1}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}, "schedulation": {"description": "The scheduled record is a <struct> of its own", "title": "Schedulation", "type": "object", "properties": {"executionTime": {"description": "parameter is used to define the time when a task was run or should be run", "title": "Execution Time", "type": "string", "format": "date-time"}, "recurrence": {"description": "parameter is, in combination with the recurrenceValue parameter, used to define how often a scheduled task should be executed", "title": "Recurrence", "enum": ["daily", "weekly", "monthly"]}, "recurrenceValue": {"description": "This parameter defines the interval of the recurrence", "title": "Recurrence Value", "type": "string", "format": "Numeric"}}}}}}}