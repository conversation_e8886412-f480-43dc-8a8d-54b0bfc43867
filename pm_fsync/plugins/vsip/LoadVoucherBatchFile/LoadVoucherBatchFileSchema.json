{"typeId": "6.7", "name": "LoadVoucherBatchFile", "title": "Load Voucher Batch File", "description": "The message LoadVoucherBatchFile is used to schedule the loading of a batch file", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["batchId", "filename"], "properties": {"batchId": {"description": "parameter indicates what batch a voucher belongs to", "title": "Batch Id", "type": "string", "minLength": 1}, "filename": {"description": "parameter is the filename generated as output from the specific operation", "title": "File Name", "type": "string", "minLength": 1}, "newState": {"description": "parameter is used to represent the state of the voucher as it is, or will be, after a specific event", "title": "New State", "type": "string"}, "newSubState": {"description": "parameter is used to represent the state of the voucher as it is, or will be, after a specific event", "title": "New Sub State", "type": "string"}, "schedulation": {"description": "The scheduled record is a <struct> of its own", "title": "Schedulation", "type": "object", "properties": {"executionTime": {"description": "parameter is used to define the time when a task was run or should be run", "title": "Execution Time", "type": "string", "format": "date-time"}}}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}}}}}