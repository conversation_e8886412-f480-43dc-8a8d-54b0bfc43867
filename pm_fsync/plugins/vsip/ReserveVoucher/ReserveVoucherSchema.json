{"typeId": "6.14", "name": "ReserveVoucher", "title": "Reserve Voucher", "description": "This message is used to reserve a voucher. The message represents the start of a refill transaction.", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["activationCode", "subscriberId", "transactionId"], "properties": {"activationCode": {"description": "The activationCode parameter is the unique secret code which is used to refill the account. The activation code may have leading zeros. The element size defined below defines the limit at protocol level, and may be further restricted at application level by the server side.", "title": "Activation Code", "type": "string", "minLength": 8, "maxLength": 20}, "subscriberId": {"description": "The subscriberId parameter is used to identify a subscriber in the system. This field will hold the phone number of the subscriber in the same format as held in the account database. The number is usually in national format. Leading zeroes are allowed.", "title": "Subscriber Id", "type": "string", "minLength": 1}, "transactionId": {"description": "The transactionId parameter should be unique among transactions, and it must be common among different requests within the same transaction.", "title": "Transaction ID", "type": "string", "minLength": 1}}}}}