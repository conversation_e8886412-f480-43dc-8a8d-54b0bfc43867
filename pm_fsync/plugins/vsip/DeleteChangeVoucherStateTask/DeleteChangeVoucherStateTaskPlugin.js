"use strict";
/**
 *  VSIP DeleteChangeVoucherStateTask Plugin
 *
 *  <AUTHOR>
 **/
const VSIPPlugin = require("../vsipPlugin");

let schema;

class DeleteChangeVoucherStateTaskPlugin extends VSIPPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof DeleteChangeVoucherStateTaskPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof DeleteChangeVoucherStateTaskPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }
    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof DeleteChangeVoucherStateTaskPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = DeleteChangeVoucherStateTaskPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./DeleteChangeVoucherStateTaskSchema.json");
    return schema;
}