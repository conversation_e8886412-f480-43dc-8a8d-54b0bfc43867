{"typeId": "6.3", "name": "UpdateVoucherState", "title": "Update Voucher State", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["activationCode", "serialNumber", "newState"], "properties": {"activationCode": {"description": "parameter is the unique secret code which is used to refill the account", "title": "Activation Code", "type": "string", "minLength": 1}, "serialNumber": {"description": "parameter is used to state the unique voucher serial number that is used to identify the voucher", "title": "Serial Number", "type": "string", "minLength": 1}, "newState": {"description": "parameter is used to represent the state of the voucher as it is, or will be, after a specific event", "title": "New State", "type": "string", "minLength": 1}, "newSubState": {"description": "parameter is used to represent the state of the voucher as it is, or will be, after a specific event", "title": "New Sub State", "type": "string"}, "oldState": {"description": "parameter is used to represent the state of the voucher as it is, or was, prior to a specific event", "title": "Old State", "type": "string"}, "oldSubState": {"description": "parameter is used to represent the state of the voucher as it is, or was, prior to a specific event", "title": "Old Sub State", "type": "string"}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}}}}}