{"typeId": "6", "name": "vsip", "title": "VSIP_v5 Modules", "description": "VSIP(Voucher Communication Interface Protocol) is intended to be used for integration with voucher administrative systems other than Charging System. VSIP is an RPC style protocol wherein each request-response consists of XML messages sent over HTTP. This makes it easy to integrate with a central integration point within a network. The protocol supports a wide variety of administrative and refill related services. A VSIP request is sent to one of the redundant Voucher Servers.", "expand": false}