"use strict";
/**
 *  VSIP ChangeVoucherState Plugin
 *
 *  <AUTHOR>
 **/
const VSIPPlugin = require("../vsipPlugin");

let schema;

class ChangeVoucherStatePlugin extends VSIPPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof ChangeVoucherStatePlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof ChangeVoucherStatePlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof ChangeVoucherStatePlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = ChangeVoucherStatePlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./ChangeVoucherStateSchema.json");
    return schema;
}