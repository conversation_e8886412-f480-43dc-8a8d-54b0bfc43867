{"typeId": "6.10", "name": "ChangeVoucherState", "title": "Change Voucher State", "description": "The message ChangeVoucherState message is used to schedule a task to change the state of vouchers", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["batchId", "activationCode", "serialNumber", "serialNumberFirst", "serialNumberLast", "newState"], "properties": {"batchId": {"description": "parameter indicates what batch a voucher belongs to", "title": "Batch Id", "type": "string", "minLength": 1}, "activationCode": {"description": "parameter is the unique secret code which is used to refill the account", "title": "Activation Code", "type": "string", "minLength": 1}, "serialNumber": {"description": "parameter is used to state the unique voucher serial number that is used to identify the voucher", "title": "Serial Number", "type": "string", "minLength": 1}, "serialNumberLast": {"description": "parameter states the last voucher in the serial number range to be checked", "title": "Serial Number Last", "type": "string", "minLength": 1}, "serialNumberFirst": {"description": "parameter is used to state the first voucher in the serial number range to be checked", "title": "Serial Number First", "type": "string", "minLength": 1}, "newState": {"description": "parameter is used to represent the state of the voucher as it is, or will be, after a specific event", "title": "New State", "type": "string", "minLength": 1}, "newSubState": {"description": "parameter is used to represent the state of the voucher as it is, or will be, after a specific event", "title": "New Sub State", "type": "string"}, "oldState": {"description": "parameter is used to represent the state of the voucher as it is, or was, prior to a specific event", "title": "Old State", "type": "string"}, "oldSubState": {"description": "parameter is used to represent the state of the voucher as it is, or was, prior to a specific event", "title": "Old Sub State", "type": "string"}, "reportFormat": {"description": "parameter is used to determine what output format the result report file will have", "title": "Report Format", "enum": ["0", "1"]}, "schedulation": {"description": "The scheduled record is a <struct> of its own", "title": "Schedulation", "type": "object", "properties": {"executionTime": {"description": "parameter is used to define the time when a task was run or should be run", "title": "Execution Time", "type": "string", "format": "date-time"}}}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}}}}}