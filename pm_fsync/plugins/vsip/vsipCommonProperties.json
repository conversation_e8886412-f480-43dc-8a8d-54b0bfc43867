{"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["host", "port", "path"], "properties": {"host": {"description": "The hostname of the VSIP", "title": "Host", "default": "127.0.0.1", "type": "string", "format": "ipv4"}, "port": {"description": "The port number of the VSIP", "title": "Port", "default": 3306, "type": "integer", "minimum": 1024, "maximum": 65535}, "timeout": {"description": "The time interval defines the Request timeout in millis", "title": "Request Timeout in ms", "default": 10000, "type": "integer"}, "path": {"description": "Path of the VSIP client", "title": "Path", "type": "string", "minLength": 1}, "username": {"description": "User Name to connect VSIP client", "title": "User name", "type": "string", "minLength": 1}, "password": {"description": "Password to connect VSIP client", "title": "Password", "hint": "password", "type": "string", "minLength": 1}, "Content-Type": {"description": "Content Type to send in request header", "title": "Content type", "default": "text/xml", "enum": ["text/xml", "application/json", "text/json"], "minLength": 1}, "Accept-Charset": {"description": "Content Type to send in request header", "title": "Accept charset", "default": "UTF-8", "enum": ["US-ASCII", "UTF-8", "UTF-32", "UTF-64"], "minLength": 1}, "User-Agent": {"description": "The extrenal user agent to send in the HTTP request header", "title": "User agent", "type": "string", "minLength": 1}, "maxRetry": {"description": "The Max number of retries to connect to the IN", "title": "maxRetry", "default": 1, "type": "integer"}, "retryInterval": {"description": "The time interval betwwen retries to connect to the IN in millis", "title": "retryInterval", "default": 3000, "type": "integer"}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"responseCode": {"description": "parameter is sent back after a message has been processed and indicates success or failure of the message ", "title": "Response Code", "type": "string"}, "expiryDate": {"description": "parameter is used to identify the last date when the voucher will be usable in the system", "title": "Expiry Date", "type": "string"}, "value": {"description": "parameter is used to specify the actual value of the voucher in currency units", "title": "Value", "type": "string"}, "voucherGroup": {"description": "parameter is used to define a set of properties that are associated with a voucher", "title": "Voucher Group", "type": "string"}, "agent": {"description": "parameter is used to indicate the name of the dealer who has received the card from the service provider", "title": "Agent", "type": "string"}, "extensionText1": {"description": "parameter is used to store Additional information", "title": "Extension Text1", "type": "string"}, "extensionText2": {"description": "parameter is used to store Additional information", "title": "Extension Text2", "type": "string"}, "extensionText3": {"description": "parameter is used to store Additional information", "title": "Extension Text3", "type": "string"}, "activationCode": {"description": "parameter is the unique secret code which is used to refill the account", "title": "Activation Code", "type": "string"}, "batchId": {"description": "parameter indicates what batch a voucher belongs to", "title": "batch Id", "type": "string"}, "currency": {"description": "parameter is used to indicate the currency of the voucher value", "title": "currency", "type": "string"}, "serialNumber": {"description": "parameter is used to state the unique voucher serial number that is used to identify the voucher", "title": "Serial Number", "type": "string"}, "operatorId": {"description": "parameter is used to define the name of the operator who carried out the operation", "title": "Operator Id", "type": "string"}, "state": {"description": "parameter is used to represent the state of a voucher, as it currently is", "title": "State", "type": "string"}, "subscriberId": {"description": "parameter is used to identify a subscriber in the system", "title": "Subscriber Id", "type": "string"}, "timestamp": {"description": "parameter is detailing the time a voucher state change was done", "title": "Time stamp", "type": "string", "format": "date-time"}, "voucherExpired": {"description": "parameter is used to indicate if the voucher has passed the expiration date", "title": "Voucher Expired", "enum": ["1", "0"]}, "supplierId": {"description": "parameter is used to represent the state of a voucher, as it currently is", "title": "Supplier Id", "type": "string"}, "subState": {"description": "describes the exact condition of the voucher within a state at any given point of time", "title": "Sub State", "type": "string"}, "dynamicAttributes": {"description": "dynamicAttribute is an array structure with zero or at most five dynamic attributes", "title": "Dynamic Attributes", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"dynamicAttributeName": {"description": "is the designation of the dynamic attribute, for example", "title": "Dynamic Attribute Name", "type": "string"}, "dynamicAttributeValue": {"description": "holds the value of the dynamic attribute name", "title": "Dynamic Attribute Value", "type": "string"}}}}}}, "output": {"description": "The output params", "type": "object"}}