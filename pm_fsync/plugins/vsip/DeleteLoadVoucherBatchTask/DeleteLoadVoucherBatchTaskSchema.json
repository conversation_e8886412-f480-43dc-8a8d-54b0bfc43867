{"typeId": "6.15", "name": "DeleteLoadVoucherBatchTask", "title": "Delete Load Voucher Batch Task", "description": "The DeleteLoadVoucherBatchTask message is used to delete a task", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "properties": {"taskId": {"description": "parameter is used to state the unique Id that identifies a task in the VS Scheduler", "title": "Task Id", "type": "string", "fromat": "Numeric", "minLength": 1}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}}}}}