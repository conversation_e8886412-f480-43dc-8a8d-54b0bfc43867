{"typeId": "6.23", "name": "ChangeVoucherAttribute", "title": "Change Voucher Attribute", "description": "The message ChangeVoucherAttribute message is used to change (update) user-defined attributes for vouchers that are already loaded into the database", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["batchId", "activationCode", "serialNumber", "serialNumberFirst", "serialNumberLast"], "properties": {"batchId": {"description": "parameter indicates what batch a voucher belongs to", "title": "batch Id", "type": "string", "minLength": 1}, "activationCode": {"description": "parameter is the unique secret code which is used to refill the account", "title": "Activation Code", "type": "string", "minLength": 1}, "serialNumber": {"description": "parameter is used to state the unique voucher serial number that is used to identify the voucher", "title": "Serial Number", "type": "string", "minLength": 1}, "serialNumberLast": {"description": "parameter states the last voucher in the serial number range to be checked", "title": "Serial Number Last", "type": "string", "minLength": 1}, "serialNumberFirst": {"description": "parameter is used to state the first voucher in the serial number range to be checked", "title": "Serial Number First", "type": "string", "minLength": 1}, "dynamicAttributes": {"description": "dynamicAttribute is an array structure with zero or at most five dynamic attributes", "title": "Dynamic Attributes", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"dynamicAttributeName": {"description": "is the designation of the dynamic attribute, for example", "title": "Dynamic Attribute Name", "type": "string"}, "dynamicAttributeValue": {"description": "holds the value of the dynamic attribute name", "title": "Dynamic Attribute Value", "type": "string"}}}}, "schedulation": {"description": "The scheduled record is a <struct> of its own", "title": "Schedulation", "type": "object", "properties": {"executionTime": {"description": "parameter is used to define the time when a task was run or should be run", "title": "Execution Time", "type": "string", "format": "date-time"}}}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}, "expiryDate": {"description": "parameter is used to identify the last date when the voucher will be usable in the system", "title": "Expiry Date", "type": "string"}, "value": {"description": "parameter is used to specify the actual value of the voucher in currency units", "title": "Value", "type": "string"}, "voucherGroup": {"description": "parameter is used to define a set of properties that are associated with a voucher", "title": "Voucher Group", "type": "string"}, "agent": {"description": "parameter is used to indicate the name of the dealer who has received the card from the service provider", "title": "Agent", "type": "string"}, "extensionText1": {"description": "parameter is used to store Additional information", "title": "Extension Text1", "type": "string"}, "extensionText2": {"description": "parameter is used to store Additional information", "title": "Extension Text2", "type": "string"}, "extensionText3": {"description": "parameter is used to store Additional information", "title": "Extension Text3", "type": "string"}}}}}