{"typeId": "6.24", "name": "ChangeEncryptionKey", "title": "Change Encryption Key", "description": "The message ChangeEncryptionKey is used to change the encryption key for the default cipher profile in the Voucher Server", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["newEncryptionKey"], "properties": {"newEncryptionKey": {"description": "parameter is the new encryption key set for the default cipher profile", "title": "New Encryption Key", "type": "string"}, "oldEncryptionKey": {"description": "parameter is the existing encryption key for the default cipher profile", "title": "Old Encryption Key", "type": "string"}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}}}}}