{"typeId": "6.6", "name": "GetGenerateVoucherTaskInfo", "title": "Get Generate Voucher TaskInfo", "description": "The message GetGenerateVoucherTaskInfo is used to retrieve information about a GenerateVoucher task", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "properties": {"taskId": {"description": "parameter is used to state the unique Id that identifies a task in the VS Scheduler", "title": "Task Id", "type": "string", "fromat": "Numeric", "minLength": 1}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}}}}}