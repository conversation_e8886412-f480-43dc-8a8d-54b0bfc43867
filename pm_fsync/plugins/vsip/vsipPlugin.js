"use strict";
/**
 *  VSIP Plugins
 *
 *  <AUTHOR>
 **/
const XML_RPC = require("../xmlrpcImplClass");
const CommonProperties = require("./vsipCommonProperties.json");
/**
 * @class VSIPPlugin
 */
class VSIPPlugin extends XML_RPC {

    init(contextData) {
        return super.init(contextData);
    }

    getMetaDataInfo(contextData) {
        contextData = setProperties(contextData);
        return super.getMetaDataInfo(contextData);
    }

    /**
     * plugin validate method
     *
     * @param {any} contextData
     * @param {any} schemaData
     * @returns
     * @memberof VSIPPlugin
     * @returns module validation result object
     */
    validate(contextData, schemaData) {
        schemaData = setProperties(schemaData);
        return super.validate(contextData, schemaData);
    }

    /**
     * exec method
     * @param {any} contextData
     * @memberof VSIPPlugin
     * @returns result object
     */
    exec(contextData) {
        return super.exec(contextData);
    }

    prepare(schema, moduleContext) {
        moduleContext.process = super.prepare(schema, moduleContext.process);
        return moduleContext;
    }

    close() {
        return super.close();
    }
}

module.exports = VSIPPlugin;

function setProperties(contextData) {
    contextData.category = "vsip";
    contextData.version = "v5.0";
    contextData.type = "object";
    contextData.required = [
        "name",
        "coordinates",
        "settings",
        "process",
        "output"
    ];
    Object.assign(contextData.properties, CommonProperties);
    return contextData;
}
