{"typeId": "6.17", "name": "GenerateVoucherDetailsReport", "title": "Generate Voucher Details Report", "description": "The GenerateVoucherDetailsReport message is used to schedule a report file of all vouchers in a specified batch", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["batchId"], "properties": {"batchId": {"description": "parameter indicates what batch a voucher belongs to", "title": "Batch Id", "type": "string", "minLength": 1}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}, "schedulation": {"description": "The scheduled record is a <struct> of its own", "title": "Schedulation", "type": "object", "properties": {"executionTime": {"description": "parameter is used to define the time when a task was run or should be run", "title": "Execution Time", "type": "string", "format": "date-time"}, "recurrence": {"description": "parameter is, in combination with the recurrenceValue parameter, used to define how often a scheduled task should be executed", "title": "Recurrence", "enum": ["daily", "weekly", "monthly"]}, "recurrenceValue": {"description": "This parameter defines the interval of the recurrence", "title": "Recurrence Value", "type": "string", "format": "Numeric"}}}}}}}