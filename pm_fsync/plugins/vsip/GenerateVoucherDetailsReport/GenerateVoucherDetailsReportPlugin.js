"use strict";
/**
 *  VSIP GenerateVoucherDetailsReport Plugin
 *
 *  <AUTHOR>
 **/
const VSIPPlugin = require("../vsipPlugin");

let schema;

class GenerateVoucherDetailsReportPlugin extends VSIPPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof GenerateVoucherDetailsReportPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof GenerateVoucherDetailsReportPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof GenerateVoucherDetailsReportPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = GenerateVoucherDetailsReportPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./GenerateVoucherDetailsReportSchema.json");
    return schema;
}