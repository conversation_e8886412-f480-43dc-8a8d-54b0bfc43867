{"typeId": "6.5", "name": "GenerateVoucher", "title": "Generate Voucher", "description": "The message GenerateVoucher is used to schedule a generate voucher task", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["numberOfVouchers", "activationCodeLength", "serialNumber", "currency", "value", "voucherGroup"], "properties": {"numberOfVouchers": {"description": "parameter is used to define the number of vouchers in a batch or a serial number range", "title": "Number Of Vouchers", "type": "string"}, "activationCodeLength": {"description": "parameter describes how long an activation code must be", "title": "Activation Code Length", "type": "string"}, "serialNumber": {"description": "parameter is used to state the unique voucher serial number that is used to identify the voucher", "title": "Serial Number", "type": "string", "minLength": 1}, "currency": {"description": "parameter is used to indicate the currency of the voucher value", "title": "currency", "type": "string"}, "value": {"description": "parameter is used to specify the actual value of the voucher in currency units", "title": "Value", "type": "string"}, "voucherGroup": {"description": "parameter is used to define a set of properties that are associated with a voucher", "title": "Voucher Group", "type": "string"}, "expiryDate": {"description": "parameter is used to identify the last date when the voucher will be usable in the system", "title": "Expiry Date", "type": "string"}, "agent": {"description": "parameter is used to indicate the name of the dealer who has received the card from the service provider", "title": "Agent", "type": "string"}, "extensionText1": {"description": "parameter is used to store Additional information", "title": "Extension Text1", "type": "string"}, "extensionText2": {"description": "parameter is used to store Additional information", "title": "Extension Text2", "type": "string"}, "extensionText3": {"description": "parameter is used to store Additional information", "title": "Extension Text3", "type": "string"}, "dynamicAttributes": {"description": "dynamicAttribute is an array structure with zero or at most five dynamic attributes", "title": "Dynamic Attributes", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"dynamicAttributeName": {"description": "is the designation of the dynamic attribute, for example", "title": "Dynamic Attribute Name", "type": "string"}, "dynamicAttributeValue": {"description": "holds the value of the dynamic attribute name", "title": "Dynamic Attribute Value", "type": "string"}}}}, "schedulation": {"description": "The scheduled record is a <struct> of its own", "title": "Schedulation", "type": "object", "properties": {"executionTime": {"description": "parameter is used to define the time when a task was run or should be run", "title": "Execution Time", "type": "string", "format": "date-time"}}}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}}}}}