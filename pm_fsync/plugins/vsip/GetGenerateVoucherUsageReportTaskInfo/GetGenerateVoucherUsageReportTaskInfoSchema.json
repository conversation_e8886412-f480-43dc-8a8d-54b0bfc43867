{"typeId": "6.22", "name": "GetGenerateVoucherUsageReportTaskInfo", "title": "Get Generate Voucher Usage Report TaskInfo", "description": "The GetGenerateVoucherUsageReportTaskInfo message is used to return information about a specific or all GenerateVoucherUsageReport tasks", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "properties": {"taskId": {"description": "parameter is used to state the unique Id that identifies a task in the VS Scheduler", "title": "Task Id", "type": "string", "fromat": "Numeric", "minLength": 1}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}}}}}