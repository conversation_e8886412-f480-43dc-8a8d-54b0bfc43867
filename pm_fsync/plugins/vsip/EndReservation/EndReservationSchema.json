{"typeId": "6.15", "name": "EndReservation", "title": "End Reservation", "description": "This message is used to reserve a voucher. The message represents the start of a refill transaction.", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["activationCode", "action", "subscriberId", "transactionId"], "properties": {"activationCode": {"description": "The activationCode parameter is the unique secret code which is used to refill the account. The activation code may have leading zeros. The element size defined below defines the limit at protocol level, and may be further restricted at application level by the server side.", "title": "Activation Code", "type": "string", "minLength": 8, "maxLength": 20}, "action": {"description": "This flag is used on completion of the refill transaction. It indicates to the Voucher Server if the transaction should be committed or rolled back.", "title": "Action", "type": "string", "default": "commit", "enum": ["commit", "rollback"]}, "subscriberId": {"description": "The subscriberId parameter is used to identify a subscriber in the system. This field will hold the phone number of the subscriber in the same format as held in the account database. The number is usually in national format. Leading zeroes are allowed.", "title": "Subscriber Id", "type": "string", "minLength": 1}, "transactionId": {"description": "The transactionId parameter should be unique among transactions, and it must be common among different requests within the same transaction.", "title": "Transaction ID", "type": "string", "minLength": 1}}}}}