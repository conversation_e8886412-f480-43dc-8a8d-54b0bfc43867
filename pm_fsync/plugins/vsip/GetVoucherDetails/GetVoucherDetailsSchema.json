{"typeId": "6.1", "name": "GetVoucherDetails", "title": "Get Voucher Details", "description": "The message GetVoucherDetails is used in order to obtain detailed information on an individual voucher", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["serialNumber", "activationCode"], "properties": {"serialNumber": {"description": "The serialNumber parameter is used to state the unique voucher serial number that is used to identify the voucher. Leading zeros are allowed. The element size defined below defines the limit at protocol level, and may be further restricted at application level by the server side", "title": "Serial Number", "type": "string", "minLength": 8, "maxLength": 20}, "activationCode": {"description": "The activationCode parameter is the unique secret code which is used to refill the account. The activation code may have leading zeros. The element size defined below defines the limit at protocol level, and may be further restricted at application level by the server side.", "title": "Activation Code", "type": "string", "minLength": 8, "maxLength": 20}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}}}}}