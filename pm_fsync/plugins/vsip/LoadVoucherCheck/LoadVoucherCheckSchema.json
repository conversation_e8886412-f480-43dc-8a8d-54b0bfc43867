{"typeId": "6.4", "name": "LoadVoucherCheck", "title": "Load Voucher Check", "description": "The message LoadVoucherCheck is used to check if the vouchers in a serial number range are loaded into the database", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["serialNumberFirst", "serialNumberLast"], "properties": {"serialNumberFirst": {"description": "parameter is used to state the first voucher in the serial number range to be checked", "title": "Serial Number First", "type": "string", "minLength": 1}, "serialNumberLast": {"description": "parameter states the last voucher in the serial number range to be checked", "title": "Serial Number Last", "type": "string", "minLength": 1}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}}}}}