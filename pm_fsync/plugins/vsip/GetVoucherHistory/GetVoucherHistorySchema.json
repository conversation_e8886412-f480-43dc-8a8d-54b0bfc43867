{"typeId": "6.2", "name": "GetVoucherHistory", "title": "Get Voucher History", "description": "The message GetVoucherHistory is used to get historical information for a voucher including information about voucher state changes performed for a specific voucher", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "properties": {"activationCode": {"description": "parameter is the unique secret code which is used to refill the account", "title": "Activation Code", "type": "string", "minLength": 1}, "serialNumber": {"description": "parameter is used to state the unique voucher serial number that is used to identify the voucher", "title": "Serial Number", "type": "string", "minLength": 1}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}}}}}