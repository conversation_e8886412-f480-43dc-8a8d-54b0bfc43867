"use strict";
/**
 *  VSIP GetPurgeVouchersTaskInfo Plugin
 *
 *  <AUTHOR>
 **/
const VSIPPlugin = require("../vsipPlugin");

let schema;

class GetPurgeVouchersTaskInfoPlugin extends VSIPPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof GetPurgeVouchersTaskInfoPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof GetPurgeVouchersTaskInfoPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof GetPurgeVouchersTaskInfoPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = GetPurgeVouchersTaskInfoPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./GetPurgeVouchersTaskInfoSchema.json");
    return schema;
}