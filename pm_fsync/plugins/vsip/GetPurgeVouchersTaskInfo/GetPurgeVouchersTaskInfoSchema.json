{"typeId": "6.13", "name": "GetPurgeVouchersTaskInfo", "title": "Get Purge Vouchers TaskInfo", "description": "The message GetPurgeVouchersTaskInfo message is used to return information about a PurgeVoucherTask", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "properties": {"taskId": {"description": "parameter is used to state the unique Id that identifies a task in the VS Scheduler", "title": "Task Id", "type": "string", "fromat": "Numeric", "minLength": 1}, "networkOperatorId": {"description": "parameter is used to reference a Mobile Virtual Network Operator", "title": "Network Operator Id", "type": "string"}}}}}