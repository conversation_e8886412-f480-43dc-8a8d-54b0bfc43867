{"typeId": "4.1", "name": "AddPeriodicAccountManagementData", "title": "Add Periodic Account Management Data", "description": "The message AddPeriodicAccountManagementData adds periodic account management data to a subscriber", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber", "pamInformationList"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "originOperatorID": {"description": "The originOperatorID parameter is the identity of the system user or the session from where the operation was initiated. It might be used for security management or logging purposes.", "title": "Origin Operator ID", "type": "string"}, "pamInformationList": {"description": "is a list of pamInformation", "title": "PAM Information List", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"pamInformation": {"description": "information used for periodic account management", "title": "PAM Information", "type": "object", "required": ["pamServiceID", "pamClassID", "scheduleID"], "properties": {"pamServiceID": {"description": "The pamIndicator parameteris usedfor differentiation intheperiodic account management structure when executing a periodic account management evaluation.", "title": "PAM Service ID", "type": "integer"}, "pamClassID": {"description": "The pamClassID parameter specifies the periodic account management class used by the periodic account management service.", "title": "PAM Class ID", "type": "integer"}, "scheduleID": {"description": "The scheduleID parameter contains the schedule that is used by the periodic account management service.", "title": "Schedule ID", "type": "integer"}, "currentPamPeriod": {"description": "The currentPamPeriod parameter contains the periodic account management period that is currently used for the subscriber.", "title": "Current PAM Period", "type": "integer"}, "deferredToDate": {"description": "The deferredToDate parameter contains the deferred to date for the Periodic Account Management service. If deferredToDate is set in the past in a request, the deferred to date will be removed.", "title": "Deffered To Date", "type": "string", "format": "date-time"}, "lastEvaluationDate": {"description": "The lastEvaluationDate parameter contains the date of the last periodic account management evaluation.", "title": "Last Evaluation Date", "type": "string", "format": "date-time"}}}}}}}}}}