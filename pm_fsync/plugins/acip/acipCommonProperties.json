{"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["host", "port", "path"], "properties": {"host": {"description": "The hostname of the ACIP", "title": "Host", "default": "127.0.0.1", "type": "string", "format": "ipv4"}, "port": {"description": "The port number of the ACIP", "title": "Port", "default": 3306, "type": "integer", "minimum": 1024, "maximum": 65535}, "timeout": {"description": "The time interval defines the Request timeout in millis", "title": "Request Timeout in ms", "default": 10000, "type": "integer"}, "path": {"description": "Path of the ACIP client", "title": "Path", "type": "string", "minLength": 1}, "username": {"description": "User Name to connect ACIP client", "title": "User name", "type": "string", "minLength": 1}, "password": {"description": "Password to connect ACIP client", "title": "Password", "hint": "password", "type": "string", "minLength": 1}, "Content-Type": {"description": "Content Type to send in request header", "title": "Content type", "default": "text/xml", "enum": ["text/xml", "application/json", "text/json"], "minLength": 1}, "Accept-Charset": {"description": "Content Type to send in request header", "title": "Accept charset", "default": "UTF-8", "enum": ["US-ASCII", "UTF-8", "UTF-32", "UTF-64"], "minLength": 1}, "User-Agent": {"description": "The extrenal user agent to send in the HTTP request header", "title": "User agent", "type": "string", "minLength": 1}, "maxRetry": {"description": "The Max number of retries to connect to the IN", "title": "maxRetry", "default": 1, "type": "integer"}, "retryInterval": {"description": "The time interval betwwen retries to connect to the IN in millis", "title": "retryInterval", "default": 3000, "type": "integer"}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"responseCode": {"description": "indicates success or failure of the message", "title": "Response Code", "type": "integer"}, "originTransactionID": {"description": "origin trans id", "type": "string"}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated ", "title": "Origin Operator ID", "type": "string"}, "accumulatorInformation": {"description": "The accumulatorInformation is enclosed in a <struct> of its own. Structs are placed in an <array>", "title": "Accumulator Information", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"accumulatorID": {"description": "parameter contains the accumulator identity", "title": "Accumulator ID", "type": "string", "format": "Numeric"}, "accumulatorValue": {"description": "parameter contains an accumulator value", "title": "Accumulator Value", "type": "string", "format": "Numeric"}, "accumulatorStartDate": {"description": "parameter indicates the date on which the accumulator was last reset ", "title": "Accumulator Start Date", "type": "string", "format": "date-time"}, "accumulatorEndDate": {"description": "parameter indicates the date on which the accumulator will be reset to the initial value again", "title": "Accumulator End Date", "type": "string", "format": "date-time"}}}}, "negotiatedCapabilities": {"description": "parameter is used to indicate the negotiated capabilities between the client and server node", "title": "Negotiated Capabilities", "type": "array", "items": {"type": "integer"}}, "availableServerCapablities": {"description": "parameter is used to indicate the available capabilities at the server node", "title": "Available Server Capablities", "type": "array", "items": {"type": "integer"}}}}, "output": {"description": "The output params", "type": "object"}}