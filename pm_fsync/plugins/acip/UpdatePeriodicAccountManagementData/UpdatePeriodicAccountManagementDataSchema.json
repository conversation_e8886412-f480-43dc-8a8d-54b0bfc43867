{"typeId": "4.21", "name": "UpdatePeriodicAccountManagementData", "title": "Update Periodic Account Management Data", "description": "The message UpdatePeriodicAccountManagementData changes periodic account management data for a subscriber", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber", "pamUpdateInformationList"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated", "title": "Origin Operator ID", "type": "string"}, "pamUpdateInformationList": {"description": "is a list of pamInformation", "title": "PAM UpdateInformation List", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"pamUpdateInformation": {"description": "is enclosed in a <struct> of its own and contains information used when updating periodic account management data", "title": "PAM Update Information", "type": "object", "required": ["pamServiceID"], "properties": {"pamServiceID": {"description": "parameter specifies the id of the periodic account management service", "title": "PAM Service ID", "type": "string"}, "pamClassIDOld": {"description": "parameter specifies the old periodic account management class used by the periodic account management service when changing the periodic account management class", "title": "PAM Class ID Old", "type": "string"}, "pamClassIDNew": {"description": "parameter specifies the new periodic account management class used by the periodic account management service when changing the periodic account management class", "title": "PAM Class ID New", "type": "string"}, "scheduleIDOld": {"description": "parameter contains the old schedule used when changing the schedule", "title": "Schedule ID Old", "type": "string"}, "scheduleIDNew": {"description": "parameter contains the new schedule used when changing the schedule", "title": "Schedule ID New", "type": "string"}, "currentPamPeriod": {"description": "parameter contains the periodic account management period that is currently used for the subscriber", "title": "Current PAM Period", "type": "string"}, "deferredToDate": {"description": "parameter contains the deferred to date for the Periodic Account Management service", "title": "Deffered To Date", "type": "string", "format": "date-time"}, "pamServicePriorityOld": {"description": "parameter indicates the old priority between PAM services at PAM evaluation, if priority is updated", "title": "PAM Service Priority Old", "type": "string"}, "pamServicePriorityNew": {"description": "parameter indicates the new priority between PAM services at PAM evaluation, if priority is updated", "title": "PAM Service Priority New", "type": "string"}}}}}}}}}}