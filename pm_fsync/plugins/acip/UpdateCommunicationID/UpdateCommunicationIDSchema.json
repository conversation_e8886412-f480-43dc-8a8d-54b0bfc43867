{"typeId": "4.19", "name": "UpdateCommunicationID", "title": "Update CommunicationID", "description": "The communication ID change operation changes the Communication ID", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated", "title": "Origin Operator ID", "type": "string"}, "msisdnCurrent": {"description": "parameter contains the existing subscriber identity of the subscriber", "title": "Msisdn Current", "type": "string"}, "msisdnCurrentNAI": {"description": "parameter contains the Nature of Address Indicator identifies the format of the msisdnCurrent parameter", "title": "Msisdn Current NAI", "type": "string", "format": "Numeric"}, "msisdnNew": {"description": "parameter contains the MSISDN format of the msisdnNew parameter", "title": "msisdn New", "type": "string"}, "msisdnNewNAI": {"description": "parameter contains the MSISDN format of the msisdnNew parameter", "title": "msisdn New NAI", "type": "string", "format": "Numeric"}, "imsiCurrent": {"description": "parameter contains the subscriber's existing IMSI", "title": "imsi Current", "type": "string", "format": "Numeric"}, "imsiNew": {"description": "parameter contains the subscriber's new IMSI", "title": "imsi New", "type": "string", "format": "Numeric"}, "naiCurrent": {"description": "parameter (Network Access Identifier) contains the subscriber's existing NAI", "title": "nai Current", "type": "string", "format": "Numeric"}, "naiNew": {"description": "parameter (Network Access Identifier) contains the subscriber's new NAI", "title": "nai <PERSON>", "type": "string", "format": "Numeric"}, "sipUriCurrent": {"description": "parameter contains the subscriber's existing SIP-URI", "title": "sip Uri Current", "type": "string", "format": "Numeric"}, "sipUriNew": {"description": "parameter contains the subscriber's new SIP-URI", "title": "sip Uri New", "type": "string", "format": "Numeric"}, "privateCurrent": {"description": "parameter contains the subscriber's existing address in Private format", "title": "Private Current", "type": "string", "format": "Numeric"}, "privateNew": {"description": "parameter contains the subscriber's new address in Private format", "title": "Private New", "type": "string", "format": "Numeric"}, "chargingInformation": {"description": "parameter contains request information for an charged Online Communication ID Change", "title": "Charging Information", "type": "object", "properties": {"chargingIndicator": {"description": "parameter contains an indicator for rating differentiation", "title": "Charging Indicator", "type": "string"}, "specifiedPrice": {"description": "parameter contains a price that shall be used to charge for the operation", "title": "Specified Price", "type": "string"}, "transactionCurrency": {"description": "parameter contains an ID to point out what currency is used for the transaction", "title": "Transaction Currency", "type": "string"}, "suppressDeduction": {"description": "parameter indicates if the calculated cost is to be withdrawn from the account or not", "title": "Suppress Deduction", "enum": ["0", "1"]}}}, "externalContract": {"description": "parameter is used to indicate if the account resides in Charging Compound or in an external system in a convergent solution", "title": "External Contract", "enum": ["0", "1"]}}}}}