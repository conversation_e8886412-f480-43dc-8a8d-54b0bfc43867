/**
 *  ACIP DeleteAccumulators Plugin
 *
 *  <AUTHOR>
 **/
const ACIPPlugin = require("../acipPlugin");

let schema;

class DeleteAccumulatorsPlugin extends ACIPPlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof DeleteAccumulatorsPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof DeleteAccumulatorsPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof DeleteAccumulatorsPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = DeleteAccumulatorsPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./DeleteAccumulatorsSchema.json");
    return schema;
}
