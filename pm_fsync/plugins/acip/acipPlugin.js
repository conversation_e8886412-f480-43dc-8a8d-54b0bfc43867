"use strict";
/**
 *  ACIP Plugins
 *
 *  <AUTHOR>
 **/
const XML_RPC = require("../xmlrpcImplClass");
const CommonProperties = require("./acipCommonProperties.json");
const utility = require("utility");

/**
 * @class ACIPPlugin
 */
class ACIPPlugin extends XML_RPC {

    init() {

    }

    getMetaDataInfo(contextData) {
        contextData = setProperties(contextData);
        return super.getMetaDataInfo(contextData);
    }

    /**
     * plugin validate method
     *
     * @param {any} contextData
     * @param {any} schemaData
     * @returns
     * @memberof ACIPPlugin
     * @returns module validation result object
     */
    validate(contextData, schemaData) {
        schemaData = setProperties(schemaData);
        return super.validate(contextData, schemaData);
    }

    /**
     * exec method
     * @param {any} contextData
     * @memberof ACIPPlugin
     * @returns result object
     */
    exec(contextData) {
        return super.exec(contextData);
    }

    prepare(schema, moduleContext) {
        moduleContext.process = super.prepare(schema, moduleContext.process);
        moduleContext.process.originTransactionID = String(utility.getUniqueTxnId());
        return moduleContext;
    }

    close() {

    }
}

module.exports = ACIPPlugin;

function setProperties(contextData) {
    contextData.category = "acip";
    contextData.version = "v5.0";
    contextData.type = "object";
    contextData.required = [
        "name",
        "coordinates",
        "settings",
        "process",
        "output"
    ];
    Object.assign(contextData.properties, CommonProperties);
    return contextData;
}