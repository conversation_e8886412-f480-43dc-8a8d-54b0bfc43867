{"typeId": "4.13", "name": "InstallSubscriber", "title": "InstallSubscriber", "description": "The message InstallSubscriber performs an installation of a subscriber with relevant account and subscriber data", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated", "title": "Origin Operator ID", "type": "string"}, "serviceClassNew": {"description": "parameter contains the new service class for the refill", "title": "Service Class New", "type": "string"}, "temporaryBlockedFlag": {"description": "parameter is used to indicate which action to take when updating the promotion plan data on account", "title": "Temporary Blocked Flag", "enum": [false, true]}, "languageIDNew": {"description": "parameter contains the subscriber's new preferred language", "title": "Language ID New", "type": "string"}, "ussdEndOfCallNotificationID": {"description": "parameter identifies which decision tree to use, when selecting the appropriate USSD text string for the End of Call Notification message to the subscriber", "title": "USSD EndOf Call Notification ID", "type": "string"}, "accountGroupID": {"description": "parameter contains the Account Group identity for the account", "title": "Account Group ID", "type": "string"}, "serviceOfferings": {"description": "parameter contains the values of the service offerings defined on an account", "title": "Service Offerings", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"serviceOfferingID": {"description": "parameter contains the identity of a current service offering defined on an account", "title": "Service Offering ID", "type": "string"}, "serviceOfferingActiveFlag": {"description": "indicates if a specific service offering pointed out by the serviceOfferingID parameter is active or not", "title": "Service Offering Active Flag", "enum": ["0", "1"]}}}}, "promotionPlanID": {"description": "parameter contains the identity of one of the current promotion plans of a subscriber", "title": "Promotion Plan ID", "type": "string", "format": "Numeric"}, "promotionStartDate": {"description": "parameter contains the value of a non - monetary usage counter, without any factor", "title": "Promotion Start Date", "type": "string", "format": "date-time"}, "promotionEndDate": {"description": "parameter specifies the end date of the associated promotion plan", "title": "Promotion End Date", "type": "string", "format": "date-time"}, "accountHomeRegion": {"description": "parameter contains the home region for the account", "title": "Account Home Region", "type": "string"}, "pamInformationList": {"description": "is a list of pamInformation", "title": "PAM Information List", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"pamInformation": {"description": "information used for periodic account management", "title": "PAM Information", "type": "object", "properties": {"pamServiceID": {"description": "parameter specifies the id of the periodic account management service", "title": "PAM Service ID", "type": "string"}, "pamClassID": {"description": "parameter specifies the periodic account management class used by the periodic account management service", "title": "PAM Class ID", "type": "string"}, "scheduleID": {"description": "parameter contains the schedule that is used by the periodic account management service", "title": "Schedule ID", "type": "string"}, "currentPamPeriod": {"description": "parameter contains the periodic account management period that is currently used for the subscriber", "title": "Current PAM Period", "type": "string"}, "deferredToDate": {"description": "parameter contains the deferred to date for the Periodic Account Management service", "title": "Deffered To Date", "type": "string", "format": "date-time"}, "pamServicePriority": {"description": "parameter indicates the priority between PAM services at PAM evaluation", "title": "PAM Service Priority", "type": "string"}}}}}}, "accountTimeZone": {"description": "This parameter contains the accountsTimeZone", "title": "Account Time Zone", "type": "string"}, "transactionCurrency": {"description": "parameter contains an ID to point out what currency is used for the transaction", "title": "Transaction Currency", "type": "string"}, "AccountPrepaidEmptyLimit": {"description": "is used to set a lowest allowed balance on an account", "title": "Account Prepaid Empty Limit", "type": "string"}, "localProviderType": {"description": "parameter contains the Account Group identity for the account", "title": "localProviderType", "enum": ["1", "2"]}, "offerUpdateInformationList": {"description": "contains dates (or dates and time) for offers ", "title": "Offer Update Information List", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"pamServiceID": {"description": "parameter specifies the id of the periodic account management service", "title": "PAM Service ID", "type": "string"}, "offerID": {"description": "The offerID parameter contains the identity of an offer", "title": "Offer ID", "type": "integer"}, "startDate": {"description": "parameter contains the start date for a dedicated account", "title": "Start Date", "type": "string", "format": "date-time"}, "expiryDate": {"description": "parameter contains the end date for a dedicated account", "title": "Expiry Date", "type": "string", "format": "date-time"}, "startDateTime": {"description": "parameter contains the start date and time for an offer", "title": "Start Date", "type": "string", "format": "date-time"}, "expiryDateTime": {"description": "parameter contains the expiry date and time", "title": "Expiry Date", "type": "string", "format": "date-time"}, "offerType": {"description": "parameter identifies the offer type", "title": "offer Type", "enum": ["0", "1", "2", "3", "4", "6", "7"], "format": "Numeric"}, "offerState": {"description": "parameter specifies the actual offer state", "title": "Offer State", "type": "string"}, "offerProviderID": {"description": "parameter contains the subscriber number as represented in the SDP database for the provider", "title": "Offer Provider ID", "type": "string", "format": "Numeric"}, "productID": {"description": "parameter contains the identity of a product", "title": "Product ID", "type": "string"}, "externalproductID": {"description": "parameter contains the identity of a product", "title": "External Product ID", "type": "string", "format": "Numeric"}, "updateAction": {"description": "parameter is used to indicate which action to take on the resource", "title": "Update Action", "type": "string"}, "dedicatedAccountUpdateInformation": {"description": "parameter contains a value to assign to a main account", "title": "Dedicated AccountUpdate Information", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"dedicatedAccountID": {"description": "The dedicatedAccountID parameter contains the identity of the dedicated account in order to be able to distinguish between the various dedicated accounts in an array of dedicated accounts.", "title": "Dedicated Account ID", "type": "integer"}, "adjustmentAmountRelative": {"description": "The adjustmentAmountRelative parameter defines the amount of the adjustment (positive or negative) to be applied to the account.", "title": "Adjustment Amount Relative", "type": "string"}, "dedicatedAccountValueNew": {"description": "The dedicatedAccountValueNew parameter contains value to assign to a dedicated account. This is not taking in consideration any ongoing chargeable events.", "title": "Dedicated Account Value New", "type": "string"}, "adjustmentDateRelative": {"description": "The adjustmentDateRelative parameter is used to make a relative adjustment to the current expiry date. The adjustment can be positive or negative. It is expressed in number of days.", "title": "Adjustment Date Relative", "type": "integer"}, "expiryDate": {"description": "The expiryDate parameter contains the expiry date for a dedicated account.", "title": "Expiry Date", "type": "string", "format": "date-time"}, "startDate": {"description": "The startDate parameter defines the date when a dedicated account, FaF entry or offer will be considered as active. The parameter may also be used to define start date for other entities depending on the context where it is used.", "title": "Start Date", "type": "string", "format": "date-time"}, "adjustmentStartDateRelative": {"description": "The adjustmentStartDateRelative parameter is used to make a relative adjustment to the current start date. The adjustment can be positive or negative. It is expressed in number of days.", "title": "Adjustment Start Date Relative", "type": "integer"}, "dedicatedAccountUnit": {"description": "The dedicatedAccountUnit parameter contains the unit of the dedicated account values.", "title": "Dedicated Account Unit", "enum": ["0", "1", "2", "3", "4", "5", "6"]}, "expiryDateCurrent": {"description": "The expiryDateCurrent parameter contains the current expiry date for a dedicated account. The parameter may also be used to define expiry date for other entities depending on the context where it is used. Used for validation. No validation is performed if omitted.", "title": "Expiry Date Current", "type": "string", "format": "date-time"}, "startDateCurrent": {"description": "The startDateCurrent parameter contains the current start date for a dedicated account. The parameter may also be used to define start date for other entities depending on the context where it is used. Used for validation. No validation is performed if omitted.", "title": "Start Date", "type": "string", "format": "date-time"}}}}, "attributeUpdateInformationList": {"description": "contains information about attributes", "title": "Attribute Update Information List", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"attributeName": {"description": "contains the name of the attribute", "title": "Attribute Name", "type": "string"}, "attributeValueString": {"description": "contains the string value of an attribute", "title": "Attribute Value String", "type": "string"}, "attributeValueInteger": {"description": "contains the integer value of an attribute", "title": "attributeValueInteger", "type": "string"}, "attributeValueDate": {"description": "contains the Date value of an attribute", "title": "Attribute Value Date", "type": "string"}, "attributeUpdateAction": {"description": "contains the Source of an attribute", "title": "Attribute Update Action", "enum": ["ADD", "DELETE", "CLEAR", "SET"]}}}}}}}}}}}