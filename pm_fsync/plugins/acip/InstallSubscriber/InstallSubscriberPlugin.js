/**
 *  ACIP InstallSubscriber Plugin
 *
 *  <AUTHOR>
 **/
const ACIPPlugin = require("../acipPlugin");

let schema;

class InstallSubscriberPlugin extends ACIPPlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof InstallSubscriberPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof InstallSubscriberPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof InstallSubscriberPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = InstallSubscriberPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./InstallSubscriberSchema.json");
    return schema;
}
