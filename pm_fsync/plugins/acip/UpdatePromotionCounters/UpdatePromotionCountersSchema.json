{"typeId": "4.22", "name": "UpdatePromotionCounters", "title": "Update Promotion Counters", "description": "The message UpdatePromotionCounters give access to modify the counters used in the calculation when to give a promotion or promotion plan progression", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated", "title": "Origin Operator ID", "type": "string"}, "transactionCurrency": {"description": "parameter contains an ID to point out what currency is used for the transaction", "title": "Transaction Currency", "type": "string"}, "promotionRefillAmountRelative": {"description": "parameter specifies an amount to be adjusted to the accumulated value of account refills made within the current promotion plan of a subscriber", "title": "Promotion Refill Amount Relative", "type": "string"}, "promotionRefillCounterStepRelative": {"description": "parameter contains the number of steps to be adjusted to the accumulated number of account refills within the current promotion plan of a subscriber", "title": "Promotion Refill Counter Step Relative", "type": "string"}, "progressionRefillAmountRelative": {"description": "parameter specifies an amount to be adjusted to the accumulated value of account refill for promotion plan progression", "title": "Progression Refill Amount Relative", "type": "string"}, "progressionRefillCounterStepRelative": {"description": "parameter contains the number of steps to be adjusted on the accumulated number of account refills for promotion plan progression", "title": "Pogression Refill Counter Step Relative", "type": "string"}}}}}