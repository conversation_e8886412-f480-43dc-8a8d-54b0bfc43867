{"typeId": "4.27", "name": "UpdateTimeRestriction", "title": "Update Time Restriction", "description": "This message handles both creation and updates to time restrictions. If a restriction id is given that does not exist the restriction will be created", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated ", "title": "Origin Operator ID", "type": "string"}, "timeRestrictionInformation": {"description": "parameter contains information about a time restriction", "title": "Time Restriction Information", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"timeRestrictionID": {"description": "parameter identifies the specific time restriction", "title": "Time Restriction ID", "type": "string"}, "timeRestrictionFlags": {"description": "contains information about how and when a time restriction should be applied", "title": "Time Restriction Flags", "type": "object", "properties": {"mondayFlag": {"description": "indicates restrictions for the day", "title": "Monday Flag", "enum": [false, true]}, "tuesdayFlag": {"description": "indicates restrictions for the day", "title": "Tuesday Flag", "enum": [false, true]}, "wednesdayFlag": {"description": "indicates restrictions for the day", "title": "Wednesday Flag", "enum": [false, true]}, "thursdayFlag": {"description": "indicates restrictions for the day", "title": "Thursday Flag", "enum": [false, true]}, "fridayFlag": {"description": "indicates restrictions for the day", "title": "Friday Flag", "enum": [false, true]}, "saturdayFlag": {"description": "indicates restrictions for the day", "title": "Saturday Flag", "enum": [false, true]}, "sundayFlag": {"description": "indicates restrictions for the day", "title": "Sunday Flag", "enum": [false, true]}, "timeRestrictionSuspendedFlag": {"description": "The flag indicates if the restriction is suspended or not", "title": "timeRestrictionSuspendedFlag", "enum": [false, true]}}}, "timeRestrictionStartTime": {"description": "The start of the time restriction given as seconds since midnight", "title": "Time Restriction Start Time", "type": "string"}, "timeRestrictionEndTime": {"description": "parameter contains the periodic account management period that is currently used for the subscriber", "title": "Time Restriction End Time", "type": "string"}, "offerID": {"description": "The offerID parameter contains the identity of an offer", "title": "Offer ID", "type": "integer"}}}}}}}}