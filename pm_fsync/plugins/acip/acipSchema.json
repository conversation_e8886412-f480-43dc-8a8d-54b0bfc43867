{"typeId": "4", "name": "acip", "title": "ACIP v5 Modules", "description": "ACIP(Administrative Communication Interface Protocol) is an IP-based protocol used for integration towards the AIR server from the external administrative application. ACIP is an XML over HTTP based protocol, which makes it easy to integrate with a central integration point within a network. The protocol supports both session as well as event based clients. An ACIP request is sent to one of the AIR servers within the network and for redundancy purposes it is required to have N+1 AIR system in the network. ", "expand": false}