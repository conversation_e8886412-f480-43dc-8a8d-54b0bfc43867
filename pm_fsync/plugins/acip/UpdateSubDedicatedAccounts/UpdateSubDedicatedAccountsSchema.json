{"typeId": "4.25", "name": "UpdateSubDedicatedAccounts", "title": "Update Sub-Dedicated Accounts", "description": "The message UpdateSubDedicatedAccounts is used by external system to adjust balances, start dates and expiry dates on the sub dedicated accounts", "properties": {"process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["originNodeType", "originHostName", "originTransactionID", "originTimeStamp", "subscriberNumber", "subDedicatedAccountUpdateInformation"], "properties": {"originNodeType": {"description": "The originNodeType parameter defines the origin node type that is set by AIR or provided by an external network element. It contains the type of the logical node from where the operation originated. External network elements are not allowed to use the reserved names on network element types, (except EXT).", "title": "Origin Node Type", "enum": ["EXT", "AIR", "ADM", "UGW", "IVR", "OGW", "SDP"]}, "originHostName": {"description": "The originHostName parameter contains an identifier string for the host where the operation originated from. The host name shall be unique within the network for a given network element type.", "title": "Origin Host Name", "type": "string", "minLength": 1}, "originTransactionID": {"description": "The originTransactionID parameter reference to a single operation, generated by the system it was initiated from.", "title": "Origin Transaction ID", "type": "string", "minLength": 1}, "originTimeStamp": {"description": "The originTimeStamp parameter contains the date and time of sending the request by the entity that initiated the operation.", "title": "Origin Time Stamp", "type": "string", "format": "date-time", "minLength": 1}, "subscriberNumberNAI": {"description": "The subscriberNumberNAI parameter contains the Nature of Address Indicator identifies the format of the subscriberNumber parameter.", "title": "Subscriber Number NAI", "format": "Numeric", "minLength": 1, "enum": ["0", "1", "2"]}, "subscriberNumber": {"description": "The subscriberNumber parameter contains the subscriber identity of the subscriber related to the operation. The default format of the parameter is the same numbering format as used by the account database, this also includes support of leading zeroes. If another format is used then it must be indicated by subscriberNumberNAI parameter.", "title": "Subscriber Number", "type": "string", "minLength": 1}, "originOperatorID": {"description": "parameter is the identity of the system user or the session from where the operation was initiated", "title": "Origin Operator ID", "type": "string"}, "transactionCurrency": {"description": "parameter contains an ID to point out what currency is used for the transaction", "title": "Transaction Currency", "type": "string"}, "subDedicatedAccountUpdateInformation": {"description": "contains information for updating balances and expiry date for sub dedicated accounts", "title": "Sub Dedicated Account Update Information", "hint": "ArrayofObjects", "type": "array", "items": {"hint": "grouped", "type": "object", "properties": {"dedicatedAccountID": {"description": "parameter contains the identity of the dedicated account in order to be able to distinguish between the various dedicated accounts in an array of dedicated accounts", "title": "Dedicated Account ID", "type": "string"}, "adjustmentAmountRelative": {"description": "parameter contains the amount of the adjustment(positive or negative) to be applied to the account", "title": "Adjustment Amount Relative", "type": "string"}, "subDedicatedAccountIdentifier": {"description": "contains information for identifying a unique sub dedicated account", "title": "subDedicatedAccountIdentifier", "type": "object", "properties": {"startDateCurrent": {"description": "parameter contains the current start date for a dedicated account", "title": "Start Date Current", "type": "string", "format": "date-time"}, "expiryDateCurrent": {"description": "parameter contains the current expiry date for a dedicated account", "title": "Expiry Date Current", "type": "string", "format": "date-time"}}}, "subDedicatedAccountValueAbsolute": {"description": "parameter contains units to assign to a sub dedicated account", "title": "Sub Dedicated Account Value Absolute", "type": "string"}, "adjustmentDateRelative": {"description": "parameter is used to make a relative adjustment to the current expiry date", "title": "Adjustment Date Relative", "type": "string", "format": "Numeric"}, "startDate": {"description": "parameter contains the start date for a dedicated account", "title": "Start Date", "type": "string", "format": "date-time"}, "expiryDate": {"description": "parameter contains the end date for a dedicated account", "title": "Expiry Date", "type": "string", "format": "date-time"}, "expiryPamPeriodIndicator": {"description": "parameter indicates the Periodic Account Management period when the offer or DA becomes invalid", "title": "Expiry Pam Period Indicator", "type": "string", "format": "Numeric"}, "startPamPeriodIndicator": {"description": "parameter indicates the Periodic Account Management period when the offer and DA becomes valid", "title": "Start Pam Period Indicator", "type": "string", "format": "Numeric"}, "adjustmentStartDateRelative": {"description": "parameter is used to make a relative adjustment to the current start date", "title": "Adjustment Start Date Relative", "type": "string", "format": "Numeric"}, "pamServiceID": {"description": "parameter specifies the id of the periodic account management service", "title": "PAM Service ID", "type": "string"}, "startDateCurrent": {"description": "parameter contains the current start date for a dedicated account", "title": "Start Date Current", "type": "string", "format": "date-time"}, "expiryDateCurrent": {"description": "parameter contains the current expiry date for a dedicated account", "title": "Expiry Date Current", "type": "string", "format": "date-time"}, "dedicatedAccountUnitType": {"description": "parameter contains the unit of the dedicated account values ", "title": "Dedicated Account Unit Type", "enum": ["0", "1", "2", "3", "4", "5", "6"]}}}}, "transactionType": {"description": "parameter is used to specify the operation in more detail", "title": "Transaction Type", "enum": ["EUC", "PIN", "TT", "GSM"]}, "transactionCode": {"description": "parameter is used to specify the operation in more detail ", "title": "Transaction Code", "enum": ["SCC", "FAF", "CBE", "ADJ", "TC", "TV", "REBATE", "DEBIT", "DEDUCTIONGSM", "DEDUCTIONPERIOD"]}, "externalData1": {"description": "These parameters are used as a spare parameter for  customizations to include in data records.", "title": "External Data1", "type": "string"}, "externalData2": {"description": "These parameters are used as a spare parameter for  customizations to include in data records.", "title": "External Data2", "type": "string"}}}}}