/**
 *  ACIP UpdateSubDedicatedAccounts Plugin
 *
 *  <AUTHOR>
 **/
const ACIPPlugin = require("../acipPlugin");

let schema;

class UpdateSubDedicatedAccountsPlugin extends ACIPPlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof UpdateSubDedicatedAccountsPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof UpdateSubDedicatedAccountsPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof UpdateSubDedicatedAccountsPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = UpdateSubDedicatedAccountsPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./UpdateSubDedicatedAccountsSchema.json");
    return schema;
}
