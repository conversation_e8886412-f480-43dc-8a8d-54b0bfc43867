/**
 *  ACIP DeleteUsageThreshold Plugin
 *
 *  <AUTHOR>
 **/
const ACIPPlugin = require("../acipPlugin");

let schema;

class DeleteUsageThresholdPlugin extends ACIPPlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof DeleteUsageThresholdPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof DeleteUsageThresholdPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof DeleteUsageThresholdPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = DeleteUsageThresholdPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./DeleteUsageThresholdSchema.json");
    return schema;
}
