/**
 *  ACIP DeletePeriodicAccountManagementData Plugin
 *
 *  <AUTHOR>
 **/
const ACIPPlugin = require("../acipPlugin");

let schema;

class DeletePeriodicAccountManagementDataPlugin extends ACIPPlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof DeletePeriodicAccountManagementDataPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof DeletePeriodicAccountManagementDataPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof DeletePeriodicAccountManagementDataPlugin
     */
    exec(moduleContext) {
        moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = DeletePeriodicAccountManagementDataPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./DeletePeriodicAccountManagementDataSchema.json");
    return schema;
}
