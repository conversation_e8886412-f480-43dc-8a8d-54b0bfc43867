/**
 *  leap Plugin
 *
 *  <AUTHOR>
 **/
const Ajv = require("ajv");

const error = "error";
const warn = "warn";

const message = require("message");
const error_codes = message.error_codes;
const http = require('http');
const https = require('https');
const url = require('url');
const axios = require('axios');
const fs = require('fs');

const httpAgents = new Map();
const httpsAgents = new Map();

function getAgentForEndpoint(endpointUrl, useHttps, rejectUnauthorized = false) {
    const parsedUrl = url.parse(endpointUrl);
    const key = `${parsedUrl.protocol}//${parsedUrl.host}`; // Protocol + Domain name

    const agentsMap = useHttps ? httpsAgents : httpAgents;
    if (!agentsMap.has(key)) {
        const agentOptions = {
            maxSockets: 100,
            keepAlive: true,
            maxFreeSockets: 10,
            timeout: 15000,
            keepAliveMsecs: 30000,
            rejectUnauthorized: rejectUnauthorized // Added rejectUnauthorized option
        };
        const agent = useHttps ? new https.Agent(agentOptions) : new http.Agent(agentOptions);
        agentsMap.set(key, agent);
    }

    return agentsMap.get(key);
}


class LeapPlugin {

    init() {

    }

    async getMetaDataInfo(moduleData) {

        if (global.logger.isTraceEnabled()) {
            global.logger.trace("Category: %s, getMetainfo():%s ", moduleData.category, JSON.stringify(moduleData));
        }
        if (moduleData.properties != null) {
            if (moduleData.properties.response != null) {
                let response = await deepTree(moduleData.properties.response);
                moduleData.properties.response = {
                    ...moduleData,
                    properties: response
                };
            }
            if (moduleData.properties.output != null) {
                moduleData.properties.output.customCode = "// Custom code panel\n// main function is the default method executed after processing current module\nfunction main(){\n  return \"\";// return end moduleId\n}";
                moduleData.properties.output.codeActive = true;
            }
        }
        return moduleData;
    }

    /**
     * module validate method
     *
     * @param {any} moduleData
     * @param {any} schema
     * @returns validation result object
     * @memberof LeapPlugin
     */
    validate(moduleData, schema) {
        let ajv = new Ajv({
            allErrors: true
        });
        let resObj = {
            "isValid": false,
            "errors": []
        };
        ajv.addFormat("Numeric", {
            type: "integer",
            validate: () => false
        });
        ajv.addFormat("Numeric", {
            type: "String",
            validate: () => false
        });

        let validate = ajv.compile(schema);
        let valid = validate(moduleData);
        // Check is module valid
        if (valid) {
            resObj.isValid = true;
        } else {
            validate.errors.forEach(e => {
                let errorInfo = {
                    "parameter": "",
                    "path": "",
                    "severity": "",
                    "msg": ""
                };

                if (e) {
                    // Check is mandotory paramater missing
                    if (e.keyword === "required") {
                        errorInfo.parameter = e.params.missingProperty;
                        errorInfo.severity = error;
                        errorInfo.path = e.dataPath.slice(1);
                    } else {
                        let arrayOfPath = e.dataPath.split(".");
                        let path = "";
                        for (let i = 1; i < arrayOfPath.length - 1; i++) {
                            path = path + arrayOfPath[i] + ".";
                        }
                        errorInfo.path = path.slice(0, -1);
                        errorInfo.parameter = arrayOfPath[arrayOfPath.length - 1];
                        errorInfo.severity = warn;
                    }
                    errorInfo.msg = e.message;
                    resObj.errors.push(errorInfo);
                }
            });
        }
        return resObj.errors;
    }

    exec() {

    }

    close() {

    }

    async httpCall(jsonInput) {
        try {
            // Validate URL
            if (!jsonInput.URL) {
                throw new Error('URL is required');
            }

            // Check if URL is HTTPS
            const isHttps = jsonInput.URL.startsWith('https://');

            let timeout = jsonInput.timeout || 10000;

            // Configure the request
            const config = {
                method: jsonInput.type || 'GET',
                timeout: timeout,
                url: jsonInput.URL,
                responseType: 'json',
                responseEncoding: jsonInput.encoding || 'utf8',
                httpAgent: isHttps ? null : getAgentForEndpoint(jsonInput.URL, false),
                httpsAgent: isHttps ? getAgentForEndpoint(jsonInput.URL, true, jsonInput.rejectUnauthorized) : null,
                headers: jsonInput.headerBody || {},
                data: jsonInput.requestBody || {},
                // Additional SSL/TLS Options
                ...(jsonInput.certificatePath && {
                    cert: fs.readFileSync(jsonInput.certificatePath),
                }),
                ...(jsonInput.keyPath && {
                    key: fs.readFileSync(jsonInput.keyPath),
                }),
                ...(jsonInput.caPath && {
                    ca: fs.readFileSync(jsonInput.caPath),
                }),
                rejectUnauthorized: jsonInput.hasOwnProperty('rejectUnauthorized')
                    ? jsonInput.rejectUnauthorized
                    : false,
            };

            // Include a proxy if provided
            if (jsonInput.proxy) {
                config.proxy = jsonInput.proxy;
            }

            // Make the request
            const response = await axios(config);
            let responseJson = {
                "data": response.data,
                "code": response.status
            }
            return responseJson;
        } catch (error) {
            if (!(error.response && error.response.status && error.response.data)) {
                //Non HTTP error code occured, hence print entire error object
                global.logger.error(error);
                let responseJson;
                if (error.code == "ESOCKETTIMEDOUT" || error.code == "ETIMEDOUT") {
                    responseJson = {
                        "data": "ESOCKETTIMEDOUT",
                        "code": "896"
                    }
                } else if (error.code == "ECONNRESE" || error.code == "ECONNRESET") {
                    responseJson = {
                        "data": "ECONNRESET",
                        "code": "897"
                    }
                } else if (error.code == "ECONNREFUSE" || error.code == "ECONNREFUSED") {
                    responseJson = {
                        "data": "ECONNREFUSED",
                        "code": "898"
                    }
                } else if (error.code == "ECONNABORTE" || error.code == "ECONNABORTED") {
                    responseJson = {
                        "data": "ECONNABORTED",
                        "code": "899"
                    }
                }
                else {
                    responseJson = {
                        "code": error.code,
                        "data": error.code
                    }
                }
                return responseJson;
            }
            else {
                //HTTP error code has occured
                let responseJson = {
                    "data": error.response.data,
                    "code": error.response.status
                }
                return responseJson;
            }
        }
    }
}

module.exports = LeapPlugin;
const agentpool = {};

async function deepTree(opts) {
    let item = {};
    try {
        if (typeof opts === "string") {
            opts = JSON.parse(opts);
        }

        if (opts.type === "object") {
            const keys = Object.keys(opts.properties);
            for (let i = 0; i < keys.length; i++) {
                const key = keys[i];
                let obj = opts.properties[key];
                if (obj.type === "object") {
                    item[key] = await deepTree(obj);
                } else if (obj.type === "array") {
                    item[key + "[0]"] = Object.assign({}, obj);
                    if (obj.items && obj.items.properties) {
                        delete item[key + "[0]"].items;
                        item[key + "[0]"].properties = await deepTree(obj.items);
                    }
                } else {
                    item[key] = obj;
                }
            }
        }
    } catch (error) {
        //ignore
        global.logger.error(error);
    }
    return item;
}
