const events = require("events");
const PIPE = "|";
class CDRWriter extends events.EventEmitter {
  constructor() {
    super();
    this.registerEvents();
  }

  registerEvents() {
    this.on("EXEC_CDR", this.writeCDR);
    this.on("MOBIQUITY_CDR", (appId, mid, pluginName, resTime, statusCode, reqObj, res) => {
      reqObj.COMMAND && delete reqObj.COMMAND.PIN;
      this.writeCDR(appId, mid, pluginName, resTime, statusCode, reqObj, res);
    });
  }

  writeCDR(appId, mid, pluginName, resTime, statusCode, reqObj, res, sessionId, MSISDN) {
    try {
      if (res == null) res = {};
      if (global.pluginCDR && global.pluginCDR.isInfoEnabled()) {
        global.pluginCDR.error(appId + PIPE + mid + PIPE + pluginName + PIPE + statusCode + PIPE + resTime + PIPE + JSON.stringify(reqObj) + PIPE + JSON.stringify(res) + PIPE + sessionId + PIPE + MSISDN);
      }
    } catch (e) {
      global.logger.error("Expection occured while writing CDRS:", e);
    } finally {
        appId = null,
        mid = null,
        pluginName = null;
        resTime = null;
        statusCode = null;
        reqObj = null;
        res = null;
    }
  }
}

module.exports = new CDRWriter();
