{"typeId": "8.4", "name": "Write CDR", "title": "Write CDR", "category": "fileSystem", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["cdrpath", "filename"], "properties": {"cdrpath": {"description": "CDR Path", "title": "CDR Path", "type": "string", "default": "/data/leap/cdrs"}, "filename": {"description": "File name prefix", "title": "File name prefix", "type": "string", "default": "plugin_cdr"}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["query"], "properties": {"query": {"description": "Pipe Separated Properties", "title": "Pipe Separated Properties", "type": "string"}}}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"responseCode": {"description": "parameter is sent back after a message has been processed and indicates success or failure of the message ", "title": "Response Code", "type": "string"}, "message": {"description": "Response message", "title": "Response Message", "type": "string"}}}, "output": {"description": "The output params", "type": "object"}}}