"use strict";
/**
*  FileSystemPlugin Plugins - Write CDR Plugin
*
*  <AUTHOR>
**/

const FileSystemPlugin = require("../fileSystemPlugin");
const FSR = require("file-stream-rotator");
const path = require("path");
const utility = require("utility");

const filestreams = {};
let schema;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./WriteCDRSchema.json");
    return schema;
}

class WriteCDRPlugin extends FileSystemPlugin {
    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof WriteCDRPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof WriteCDRPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof WriteCDRPlugin
     */
    exec(moduleContext) {
        try {
            if (global.log4js != null && filestreams[moduleContext.mid] == null) {
                let filename = path.join(moduleContext.settings.cdrpath, moduleContext.settings.filename || "plugin_cdr")
                filestreams[moduleContext.mid] = FSR.getStream({
                    filename: filename + "-%DATE%.cdr",
                    frequency: "1h",
                    verbose: false, date_format: "YYYY-MM-DD-HH"
                });
            }
            let str = utility.YYYYMMDDHHmmss(null, "-") + "|" + moduleContext.process.query + "\n";
            filestreams[moduleContext.mid].write(str);
        } catch (error) {
            global.logger.error(error);
        }
        return super.exec(moduleContext);
    }

    close() {

    }
}

module.exports = WriteCDRPlugin;
