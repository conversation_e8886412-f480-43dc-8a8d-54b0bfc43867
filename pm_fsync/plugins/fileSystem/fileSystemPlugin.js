"use strict";
/**
 *  FileSystemPlugin
 *
 *  <AUTHOR>
 **/
const LeapPlugin = require("../LeapPlugin");
const message = require("message");
const error_codes = message.error_codes;

/**
 * @class FileSystemPlugin
 */
class FileSystemPlugin extends LeapPlugin {

    init() {

    }

    getMetaDataInfo(moduleData) {
        moduleData.category = "fileSystem";
        return super.getMetaDataInfo(moduleData);
    }

    /**
     * plugin validate method
     *
     * @param {any} moduleData
     * @param {any} schemaData
     * @returns
     * @memberof channelPlugin
     * @returns module validation result object
     */
    validate(moduleData, schemaData) {
        return [];
    }

    /**
     * exec method
     * @param {any} moduleContext
     * @memberof channelPlugin
     * @returns result object
     */
    exec(moduleContext) {
        return message.getResponseJson(moduleContext.language, error_codes.success);
    }

    close() {

    }
}

module.exports = FileSystemPlugin;
