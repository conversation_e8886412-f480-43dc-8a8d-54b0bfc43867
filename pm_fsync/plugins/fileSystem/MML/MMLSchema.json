{"typeId": "8.3", "name": "MML", "title": "Man-Machine-Language(MML)", "category": "fileSystem", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["host", "port"], "properties": {"host": {"description": "The hostname of the database", "title": "Host", "type": "string", "format": "ipv4"}, "port": {"description": "The port number of the database", "title": "Port", "type": "integer", "minimum": 20, "maximum": 65535}, "plugin_timeout": {"description": "No. of milliseconds before a timeout occurs during the initial connection to the MML server", "title": "Plugin Connection Timeout(ms)", "default": 40000, "type": "integer", "minimum": 1, "maximum": 99999999}, "connection_timeout": {"description": "No. of milliseconds before a timeout occurs during the initial connection to the MML server", "title": "TCP Connection Timeout(ms)", "default": 20000, "type": "integer", "minimum": 1, "maximum": 99999999}, "retry_interval": {"description": "No. of milliseconds before a timeout occurs during the initial connection to the MML server", "title": "Handshake Time Interval(ms)", "default": 2000, "type": "integer", "minimum": 1, "maximum": 99999999}, "response_timeout": {"description": "No. of milliseconds before a timeout occurs during the initial connection to the MML server", "title": "Response TimeOut(ms)", "default": 6000, "type": "integer", "minimum": 1, "maximum": 99999999}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "properties": {"command": {"description": "MML Commands Editor", "title": "MML Commands Editor", "type": "string", "hint": "messageSender", "minLength": 1}}}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"responseCode": {"description": "parameter is sent back after a message has been processed and indicates success or failure of the message ", "title": "Response Code", "type": "string"}, "resultsets": {"description": "Resultsets of Query", "title": "Resultsets of Query", "type": "object", "properties": {"rows": {"title": "result rows", "type": "array"}}}}}, "output": {"description": "The output params", "type": "object"}}}