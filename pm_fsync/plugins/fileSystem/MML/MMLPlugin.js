/**
 *  MML commands MML Plugin
 *
 *  <AUTHOR>
 **/
const net = require('net');
const FileSystemPlugin = require("../fileSystemPlugin");
const message = require("message");
const error_codes = message.error_codes;
const BPromise = require("bluebird");
let client = new net.Socket();
let flag;
let schema;
/**
 * @class MMLPlugin
 */
class MMLPlugin extends FileSystemPlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof MMLPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof MMLPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }
    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof MMLPlugin
     */
    async exec(moduleContext, options) {
        if (options && options.mode === "testConn") {
            return await testConn(moduleContext);
        }
        if (options && options.mode === "testCommand") {
            return await testMode(moduleContext, options);
        }
        return await execMode(moduleContext, options);
    }

    close() {

    }
}



/* loads the MML schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./MMLSchema.json");
    return schema;
}

function testConn(moduleContext) {
    return new BPromise((resolve, reject) => {
        try {
            global.logger.trace("Module context is:" + JSON.stringify(moduleContext));
            if (!moduleContext.settings) {
                return resolve(message.getResponseJson(moduleContext.language, error_codes.settingsNotFound));
            }
            let client = new net.Socket();;
            client.connect(moduleContext.settings.port, moduleContext.settings.host, function () {
                client.end();
                return resolve(message.getResponseJson(moduleContext.language, error_codes.success));
            });

            client.on('error', function (err) {
                return resolve(message.getResponseJson(moduleContext.language, error_codes.connectionFail));
            });
        }
        catch (error) {
            return reject(error);
        }
    });
}

function testMode(moduleContext, options) {
    return new BPromise(async (resolve, reject) => {
        try {
            let result;
            if (!moduleContext.settings) {
                return resolve(message.getResponseJson(moduleContext.language, error_codes.settingsNotFound));
            }

            if (!moduleContext.process.command) {
                return resolve(message.getResponseJson(moduleContext.language, error_codes.commandNotFound));
            }
            client.connect(moduleContext.settings.port, moduleContext.settings.host);
            client.on('error', function (err) {
                return resolve(message.getResponseJson(moduleContext.language, error_codes.connectionRefused));
            });
            client.setKeepAlive(true, moduleContext.settings.retry_interval);
            client.setTimeout(moduleContext.settings.connection_timeout)
            client.on('timeout', function () {
                client.destroy();
            });
            result = await executeMultipleCommand(client, moduleContext, options);
            client.on('close', function () {
                return resolve(result);
            });
            return resolve(result);
        }
        catch (error) {
            return reject(error);
        }
    });
}

function execMode(moduleContext, options) {
    return new BPromise(async resolve => {
        try {
            if (!moduleContext.settings) {
                return resolve(message.getResponseJson(moduleContext.language, error_codes.settingsNotFound));
            }

            if (!moduleContext.process.command) {
                return resolve(message.getResponseJson(moduleContext.language, error_codes.commandNotFound));
            }
            client.connect(moduleContext.settings.port, moduleContext.settings.host);
            client.on('error', function (err) {
                return resolve(message.getResponseJson(moduleContext.language, error_codes.connectionRefused));
            });
            client.setKeepAlive(true, moduleContext.settings.retry_interval);
            client.setTimeout(moduleContext.settings.connection_timeout)
            client.on('timeout', function () {
                client.destroy();
            });
            let result = await executeMultipleCommand(client, moduleContext, options);
            return resolve(result);
        }
        catch (error) {
            global.logger.error(error);
            return resolve(message.getResponseJson(moduleContext.language, error_codes.pluginInternalError));
        }
    });
}

function executeMultipleCommand(client, moduleContext, options) {
    let MMLCommands = moduleContext.process.command;
    return new BPromise((resolve, reject) => {
        try {
            flag = true;
            let finalResult = {
                code: error_codes.success,
                resultsets: {}
            };
            //The MML statements will always ends with ";" while splitting it will give empty Query so removing the last ";"
            if (MMLCommands.substr(MMLCommands.length - 1) === ";") {
                MMLCommands = MMLCommands.slice(0, -1);
            }
            MMLCommands = MMLCommands.replace(/\n/g, "")
            let index = 1;
            //Splitting the multiple MML statements into single and executing
            BPromise.reduce(MMLCommands.split(";"), async (finalResult1, MMLCommand) => {
                let key = "result" + index;
                index++;
                try {
                    if (flag)
                        finalResult.resultsets[key] = await sendRequest(moduleContext, client, MMLCommand);
                    else {
                        finalResult.resultsets[key] = message.getResponseJson(moduleContext.language, error_codes.serverError);
                    }
                } catch (error) {
                    finalResult.resultsets[key] = message.getResponseJson(moduleContext.language, error_codes.serverError);
                }
                return finalResult.resultsets[key];
            }, finalResult)
                .then((finalResult1) => {
                    return resolve(finalResult);
                });
        } catch (error) {
            reject(error);
        }
    });
}

function sendRequest(moduleContext, client, MMLCommand) {
    return new Promise((resolve, reject) => {
        let res = true;
        try {
            client.write(MMLCommand);
            //MML response timeout defintion
            setTimeout(function () {
                res = false;
            }, moduleContext.settings.response_timeout)
            let response;
            //MML response
            client.on('data', (data) => {
                if (res == false) {
                    resolve(message.getResponseJson(moduleContext.language, error_codes.noResponse))

                }
                else if (MMLCommand == 'LGO') {
                    response = data.toString();
                    client.destroy();
                }
                else {
                    resolve({
                        code: 0,
                        result: data.toString()
                    });
                }
            });
            client.on('close', function () {
                flag = false;
                resolve({
                    code: 0,
                    result: response
                });
            });
        }
        catch (error) {
            reject(error)
        }
    });
}
module.exports = MMLPlugin;
