"use strict";
/**
 *  App Module appEnd Plugin
 *
 *  <AUTHOR>
 **/
const AppModulePlugin = require("../appModulePlugin");

let schema;

class AppEndPlugin extends AppModulePlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof appEndPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof appEndPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof appEndPlugin
     */
    exec(moduleContext) {
        return super.exec(moduleContext);
    }

    close() {

    }
}

module.exports = AppEndPlugin;

/* loads the app end schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./appEndSchema.json");
    return schema;
}
