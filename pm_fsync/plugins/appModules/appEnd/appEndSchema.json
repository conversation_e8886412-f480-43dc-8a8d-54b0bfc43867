{"typeId": "0.2", "name": "appEnd", "title": "App End", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object"}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "properties": {"success": {"description": "Success response to the subscriber", "title": "Success Message", "code": "Code", "message": "Message", "type": "object", "properties": {"code": {"type": ["string", "array"], "minItems": 1, "minLength": 1, "default": ["0", "200-226", "S9000"]}, "message": {"type": "string", "minLength": 1, "default": "Dear Customer, Your request has been successfully processed"}}}, "customErrors": {"description": "Developer can define subscriber messages for each internal error codes", "title": "Custom Errors", "code": "Code", "message": "Message", "type": "array", "additionalItems": true, "items": {"hint": "grouped", "type": "object", "required": ["code", "message"], "properties": {"code": {"type": ["string", "array"], "minItems": 0}, "message": {"type": "string"}}}, "default": []}, "defaultError": {"description": "De<PERSON><PERSON> reponse to the subscriber", "title": "<PERSON><PERSON><PERSON>", "type": "object", "code": "Code", "message": "Message", "properties": {"code": {"title": "Code", "type": "string", "default": "E9000"}, "message": {"title": "Message", "type": "string", "minLength": 1, "default": "Dear Customer, Your request cannot be processed now. Please try again later."}}}, "staticMessage": {"description": "End of Session Static Response", "title": "End of Session USSD Flash Notification", "type": "object", "code": "Code", "message": "Message", "properties": {"code": {"title": "Enable USSD Flash Notification", "type": "boolean", "default": false}, "message": {"title": "Message", "type": "string", "minLength": 1, "default": "Thanks for using LEAP-USSD Services"}}}, "systemBusyMessage": {"description": "System busy Response", "title": "System busy Response", "type": "object", "code": "Code", "message": "Message", "properties": {"code": {"title": "Code", "type": "string", "default": "E898"}, "message": {"title": "Message", "type": "string", "minLength": 1, "default": "System is busy, Please try again later."}}}}}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"responseCode": {"description": "parameter is sent back after a message has been processed and indicates success or failure of the message ", "title": "Response Code", "type": "string"}}}, "output": {"description": "The output params", "type": "object"}}}