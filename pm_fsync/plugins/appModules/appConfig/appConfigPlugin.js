"use strict";
/**
 *  App Module appConfigPlugin
 *
 *  <AUTHOR>
 **/
const AppModulePlugin = require("../appModulePlugin");

let schema;

/**
 * @class appConfigPlugin
 */
class AppConfigPlugin extends AppModulePlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof appConfigPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof appConfigPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof appConfigPlugin
     */
    exec(moduleContext) {
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = AppConfigPlugin;

/* loads the app config schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./appConfigSchema.json");
    return schema;
}
