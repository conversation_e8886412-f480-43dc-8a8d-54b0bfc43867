{"typeId": "0.1", "name": "appStart", "title": "App Start", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["method", "aparty"], "oneOf": [{"properties": {"method": {"enum": ["POST"]}}, "required": ["contentType", "samplePayload"]}, {"properties": {"method": {"enum": ["GET"]}}, "required": ["contentType"]}], "properties": {"auth_required": {"description": "Basic Authentication", "title": "Basic Authentication", "type": "boolean", "hint": "checkBox", "default": true}, "username": {"description": "Username", "title": "Username", "type": "string", "default": "leap"}, "password": {"description": "Password", "title": "Password", "type": "string", "hint": "password", "default": "leap123"}, "immediateResponseFlag": {"description": "Immdediate Response Required", "title": "Immdediate Response Required", "type": "boolean", "hint": "checkBox", "default": false}, "method": {"description": "HTTP Request Method", "title": "HTTP Request Method", "default": "GET", "hint": "radio", "enum": ["GET", "POST"]}, "samplePayload": {"description": "Request payload", "title": "Sample payload", "hint": "textArea", "type": "string", "default": "{\"MSISDN\": \"919876543210\",\n\"subscriberInput\": \"919876543210\",\"query\": {\"subscriberType\": 1,\"contentType\": \"text\"}"}, "contentType": {"description": "Content Type to send in request header Of the App Module", "title": "Content-Type", "default": "application/json", "enum": ["application/json"]}, "aparty": {"description": "A Party Query String Parameter", "title": "A Party Query String Param", "type": "string", "default": "MSISDN"}, "params": {"description": "Array of output params", "title": "App Request params", "type": "array", "default": ["MSISDN"]}, "optional": {"description": "Array of output params", "title": "Declare optional parameters", "type": "array", "default": []}, "freeflow": {"description": "App Response header", "title": "Response Header-Freeflow", "type": "string", "default": "Freeflow"}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object"}, "output": {"description": "The output params", "type": "object"}}}