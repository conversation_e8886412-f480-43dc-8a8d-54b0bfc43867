"use strict";
/**
 *  App Module appStart Plugin
 *
 *  <AUTHOR>
 **/
const AppModulePlugin = require("../appModulePlugin");

let schema;
const message = require("message");
const error_codes = message.error_codes;
const CDRWriter = require("../../pluginCDRWriter");
const logger = global.logger || require("log4js").getLogger();
const NS_PER_SEC = 1e9;
/**
 * @class appStartPlugin
 */
class AppStartPlugin extends AppModulePlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof appStartPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof appStartPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof appStartPlugin
     */
    exec(moduleContext) {
        let throttleValue = global.peThrottleValue || -1;
       let diagnostics = {
    };
        let startTime = process.hrtime(), diff;

        if (global.logger.isTraceEnabled()) {
            global.logger.trace("Throttle Check::: throttleValue:" + throttleValue + ", pecounter:" + global.pecounter);
        }
        if (throttleValue > 0 && global.pecounter > throttleValue) {
            global.logger.error("Returing the error response as exceed throttle limit: throttle value " + throttleValue + " current request counter:" + global.pecounter);
            return message.getResponseJson(moduleContext.language, error_codes.pluginExecBusy);
        } else {
       diff = process.hrtime(startTime);
       diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
      CDRWriter.emit("EXEC_CDR",moduleContext.appId, moduleContext.mid,moduleContext.pluginName, error_codes.success,diagnostics.pluginResponse,throttleValue );
            return super.exec(moduleContext);
        }
    }

    close() {

    }
}
module.exports = AppStartPlugin;

/* loads the app start schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./appStartSchema.json");
    return schema;
}
