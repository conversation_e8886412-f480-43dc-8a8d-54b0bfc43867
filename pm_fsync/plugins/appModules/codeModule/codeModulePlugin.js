"use strict";
/**
 *  App Modules Module codeModulePlugin
 *
 *  <AUTHOR>
 **/
const AppModulePlugin = require("../appModulePlugin");
const vm = require("vm");
const app_macros = require("app_macros");
const request = require("request");
const util = require("util");
const bPromise = require("bluebird");

let schema;

/**
 * @class codeModulePlugin
 */
class CodeModulePlugin extends AppModulePlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof codeModulePlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof codeModulePlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof codeModulePlugin
     */
    exec(moduleContext) {
        try {
            let sandbox = Object.assign({
                console: console,
                logger: global.logger,
                request: require("request"),
                util: require("util"),
                bPromise: require("bluebird")
            }, moduleContext.process.dataTable);
            delete sandbox.query;
            sandbox = Object.assign(sandbox, app_macros.definitions);
            sandbox.timeOut = function (a, b) {
                if (typeof (a) == 'string') {
                    let fn = function () { return vm.createScript(a, '').runInContext(sandbox) };
                    return setTimeout(fn, b);
                } else {
                    setTimeout(a, b);
                }
            };
            const context = vm.createContext(sandbox);
            vm.runInNewContext(moduleContext.process.query + "\n main();", context, {
                displayErrors: true,
                breakOnSigint: true
            });
            Object.keys(sandbox)
                .forEach(key => {
                    switch (key) {
                        case "console":
                        case "main":
                        case "logger":
                        case "bPromise":
                        case "util":
                        case "request":
                        case "setTimeOut":
                            delete sandbox[key];
                            break;
                        default:
                            if (key.startsWith("_")) {
                                delete sandbox[key];
                            } else {
                                console.log(key);
                            }
                    }
                });
            return { code: 0, msg: sandbox };
        } catch (e) {
            console.error(e);
            return { code: 809, msg: e.toString() };
        }
    }

    close() {

    }
}
module.exports = CodeModulePlugin;

/* loads the codeModulePlugin schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./codeModuleSchema.json");
    return schema;
}
