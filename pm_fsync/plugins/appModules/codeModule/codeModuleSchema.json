{"typeId": "0.5", "name": "codeModule", "title": "Code Module", "type": "object", "required": ["name", "coordinates", "settings", "process"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "properties": {}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "properties": {"query": {"description": "JavaScript Editor", "title": "JavaScript Editor", "type": "string", "hint": "javascriptEditor", "minLength": 1, "default": "// JavaScript Code module to develop custom plugin by using nodejs libraries u can require util, request, http, https etc\n// main function is the default method that is executed first\nfunction main(){\n\n}"}}}}}