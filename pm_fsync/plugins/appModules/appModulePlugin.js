"use strict";
/**
 *  AppModule Plugins
 *
 *  <AUTHOR>
 **/
const LeapPlugin = require("../LeapPlugin");
const message = require("message");
const error_codes = message.error_codes;

/**
 * @class appModulePlugin
 */
class AppModulePlugin extends LeapPlugin {

    init() {

    }

    getMetaDataInfo(moduleData) {
        moduleData.category = "appmodules";
        return super.getMetaDataInfo(moduleData);
    }

    /**
     * plugin validate method
     *
     * @param {any} moduleData
     * @param {any} schemaData
     * @returns
     * @memberof appModulePlugin
     * @returns module validation result object
     */
    validate(moduleData, schemaData) {
        return super.validate(moduleData, schemaData);
    }

    /**
     * exec method
     * @param {any} moduleContext
     * @memberof appModulePlugin
     * @returns result object
     */
    exec(moduleContext) {
        return message.getResponseJson(moduleContext.language, error_codes.success);
    }

    close() {

    }
}

module.exports = AppModulePlugin;