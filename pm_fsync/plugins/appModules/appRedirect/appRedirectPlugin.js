"use strict";
/**
 *  App Redirect Module appRedirectPlugin
 *
 *  <AUTHOR>
 **/
const AppModulePlugin = require("../appModulePlugin");

let schema;

/**
 * @class appRedirectPlugin
 */
class AppRedirectPlugin extends AppModulePlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof appRedirectPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof appRedirectPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof appRedirectPlugin
     */
    exec(moduleContext) {
        return super.exec(moduleContext);
    }

    close() {

    }
}
module.exports = AppRedirectPlugin;

/* loads the appRedirectPlugin schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./appRedirectSchema.json");
    return schema;
}
