"use strict";
/**
 *  App Menu Module Plugin
 *
 *  <AUTHOR>
 **/
const AppModulePlugin = require("../appModulePlugin");

let schema;

/**
 * @class appMenuPlugin
 */
class AppMenuPlugin extends AppModulePlugin {

    init() {
    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof appMenuPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof appMenuPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} moduleContext
     * @returns
     * @memberof appMenuPlugin
     */
    exec(moduleContext) {
        return super.exec(moduleContext);
    }

    close() {

    }
}

module.exports = AppMenuPlugin;

/* loads the app start schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./appMenuSchema.json");
    return schema;
}
