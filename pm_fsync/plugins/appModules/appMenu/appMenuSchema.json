{"typeId": "0.3", "name": "appMenu", "title": "<PERSON><PERSON>", "description": "Menu design module", "type": "object", "required": ["name", "coordinates", "settings", "process", "output", "menuNavigationOptions"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "menuNavigationOptions": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["mainMenu", "backwardSkipMenu", "firstPage", "lastPage", "previousPage", "nextPage"], "properties": {"mainMenu": {"required": ["code", "message"], "properties": {"code": {"description": "Root/Main Menu user action option key to be displayed in handset", "title": "0", "type": "string", "default": "0", "minLength": 1, "maxLength": 1}, "message": {"description": "Root/Main Menu 'message' to be displayed in handset", "title": "Main Menu", "type": "string", "default": "Main Menu", "minLength": 1}}}, "backwardSkipMenu": {"required": ["code", "message"], "properties": {"code": {"description": "Backward skip option, user action key to skip a page and display one level page", "title": "s", "type": "string", "default": "s", "minLength": 1, "maxLength": 1}, "message": {"description": "Backward skip message, user action key to skip a page and display one level page", "title": "Backward skip", "type": "string", "default": "Backward skip", "minLength": 1}}}, "firstPage": {"required": ["code", "message"], "properties": {"code": {"description": "First page option key, user action key to display first page", "title": "f", "type": "string", "default": "f", "minLength": 1, "maxLength": 1}, "message": {"description": "First page option message, user action key to display first page", "title": "First page", "type": "string", "default": "First page", "minLength": 1}}}, "lastPage": {"required": ["code", "message"], "properties": {"code": {"description": "Last page option key, user action key to display last page of the level", "title": "l", "type": "string", "default": "l", "minLength": 1, "maxLength": 1}, "message": {"description": "Last page option message, user action key to display last page of the level", "title": "Last page", "type": "string", "default": "Last page", "minLength": 1}}}, "previousPage": {"required": ["code", "message"], "properties": {"code": {"description": "Previous page option key, user action key to display previous page", "title": "b", "type": "string", "default": "b", "minLength": 1, "maxLength": 1}, "message": {"description": "Previous page option message, user action key to display previous page", "title": "Prev page", "type": "string", "default": "Prev page", "minLength": 1}}}, "nextPage": {"required": ["code", "message"], "properties": {"code": {"description": "Next page option key, user action key to display next page", "title": "*", "type": "string", "default": "*", "minLength": 1, "maxLength": 1}, "message": {"description": "Next page option message, user action key to display next page", "title": "Next page", "type": "string", "default": "Next page", "minLength": 1}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "properties": {"menuNavigationEnable": {"description": "Menu Navigation Enable", "title": "Menu Navigation Enable", "type": "boolean", "hint": "checkBox", "default": false}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["menu"], "properties": {"menu": {"description": "App Menu", "title": "USSD Menu", "type": "object", "required": ["children"]}}}, "output": {"description": "The output params", "type": "object"}}}