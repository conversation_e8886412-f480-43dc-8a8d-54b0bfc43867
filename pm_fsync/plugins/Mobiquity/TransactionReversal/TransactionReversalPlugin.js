/**
 *  Mobiquity TransactionReversal Plugin
 *
 *  <AUTHOR>
 **/
const MobiquityPlugin = require("../MobiquityPlugin");

let schema;

class TransactionReversalPlugin extends MobiquityPlugin {

  init() {

  }

  /**
   * method getMetaDataInfo
   * @returns
   * @memberof TransactionReversalPlugin
   */
  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  /**
   * method validate
   * @param {any} module
   * @returns
   * @memberof TransactionReversalPlugin
   */
  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  /**
   * method exec
   * @param {any} moduleContext
   * @returns
   * @memberof TransactionReversalPlugin
   */
  exec(moduleContext) {
    moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
    return super.exec(moduleContext);
  }

  close() {

  }
}
module.exports = TransactionReversalPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./TransactionReversalSchema.json");
  return schema;
}
