/**
 *  Mobiquity MerchantPaymentWithPin Plugin
 *
 *  <AUTHOR>
 **/
const MobiquityPlugin = require("../MobiquityPlugin");

let schema;

class MerchantPaymentWithPinPlugin extends MobiquityPlugin {

  init() {

  }

  /**
   * method getMetaDataInfo
   * @returns
   * @memberof MerchantPaymentWithPinPlugin
   */
  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  /**
   * method validate
   * @param {any} module
   * @returns
   * @memberof MerchantPaymentWithPinPlugin
   */
  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  /**
   * method exec
   * @param {any} moduleContext
   * @returns
   * @memberof MerchantPaymentWithPinPlugin
   */
  exec(moduleContext) {
    moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
    return super.exec(moduleContext);
  }

  close() {

  }
}
module.exports = MerchantPaymentWithPinPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./MerchantPaymentWithPinSchema.json");
  return schema;
}
