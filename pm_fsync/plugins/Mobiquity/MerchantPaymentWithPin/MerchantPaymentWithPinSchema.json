{"typeId": "7.1", "name": "MerchantPaymentWithPin", "title": "Merchant Payment With Pin", "description": "The API can be used by 3rd party to initiate merchant payment where the AM Customer is entering the AM PIN on 3rd party portal", "properties": {"settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["method", "url"], "properties": {"method": {"description": "HTTP Request Method", "title": "HTTP Request Method", "enum": ["GET", "POST"]}, "url": {"description": "URL of the connecting server", "title": "URL", "type": "string", "minLength": 1}, "connectionTimeOut": {"description": "Time out to connecting server", "title": "Connection Time Out", "default": 3000, "type": "integer"}, "timeout": {"description": "The time interval defines the Request timeout in millis", "title": "Request Timeout in ms", "default": 10000, "type": "integer"}}}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["COMMAND"], "properties": {"COMMAND": {"description": "Setting properties Of the Module", "title": "COMMAND", "type": "object", "required": ["TYPE", "MSISDN", "MSISDN2", "INTERFACEID", "AMOUNT", "PIN", "EXTTRID", "REFERENCE"], "properties": {"TYPE": {"description": "Service Type Constant Value", "title": "TYPE", "type": "string", "minLength": 1}, "MSISDN": {"description": "Payer MSISDN without Country Code.", "title": "Payer MSISDN", "type": "string", "minLength": 1}, "MSISDN2": {"description": "Payee MSISDN without Country Code.", "title": "Payee MSISDN", "type": "string", "minLength": 1}, "INTERFACEID": {"description": "It is constant value and use to identify the third party interface.", "title": "INTERFACEID", "type": "string", "minLength": 1}, "AMOUNT": {"description": "Amount to credit/debit without currency and format. Plain digit like 1000", "title": "AMOUNT", "type": "string", "minLength": 1}, "PIN": {"description": "Airtel Money password(mPIN) of payer", "title": "PIN", "type": "string", "minLength": 1}, "EXTTRID": {"description": "External Transaction reference number.", "title": "EXTTRID", "type": "string", "minLength": 1}, "REFERENCE": {"description": "Reference for service/goods purchased", "title": "REFERENCE", "type": "string", "minLength": 1}, "USERNAME": {"description": "User name", "title": "USERNAME", "type": "string", "minLength": 1}, "PASSWORD": {"description": "Password", "title": "PASSWORD", "type": "string", "minLength": 1}}}}}}}