"use strict";
/**
 *  Mobiquity Plugins
 *
 *  <AUTHOR>
 **/
const mobiquity = require("../mobiquityImplClass");
const CommonProperties = require("./MobiquityCommonProperties.json");

/**
 * @class MobiquityPlugin
 */
class MobiquityPlugin extends mobiquity {

  init() {

  }

  getMetaDataInfo(contextData) {
    contextData = setProperties(contextData);
    return super.getMetaDataInfo(contextData);
  }

  /**
   * plugin validate method
   *
   * @param {any} contextData
   * @param {any} schemaData
   * @returns
   * @memberof MobiquityPlugin
   * @returns module validation result object
   */
  validate(contextData, schemaData) {
    schemaData = setProperties(schemaData);
    return super.validate(contextData, schemaData);
  }

  /**
   * exec method
   * @param {any} contextData
   * @memberof MobiquityPlugin
   * @returns result object
   */
  exec(contextData) {
    return super.exec(contextData);
  }

  prepare(schema, moduleContext) {
    moduleContext.process = super.prepare(schema, moduleContext.process);
    return moduleContext;
  }

  close() {

  }
}

module.exports = MobiquityPlugin;

function setProperties(contextData) {
  contextData.category = "Mobiquity";
  contextData.version = "v3.0";
  contextData.type = "object";
  contextData.required = [
    "name",
    "coordinates",
    "settings",
    "process",
    "output"
  ];
  Object.assign(contextData.properties, CommonProperties);
  return contextData;
}