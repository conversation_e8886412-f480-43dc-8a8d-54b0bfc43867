{"typeId": "7.0", "name": "TransactionEnquiry", "title": "Transaction Enquiry", "description": "The API Mobiquity shall reverse the transaction amount and charges", "properties": {"settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["method", "url"], "properties": {"method": {"description": "HTTP Request Method", "title": "HTTP Request Method", "enum": ["GET", "POST"]}, "url": {"description": "URL of the connecting server", "title": "URL", "type": "string", "minLength": 1}, "connectionTimeOut": {"description": "Time out to connecting server", "title": "Connection Time Out", "default": 3000, "type": "integer"}, "timeout": {"description": "The time interval defines the Request timeout in millis", "title": "Request Timeout in ms", "default": 10000, "type": "integer"}}}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["COMMAND"], "properties": {"COMMAND": {"description": "Setting properties Of the Module", "title": "COMMAND", "type": "object", "required": ["TYPE", "INTERFACEID", "TXNID"], "properties": {"TYPE": {"description": "Service Type Constant Value", "title": "TYPE", "type": "string", "minLength": 1}, "INTERFACEID": {"description": "It is constant value and use to identify the third party interface.", "title": "INTERFACEID", "type": "string", "minLength": 1}, "EXTTRID": {"description": "External transaction reference number", "title": "EXTTRID", "type": "string"}, "USERID": {"description": "External transaction reference number", "title": "USERID", "type": "string"}}}}}}}