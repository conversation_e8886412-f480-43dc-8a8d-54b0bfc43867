/**
 *  Mobiquity TransactionEnquiry Plugin
 *
 *  <AUTHOR>
 **/
const MobiquityPlugin = require("../MobiquityPlugin");

let schema;

class TransactionEnquiryPlugin extends MobiquityPlugin {

  init() {

  }

  /**
   * method getMetaDataInfo
   * @returns
   * @memberof TransactionEnquiryPlugin
   */
  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  /**
   * method validate
   * @param {any} module
   * @returns
   * @memberof TransactionEnquiryPlugin
   */
  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  /**
   * method exec
   * @param {any} moduleContext
   * @returns
   * @memberof TransactionEnquiryPlugin
   */
  exec(moduleContext) {
    moduleContext = super.prepare(loadSchemaFile().properties.process, moduleContext);
    return super.exec(moduleContext);
  }

  close() {

  }
}
module.exports = TransactionEnquiryPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./TransactionEnquirySchema.json");
  return schema;
}
