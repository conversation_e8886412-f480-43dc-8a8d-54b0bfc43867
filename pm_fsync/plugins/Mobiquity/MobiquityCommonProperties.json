{"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"COMMAND": {"description": "The COMMAND is enclosed in a <struct> of its own.", "title": "COMMAND", "hint": "grouped", "type": "object", "properties": {"TYPE": {"description": "Service Type Constant Value", "title": "TYPE", "type": "string"}, "TXNID": {"description": "Mobiquity Transaction ID", "title": "TXNID", "type": "string"}, "EXTTRID": {"description": "External transaction reference number", "title": "EXTTRID", "type": "string"}, "TXNSTATUS": {"description": "Transaction status if 200 success else fail", "title": "TXNSTATUS", "type": "string"}, "MESSAGE": {"description": "Description message for the transaction status", "title": "MESSAGE", "type": "string"}}}}}, "output": {"description": "The output params", "type": "object"}}