"use strict";
/**
 *  OpenAI Plugin
 *
 *  <AUTHOR>
 **/
const ChannelModulePlugin = require("../channelModulePlugin");
const message = require("message");
const { Configuration, OpenAIApi } = require("openai");
const CDRWriter = require("../../pluginCDRWriter");
const error_codes = message.error_codes;

let schema;

/**
 * @class OpenAIPlugin
 */
class OpenAIPlugin extends ChannelModulePlugin {

    init() {
        // Initialization logic (if needed)
    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof OpenAIPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof OpenAIPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} context
     * @returns
     * @memberof OpenAIPlugin
     */
    exec(context) {
        return new Promise((resolve) => {
            try {
                const configuration = new Configuration({
                    apiKey: context.settings.api_key
                });
                const openai = new OpenAIApi(configuration);

                // Making the asynchronous call
                openai.createCompletion({
                    model: "text-davinci-003",
                    prompt: context.process.prompt,
                    max_tokens: 1000
                }).then(completion => {
                    let answer = completion.data.choices[0].text;
                    resolve({
                        code: error_codes.success,
                        msg: {
                            "answer": answer
                        }
                    });
                    CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, answer, "200", context.process.prompt, "-", context.query.txnId, context.userId);
                }).catch(error => {
                    global.logger.error(error);
                    resolve({
                        code: error_codes.pluginInternalError,
                        msg: error
                    });
                    CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, "-", error_codes.pluginInternalError, context.process.prompt, "-", context.query.txnId, context.userId);
                });
            } catch (error) {
                global.logger.error(error);
                resolve({
                    code: error_codes.pluginInternalError,
                    msg: error
                });
                CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, "-", error_codes.pluginInternalError, context.process.prompt, "-", context.query.txnId, context.userId);
            }
        });
    }

    close() {
        // Cleanup logic (if needed)
    }
}
module.exports = OpenAIPlugin;

/* loads the app start schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./OpenAISchema.json");
    schema.category = "OpenAI";
    return schema;
}
