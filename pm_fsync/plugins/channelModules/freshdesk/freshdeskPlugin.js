"use strict";
/**
 *  Freshdesk Plugin
 *
 *  <AUTHOR>
 **/
const ChannelModulePlugin = require("../channelModulePlugin");
const message = require("message");
const error_codes = message.error_codes;
const CDRWriter = require("../../pluginCDRWriter");
const axios = require('axios');
const FormData = require('form-data');

const NS_PER_SEC = 1e9;

let schema;

/**
 * @class FreshdeskPlugin
 */
class FreshdeskPlugin extends ChannelModulePlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof FreshdeskPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof FreshdeskPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} context
     * @returns
     * @memberof FreshdeskPlugin
     */

    exec(context) {
        return new Promise((resolve) => {
            try {
                const url = 'https://' + context.settings.domain + '/api/v2/tickets';

                const formData = new FormData();
                formData.append('status', context.process.status);
                formData.append('priority', context.process.priority);
                formData.append('email', context.process.email);
                formData.append('subject', context.process.subject);
                formData.append('description', context.process.description);

                const requestObj = {
                    'domain': context.settings.domain,
                    'api_key': 'xxxx',
                    'status': context.process.status,
                    'priority': context.process.priority,
                    'email': context.process.email,
                    'subject': context.process.subject,
                    'description': context.process.description
                };

                const config = {
                    auth: {
                        username: context.settings.api_key,
                        password: "X" //NOSONAR : This is just a sample value
                    },
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    },
                    timeout: 10000
                };

                const startTime = Date.now();

                // Call axios without async/await
                axios.post(url, formData, config)
                    .then((response) => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, "200", requestObj, response.data, context.query.txnId, context.userId);
                        resolve({
                            code: error_codes.success,
                            msg: response.data
                        });
                    })
                    .catch((error) => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        if (!(error.response && error.response.status && error.response.data)) {
                            // Non HTTP error code occurred
                            CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, "800", requestObj, error.message, context.query.txnId, context.userId);
                            resolve({
                                code: error_codes.pluginInternalError,
                                msg: error.message
                            });
                        } else {
                            // HTTP error code has occurred
                            CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, error.response.status, requestObj, error.response.data, context.query.txnId, context.userId);
                            resolve({
                                code: error.response.status,
                                msg: error.response.data
                            });
                        }
                    });
            } catch (error) {
                global.logger.error(error);
                resolve({
                    code: error_codes.pluginInternalError,
                    msg: error
                });
                CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, '-', error_codes.pluginInternalError, '-', '-', context.query.txnId, context.userId);
            }
        });
    }


    close() {

    }
}
module.exports = FreshdeskPlugin;

/* loads the app start schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./freshdeskSchema.json");
    schema.category = "freshdesk";
    return schema;
}
