"use strict";
/**
 *  Shopify Plugin
 *
 *  <AUTHOR>
 **/
const ChannelModulePlugin = require("../channelModulePlugin");
const message = require("message");
const error_codes = message.error_codes;
const CDRWriter = require("../../pluginCDRWriter");
const axios = require('axios');

let schema;

/**
 * @class ShopifyPlugin
 */
class ShopifyPlugin extends ChannelModulePlugin {

    init() {
        // Initialization logic (if needed)
    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof ShopifyPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof ShopifyPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} context
     * @returns
     * @memberof ShopifyPlugin
     */
    exec(context) {
        return new Promise((resolve) => {
            try {
                const date = new Date();
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const yearMonth = `${year}-${month}`;
                const url = 'https://' + context.settings.domain + '/admin/api/' + yearMonth + '/orders.json';

                let requestObj = {
                    "order": {
                        "line_items": [
                            {
                                "title": context.process.title,
                                "price": parseFloat(context.process.price),
                                "quantity": 1,
                                "tax_lines": [
                                    {
                                        "price": parseFloat(context.process.tax),
                                        "rate": parseFloat(context.process.rate_of_tax),
                                        "title": "State tax"
                                    }
                                ]
                            }
                        ],
                        "transactions": [
                            {
                                "kind": "sale",
                                "status": "success",
                                "amount": parseFloat(context.process.price) + parseFloat(context.process.tax)
                            }
                        ],
                        "total_tax": parseFloat(context.process.tax),
                        "currency": context.process.currency
                    }
                };

                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Shopify-Access-Token': context.settings.access_token
                    }
                };

                const startTime = Date.now();

                // Making the HTTP call
                axios.post(url, requestObj, config)
                    .then(response => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, "200", requestObj, response.data, context.query.txnId, context.userId);
                        resolve({
                            code: error_codes.success,
                            msg: response.data
                        });
                    })
                    .catch(error => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, error.response.status, requestObj, error.response.data, context.query.txnId, context.userId);
                        resolve({
                            code: error.response.status,
                            msg: error.response.data
                        });
                    });
            } catch (error) {
                global.logger.error(error);
                resolve({
                    code: error_codes.pluginInternalError,
                    msg: error
                });
                CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, '-', error_codes.pluginInternalError, '-', context.query.txnId, context.userId);
            }
        });
    }

    close() {
        // Cleanup logic (if needed)
    }
}
module.exports = ShopifyPlugin;

/* loads the app start schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./shopifySchema.json");
    schema.category = "shopify";
    return schema;
}
