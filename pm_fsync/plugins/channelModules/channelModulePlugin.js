"use strict";
/**
 *  ChannelModule Plugins
 *
 *  <AUTHOR>
 **/
const LeapPlugin = require("../LeapPlugin");
const message = require("message");
const error_codes = message.error_codes;

/**
 * @class channelPlugin
 */
class ChannelPlugin extends LeapPlugin {

    init() {

    }

    getMetaDataInfo(moduleData) {
        return super.getMetaDataInfo(moduleData);
    }

    /**
     * plugin validate method
     *
     * @param {any} moduleData
     * @param {any} schemaData
     * @returns
     * @memberof channelPlugin
     * @returns module validation result object
     */
    validate(moduleData, schemaData) {
        return super.validate(moduleData, schemaData);
    }

    /**
     * exec method
     * @param {any} moduleContext
     * @memberof channelPlugin
     * @returns result object
     */
    exec(moduleContext) {
        let msg = message.getResponseJson(moduleContext.language, error_codes.success);
        msg.error = error_codes.success;
        return msg;
    }

    close() {

    }

    httpCall(req) {
        return super.httpCall(req);
    }
}

module.exports = ChannelPlugin;