"use strict";
/**
 *  voice Plugin
 *
 *  <AUTHOR>
 **/
const ChannelModulePlugin = require("../channelModulePlugin");
const message = require("message");
const error_codes = message.error_codes;
const CDRWriter = require("../../pluginCDRWriter");
const axios = require('axios');

let schema;

/**
 * @class voicePlugin
 */
class voicePlugin extends ChannelModulePlugin {

    init() {
        // Initialization logic (if needed)
    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof voicePlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof voicePlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} context
     * @returns
     * @memberof voicePlugin
     */
    exec(context) {
        return new Promise((resolve) => {
            try {
                const startTime = Date.now();
                const url = global.config.pm_fsync.pluginData.ngageBaseUrl + global.config.pm_fsync.pluginData.nodeData.voice.route;

                let requestObj = {
                    "interfaceType": 0,
                    "aud_src": "TTS",
                    "name": "Campaign",
                    "category": "PROMO",
                    "scheduleType": "onetime",
                    "callerId": context.process.callerId,
                    "description": "CMP",
                    "country": "IN",
                    "campaignType": "static",
                    "recipients": context.process.recipients,
                    "expDuration": "1h",
                    "retrial": 1,
                    "configureRetry": context.process.configureRetry,
                    "staticAudio": context.process.staticAudio,
                    "interactiveIvr": context.process.interactiveIvr,
                    "drCallback": global.config.pm_fsync.pluginData.ngageBaseUrl + "/app_engine/production/" + context.appId
                };

                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': context.settings.authorization
                    },
                    timeout: 10000
                };

                let jsonRequest = {
                    "URL": url,
                    "type": "POST",
                    "timeout": 10000,
                    "encoding": "utf8",
                    "headerBody": config.headers,
                    "requestBody": requestObj,
                    "rejectUnauthorized": false
                };

                super.httpCall(jsonRequest)
                    .then(response => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, "200", requestObj, response.data, context.query.txnId, context.userId);
                        resolve({
                            code: error_codes.success,
                            msg: response.data
                        });
                    })
                    .catch(error => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        if (!(error.response && error.response.status && error.response.data)) {
                            // Non HTTP error code occurred, hence print entire error object
                            CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, "800", requestObj, error.message, context.query.txnId, context.userId);
                            resolve({
                                code: error_codes.pluginInternalError,
                                msg: error
                            });
                        } else {
                            // HTTP error code occurred
                            CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, error.response.status, requestObj, error.response.data, context.query.txnId, context.userId);
                            resolve({
                                code: error.response.status,
                                msg: error.response.data
                            });
                        }
                    });
            } catch (error) {
                global.logger.error(error);
                resolve({
                    code: error_codes.pluginInternalError,
                    msg: error
                });
                CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, '-', error_codes.pluginInternalError, '-', '-', context.query.txnId, context.userId);
            }
        });
    }

    close() {
        // Cleanup logic (if needed)
    }
}
module.exports = voicePlugin;

/* loads the app start schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./voiceSchema.json");
    schema.category = "voice";
    return schema;
}
