"use strict";
/**
 *  Email Plugin
 *
 *  <AUTHOR>
 **/
const ChannelModulePlugin = require("../channelModulePlugin");
const message = require("message");
const error_codes = message.error_codes;
const CDRWriter = require("../../pluginCDRWriter");
const axios = require('axios');

const NS_PER_SEC = 1e9;

let schema;

/**
 * @class EmailPlugin
 */
class EmailPlugin extends ChannelModulePlugin {

  init() {

  }

  /**
   * method getMetaDataInfo
   * @returns
   * @memberof EmailPlugin
   */
  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  /**
   * method validate
   * @param {any} module
   * @returns
   * @memberof EmailPlugin
   */
  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  /**
   * method exec
   * @param {any} context
   * @returns
   * @memberof EmailPlugin
   */

  exec(context) {
    return new Promise((resolve) => {
      try {
        const startTime = Date.now();
        const url = global.config.pm_fsync.pluginData.ngageBaseUrl + global.config.pm_fsync.pluginData.nodeData.email.route;

        let requestObj = {
          "name": "Non Bulk campaign",
          "description": "test non bulk campaign",
          "emailBody": context.process.body,
          "category": "promo",
          "emailFromAddr": context.process.senderId,
          "emailToAddr": context.process.receiverAddress,
          "subject": context.process.subject,
          "displayName": "ECP",
          "clientTxnId": context.query.txnId,
          "sourceType": "1",
          "expDuration": "1d",
          "attachmentsPath": [],
          "drCallback": global.config.pm_fsync.pluginData.ngageBaseUrl + "/app_engine/production/" + context.appId
        };

        const config = {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': context.settings.authorization
          },
          timeout: 10000
        };

        let jsonRequest = {
          "URL": url,
          "type": "POST",
          "timeout": 10000,
          "encoding": "utf8",
          "headerBody": config.headers,
          "requestBody": requestObj,
          "rejectUnauthorized": false
        };

        // Call super.httpCall without async/await
        super.httpCall(jsonRequest)
          .then((response) => {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, "200", requestObj, response.data, context.query.txnId, context.userId);
            resolve({
              code: error_codes.success,
              msg: response.data
            });
          })
          .catch((error) => {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            if (!(error.response && error.response.status && error.response.data)) {
              // Non HTTP error code occurred
              CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, "800", requestObj, error.message, context.query.txnId, context.userId);
              resolve({
                code: error_codes.pluginInternalError,
                msg: error
              });
            } else {
              // HTTP error code has occurred
              CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, error.response.status, requestObj, error.response.data, context.query.txnId, context.userId);
              resolve({
                code: error.response.status,
                msg: error.response.data
              });
            }
          });
      } catch (error) {
        global.logger.error(error);
        resolve({
          code: error_codes.pluginInternalError,
          msg: error
        });
        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, '-', error_codes.pluginInternalError, '-', '-', context.query.txnId, context.userId);
      }
    });
  }


  close() {

  }
}
module.exports = EmailPlugin;

/* loads the app start schema file (once and only once) and returns its schema */
function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./emailSchema.json");
  schema.category = "email";
  return schema;
}