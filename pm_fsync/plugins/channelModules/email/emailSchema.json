{"typeId": "1.3", "name": "email", "title": "e-mails Push", "description": "HTTP Channel functions as a request– response protocol in the client– server computing mode", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["host", "port", "auth"], "properties": {"host": {"description": "The hostname of the SMTP server", "title": "Host", "default": "127.0.0.1", "type": "string", "format": "ipv4"}, "port": {"description": "The port number of the UCIP", "title": "Port", "default": 25, "type": "integer", "minimum": 25, "maximum": 65535}, "timeout": {"description": "The time interval defines the Request timeout in millis", "title": "Request Timeout in ms", "default": 10000, "type": "integer"}, "secure": {"description": "Content Type to send in request header", "title": "Secure", "default": false, "enum": [false, true]}, "authRequired": {"description": "Content Type to send in request header", "title": "Authorization Required", "default": false, "enum": [false, true]}, "tlsRequired": {"description": "Content Type to send in request header", "title": "tlsRequired", "default": false, "enum": [false, true]}, "sslRequired": {"description": "Content Type to send in request header", "title": "sslRequired", "default": false, "enum": [false, true]}, "sslPort": {"description": "Content Type to send in request header", "title": "sslPort", "default": 587, "type": "integer"}, "auth": {"description": "Content Type to send in request header", "title": "<PERSON><PERSON>", "type": "object", "required": ["user", "pass"], "properties": {"user": {"title": "From Email Address", "type": "string", "format": "email"}, "pass": {"title": "Password", "hint": "password", "type": "string", "minLength": 1}}}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["method"], "properties": {"to": {"description": "HTTP Request Method", "title": "To Address", "type": "string"}, "subject": {"description": "HTTP Request Method", "title": "Subject", "type": "string"}, "body": {"description": "HTTP Request Method", "title": "Email Body", "type": "string", "hint": "emailEditor"}}}, "output": {"description": "The output params", "type": "object"}}}