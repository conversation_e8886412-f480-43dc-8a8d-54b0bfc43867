"use strict";
/**
 *  SMS Plugin
 *
 *  <AUTHOR>
 **/
const ChannelModulePlugin = require("../channelModulePlugin");
const message = require("message");
const error_codes = message.error_codes;
const CDRWriter = require("../../pluginCDRWriter");
const axios = require('axios');

let schema;

/**
 * @class SMSPlugin
 */
class SMSPlugin extends ChannelModulePlugin {

    init() {
        // Initialization logic (if needed)
    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof SMSPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof SMSPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} context
     * @returns
     * @memberof SMSPlugin
     */
    exec(context) {
        return new Promise((resolve) => {
            try {
                const startTime = Date.now();
                const url = global.config.pm_fsync.pluginData.ngageBaseUrl + global.config.pm_fsync.pluginData.nodeData.sms.route;

                let requestObj = {
                    "msg": context.process.text_message,
                    "country": "IN",
                    "name": "Testing",
                    "recipient": context.process.receiverAddress,
                    "sender": context.process.senderAddress,
                    "category": "Promo",
                    "clientTxnId": context.query.txnId,
                    "drCallback": global.config.pm_fsync.pluginData.ngageBaseUrl + "/app_engine/production/" + context.appId
                };

                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': context.settings.authorization
                    },
                    timeout: 10000
                };

                let jsonRequest = {
                    "URL": url,
                    "type": "POST",
                    "timeout": 10000,
                    "encoding": "utf8",
                    "headerBody": config.headers,
                    "requestBody": requestObj,
                    "rejectUnauthorized": false
                };

                super.httpCall(jsonRequest)
                    .then(response => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, "200", requestObj, response.data, context.query.txnId, context.userId);
                        resolve({
                            code: error_codes.success,
                            msg: response.data
                        });
                    })
                    .catch(error => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        if (!(error.response && error.response.status && error.response.data)) {
                            // Non HTTP error code occurred, hence print entire error object
                            CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, "800", requestObj, error.message, context.query.txnId, context.userId);
                            resolve({
                                code: error_codes.pluginInternalError,
                                msg: error
                            });
                        } else {
                            // HTTP error code occurred
                            CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, error.response.status, requestObj, error.response.data, context.query.txnId, context.userId);
                            resolve({
                                code: error.response.status,
                                msg: error.response.data
                            });
                        }
                    });
            } catch (error) {
                global.logger.error(error);
                resolve({
                    code: error_codes.pluginInternalError,
                    msg: error
                });
                CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, '-', error_codes.pluginInternalError, '-', '-', context.query.txnId, context.userId);
            }
        });
    }

    close() {
        // Cleanup logic (if needed)
    }
}
module.exports = SMSPlugin;

/* loads the app start schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./smsSchema.json");
    schema.category = "sms";
    return schema;
}
