{"typeId": "1.1", "name": "SMS", "title": "SMS Push", "description": "This module is used to push the message using smsc", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["host", "port", "username", "password"], "properties": {"host": {"description": "The hostname of the SMSC", "title": "Host", "default": "127.0.0.1", "type": "string", "format": "ipv4"}, "port": {"description": "The port number of the SMSC", "title": "Port", "default": 3306, "type": "integer", "minimum": 1024, "maximum": 65535}, "timeout": {"description": "The time interval defines the Request timeout in millis", "title": "Request Timeout in ms", "default": 5000, "type": "integer"}, "bindTimeout": {"description": "The time interval defines the BIND Request timeout in millis", "title": "BIND Request Timeout in ms", "default": 10000, "type": "integer"}, "username": {"description": "The system id of smsc", "title": "User Name", "type": "string", "minLength": 1}, "password": {"description": "The password of that smsc user", "title": "Password", "hint": "password", "type": "string", "minLength": 1}, "system_type": {"description": "System Type", "title": "System Type", "type": "string", "default": ""}, "version": {"description": "Interface Version", "title": "Interface Version", "type": "string", "default": "3.4"}, "smsNotificationType": {"description": "This parameter describes to send either Flash SMS or Stored SMS", "title": "SMS Notification Type", "default": "Stored SMS", "type": "string", "enum": ["Stored SMS", "Flash SMS", "SIMCARD SMS"]}, "multifragmentMode": {"description": "This parameter describes to send either Flash SMS or Stored SMS", "title": "Long Message Handler", "default": "Concatenated", "type": "string", "enum": ["Concatenated", "MultiPart"]}, "maxRetry": {"description": "The Max number of retries to connect to the SMSC", "title": "maxRetry", "default": 1, "type": "integer"}, "retryInterval": {"description": "The time interval betwwen retries to connect to the SMSC in millis", "title": "retryInterval", "default": 3000, "type": "integer"}, "enquiryLinkInterval": {"description": "The time interval betwwen enquiry_link to connect to the SMSC in millis", "title": "Enquiry Link Interval", "default": 10000, "type": "integer"}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object", "properties": {}}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["source_addr", "destination_addr", "short_message"], "properties": {"source_addr": {"description": "Address from whom the message is sending", "title": "Source Address", "type": "string", "minLength": 1}, "destination_addr": {"description": "Address to whom the message is sending", "title": "Destination Address", "type": "string", "minLength": 1}, "short_message": {"description": "The message to be send", "title": "Short Message", "hint": "textAreaNS", "type": "string", "minLength": 1}}}, "response": {"title": "Response", "description": "Plugin response", "type": "object", "properties": {"responseCode": {"description": "parameter is sent back after a message has been processed and indicates success or failure of the message ", "title": "Response Code", "type": "string"}}}, "output": {"description": "The output params", "type": "object"}}}