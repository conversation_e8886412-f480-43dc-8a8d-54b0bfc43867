"use strict";
/**
 *  Zendesk Plugin
 *
 *  <AUTHOR>
 **/
const ChannelModulePlugin = require("../channelModulePlugin");
const message = require("message");
const error_codes = message.error_codes;
const CDRWriter = require("../../pluginCDRWriter");
const axios = require('axios');

const NS_PER_SEC = 1e9;

let schema;

/**
 * @class ZendeskPlugin
 */
class ZendeskPlugin extends ChannelModulePlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof ZendeskPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof ZendeskPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} context
     * @returns
     * @memberof ZendeskPlugin
     */

    exec(context) {
        return new Promise((resolve) => {
            try {
                const url = "https://" + context.settings.domain + "/api/v2/tickets.json";

                let requestObj = {
                    "ticket": {
                        "subject": context.process.subject,
                        "comment": {
                            "body": context.process.description
                        }
                    }
                };

                const config = {
                    auth: {
                        username: context.settings.email + "/token",
                        password: context.settings.api_key
                    },
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };

                const startTime = Date.now();

                axios.post(url, requestObj, config)
                    .then(response => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, "200", requestObj, response.data, context.query.txnId, context.userId);
                        resolve({
                            code: error_codes.success,
                            msg: response.data
                        });
                    })
                    .catch(error => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, error.response.status, requestObj, error.response.data, context.query.txnId, context.userId);
                        resolve({
                            code: error.response.status,
                            msg: error.response.data
                        });
                    });
            } catch (error) {
                global.logger.error(error);
                resolve({
                    code: error_codes.pluginInternalError,
                    msg: error
                });
                CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, '-', error_codes.pluginInternalError, '-', context.query.txnId, context.userId);
            }
        });
    }


    close() {

    }
}
module.exports = ZendeskPlugin;

/* loads the app start schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./zendeskSchema.json");
    schema.category = "zendesk";
    return schema;
}
