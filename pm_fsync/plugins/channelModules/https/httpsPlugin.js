"use strict";
/**
 *  HTTPS Plugin
 *
 *  <AUTHOR>
 **/
const parser = require("xml2json");
const ChannelModulePlugin = require("../channelModulePlugin");
const message = require("message");
const qs = require("querystring");
const error_codes = message.error_codes;
const CDRWriter = require("../../pluginCDRWriter");
const path = require("path");
const fs = require("fs");

const NS_PER_SEC = 1e9;

let schema;

/**
 * @class HTTPSPlugin
 */
class HTTPSPlugin extends ChannelModulePlugin {

    init() {
        // Initialization logic (if needed)
    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof HTTPSPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof HTTPSPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} context
     * @returns
     * @memberof HTTPSPlugin
     */
    exec(context) {
        let startTime = process.hrtime(), diff;
        return new Promise((resolve) => {
            let time = process.hrtime(),
                httpRequest,
                diagnostics = {},
                httpResponse,
                response;
            try {
                context.pluginName = "https";
                if (context.process.headers) {
                    context.process.headers = setHeaders(context.process.headers);
                }

                context.process.headers["Authorization"] = "Basic " + Buffer.from(context.process.headers.Authorization).toString("base64");

                context.settings.url = "https://" + context.process.host + ":" + context.process.port + context.process.path;

                let timeout = context.settings.timeout || 30000;
                diagnostics.url = context.settings.url;
                diagnostics.timeout = timeout;

                httpRequest = {
                    headers: context.process.headers || { "Content-Type": "text/html" },
                    url: context.settings.url,
                    method: context.process.method || "GET",
                    timeout,
                    agentOptions: {
                        rejectUnauthorized: false
                    }
                };

                try {
                    if (context.process.certFilePath != null) {
                        httpRequest.cert = fs.readFileSync(path.resolve(context.process.certFilePath));
                    }
                    if (context.process.keyFilePath != null) {
                        httpRequest.key = fs.readFileSync(path.resolve(context.process.keyFilePath));
                    }
                    if (context.process.caFilePath != null) {
                        httpRequest.ca = fs.readFileSync(path.resolve(context.process.caFilePath));
                    }
                    if (context.process.passphrase != null) {
                        httpRequest.passphrase = context.process.passphrase;
                    }
                    httpRequest.rejectUnauthorized = context.process.rejectUnauthorized;
                    httpRequest.securityOptions = "TLSv1_2_method";
                } catch (e) {
                    // ignore
                }

                if (httpRequest.method != "GET") {
                    let contentType = httpRequest.headers["Content-Type"] || "text/html";
                    if (contentType.includes("json")) {
                        httpRequest.json = context.process.form;
                        diagnostics.payload = context.process.form;
                    } else if (contentType.includes("xml")) {
                        httpRequest.body = context.process.form;
                    } else {
                        diagnostics.payload = context.process.form;
                        httpRequest.form = context.process.form;
                    }
                }
                global.logger.info("HTTPS Request:" + httpRequest.url + ",Body" + httpRequest.body);
                diff = process.hrtime(time);
                diagnostics.serializerTime = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
                time = process.hrtime();

                // Use .then() for the async call
                super.httpCall(httpRequest)
                    .then(httpResponse => {
                        global.logger.info("HTTPS Response:" + JSON.stringify(httpResponse));
                        diagnostics.elapsedTime = httpResponse.diagnostics.elapsedTime;
                        diagnostics.responseStartTime = httpResponse.diagnostics.responseStartTime;
                        diagnostics.timingStart = httpResponse.diagnostics.timingStart;
                        diagnostics.timings = httpResponse.diagnostics.timings;
                        diagnostics.timingPhases = httpResponse.diagnostics.timingPhases;
                        diff = process.hrtime(time);
                        diagnostics.httpRTT = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
                        time = process.hrtime();

                        if (httpResponse.code == 200 || httpResponse.code == 500) {
                            try {
                                let contentType = httpResponse.headers["content-type"];
                                diagnostics.contentType = String(contentType);
                                if (contentType == null)
                                    contentType = "application/json";
                                if (contentType.includes("xml")) {
                                    response = JSON.parse(parser.toJson(httpResponse.body));
                                } else if (contentType.includes("json") && typeof httpResponse.body == "string") {
                                    response = JSON.parse(httpResponse.body);
                                } else {
                                    response = httpResponse.body;
                                }
                            } catch (error) {
                                global.logger.error(error);
                                response = httpResponse.body;
                                let newRes = response;
                                if (newRes != null) {
                                    let mySubString = newRes.substring(
                                        newRes.lastIndexOf("<?") + 0,
                                        newRes.lastIndexOf("pe>") + 3
                                    );
                                    response = JSON.parse(parser.toJson(mySubString));
                                }
                            }
                        } else {
                            response = message.getResponseJson(context.language, httpResponse.code);
                        }
                        diff = process.hrtime(time);
                        diagnostics.deserializerTime = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
                        diff = process.hrtime(startTime);
                        diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
                        global.logger.info("Final HTTPS response:" + JSON.stringify(response));
                        resolve({
                            code: error_codes.success,
                            msg: response
                        });
                        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, diagnostics.pluginResponse, httpResponse.code, diagnostics);
                    })
                    .catch(error => {
                        global.logger.error(error);
                        diff = process.hrtime(startTime);
                        diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
                        resolve({
                            code: error_codes.pluginInternalError,
                            msg: error
                        });
                        CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, diagnostics.pluginResponse, error_codes.pluginInternalError, diagnostics);
                    });
            } catch (error) {
                global.logger.error(error);
                diff = process.hrtime(startTime);
                diagnostics.pluginResponse = Number((diff[0] * NS_PER_SEC + diff[1]) / 1000000).toFixed();
                resolve({
                    code: error_codes.pluginInternalError,
                    msg: error
                });
                CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, diagnostics.pluginResponse, error_codes.pluginInternalError, diagnostics);
            }
        });
    }

    close() {
        // Cleanup logic (if needed)
    }
}
module.exports = HTTPSPlugin;

/* loads the app start schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./httpsSchema.json");
    schema.category = "http";
    return schema;
}

function setHeaders(headers) {
    let keys = Object.keys(headers);
    let res = {};
    try {
        for (let i = 0; i < keys.length; i++) {
            let obj = headers[keys[i]];
            if (obj.key.trim().length > 0) {
                res[obj.key] = obj.value;
            }
        }
    } catch (error) {
        // Handle error if needed
    }
    return res;
}
