{"typeId": "1.4", "name": "https", "title": "HTTPS Push", "description": "HTTPS Channel functions as a request– response protocol in the client– server computing mode", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object"}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["method"], "properties": {"method": {"description": "HTTPS Request Method", "title": "HTTPS Request Method", "enum": ["GET", "POST", "PUT", "DELETE", "PURGE"]}, "host": {"description": "The hostname of the Connecting Server", "title": "Host", "default": "127.0.0.1", "type": "string", "format": "ipv4"}, "port": {"description": "The port number of the Connecting Server", "title": "Port", "default": 3306, "type": "integer", "minimum": 1024, "maximum": 65535}, "headers": {"description": "Request Headers", "title": "Request Header", "hint": "key-value", "type": "object"}, "path": {"description": "Path of the Connecting Server", "title": "Path", "type": "string", "minLength": 1}, "keyFilePath": {"description": "Key file path of the Connecting Server", "title": "Key File Path", "type": "string"}, "certFilePath": {"description": "Certificate file path of the Connecting Server", "title": "Certificate File Path", "type": "string"}, "caFilePath": {"description": "Certificate file path of the Connecting Server", "title": "CA Certificate File Path", "type": "string"}, "qs": {"description": "Request Parameters with key-value", "title": "Request Params", "hint": "key-value", "type": "object"}, "form": {"description": "Request payload", "title": "Request Payload"}}, "oneOf": [{"properties": {"method": {"enum": ["POST", "PUT", "DELETE", "PURGE"]}}, "required": ["form"]}, {"properties": {"method": {"enum": ["GET"]}}, "required": ["qs"]}]}, "output": {"description": "The output params", "type": "object"}}}