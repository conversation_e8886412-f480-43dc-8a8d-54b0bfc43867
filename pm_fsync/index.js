"use strict";

const path = require("path");
const fs = require("fs");
const args = require("commander");
const cluster = require("cluster");
const express = require("express");
const bodyParser = require("body-parser");
const ConfigTree = require("config-tree");
const OAM = require("oam");
const os = require("os");
const common = require("common");
const whiteboard = common.whiteboard;


initBasic()
  .then(async () => {
    await OAM.init(global.config.oam);
    global.logger.warn("Config loaded successfully");
    global.componentName = "pm_fsync";
    OAM.emit("clearAlert", "pmfsync_configserver_conn");

    global.logger.warn("Finished with basic initialization.");
    global.logger.warn("------------------------------------");
    global.logger.warn();

    // Find out if we have to run in single-proc mode or cluster-mode.
    global.clusterSize = getClusterSize();
    if (global.clusterSize > 1) {
      return runInClusterMode();
    } else {
      return runInSingleMode();
    }
  })
  .catch(e => {
    console.error("error while starting plugnmanager file service...", e);
    OAM.raiseCriticalAlert("pmfsync_configserver_conn")
      .then(() => {
        process.exit(1);
      }).catch(err => {
        console.error("Failed to Critical alert for PM Fsync process", err);
        process.exit(1);
      });
  });

async function initBasic() {

  args
    .version(common.version)
    .option("-H, --host [host]", "Config server host -- defaults to", process.env.CONFIG_HOST || "127.0.0.1")
    .option("-p, --port [port]", "Config server port -- defaults to", process.env.CONFIG_PORT || 6380)
    .option("-n, --db [db]", "Config server db index -- defaults to", process.env.CONFIG_DB || 0)
    .option("-a, --password [password]", "config server password", process.env.CONFIG_AUTH || undefined)
    .parse(process.argv);

  printAppInfo();
  global.args = args.opts();
  let opts = {
    args: global.args,
    configTasks: [
      "getDBInfo",
      "getModuleInfoEx",
      "getGlobalInfo"
    ],
    keys2read: {
      getGlobalInfo: ["whiteboard", "timezone", "oam", "security"],
      getDBInfo: ["app_store"],
      getModuleInfoEx: ["pm_fsync", "api_gw"]
    }
  };
  const ConfigProxy = new ConfigTree(opts);

  return ConfigProxy.readConfig()
    .then(async () => {
      global.componentName = "pm_fsync";
      delete require.cache[require.resolve("./logger")];
      require("./logger");
      ConfigProxy.on("reload_config", async (pattern, channel, key) => {
        try {
          delete require.cache[require.resolve("./logger")];
          require("./logger");
        } catch (error) {
          global.logger.error(error);
        }
      });
    })
    .catch(e => {
      console.error("Failed to start pm_fsync , Connection to:", global.args.host, global.args.port, "Failed", e);
      OAM.raiseCriticalAlert("pm_fsync")
        .then(() => {
          process.exit(1);
        }).catch(err => {
          console.error("Failed to Critical alert for PM Fsync process", err);
          process.exit(1);
        });
    });
}

async function initCall() {
  try {
    const contextPath = global.config.pm_fsync.contextPath || "/pm_fsync";
    const protocol = global.config.pm_fsync.protocol || "http";
    const port = global.config.pm_fsync.port || 4999;
    console.warn("Restify contextPath:" + contextPath);

    whiteboard.init(global.config.whiteboard);
    await require("./pm_store/index").init();
    require("./proxy/services").init();
    const app = express();
    const pluginsRoutes = require("./proxy/routes");
    app.use(bodyParser.json());
    app.use(contextPath + "/", pluginsRoutes);
    const server = (protocol === "http") ? createServer(app) : createSecureServer(app);
    server.listen(port, () => {
      console.warn("Plugin Manager File Hosting Service running at port: " + port);
      OAM.emit("clearAlert", "pm_fsync");
    });
  } catch (error) {
    console.error("pm_fsync loading failed", error);
    OAM.raiseCriticalAlert("pm_fsync")
      .then(() => {
        process.exit(1);
      }).catch(err => {
        console.error("Failed to Critical alert for PM Fsync process", err);
        process.exit(1);
      });
  }
}

function createServer(app) {
  const http = require("http");
  global.logger.warn("Preparing PluginManager FileSync server...");
  return http.createServer(app);
}

function createSecureServer(app) {
  const https = require("https");
  global.logger.warn("Preparing secure PluginManager FileSync server...");
  let privateKey = fs.readFileSync(path.resolve(__dirname, "./key.pem"), "utf8");
  let certificate = fs.readFileSync(path.resolve(__dirname, "./server.crt"), "utf8");
  let credentials = { key: privateKey, cert: certificate };
  return https.createServer(credentials, app);
}

function getClusterSize() {
  let configuredCPUs = global.config.pm_fsync && global.config.pm_fsync.clusterCPUs || 1;
  global.cpus = os.cpus().length;
  return Math.min(configuredCPUs, global.cpus);
}

function runInSingleMode() {
  global.logger.warn("Running the application in single-proc mode...");
  return initCall();
}

function runInClusterMode() {
  global.logger.warn("Running the application in cluster mode...");
  if (cluster.isMaster) {
    global.logger.warn("Master cluster setting up " + global.clusterSize + " workers...");
    for (let i = 0; i < global.clusterSize; i++) {
      cluster.fork();
    }
    cluster.on("online", worker => {
      global.logger.warn("Worker " + worker.process.pid + " is online");
    });
    cluster.on("exit", (worker, code, signal) => {
      global.logger.warn("Worker %s died with code: %s, and signal: %s ", worker.process.pid, code, signal);
      global.logger.warn("Starting a new worker");
      cluster.fork();
    });
  } else {
    initCall();
  }
}

/**
 *  Prints application title and version.
 **/
function printAppInfo() {
  let banner = fs.readFileSync(path.join(__dirname, "./banner.txt")).toString();
  console.log(banner);
}

process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);

process.on("unhandledRejection", error => {
  // Will print "unhandledRejection err is not defined"
  global.logger.error(error);
});

process.on("uncaughtException", (err) => {
  global.logger.error(err);
});

process.on("unhandledRejection", (reason, p) => {
  global.logger.error(reason, p);
});

function shutdown() {
  console.log("Received kill signal. Initiating shutdown...");
  OAM.raiseCriticalAlert("pm_fsync")
    .then(() => {
      process.exit(1);
    }).catch(err => {
      console.error("Failed to Critical alert for PM Fsync process", err);
      process.exit(1);
    });
}
