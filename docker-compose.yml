version: "3.2"

services:
  pm_fsync:
    image: leap/pm_fsync:$VERSION
    build: ./pm_fsync
    restart: always
    user: $UID:$GID
    volumes:
      - /data/leap/:/data/leap/
      - /etc/passwd:/etc/passwd:ro
      - ./leap-docker-compose-files/plugins:/home/<USER>/app/plugins
    network_mode: host
    # env_file:
    #   - ./leap-docker-compose-files/docker.env
  
  session_extender:
    image: leap/session_extender:$VERSION
    build: ./session_extender
    restart: always
    user: $UID:$GID
    volumes:
      - /data/leap/:/data/leap/
      - /etc/passwd:/etc/passwd:ro
      - ./leap-docker-compose-files/plugins:/home/<USER>/app/plugins
    network_mode: host
    # env_file:
    #   - ./leap-docker-compose-files/docker.env

  api_gw:
    image: leap/api_gw:$VERSION
    build: ./api_gw
    restart: always
    user: $UID:$GID
    depends_on:
      - pm_fsync
    volumes:
      - /data/leap:/data/leap/
      - /etc/passwd:/etc/passwd:ro
      - ./leap-docker-compose-files/certs/ui/certificates:/prd/leap/config
    network_mode: host
    # env_file:
    #   - ./leap-docker-compose-files/docker.env

  authserver:
    image: leap/authserver:$VERSION
    build : ./AuthServer
    restart: always
    user: $UID:$GID
    network_mode: host
    # env_file:
      # - ./leap-docker-compose-files/docker.env
    volumes:
      - /data/leap:/data/leap/
      - /etc/passwd:/etc/passwd:ro
      - ./leap-docker-compose-files/certs/ui/certificates:/prd/leap/config

  app_engine_dev:
    image: leap/app_engine:$VERSION
    build: ./app_engine
    restart: always
    user: $UID:$GID
    network_mode: host
    # env_file:
    #   - ./leap-docker-compose-files/docker.env
    environment:
      - LEAP_APP_ENGINE=development
    volumes:
      - /data/leap:/data/leap/
      - ./leap-docker-compose-files/certs/ui/certificates:/prd/leap/config
      - /etc/passwd:/etc/passwd:ro

  app_engine_staging:
    image: leap/app_engine:$VERSION
    restart: always
    user: $UID:$GID
    network_mode: host
    # env_file:
    #   - ./leap-docker-compose-files/docker.env
    environment:
      - LEAP_APP_ENGINE=staging
    volumes:
      - /data/leap:/data/leap/
      - /etc/passwd:/etc/passwd:ro
      - ./leap-docker-compose-files/certs/ui/certificates:/prd/leap/config

  gui:
    image: leap/gui:$VERSION
    restart: always
    build: ./studio
    command: npm run open:dist | PNAME:GUI
    network_mode: host
    volumes:
      - ./leap-docker-compose-files/certs/ui/certificates/server.crt:/prd/leap/config/server.crt
      - ./leap-docker-compose-files/certs/ui/certificates/server.key:/prd/leap/config/server.key

  cdr_sweeper:
    image: leap/cdr_sweeper:$VERSION
    build: ./cdr_processor/cdr_sweeper
    user: $UID:$GID
    restart: always
    volumes:
      - /data/leap:/data/leap/
      - /etc/passwd:/etc/passwd:ro
    network_mode: host
    # env_file:
    #   - ./leap-docker-compose-files/docker.env
    environment:
      - NODE_OPTIONS=--max-old-space-size=2048

  node_logger:
    image: leap/node_logger:$VERSION
    build: ./cdr_processor/nodeLogger
    user: $UID:$GID
    restart: always
    volumes:
      - /data/leap:/data/leap/
      - /etc/passwd:/etc/passwd:ro
    network_mode: host
    # env_file:
    #   - ./leap-docker-compose-files/docker.env
    environment:
      - NODE_OPTIONS=--max-old-space-size=2048

  influxdb:
    image: influxdb
    restart: always
    network_mode: host
    environment:
      - INFLUXDB_REPORTING_DISABLED=true
      - INFLUXDB_DATA_QUERY_LOG_ENABLED=false
      - INFLUXDB_HTTP_LOG_ENABLED=false
    volumes:
      - data_influxdb:/var/lib/influxdb
      - ./leap-docker-compose-files/conf/influxdb.conf:/etc/influxdb/influxdb.conf:ro

  telegraf:
    image: telegraf
    restart: always
    network_mode: host
    environment:
      - HOST_PROC=/rootfs/proc
      - HOST_SYS=/rootfs/sys
      - HOST_ETC=/rootfs/etc
      - HOST_MOUNT_PREFIX=/rootfs
    volumes:
      - ./leap-docker-compose-files/conf/telegraf.conf:/etc/telegraf/telegraf.conf:ro
      - /:/rootfs:ro

  grafana:
    image: grafana/grafana
    restart: always
    network_mode: host
    volumes:
      - ./leap-docker-compose-files/certs/ui/certificates:/prd/leap/config
      - data_grafana:/var/lib/grafana
    environment:
      - GF_SERVER_HTTP_PORT=3000
      - GF_SERVER_ROOT_URL=http://127.0.0.1:3000/grafana
      - GF_SERVER_SERVE_FROM_SUB_PATH=true
      - GF_LOG_MODE=console
      - GF_LOG_CONSOLE_LEVEL=error

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.5.2
    restart: always
    # env_file:
    #   - ./leap-docker-compose-files/es.env
    environment:
      - cluster.name=leap-cluster
      - node.data=true
      - node.master=true
      - bootstrap.memory_lock=true
      - xpack.security.enabled=true
      - "ES_JAVA_OPTS=-Xms2048m -Xmx2048m -Xlog:disable"
      - xpack.security.http.ssl.enabled=true
      - xpack.security.transport.ssl.enabled=true
      - xpack.security.http.ssl.key=certs/ess.key
      - xpack.security.http.ssl.certificate=certs/ess.crt
      - xpack.security.http.ssl.certificate_authorities=certs/ca.crt
      - xpack.security.transport.ssl.key=certs/ess.key
      - xpack.security.transport.ssl.certificate=certs/ess.crt
      - xpack.security.transport.ssl.certificate_authorities=certs/ca.crt
      - http.port=9007
      - xpack.security.transport.ssl.verification_mode=none
      - xpack.security.http.ssl.verification_mode=none
    volumes:
      - data_es:/usr/share/elasticsearch/data
      - type: bind
        source: ./leap-docker-compose-files/certs/elastic
        target: /usr/share/elasticsearch/config/certs
    network_mode: host
    ulimits:
      memlock:
        soft: -1
        hard: -1

  logstash:
    image: docker.elastic.co/logstash/logstash:7.5.2
    restart: always
    network_mode: host
    user: root
    environment:
      - xpack.monitoring.enabled= false
      - LOG_LEVEL=error
    volumes:
      - data_logstash:/usr/share/logstash/data
      - /data/leap:/data/leap/
      - ./leap-docker-compose-files/certs/elastic/ca.crt:/usr/share/logstash/config/certs/ca/ca.crt
      - type: bind
        source: ./leap-docker-compose-files/conf/logstash-auditTrail.conf
        target: /usr/share/logstash/pipeline/logstash.conf
  
  esbgw:
    image: leap/esbgw:3.0.8
    restart: always
    # env_file:
    #   - ./leap-docker-compose-files/env
    environment:
      PORT: 9014
      AUTH: "true"
    network_mode: host
    extra_hosts:
      - app_engine:**************
    volumes:
      - ./leap-docker-compose-files/conf/esb/bundle.datasource.config.json:/home/<USER>/app/dist/datasources/bundle.datasource.config.json
      - ./leap-docker-compose-files/clients.json:/home/<USER>/app/data/clients.json

  # openresty:
  #   image: openresty/openresty:********-2-fedora-lua
  #   network_mode: host
  #   restart: always
  #   volumes:
  #     - "./leap-docker-compose-files/nginx/conf.d:/etc/nginx/conf.d"
  #     - "./leap-docker-compose-files/nginx/certs:/etc/nginx/certs"
  #     - "./leap-docker-compose-files/nginx/include:/etc/nginx/include"
  #     - "./leap-docker-compose-files/nginx/script:/etc/nginx/script"
  #     - "./leap-docker-compose-files/nginx/html:/usr/local/openresty/nginx/html"
  #     - "nginx_log:/var/log/nginx"

volumes:
  data_es:
    driver: local
  data_grafana:
    driver: local
  data_logstash:
    driver: local
  data_influxdb:
    driver: local
